{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ValidHeaders": [ "agentid", "asterisktoken" ],
  "ValidOrigins": [ "https://progressiveqa.policybazaar.com", "localhost", "https://mobilematrix.policybazaar.com", "https://matrix.policybazaar.com", "https://matrixliveapi.policybazaar.com", "https://mobilematrixapi.policybazaar.com", "https://claim.policybazaar.com", "https://pbsupport.policybazaar.com", "https://matrixdashboard.policybazaar.com", "https://verification.policybazaar.com", "https://bms.policybazaar.ae", "https://pbmeet.policybazaar.com" ],
  "AllowedHosts": "*",
  "Communication": {
    "Environment": "UAT",
    "ConfigKey": "MatrixCoreConfigValues",

    "ConnectionString": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=MatrixCore;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=MatrixCore;",
      //"LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=BackofficeSys;Password=***********;MultiSubnetFailover=True;",
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=MatrixCore;"
    },

    "ProductDBConnection": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=MatrixCore;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=MatrixCore;",
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=BackofficeSys;Password=***********;MultiSubnetFailover=True;Application Name=MatrixCore;"
    },
    "IncentiveDBsqlConnection": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=MatrixCore;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=MatrixCore;",
      "LIVE": "Data Source=pbsqldb.cjbifvsxxi4u.ap-south-1.rds.amazonaws.com;MultisubnetFailover=True;Database=PBIncentive;user Id=PBIncentive_App;Password=**********;MultiSubnetFailover=True;Application Name=MatrixCore;"
    },
    "ReplicaConnectionString": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;Application Name=MatrixCore;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=MatrixCore;",
      "LIVE": "Data Source=PBSQL-PROD-SEC2.etechaces.com;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=MatrixCore;"
      //"LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;"
    },
    "RedisConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "MultiProdRedisConnection": {
      "DEV": "qa-matrix-lead-assignment-001.qa-matrix-lead-assignment.kskv7l.aps1.cache.amazonaws.com:6379",
      "UAT": "qa-matrix-lead-assignment-001.qa-matrix-lead-assignment.kskv7l.aps1.cache.amazonaws.com:6379",
      "LIVE": "master.prod-matrix-lead-assignment.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "MongoDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "************************************************************************;connectTimeoutms=40000&amp;socketTimeoutMS=40000",
      "LIVE": "***************************************************************************************************************************;replicaSet=rs3;readPreference=secondary;connectTimeoutms=8000;socketTimeoutMS=8000"
    },
    "MongoGridFSConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "*********************************************************************************;connectTimeoutms=40000&amp;socketTimeoutMS=8000",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "MongoLogDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "**********************************************************************;connectTimeoutms=8000;socketTimeoutMS=8000",
      "LIVE": "*******************************************************************************************************************"
    },
    "ChatDBConnection": {
      "DEV": "***************************************************************************************************************************************",
      "UAT": "***************************************************************************************************************************************",
      "LIVE": "*********************************************************************************************************************************************************************************************************"
    },
    "OneLeadDBConnection": {
      "DEV": "******************************************************************",
      "UAT": "mongodb://oneLeadUsr:0neLea98jnjdhg3!@mdb-qa1.policybazaar.com:27017,mdb-qa2.policybazaar.com:27017,mdb-qa3.policybazaar.com:27017/oneLeadDB?authSource=oneLeadDB&replicaset=rs6&readPreference=secondaryPreferred&connectTimeoutMS=10000&socketTimeoutMS=6000&ssl=false",
      //"UAT": "****************************************************************************",
      //"UAT": "**********************************************************************;connectTimeoutms=8000;socketTimeoutMS=4000",

      "LIVE": "***********************************************************************************************************************************************************************************************************************************************"
    },
    "RealTimeDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "*******************************************************************",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    //"MatrixDashboardConnection": {
    //  "DEV": "**********************************************************************************************************",
    //  "UAT": "**********************************************************************************************************",
    //  "LIVE": "****************************************************************************************************************************"
    //},

    "MatrixDashboardConnection": {
      "DEV": "******************************************************************",
      "UAT": "*******************************************************************************************************;connectTimeoutms=8000;socketTimeoutMS=8000",
      "LIVE": "************************************************************************************************************************;connectTimeoutms=8000;socketTimeoutMS=8000"
    },
    "KafkaBootstrapServers": {
      "DEV": "***********:9092",
      "UAT": "***********:9092",
      "LIVE": "***********:9092,************:9092,************:9092"
    },
    "KafkaUserName": {
      "DEV": "bmskfk",
      "UAT": "bmskfk",
      "LIVE": "bmskfk"
    },
    "KafkaPassword": {
      "DEV": "bkls76298764",
      "UAT": "bkls76298764",
      "LIVE": "bms76298764"
    },
    "AWSSecretEnvironment": {
      "DEV": "QA_Matrix",
      "UAT": "QA_Matrix",
      "LIVE": "Prod_Matrix"
    },
    "MultiProdAWSSecretEnvironment": {
      "DEV": "MultiProdRedisQA",
      "UAT": "MultiProdRedisQA",
      "LIVE": "MultiProdRedisLive"
    },
    "IsAWSSceretEnabled": {
      "DEV": "true",
      "UAT": "true",
      "LIVE": "true"
    },
    "IsMultiProdAWSSceretEnabled": {
      "DEV": "true",
      "UAT": "true",
      "LIVE": "true"
    },
    "RedisPassword": {
      "DEV": "hfS4X265vASb83F1LKags7BV",
      "UAT": "hfS4X265vASb83F1LKags7BV",
      "LIVE": "Fyt31c7B8gsS8B6a65vKJ"
    },
    "MultiProdAssigRedisPass": {
      "DEV": "ds437fghyDFM1tsa",
      "UAT": "ds437fghyDFM1tsa",
      "LIVE": "ds457fgghDAM1tzx"
    },
    "ChatDataBase": {
      "DEV": "rocketchat_test",
      "UAT": "rocketchat_test",
      "LIVE": "PBLiveChatCommon"
    },
    "TestRedisConnection": {
      "DEV": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      "UAT": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      "LIVE": "master.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379,replica.customerrevist-new.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "RealTimeDB": "RealTimeDB",
    "MongoDBCommunicationDB": "communicationD",
    "MongoGridFSDB": "communicationD",
    "MongoDBLogging": "logger",
    "ChatDBcommon": "PBLiveChatCommon",
    "OneLeadDB": "oneLeadDB",
    "MatrixDashboard": "MatrixDashboard",
    "SaltKey": "PSVJQRk9QTEpNVU1DWUZCRVFGV1VVT0ZOV1RRU1NaWQ=",
    "InitializationVectorKey": "WVdsRkxWRVpaVUZOYVdsaA==",
    "SQSLogingQueueUrl": "https://sqs.ap-south-1.amazonaws.com/721537467949/LoggingQueue",
    "CJ_Key": "PB9MUI18RLIghJ2NA6TH0JI73U1NaWQASASDADADAAS=",
    "CJ_IV": "YUJAGTH1MH3AYT9UAKEA0YLWISDDSDDASASDASDAASD=",
    "FundValue_Key": "YzJGdWFtVmxka2x1ZG1WemRHMWxiZz09",
    "FundValue_IV": "c2FuamVldkludmVzdG1lbg==",
    "VersioNum": "09-11-2022 22:41",
    "ai_fos_appointment_authentication": "https://sqs.ap-south-1.amazonaws.com/721537467949/ai_fos_appointment_authentication"


  }

}
