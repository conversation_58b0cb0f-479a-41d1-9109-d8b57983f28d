using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System;
using EmailCommunicationBLL;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json.Serialization;
using Amazon.S3.Transfer;
using Amazon.S3;
using Microsoft.AspNetCore.ResponseCompression;
using System.IO.Compression;
using GlobalErrorHandling.Extensions;
using Microsoft.AspNetCore.HttpOverrides;

namespace MatrixCoreAPI
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }
        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {

            try
            {
                services.Configure<CookiePolicyOptions>(options =>
                {
                    // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                    options.CheckConsentNeeded = context => true;
                    options.MinimumSameSitePolicy = SameSiteMode.None;
                });
                services.AddCors();
                //services.AddCors(options =>
                //{

                //    options.AddPolicy(MyAllowSpecificOrigins,
                //   builder =>
                //   {

                //       //builder.WithOrigins(Configuration.GetSection("ValidOrigins").Get<string[]>()).
                //       builder.AllowAnyOrigin().
                //          AllowAnyHeader().AllowAnyMethod();

                //   });
                //});


                services.Configure<GzipCompressionProviderOptions>
                    (options => options.Level = CompressionLevel.Optimal);
                services.AddResponseCompression(options =>
                                {
                                    options.EnableForHttps = true;
                                    options.Providers.Add<GzipCompressionProvider>();
                                });

                services.AddMvc();
                services.AddControllersWithViews();

                services.AddDefaultAWSOptions(Configuration.GetAWSOptions());
                services.AddAWSService<IAmazonS3>();
                services.AddTransient<IUploadFiletoS3BLL, UploadFiletoS3BLL>();
                services.AddTransient<ISendCommunicationBLL, SendCommunicationBLL>();
                services.AddTransient<IUpdateRenewalLeadsBLL, UpdateRenewalLeadsBLL>();
                services.AddTransient<TransferUtility>();
                services.AddTransient<ISendCommunicationBLL, SendCommunicationBLL>();
                services.AddSingleton<ILeadPrioritizationBLL, LeadPrioritizationBLL>();
                services.AddSingleton<ISalesViewBLL, SalesViewBLL>();
                services.AddSingleton<ILeadDetailsBLL, LeadDetailsBLL>();
                services.AddScoped<IFOSBLL, FOSBLL>();
                services.AddSingleton<ITicket, TicketBLL>();
                services.AddSingleton<ISME, SMEBLL>();
                services.AddSingleton<INotification, NotificationBLL>();
                services.AddSingleton<IBmsBLL, BmsBLL>();
                services.AddSingleton<IWebSiteServiceBLL, WebSiteServiceBLL>();
                services.AddSingleton<IMasterBLL, MasterBLL>();
                services.AddTransient<IUserDetailsBLL, UserDetailsBLL>();
                services.AddSingleton<IFileUploader, FileUploader>();
                services.AddSingleton<IMRSBLL, MRSBLL>();
                services.AddSingleton<IChatBLL, ChatBLL>();
                services.AddSwaggerGen();
                services.AddSingleton<IPGBLL, PGBLL>();
                services.AddSingleton<IHealthRenewalBLL, HealthRenewalBLL>();
                services.AddSingleton<ICommunicationBLL, CommunicationBLL>();
                services.AddSingleton<IDialerBll, DialerBll>();
                services.AddSingleton<IMTXPlusLoginBLL, MTXPlusLoginBLL>();
                services.AddSingleton<IFeedbackBLL, FeedbackBLL>();
               // services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
                services.AddControllers().AddNewtonsoftJson();
                services.AddMvc().AddNewtonsoftJson(options => { options.SerializerSettings.ContractResolver = new DefaultContractResolver(); }); ;
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in ConfigureServices." + ex.ToString());
            }

        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            try
            {
                //app.UseAuthenicateUser();
                app.UseResponseCompression();
                

                app.UseDeveloperExceptionPage();
                app.UseCors(x => x
               .AllowAnyMethod()
               .AllowAnyHeader()
               .SetIsOriginAllowed(origin => true) // allow any origin
               .AllowCredentials()); // allow credentials

                

                app.UseStaticFiles();
                app.UseForwardedHeaders(new ForwardedHeadersOptions
                {
                    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto,
                    ForwardLimit = null
                });
                app.UseRouting();
                //app.UseCors(MyAllowSpecificOrigins);

                if (env.IsDevelopment())
                {
                    app.UseSwagger();
                    app.UseSwaggerUI();
                }
                app.ConfigureExceptionHandler();//for globally catch error
                app.UseEndpoints(endpoints =>
                {
                    endpoints.MapControllerRoute("default", "{controller=Home}/{action=Index}");
                });
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in Configure." + ex.ToString());
            }

        }


    }
}
