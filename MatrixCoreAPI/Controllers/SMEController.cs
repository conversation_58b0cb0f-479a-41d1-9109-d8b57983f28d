﻿using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using EmailCommunicationBLL;
using PropertyLayers;
using System;

namespace MatrixCoreAPI.Controllers
{
    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class SMEController : Controller
    {

        ISME objSMEBLL;
        public SMEController(ISME _objSMEBLL)
        {
            objSMEBLL = _objSMEBLL;
        }
        [HttpPost]
        public SaveInfo ReAssignTransferLead([FromBody] AssignLeadModal oAssignLeadModal)
        {
            SaveInfo result = null;

            if (oAssignLeadModal != null && oAssignLeadModal.LeadId > 0 && oAssignLeadModal.JobId > 0 && oAssignLeadModal.AssignTo > 0 && oAssignLeadModal.AssignBy > 0)
            {
                result = objSMEBLL.ReAssignTransferLead(oAssignLeadModal);
            }
            return result;
        }
        [HttpGet]
        public List<SubProductListModal> GetSMETransferSubProducts()
        {
            List<SubProductListModal> response = null;
            response = objSMEBLL.GetSMETransferSubProducts();
            return response;
        }

        [HttpGet]
        public List<SmeIndustryType> GetSmeIndustryTypes()
        {
            return objSMEBLL.GetSmeIndustryTypes();
        }


        [HttpGet]
        public QuickSightUrl GetQuickSightURL()
        {
            return objSMEBLL.GetQuickSightURL(Request?.Headers?["AgentId"]);
        }

        [HttpGet]
        public List<MyLeadData> GetMyLeadsData(long LeadId)
        {
            return objSMEBLL.GetMyLeadsData(Request?.Headers?["AgentId"], LeadId);
        }

        [HttpGet]
        public List<MyLeadData> GetMyRenewalData()
        {
            return objSMEBLL.GetMyRenewalData(Request?.Headers?["AgentId"]);
        }

        [HttpGet]
        public SMEMasterList GetSmeInsurerMaster()
        {
            return objSMEBLL.GetSmeInsurerMaster();
        }

        [HttpPost]
        public SaveInfo UpdateSmeLeadDetails(MyLeadData data)
        {
            return objSMEBLL.UpdateSmeLeadDetails(data, Request?.Headers?["AgentId"]);
        }

        [HttpGet("{AgentType}")]
        public SalesPartnerAndSalesSpecialist GetAgentsByType(string AgentType)
        {
            return objSMEBLL.GetAgentsByType(AgentType);
        }

        [HttpPost]
        public SaveInfo CreateSmeMom(MOMData data)
        {
            return objSMEBLL.CreateSmeMom(data, Request?.Headers?["AgentId"]);
        }

        [HttpGet]
        public List<MOMData> GetSmeMomData(long CustomerId)
        {
            return objSMEBLL.GetSmeMomData(CustomerId, Request?.Headers?["AgentId"]);
        }

        [HttpPost]
        public SaveInfo SaveSmeFeedback(FeedbackData data)
        {
            data.UserId = Request?.Headers?["AgentId"];
            return objSMEBLL.SaveSmeFeedback(data);
        }

        [HttpGet]
        public string GetMobileNo(long LeadId, int ActionType, long CustomerId, long PrimaryId)
        {
            long UserId = Convert.ToInt64(Request?.Headers?["AgentId"]);
            return objSMEBLL.GetMobileNo(LeadId, UserId, ActionType, CustomerId, PrimaryId);
        }

        [HttpPost]
        public NewSVURLModel GeneratePerLifeRateURL([FromBody] GeneratePerLifeRateRequest request)
        {
            return objSMEBLL.GeneratePerLifeRateURL(request);
        }

        [HttpGet]
        public List<Master> GetMappingValues()
        {
            return objSMEBLL.GetMappingValues();
        }
        [HttpGet]
        public List<LeadInfo> GetCustomerOpenLeads(long customerId, int subProductId)
        {
            long userId = Convert.ToInt64(Request?.Headers?["AgentId"]);
            return objSMEBLL.GetCustomerOpenLeads(customerId, subProductId, userId);
        }

        [HttpGet]
        public Response GetCustomerLeadBySubProduct(string EncCustId, int SubProductId)
        {
            string encKey = Request != null ? Request.Headers["EncKey"].ToString() : string.Empty;
            string encIV = Request != null ? Request.Headers["EncIV"].ToString() : string.Empty;
            return objSMEBLL.GetCustomerLeadBySubProduct(EncCustId, SubProductId, encKey, encIV);
        }
    }
}