﻿using EmailCommunicationBLL;
using Helper;
using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace MatrixCoreAPI.Controllers
{

    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class HealthRenewalController : Controller
    {

        IHealthRenewalBLL objHealthrenewalBLL;
        public HealthRenewalController(IHealthRenewalBLL _objHealthrenewalBLL)
        {
            objHealthrenewalBLL = _objHealthrenewalBLL;
        }

        [HttpPost]
        public bool UpdateExclusiveBenifit(ExclusiveBenifit exclusiveBenifit)
        {
            return objHealthrenewalBLL.UpdateExclusiveBenifit(exclusiveBenifit);
        }

        [HttpGet]
        public ExclusiveBenifit GetExclusiveBenifit(Int64 LeadId)
        {
            return objHealthrenewalBLL.GetExclusiveBenifit(LeadId);
        }

        [HttpGet("{LeadId}")]
        public Loading FetchLoading(string LeadId)
        {
            if (CoreCommonMethods.IsValidString(LeadId))
                return objHealthrenewalBLL.FetchLoading(Convert.ToInt64(LeadId));
            return new Loading();
        }

        [HttpGet("{LeadId}")]
        public bool UpdateLoading(string LeadId)
        {
            if (CoreCommonMethods.IsValidString(LeadId))
                return objHealthrenewalBLL.UpdateLoading(Convert.ToInt64(LeadId));
            return false;
        }

        [HttpGet("{LeadId}")]
        public bool UpdatePED(string LeadId)
        {
            if (CoreCommonMethods.IsValidString(LeadId))
                return objHealthrenewalBLL.UpdatePED(Convert.ToInt64(LeadId));
            return false;
        }

        [HttpPost]
        public dynamic AddOrUpdateDocTtlLimit(AddOrUpdateDocTtlLimit addOrUpdateDocTtlLimit)
        {
            if (addOrUpdateDocTtlLimit != null && !string.IsNullOrEmpty(addOrUpdateDocTtlLimit.agentId))
                return objHealthrenewalBLL.AddOrUpdateDocTtlLimit(addOrUpdateDocTtlLimit);
            return "Please Enter AgentID and other details";
        }

        [HttpPost]
        public dynamic SetCustomerCallbackByRM(CustomerCallbackByRM customerCallbackByRM)
        {
            return objHealthrenewalBLL.SetCustomerCallbackByRM(customerCallbackByRM);

        }

        [HttpGet]
        public dynamic GetSetPortDetails(long LeadId, int type)
        {
            dynamic result = null;

            try
            {
                result = objHealthrenewalBLL.GetSetPortDetails(LeadId, type);
            }
            catch (Exception e)
            {

            }

            return result;
        }

        [HttpPost]
        public string GeneratePGLink([FromBody] PGLink pgLink)
        {
            return objHealthrenewalBLL.GeneratePGLink(pgLink);
        }

        [HttpGet("{LeadId}/{UserID}")]
        public AHCDetails GetAHCURL(long LeadId, long UserID)
        {
            return objHealthrenewalBLL.GetAHCURL(LeadId, UserID);
        }

        [HttpPost]
        public string GetPortSelectionURL(PortSelection portSelection)
        {
            return objHealthrenewalBLL.GetPortSelectionURL(portSelection);
        }

        [HttpGet("{LeadId}/{PehchanID}")]
        public void PushPehchanToBMS(long LeadId, string PehchanID)
        {
            objHealthrenewalBLL.PushPehchanToBMS(LeadId, PehchanID);
        }

        [HttpGet("{LeadId}")]
        public List<PortDetails> GetESQuickQuotesForPortV3(long LeadId)
        {
            return objHealthrenewalBLL.GetESQuickQuotesForPortV3(LeadId);
        }

        [HttpGet("{LeadId}")]
        public bool SetInceptionBookingID(long LeadId)
        {
            return objHealthrenewalBLL.SetInceptionBookingID(LeadId);
        }

        [HttpGet("{LeadId}/{CustomerID}")]
        public string FetchProposalForm(long LeadId,long CustomerID)
        {
            return objHealthrenewalBLL.FetchProposalForm(LeadId, CustomerID);
        }

        [HttpPost]
        public bool CreateRenewalManualPaymentLink(RenewalManualPaymentLink renewalManualPaymentLink)
        {
            long UserId = Request != null && CoreCommonMethods.IsValidInteger(Request?.Headers?["AgentId"]) > 0 ? Convert.ToInt64(Request?.Headers?["AgentId"]) : 0;
            return objHealthrenewalBLL.CreateRenewalManualPaymentLink(renewalManualPaymentLink, UserId);
        }

        [HttpPost]
        public bool UpdateRenewalRiders(RiderDetails riderDetails)
        {
            return objHealthrenewalBLL.UpdateRenewalRiders(riderDetails);
        }

    }
}
