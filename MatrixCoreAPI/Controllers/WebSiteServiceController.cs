using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using EmailCommunicationBLL;
using PropertyLayers;
using Helper;
using MongoConfigProject;
using System.Linq;

namespace MatrixCoreAPI.Controllers
{
    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class WebSiteServiceController : Controller
    {

        IWebSiteServiceBLL objWebSiteServiceBLL;
        public WebSiteServiceController(IWebSiteServiceBLL _objWebSiteServiceBLL)
        {
            objWebSiteServiceBLL = _objWebSiteServiceBLL;
        }

        [HttpPost]
        public SaveInfo SaveSelectedQuotes(SelectedQuote objRenewalLead)
        {
            return objWebSiteServiceBLL.SaveSelectedQuotes(objRenewalLead);
        }

        [HttpGet]
        public Response GetEncryptedUrl(string Url, string source)
        {
            Response response = new Response();
            try
            {
                response.message = Crypto.Encrytion_Payment_AES(Url, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings());
                response.status = true;
            }
            catch (Exception ex)
            {
                response.status = false;
                response.message = ex.ToString();
                LoggingHelper.LoggingHelper.Log(null, 0, ex.ToString(), "GetEncryptedUrl", "MatrixCore", ex.ToString(), "", "", DateTime.Now, DateTime.Now);
            }
            return response;
        }

        [HttpPost]
        public Response SaveCustomerIntent(List<CustomerIntent> customerIntent)
        {
            Response response = new Response();
            if (customerIntent.Count <= 100)
                response = objWebSiteServiceBLL.SaveCustomerIntent(customerIntent);
            else
            {
                response.message = "Data greater than 100";
            }
            return response;
        }

        [HttpPost]
        public Response SaveLeadAction([FromBody] LeadAction leadAction)
        {
            List<string> actions = new List<string>()
            {
                "Term Compare Click",
                "Trop Selected",
                "Limited Pay",
                "Term HLV",
                "Term Monthly Premium",
                "Term Annual Premium",
                "Life Cover Amount Change",
                "Cover Till Age Change",
                "Premium Payment Frequency Toggle",
                "Filter Tab Click"
            };
            Response response = new Response();
            if (actions.Where(x => x == leadAction.Action).Count() == 1)
                response = objWebSiteServiceBLL.SaveLeadAction(leadAction);
            else
            {
                response.message = "Wrong action passed.";
            }

            return response;
        }

        [HttpGet]
        public LeadStatusResponse GetLeadQuoteUploadStatus(long LeadId)
        {
            return objWebSiteServiceBLL.GetLeadQuoteUploadStatus(LeadId);
        }

        [HttpPost]
        public SaveInfo UploadSmeRequestForQuotes([FromBody] List<SmeRequestForQuotesEntity> rfqEntities)
        {
            var response = new SaveInfo();
            try
            {
                response = objWebSiteServiceBLL.UploadSmeRequestForQuotes(rfqEntities);
            }
            catch
            {
                response.IsSaved = false;
                response.Message = "Failed";
            }
            return response;
        }

        [HttpGet]
        public string GetOfflineBookedLeads(string date, int productId)
        {
            return objWebSiteServiceBLL.GetOfflineBookedLeads(date, productId);
        }

        [HttpGet]
        public string GetLeadDetailsForPcd(long leadId)
        {
            return objWebSiteServiceBLL.GetLeadDetailsPcd(leadId);
        }

        [HttpGet]
        public LeadDetailsForCustIdResponse GetLeadDetailsForCustId(long CustomerId)
        {
            return objWebSiteServiceBLL.GetLeadDetailsForCustId(CustomerId);
        }

        [HttpGet]
        public CallDataResponse GetCallData(long LeadId, long CreationDate, string? CallType)
        {
            var response = new CallDataResponse();

            if (LeadId > 0 && CreationDate > 0)
            {
                response = objWebSiteServiceBLL.GetCallData(LeadId, CreationDate, CallType);
            }
            else
            {
                response.Status = false;
                response.Message = "Invalid inputs!";
            }
            return response;
        }

        [HttpPost]
        public LeadDeatils GetBasicLeadDetails(GetBasicLeadDetails objGetBasicLeadDetails)
        {
            LeadDeatils leaddet = new LeadDeatils();
            string LeadId = string.Empty;
            string EncKey = string.Empty;
            string EncIV = string.Empty;

            try
            {
                LeadId = objGetBasicLeadDetails.LeadId;
                string Source = Request != null ? (Request.Headers["source"]).ToString().ToLower() : string.Empty;
                EncKey = Request != null ? (Request.Headers["EncKey"]).ToString() : string.Empty;
                EncIV = Request != null ? (Request.Headers["EncIV"]).ToString() : string.Empty;

                leaddet = objWebSiteServiceBLL.GetAllBasicLeadDetails(LeadId, Source, EncKey, EncIV);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId, Convert.ToInt64(LeadId), ex.ToString(), "GetBasicLeadDetails", "MatrixCore", "WebSiteController", LeadId, ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return leaddet;
        }

        [HttpGet("{LeadId}")]
        public int getPreBookingTalkTime(string LeadId)
        {
            int TalkTime = -1;
            if (CoreCommonMethods.IsValidInteger(LeadId) > 0)
                TalkTime = objWebSiteServiceBLL.getPreBookingTalkTime(Convert.ToInt64(LeadId));

            return TalkTime;
        }


        [HttpGet]
        public bool AssignLead()
        {
            bool result = false;
            string LeadID = HttpContext.Request.Query["leadId"];
            string Source = HttpContext.Request.Query["Source"];
            string EmpCode = HttpContext.Request.Query["agentCode"];
            string LeadType = HttpContext.Request.Query["LeadType"];
            string CallID = HttpContext.Request.Query["CallID"];
            string CallDate = HttpContext.Request.Query["callDate"];
            string TransferType = string.IsNullOrEmpty(HttpContext.Request.Query["TransferType"]) == true ? "" : HttpContext.Request.Query["TransferType"];

            if (CoreCommonMethods.IsValidInteger(LeadID) > 0)
                result = objWebSiteServiceBLL.AssignLead(Convert.ToInt64(LeadID), EmpCode, Source, Convert.ToInt16(LeadType), CallID, CallDate, TransferType);

            return result;
        }

        [HttpGet]
        public bool SendCustCommtoUnansLeads()
        {
            bool res = objWebSiteServiceBLL.SendCustCommtoUnansLeads();
            return res;
        }
        [HttpGet("{LeadId}/{CustomerId}/{ProductId}")]

        public UrlResponse GetExitPointURL(string LeadId, string CustomerId, string ProductId)
        {
            UrlResponse response = new() { StatusMessage = "Invalid Inputs" };
            if (CoreCommonMethods.IsValidInteger(LeadId) > 0 && CoreCommonMethods.IsValidInteger(CustomerId) > 0 && CoreCommonMethods.IsValidInteger(ProductId) > 0 && Convert.ToInt16(ProductId) != 2)
                response = objWebSiteServiceBLL.GetExitPointURL(Convert.ToInt64(LeadId), Convert.ToInt64(CustomerId), Convert.ToInt16(ProductId));

            return response;
        }
        [HttpGet]
        public AgentProfileData GetAgentProfileData(string EncLeadID)
        {
            AgentProfileData objAgentProfileData = new AgentProfileData();
            string EncKey = Request != null ? (Request.Headers["EncKey"]).ToString() : string.Empty;
            string EncIV = Request != null ? (Request.Headers["EncIV"]).ToString() : string.Empty;
            //string LeadID = "44278939";
            //string encleadid = Crypto.Encrytion_Payment_AES(LeadID, "Core", 256, 128, EncKey, EncIV, true);
            if (!string.IsNullOrEmpty(EncLeadID))
            {
                string DecryptedLeadid = Crypto.Decrytion_Payment_AES(EncLeadID, "Core", 256, 128, EncKey, EncIV, true);
                objAgentProfileData = objWebSiteServiceBLL.GetAgentProfileData(Convert.ToInt64(DecryptedLeadid));
            }
            else
            {
                objAgentProfileData.Status = false;
                objAgentProfileData.Message = "Invalid LeadID";
            }
            return objAgentProfileData;
        }


        [HttpGet("{Pincode}/{LeadId}")]
        public ResponseAPI GetCityIdByPincode(string Pincode, string LeadId)
        {
            ResponseAPI response = new() { message = "Invalid Inputs" };
            if (CoreCommonMethods.IsValidInteger(Pincode) > 0 && CoreCommonMethods.IsValidInteger(LeadId) > 0)
                response = objWebSiteServiceBLL.GetCityIdByPincode(Convert.ToInt32(Pincode), Convert.ToInt64(LeadId));

            return response;
        }


        [HttpGet]
        public EmployeeDetails GetAgentDetails(string UserId)
        {
            return objWebSiteServiceBLL.GetAgentDetails(UserId, Request?.Headers["EncKey"], Request?.Headers["EncIV"]);
        }

        [HttpGet]
        public LeadDeatils GetParentLeadSource(string LeadId)
        {
            return objWebSiteServiceBLL.GetParentLeadSource(LeadId);
        }

        [HttpPost]
        public CreateLeadResponse CreateLead(CreateLeadRequest request)
        {
            request.ApiSource = Convert.ToString(Request?.Headers["source"]) ?? string.Empty;
            request.UserId = Request != null && CoreCommonMethods.IsValidInteger(Request?.Headers?["AgentId"]) > 0 ? Convert.ToInt64(Request?.Headers?["AgentId"]) : 0;

            return objWebSiteServiceBLL.CreateLead(request);
        }

        [HttpGet]
        public List<StoreDetails> GetAvailableStore(string CityID)
        {
            return objWebSiteServiceBLL.GetAvailableStore(CityID);
        }

        [HttpPost]
        public ResponseAPI UploadUTMCampaign(UTMCampaignDetails request)
        {
            return objWebSiteServiceBLL.UploadUTMCampaign(request);
        }


        [HttpGet]
        public StoreAndAdvisorInfoModel GetStoreAndAdvisorInfo(Int32 CityID)
        {
            StoreAndAdvisorInfoModel oStoreAndAdvisorInfoModel = objWebSiteServiceBLL.GetStoreAndAdvisorInfo(CityID);

            if (oStoreAndAdvisorInfoModel.StoreInfo == null && oStoreAndAdvisorInfoModel.AdvisorInfo == null)
                HttpContext.Response.StatusCode = 204;

            return oStoreAndAdvisorInfoModel;
        }

        [HttpGet]
        public List<ProductModel> GetFOSProductMaster()
        {
            string Source = Request != null ? (Request.Headers["source"]).ToString().ToLower() : string.Empty;
            return objWebSiteServiceBLL.GetFOSProductMaster(Source);
        }

        [HttpGet]
        public List<FOSCityMaster> GetFOSCityMaster(Int32 ProductId)
        {
            return objWebSiteServiceBLL.GetFOSCityMaster(ProductId);
        }
        [HttpPost]
        public Envelope<List<CustomerLeads>> GetCustomerLeads(CustomerLeadsRequest request)
        {
            //var v = Crypto.Encrytion_Payment_AES(request.SearchInput, "Core", 256, 128, Request?.Headers["EncKey"], Request?.Headers["EncIV"], false);
            //return objWebSiteServiceBLL.GetCustomerLeads(request, Request?.Headers["EncKey"], Request?.Headers["EncIV"]);

            string message = string.Empty;
            return new Envelope<List<CustomerLeads>>()
            {
                Data = objWebSiteServiceBLL.GetCustomerLeads(request, Request?.Headers["EncKey"], Request?.Headers["EncIV"], out message),
                Error = message
            };
        }

        [HttpPost]
        public Envelope<List<CustomerLeads>> GetLeadsByCustId(CustomerLeadsRequest request)
        {
            string message = string.Empty;
            return new Envelope<List<CustomerLeads>>()
            {
                Data = objWebSiteServiceBLL.GetLeadsByCustId(request, Request?.Headers["EncKey"], Request?.Headers["EncIV"], out message),
                Error = message
            };
        }

        [HttpPost]
        public ResponseData<List<LeadInfo>> GetActiveLeadAppSet([FromBody] LeadModel oLeadInfo)
        {

            return new ResponseData<List<LeadInfo>>()
            {
                Data = objWebSiteServiceBLL.GetActiveLeadAppSet(oLeadInfo.CustId, out bool status, Request?.Headers["EncKey"], Request?.Headers["EncIV"]),
                Status = status,
                Message = string.Empty
            };
        }

        [HttpPost]

        public SaveInfo SetCsatAssignLead([FromBody] CSATModel oCSATModel)
        {
            SaveInfo oResponseData = null;

            if (oCSATModel != null)
                oResponseData = objWebSiteServiceBLL.SetCsatAssignLead(oCSATModel);

            return oResponseData;

        }

        public ResponseData<string> AddUpdateCallAnalysis([FromBody] CallAnalyserRequest request)
        {
            return objWebSiteServiceBLL.AddUpdateCallAnalysis(request);
        }

        [HttpGet("{Pincode}/{ProductId}")]
        public ResponseData<Dictionary<string, string>> GetCityByPinCodeProduct(string Pincode, Int16 ProductId)
        {
            ResponseData<Dictionary<string, string>> oResponseData = new ResponseData<Dictionary<string, string>>() { Status = false, Message = "Invalid Inputs", Data = new Dictionary<string, string>() { { "CityId", "0" }, { "CityName", "" } } };
            string Source = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["source"]) ? Request.Headers["source"].ToString().ToLower() : string.Empty;
            if (CoreCommonMethods.IsValidString(Pincode) && Convert.ToInt32(Pincode) > 0 && ProductId > 0)
            {
                return objWebSiteServiceBLL.GetCityByPinCodeProduct(Convert.ToInt32(Pincode), ProductId, Source);
            }

            return oResponseData;
        }


        [HttpPost]
        public SaveInfo SavePlanRecommendation([FromBody] PlanRecommendationModel planRecommModel)
        {
            SaveInfo? oResponseData = new() { Message = "Please input valid data" };

            if (planRecommModel != null && planRecommModel.AlternatePlanDetails != null && planRecommModel.AlternatePlanDetails.SelectedPlans.Count < 7)
                oResponseData = objWebSiteServiceBLL.SavePlanRecommendation(planRecommModel);
            else
                oResponseData.Message = "Please enter 6 plans only";


            return oResponseData;
        }

        [HttpGet("{LeadId}")]
        public ResponseData<PlanRecommendationModel>? GetPlanRecommendation(Int64 LeadId)
        {
            if (LeadId == 0)
                return null;

            return new ResponseData<PlanRecommendationModel>()
            {
                Data = objWebSiteServiceBLL.GetPlanRecommendation(LeadId, out bool status),
                Status = status,
                Message = string.Empty
            };
        }

        [HttpPost]
        public ResponseData<string> PushNotification(NotificationRequest request)
        {
            return objWebSiteServiceBLL.PushNotification(request);
        }

        [HttpPost]
        public ResponseData<string> PushReadNotification(ReadNotificationRequest request)
        {
            return objWebSiteServiceBLL.PushReadNotification(request);
        }

        //[HttpGet("{EmpId}")]
        //public ResponseData<List<KnowYoutAdvisorModel>>? GetProductListByEmpId(string empId)
        //{
        //    ResponseData<List<KnowYoutAdvisorModel>> oResponseData = new() { Status = false, Message = "something Went Wrong", Data = new List<KnowYoutAdvisorModel>() };

        //    if (CoreCommonMethods.IsValidString(empId))
        //        return new ResponseData<List<KnowYoutAdvisorModel>>()
        //        {
        //            Data = objWebSiteServiceBLL.GetProductListByEmpId(empId, out bool status),
        //            Status = status,
        //            Message = string.Empty
        //        };


        //    return oResponseData;
        //}
        [HttpGet]
        public NotificationsResponse GetNotificationsData(short? NumberOfDays)
        {
            return objWebSiteServiceBLL.GetNotificationsData(Request?.Headers?["AgentId"], NumberOfDays);
        }

        [HttpPost]
        public ResponseData<string> ValidateCustomerContact(CustomerContactDetails contactDetails)
        {
            return objWebSiteServiceBLL.ValidateCustomerContact(contactDetails, Request?.Headers?["EncKey"], Request?.Headers?["EncIV"]);
        }
        [HttpPost]
        public ResponseData<string> SaveAIAudioData([FromBody] AIAudioModel aiModel)
        {
            return objWebSiteServiceBLL.SaveAIAudioData(aiModel);
        }

        [HttpPost]
        public ResponseData<List<KnowYoutAdvisorModel>>? GetProductListByEmpId([FromBody] KnowYoutAdvisorModel knowYoutAdvisorModel)
        {
            ResponseData<List<KnowYoutAdvisorModel>> oResponseData = new() { Status = false, Data = new List<KnowYoutAdvisorModel>() };

            if (CoreCommonMethods.IsValidString(knowYoutAdvisorModel.EmployeeId) && CoreCommonMethods.IsValidString(knowYoutAdvisorModel.encryptCustId))
                return new ResponseData<List<KnowYoutAdvisorModel>>()
                {
                    Data = objWebSiteServiceBLL.GetProductListByEmpId(knowYoutAdvisorModel, out bool status, out Int16 code),
                    Status = status,
                    Code = code
                };
            return oResponseData;

        }
        [HttpGet]
        public ResponseData<string> GetPGPaymentLink(string LeadId, string? Source)
        {
            ResponseData<string> objres = new ResponseData<string>();
            if (string.IsNullOrEmpty(LeadId) && Convert.ToInt64(LeadId) <= 0)
                return null;
            objres = objWebSiteServiceBLL.GetPGPaymentLink(LeadId, Source);
            return objres;
        }
        [HttpGet]
        public ResponseData<string> GetEMandateEnableLink(string LeadId)
        {
            ResponseData<string> objres = new ResponseData<string>();
            if (string.IsNullOrEmpty(LeadId) && Convert.ToInt64(LeadId) <= 0)
                return null;
            objres = objWebSiteServiceBLL.GetEMandateEnableLink(LeadId);
            return objres;

        }
        [HttpPost]
        public ResponseData<string> NotifyAgent([FromBody] NotifyAgentRequest NotifyAgentReq)
        {
            return objWebSiteServiceBLL.NotifyAgent(NotifyAgentReq);
        }
        [HttpPost]
        public ResponseData<string> InsertAgentAPEData([FromBody] InsertAgentAPEDataRequest objreq)
        {
            return objWebSiteServiceBLL.InsertAgentAPEData(objreq);
        }

        [HttpGet]
        public ApplicationMonitorMaster GetApplicationMonitoringMaster()
        {
            ApplicationMonitorMaster objres = new ApplicationMonitorMaster();
            objres = objWebSiteServiceBLL.GetApplicationMonitoringMaster();
            return objres;
        }

        [HttpPost]
        public SaveInfo PushApplicationMonitoringDetails([FromBody] AppMonitorReqModel objAppMonitorReqModel)
        {
            SaveInfo objres = new SaveInfo();

            if (objAppMonitorReqModel != null && objAppMonitorReqModel.ApplicationID > 0 && objAppMonitorReqModel.MemberDetails != null)
                objres = objWebSiteServiceBLL.PushApplicationMonitoringDetails(objAppMonitorReqModel);
            return objres;
        }

        [HttpGet]
        public List<AppMonitorData> GetApplicationMonitoringData()
        {
            List<AppMonitorData> objres = new List<AppMonitorData>();
            objres = objWebSiteServiceBLL.GetApplicationMonitoringData();
            return objres;
        }

        [HttpGet]
        public bool DeleteApplicationData(int ID)
        {
            bool res = false;
            res = objWebSiteServiceBLL.DeleteApplicationData(ID);
            return res;
        }

        [HttpGet("{LeadId}")]
        public string GetRenewalLeadData(long LeadId)
        {
            return objWebSiteServiceBLL.GetRenewalLeadData(LeadId);
        }

        [HttpPost]
        public void SetPetDetails([FromBody] PetDetails petDetail)
        {
            objWebSiteServiceBLL.SetPetDetails(petDetail);
        }

        [HttpGet("{LeadId}")]
        public ResponseData<SmeData> GetSmeLeadData(long LeadId)
        {
            return objWebSiteServiceBLL.GetSmeLeadData(LeadId);
        }

        [HttpGet]
        public ResponseData<List<SupplierDTO>> GetSuppliersByLeadId(long LeadId, string? ProductId, string? LeadSource)
        {
            return objWebSiteServiceBLL.GetSuppliersByLeadId(LeadId, ProductId, LeadSource);
        }

        [HttpGet]
        public ResponseData<List<Plans>> GetPlansByLeadId(long LeadId, short SupplierId, string? ProductId, string? LeadSource)
        {
            return objWebSiteServiceBLL.GetPlansByLeadId(LeadId, SupplierId, ProductId, LeadSource);
        }

        [HttpGet]
        public UserDetails GetAgentDetailsByECode(string EmployeeCode = "")
        {
            return objWebSiteServiceBLL.GetAgentDetailsByECode(EmployeeCode);
        }

        [HttpPost]
        public ResponseAPI UpdateSelfiesData([FromBody] SelfieModel request)
        {
            return objWebSiteServiceBLL.UpdateSelfiesData(request);
        }

        [HttpPost]
        public bool SetCustomerCtcClick(long LeadId, bool IsCallBackScheduled)
        {
            return objWebSiteServiceBLL.SetCustomerCtcClick(LeadId, IsCallBackScheduled);
        }

        [HttpPost]
        public bool InsertShortCallData([FromBody] ShortCallData obj)
        {
            return objWebSiteServiceBLL.InsertShortCallData(obj);
        }

        [HttpPost]
        public bool PushHWEligibleData([FromBody] HWEligibleData obj)
        {
            string UserId = Request.Headers["AgentId"];
            if (CoreCommonMethods.IsValidString(UserId))
                obj.UserId = Convert.ToInt64(UserId);

            return objWebSiteServiceBLL.PushHWEligibleData(obj);
        }

        [HttpPost]
        public bool SetCouponRedeemValue([FromBody] CouponRedeemModel obj)
        {
            return objWebSiteServiceBLL.SetCouponRedeemValue(obj.CustomerId);
        }
        [HttpPost]
        public bool GetCouponRedeemValue([FromBody] CouponRedeemModel obj)
        {
            return objWebSiteServiceBLL.GetCouponRedeemValue(obj.CustomerId);
        }
        [HttpPost]
        public CouponRedeemModel SetCouponRaiseRequest([FromBody] CouponRedeemModel obj)
        {
            return objWebSiteServiceBLL.SetCouponRaiseRequest(obj);
        }
        [HttpGet]
        public List<CouponRedeemModel> GetCouponRaiseRequest()
        {
            string UserId = Request.Headers["AgentId"];
            return objWebSiteServiceBLL.GetCouponRaiseRequest(UserId);
        }
        [HttpPost]
        public bool UpdateCouponRaiseRequest([FromBody] CouponRedeemModel obj)
        {
            return objWebSiteServiceBLL.UpdateCouponRaiseRequest(obj);
        }

        [HttpPost]
        public bool PushVCPitchData([FromBody] VCPitchData obj)
        {
            if (obj.LeadID > 0 && obj.CallDataID > 0)
            {
                return objWebSiteServiceBLL.PushVCPitchData(obj);
            }

            return false;
        }


        [HttpPost]
        public ResponseData<string> SaveFeatureData([FromBody] FeatureData featureData)
        {
            return objWebSiteServiceBLL.SaveFeatureData(featureData);
        }

        [HttpGet("{InputValue}/{TypeId}")]
        public ResponseData<List<LeadInfo>> GetActiveLeads(string InputValue, int TypeId)
        {
            string EncKey = Request != null ? (Request.Headers["EncKey"]).ToString() : string.Empty;
            string EncIV = Request != null ? (Request.Headers["EncIV"]).ToString() : string.Empty;
            string source = Request != null ? (Request.Headers["source"]).ToString() : string.Empty;
            
            return objWebSiteServiceBLL.GetActiveLeads(InputValue, TypeId, EncKey, EncIV, source);

        }

        [HttpPost]
        public List<AgentCallDetails> GetAgentCallDetails(AgentCallData obj)
        {

            return objWebSiteServiceBLL.GetAgentCallDetails(obj.pbcentrallogin, obj.MobileNo);
        }
        [HttpPost]
        public List<Response> RejectAllLeads(List<LeadInfo> rejectLeadInfo)
        {
            string ECode = Request != null ? (Request.Headers["EmployeeId"]).ToString() : string.Empty;
            string source = Request != null ? (Request.Headers["source"]).ToString() : string.Empty;

            return objWebSiteServiceBLL.RejectAllLeads(ECode, source, rejectLeadInfo);
        }
        [HttpGet("{CustomerId}/{RegNo}/{ActionType}/{ReasonId}")]
        public Response ProcessLeadByCustomerId(long CustomerId, string regNo, string ActionType, int ReasonId)
        {
            string source = Request != null ? (Request.Headers["source"]).ToString() : string.Empty;

            return objWebSiteServiceBLL.ProcessLeadByCustomerId(CustomerId, regNo, ActionType,source, ReasonId);
        }

        [HttpPost]
        public LeadDeatils GetBasicLeadDetailsWrapper(GetBasicLeadDetails objGetBasicLeadDetails)
        {
            LeadDeatils leaddet = new LeadDeatils();
            string LeadId = string.Empty;
            string EncKey = string.Empty;
            string EncIV = string.Empty;

            try
            {
                long _LeadId = CommonMethods.GetDecryptIdBySource(objGetBasicLeadDetails.LeadId);
                LeadId = Convert.ToString(_LeadId);
                string Source = Request != null ? (Request.Headers["source"]).ToString().ToLower() : string.Empty;
                EncKey = Request != null ? (Request.Headers["EncKey"]).ToString() : string.Empty;
                EncIV = Request != null ? (Request.Headers["EncIV"]).ToString() : string.Empty;

                leaddet = objWebSiteServiceBLL.GetAllBasicLeadDetails(LeadId, Source, EncKey, EncIV);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId, Convert.ToInt64(LeadId), ex.ToString(), "GetBasicLeadDetails", "MatrixCore", "WebSiteController", LeadId, ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return leaddet;
        }

        [HttpGet("{mobileNo}")]
        public bool IsCustomerDNC(long mobileNo)
        {            
            bool result = objWebSiteServiceBLL.IsCustomerDNC(mobileNo);

            return result;
        }

        [HttpPost]
        public bool SaveAssistanceData([FromBody] AssistanceData obj)
        {
            if (obj.CustomerId > 0)
            {
                return objWebSiteServiceBLL.SaveAssistanceData(obj);
            }

            return false;
        }

        [HttpGet("{LeadId}")]
        public CouponRedeemModel GetCouponDataByLeadId(long LeadId)
        {
            return objWebSiteServiceBLL.GetCouponDataByLeadId(LeadId);
        }
        
        [HttpGet("{agentid}/{leadid}/{rating}")]
        public ResponseData<bool> InsertIVRFeedBack(string agentid, string leadid, string rating, string source, string process, long MobileNo, int ProductId, long CallDataId, long AppointmentId = 0)
        {
            return new ResponseData<bool>()
            {
                Status = objWebSiteServiceBLL.InsertIVRFeedBack(agentid, leadid, rating, source, process, MobileNo, ProductId, CallDataId, AppointmentId),
                Message = string.Empty
            };
        }


        [HttpGet]
        public ResponseData<LeadAssignDetails> AssignLeadWithDetails()
        {
            ResponseData<LeadAssignDetails> respose = new ResponseData<LeadAssignDetails>();
            string LeadID = HttpContext.Request.Query["leadId"];
            string Source = HttpContext.Request.Query["Source"];
            string EmpCode = HttpContext.Request.Query["agentCode"];
            string LeadType = HttpContext.Request.Query["LeadType"];
            string CallID = HttpContext.Request.Query["CallID"];
            string CallDate = HttpContext.Request.Query["callDate"];
            string leadRank = HttpContext.Request.Query["leadRank"];
            string TransferType = string.IsNullOrEmpty(HttpContext.Request.Query["TransferType"]) == true ? "" : HttpContext.Request.Query["TransferType"];

            if (CoreCommonMethods.IsValidInteger(LeadID) > 0)
                respose = objWebSiteServiceBLL.AssignLeadWithDetails(Convert.ToInt64(LeadID), EmpCode, Source, Convert.ToInt16(LeadType), CallID, CallDate, TransferType, leadRank);

            return respose;
        }

        [HttpGet("{mobileNo}")]
        public Response IsValidMobileNo(long mobileNo)
        {
            Response response = new Response();
            response.status = objWebSiteServiceBLL.IsValidMobileNo(mobileNo);
            response.LeadId = "0";
            if (response.status == false)
            {
                response.message = "InValidNo";
            }
            return response;


        }

        [HttpPost]
        public ResponseData<Dictionary<object, object>> GetIncomeFromPayUTerm([FromBody] IncomeFromPayUData obj)
        {
            return objWebSiteServiceBLL.GetIncomeFromPayUTerm(obj);
        }

        [HttpGet]
        public ResponseData<AgentProfileData> GetAgentProfileByEmpID(string EmpID)
        {
            ResponseData<AgentProfileData> objAgentProfileData = new ResponseData<AgentProfileData>();
            if (string.IsNullOrEmpty(EmpID))
            {
                objAgentProfileData.Status = false;
                objAgentProfileData.Message = "Invalid ECode";
                objAgentProfileData.Data = null;
                return objAgentProfileData;
            }
            if (!string.IsNullOrEmpty(EmpID))
            {
                objAgentProfileData = objWebSiteServiceBLL.GetAgentProfileDataByECode(EmpID);
            }
            else
            {
                objAgentProfileData.Status = false;
                objAgentProfileData.Message = "Invalid ECode";
            }

            return objAgentProfileData;
        }

        [HttpPost]
        public ResponseData<string> SaveFOSApptCallAnalysisAI([FromBody] FOSApptCallDataAI objFosApptCallData)
        {
            return objWebSiteServiceBLL.SaveFOSApptCallAnalysisAI(objFosApptCallData);
        }

        [HttpPost]
        public ResponseData<string> FOSAppointmentCallAnalysisAI([FromBody] FOSApptCallDataAI objFosApptCallData)
        {
            return objWebSiteServiceBLL.InsertFOSApptCallAnalysisAI(objFosApptCallData);
        }

        [HttpPost]
        public bool VerifyCallBackDateTime(ShortCallData obj)
        {
            ResponseAPI response = objWebSiteServiceBLL.VerifyCallBackDateTime(obj);
            return response.status;
        }        
        
        [HttpPost]
        public bool CTCCallBack(CTCSchdular _CTCSchdular)
        {
            string? source = Request != null ? Request?.Headers?["source"] : string.Empty;
            Response response = objWebSiteServiceBLL.CTCCallBack(_CTCSchdular, source);
            response.LeadId = "0";
            if (response.status == false)
            {
                response.message = "some thing wrong";
                return false;
            }
            return true;
        }

        [HttpPost]
        
        public long CreateCustomer(CustBasicInfo objCustBasicInfo)
        {
            if(objCustBasicInfo!=null)
            {
                return objWebSiteServiceBLL.CreateCustomer(objCustBasicInfo.MobileNo, objCustBasicInfo.EmailID, objCustBasicInfo.Name, objCustBasicInfo.CountryID);
            }
            return 0;
        }


        [HttpPost]
        public Response SaveFollowUpComment(FollowUpComment obj)
        {
            
            return objWebSiteServiceBLL.SaveFollowUpComment(obj);

        }

        [HttpPost]
        public Response DisableScheduledC2C(long leadId, string source)
        {
            return objWebSiteServiceBLL.DisableScheduledC2C(leadId, source);

        }

        [HttpGet]
        public List<LeadDeatils> GetActiveLeadsForCustomer(string mobileNo, short subProductId)
        {
            string? source = Request != null ? Request?.Headers?["source"] : string.Empty;
            string? encKey = Request != null ? Request?.Headers?["EncKey"] : string.Empty;
            string? encIV = Request != null ? Request?.Headers?["EncIV"] : string.Empty;

            return objWebSiteServiceBLL.GetActiveLeadsForCustomer(mobileNo, subProductId, source, encKey, encIV);
        }

        [HttpGet("{customerId}")]
        public ResponseUltraHNICust CheckUltraHNICustomer(long customerId, string? mobileNoHash = null, long? leadId = null, bool skipPayuApiCall = false)
        {
            ResponseUltraHNICust response = new();

            if(customerId > 0 && (!string.IsNullOrEmpty(mobileNoHash) || leadId > 0)) {
                response = objWebSiteServiceBLL.CheckUltraHNICustomer(customerId, mobileNoHash, leadId ?? 0, skipPayuApiCall);
            } 
            else 
            {
                response.status = false;
                response.message = "Invalid Inputs";
            }

            return response;
        }
        
        [HttpGet("{customerId}/{productId}")]
        public ResponseData<object> LeadDetailsForWhatsappBot(long customerId, short productId)
        {
            ResponseData<object> response = new()
            {
                Status = false,
                Message = "Failed"
            };
            if (customerId > 0 && productId > 0) {
                response = objWebSiteServiceBLL.LeadDetailsForWhatsappBot(customerId, productId);
            }
            else
            {
                response.Status = false;
                response.Message = "Incorrect Inputs";
            }
            return response;
        }

        [HttpGet("{leadId}")]
        public ResponseData<object> GetLeadDetailsForAI(long leadId)
        {
            ResponseData<object> response = new()
            {
                Status = false,
                Message = "Failed"
            };
            if (leadId > 0) {
                response = objWebSiteServiceBLL.GetLeadDetailsForAI(leadId);
            } else {
                response.Status = false;
                response.Message = "Incorrect Inputs";
            }
            return response;
        }
        
        [HttpGet]
        /// <summary>
        /// This API handles the assignment of leads based on the provided CustomerID and ProductID.
        /// -First we check if there is any active lead of that customer of that product
        /// --If a lead exists:
        /// ---If Lead is assigned -
        ///   - If the assigned advisor is available, it returns the existing lead ID, assigned advisor, and group.
        ///   - If no assigned advisor is available, it assigns the lead to a new WA UHNI group and returns the details.
        /// - If no lead exists, it creates a new lead, assigns it to an advisor in a new WA group, and returns the lead ID, assigned advisor, and group.
        /// </summary>
        public ResponseData<LeadAssignResp> AssignLeadToGroup(long CustomerID, int ProductId, string identifier, int? GroupID)
        {
            string ApiSource = Convert.ToString(Request?.Headers["source"]) ?? string.Empty;
            ResponseData<LeadAssignResp> objLeadAssignResp = new ResponseData<LeadAssignResp>
            {
                Status = false,
                Message = "Invalid CustomerID or ProductId"
            };
            if(CustomerID > 0 && ProductId > 0)
            {
                objLeadAssignResp = objWebSiteServiceBLL.AssignLeadToGroup(CustomerID, ProductId, identifier, GroupID, ApiSource);
            }
            return objLeadAssignResp;
        }

        [HttpGet("{leadId}")]
        public bool CheckIsLeadInvalidAI(long leadId)
        {
            return objWebSiteServiceBLL.CheckIsLeadInvalidAI(leadId);
        }
        [HttpGet("{LeadId}")]
        public ResponseData<List<KeyValuePair<string, string>>> GetAdditionalLeadDetailsFromCJ(long LeadId)
        {
            var response = new ResponseData<List<KeyValuePair<string, string>>>
            {
                Status = false,
                Message = "Invalid LeadId",
                Data = new List<KeyValuePair<string, string>>()
            };

            if (LeadId > 0)
            {
                try
                {
                    response = objWebSiteServiceBLL.GetAdditionalLeadDetailsFromCJ(LeadId);
                }
                catch (Exception ex)
                {
                    response.Status = false;
                    response.Message = "An error occurred while fetching lead details.";
                    LoggingHelper.LoggingHelper.Log(LeadId.ToString(), 0, ex.ToString(), "GetAdditionalLeadDetailsFromCJ", "WebSiteServiceController", "MatrixCoreAPI", string.Empty, ex.ToString(), DateTime.Now, DateTime.Now);
                }
            }

            return response;
        }
        
        [HttpGet("{customerId}")]
        public ResponseData<object> GetCustomerLeadCityforAI(long customerId)
        {
            ResponseData<object> response = new()
            {
                Status = false,
                Message = "Failed"
            };
            if (customerId > 0) {
                response = objWebSiteServiceBLL.GetCustomerLeadCityforAI(customerId);
            }
            else {
                response.Status = false;
                response.Message = "Incorrect CustomerId";
            }
            return response;
        }

        [HttpPost]
        public SaveInfo GetCustRecordingConcern(LeadsSubStatusModel CustResponse)
        {
            SaveInfo objLeadAssignResp = new()
            {
                IsSaved = false,
                Message = "Failed"
            };

            if (CustResponse.AppointmentId > 0 && (CustResponse.Comments.ToUpper() == "YES" || CustResponse.Comments.ToUpper() == "NO"))
            {
                CustResponse.Source = Request != null ? (Request.Headers["source"]).ToString().ToLower() : string.Empty;
                CustResponse.Comments = CustResponse.Comments.ToUpper();
                objLeadAssignResp = objWebSiteServiceBLL.GetCustRecordingConcern(CustResponse);
            }
            return objLeadAssignResp;
        }


    }
}