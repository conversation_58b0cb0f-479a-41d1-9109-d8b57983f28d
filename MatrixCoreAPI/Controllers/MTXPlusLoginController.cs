﻿using Amazon.Auth.AccessControlPolicy;
using EmailCommunicationBLL;
using Helper;
using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace MatrixCoreAPI.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class MTXPlusLoginController : Controller
    {
        IMTXPlusLoginBLL objIMTXPlusLoginBLL;
        IUserDetailsBLL objUserDetailsBLL;

        public MTXPlusLoginController(IMTXPlusLoginBLL _objIMTXPlusLoginBLL, IUserDetailsBLL _objUserDetailsBLL)
        {
            objIMTXPlusLoginBLL = _objIMTXPlusLoginBLL;
            objUserDetailsBLL = _objUserDetailsBLL;
        }
        [HttpPost]
        public ResponseData<LDAPUserDetails> LDAPAuthCheck(MTXPlusLogInDTO obj)
        {
            ResponseData<LDAPUserDetails> objres = new ResponseData<LDAPUserDetails>();
            if (!string.IsNullOrEmpty(obj.Password) && !string.IsNullOrEmpty(obj.UserName))
            {
                var origin = Request.Headers.TryGetValue("origin", out var originHeader)
                ? originHeader.ToString()
                : "";
                objres = objIMTXPlusLoginBLL.LDAPAuthCheck(obj.UserName, obj.Password, HttpContext, origin);
            }
            return objres;
        }

        [HttpPost]
        public OtpResult GenerateLoginOtp([FromBody] GetOtpRequest request)
        {
            var response = new OtpResult()
            {
                Message = "Something went wrong, please relogin"
            };
            if (request != null && !string.IsNullOrEmpty(request.MobileNo) && CoreCommonMethods.IsValidInteger(request.UserId) > 0)
            {
                response = objUserDetailsBLL.GenerateLoginOtp(request, HttpContext);
            }
            return response;
        }

        [HttpPost]
        public OtpResult VerifyLoginOtp([FromBody] ValidateOtpRequest request)
        {
            var response = new OtpResult()
            {
                Message = "Something went wrong, please relogin"
            };
            if (request != null && !string.IsNullOrEmpty(request.MobileNo) && CoreCommonMethods.IsValidInteger(request.OTP) > 0)
            {
                response = objUserDetailsBLL.VerifyLoginOtp(request, HttpContext);
            }
            return response;
        }

        [HttpPost]
        public Result LogoutFromMtxplus(MTXPlusLogInDTO objlogindet)
        {
            Result res = new Result();
            if (!string.IsNullOrEmpty(objlogindet.EmployeeId))
            {
                res = objIMTXPlusLoginBLL.LogoutFromMtxplus(objlogindet, HttpContext);
            }
            return res;
        }

        [CommAuth]
        [HttpGet]
        public MTXPlusLoginResponse GetUserDetails(long UserID)
        {
            MTXPlusLoginResponse objres = new MTXPlusLoginResponse();
            if (Convert.ToInt32(UserID) > 0)
            {
                objres = objIMTXPlusLoginBLL.GetUserDetails(UserID, HttpContext);
            }
            return objres;
        }
        [HttpPost]
        public MTXPlusLoginResponse SetLoggedinUserDetails(MTXPlusLogInDTO objlogindet)
        {
            MTXPlusLoginResponse objres = new MTXPlusLoginResponse();
            if (!string.IsNullOrEmpty(objlogindet.EmployeeId))
            {
                var origin = Request.Headers.TryGetValue("origin", out var originHeader)
                ? originHeader.ToString()
                : "";
                objres = objIMTXPlusLoginBLL.SetLoggedinUserDetails(objlogindet, origin, HttpContext);
            }
            return objres;
        }


        [HttpGet]
        public ResponseData<List<PreLoginSurvey>> GetSurveyByLocation()
        {
            ResponseData<List<PreLoginSurvey>> response = new ResponseData<List<PreLoginSurvey>>();
            response.Data = new List<PreLoginSurvey>();          
            response.Data= objIMTXPlusLoginBLL.GetSurveyByLocation();
            response.Status = true;
            return response;


        }
        [CommAuth]
        [HttpGet("{UserId}")]
        public ResponseData<List<PreLoginSurvey>> GetPreLoginSurveyRequirement(long UserId)
        {
            return objIMTXPlusLoginBLL.GetPreLoginSurveyRequirement(UserId);
        }
        [CommAuth]
        [HttpPost]
        public void InsertEmployeeSurvey([FromBody] PreLoginSurveyResult obj)
        {
            objIMTXPlusLoginBLL.InsertEmployeeSurvey(obj);
        }
        [CommAuth]
        [HttpPost]
        public void UpdateUserPinnedMenu([FromBody] PinMenuRequest request)
        {
            if (request == null || request.UserId <= 0 || request.MenuId <= 0)
                return;

            objIMTXPlusLoginBLL.UpdateUserPinnedMenu(request.UserId, request.MenuId, request.IsPin);
        }
        [CommAuth]
        [HttpGet]
        public bool VerifyToken(string UserId,string token)
        {
            if (string.IsNullOrEmpty(UserId) || string.IsNullOrEmpty(token))
                return false;

            return objIMTXPlusLoginBLL.VerifyToken(UserId, token);
        }

    }
}
