using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using EmailCommunicationBLL;
using PropertyLayers;
using Helper;
using System.Runtime.Caching;
using Microsoft.Extensions.Configuration;
using MongoConfigProject;
using Newtonsoft.Json;

namespace MatrixCoreAPI.Controllers
{

    //[CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class FOSController : Controller
    {

        IFOSBLL objFOSBLL;
        public FOSController(IFOSBLL _objobjFOSBLL)
        {
            objFOSBLL = _objobjFOSBLL;
        }



        [HttpPost]
        public bool SaveLeadsSubStatus(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            bool result = false;
            try
            {
                if (oLeadsSubStatusModel != null && oLeadsSubStatusModel.SubStatusId > 0)
                {
                    result = objFOSBLL.SaveLeadsSubStatus(oLeadsSubStatusModel);
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in SaveLeadsSubStatus in FL." + ex.ToString());
            }
            return result;
        }


        [HttpGet]
        public List<AppointmentsDataModel> GetAgentAppointments()
        {

            List<AppointmentsDataModel> result = new List<AppointmentsDataModel>();
            try
            {
                string UserId = Request.Headers["AgentId"];
                if (CoreCommonMethods.IsValidString(UserId))
                {
                    result = objFOSBLL.GetAgentAppointments(UserId);
                }
            }
            catch (Exception ex)
            {
                //  Console.WriteLine("Exception in GetAgentAppointments in FL." + ex.ToString());
            }
            return result;
        }

        [HttpGet("{LeadId}")]
        public CustInfoModel GetFOSCustInfo(string LeadId)
        {

            CustInfoModel result = new CustInfoModel();
            try
            {
                string UserId = Request.Headers["AgentId"];
                if (CoreCommonMethods.IsValidString(UserId))
                {
                    result = objFOSBLL.GetFOSCustInfo(LeadId);
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetFOSCustInfo in FL." + ex.ToString());
            }
            return result;
        }

        [HttpGet("{LeadId}/{AppointmentId}")]
        public List<ActvityHistoryModel> GetFOSActivityHistory(string LeadId, string AppointmentId)
        {

            List<ActvityHistoryModel> result = new List<ActvityHistoryModel>();
            try
            {
                string UserId = Request.Headers["AgentId"];
                if (CoreCommonMethods.IsValidString(UserId) && CoreCommonMethods.IsValidString(LeadId) && CoreCommonMethods.IsValidString(AppointmentId))
                {
                    result = objFOSBLL.GetFOSActivityHistory(LeadId, AppointmentId);
                }
            }
            catch (Exception ex)
            {
                //  Console.WriteLine("Exception in GetFOSActivityHistory in FL." + ex.ToString());
            }
            return result;
        }

        [HttpPost]
        public APIResponse MarkAttendance(LogInDTO oLogInDTO)
        {
            APIResponse result = new APIResponse();
            try
            {
                oLogInDTO.UserId = Request != null ? Convert.ToInt64(Request.Headers["AgentId"]) : 0;

                if (oLogInDTO != null && oLogInDTO.Latitude != 0 && oLogInDTO.Longitude != 0 && !oLogInDTO.IsLogout)
                {
                    result = objFOSBLL.MarkAttendance(oLogInDTO);
                }
                else if (oLogInDTO != null && oLogInDTO.IsLogout)
                {
                    result = objFOSBLL.MarkAttendance(oLogInDTO);
                }
                else
                {
                    result.statusCode = 502;
                    result.Result = false;
                    result.Msg = "Latitude/Longitude are compulsory";
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in MarkAttendance in FL." + ex.ToString());

            }
            return result;
        }


        [HttpGet]
        public bool IsUserMarkAttendance()
        {
            bool result = false;
            //long userId = CommonMethods.GetUserIdByHeader();
            string UserId = Request.Headers["AgentId"];

            if (CoreCommonMethods.IsValidString(UserId) && Convert.ToInt64(UserId) > 0)
            {
                Int16 statusCode = objFOSBLL.IsUserActive(Convert.ToInt64(UserId));

                if (statusCode == 401)
                    HttpContext.Response.StatusCode = 401;
                else
                    result = objFOSBLL.IsMarkAttendance(Convert.ToInt64(UserId));
            }

            return result;
        }

        [HttpPost]
        public bool SaveComments(CommentModal oCommentModal)
        {

            bool result = false;
            try
            {
                string UserId = Request.Headers["AgentId"];
                if (CoreCommonMethods.IsValidString(UserId))
                {
                    oCommentModal.UserID= Convert.ToInt64(UserId);
                    result = objFOSBLL.SaveComments(oCommentModal);
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in SaveComments in FL." + ex.ToString());
            }
            return result;
        }

        [HttpPost]
        public bool UpdateAppointmentStatus(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            bool result = false;
            try
            {
                if (oLeadsSubStatusModel != null && oLeadsSubStatusModel.SubStatusId > 0)
                {
                    oLeadsSubStatusModel.Source = string.IsNullOrEmpty(oLeadsSubStatusModel.Source) ? Request.Headers["source"] : oLeadsSubStatusModel.Source;

                    oLeadsSubStatusModel.LeadID = oLeadsSubStatusModel.EncryptLeadId != null ? CommonMethods.GetDecryptIdBySource(oLeadsSubStatusModel.EncryptLeadId) : oLeadsSubStatusModel.LeadID;
                    oLeadsSubStatusModel.CustomerID = oLeadsSubStatusModel.EncryptedCustomerId != null ? CommonMethods.GetDecryptIdBySource(oLeadsSubStatusModel.EncryptedCustomerId) : oLeadsSubStatusModel.CustomerID;

                    result = objFOSBLL.UpdateAppointmentStatus(oLeadsSubStatusModel);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", oLeadsSubStatusModel.ParentID, ex.ToString(), "MatrixCore", "FOSBLL", "UpdateAppointmentStatus", JsonConvert.SerializeObject(oLeadsSubStatusModel), "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        [HttpGet("{CustomerId}/{ParentId}/{AppointmentId}")]
        public AppointmentsDataModel GetAppointmentData(string CustomerId, string ParentId, string AppointmentId)
        {
            AppointmentsDataModel result = new AppointmentsDataModel();
            if (CoreCommonMethods.IsValidString(CustomerId) && CoreCommonMethods.IsValidString(ParentId) && CoreCommonMethods.IsValidString(AppointmentId))
            {
                result = objFOSBLL.GetAppointmentDetails(CustomerId, ParentId, AppointmentId);
            }
            return result;
        }
        [HttpGet("{userId}")]
        public string GetAppToken(string userId)
        {
            string? Domain = Request != null ? Convert.ToString(Request.Host) : string.Empty;
            if (string.IsNullOrEmpty(Domain) || !(Domain.ToUpper().Contains("INTERNALMATRIXAPI")))
            {
                return null;
            }
            string result = string.Empty;
            if (CoreCommonMethods.IsValidString(userId))
            {
                result = objFOSBLL.GetAppToken(userId);
            }
            return result;
        }
        [HttpPost]
        public Data ldapValidationV2([FromBody] EmpData oEmpData)
        {
            Data result = null;
            if (CoreCommonMethods.IsValidString(oEmpData.empId) && CoreCommonMethods.IsValidString(oEmpData.password))
            {
                result = objFOSBLL.ldapValidationV2(oEmpData);
                if (result.statusCode == 401)
                    HttpContext.Response.StatusCode = 401;
            }
            return result;
        }


        [HttpPost]
        public List<ReminderModal> GetReminderData([FromBody] ReminderModal oReminderModal)
        {
            List<ReminderModal> result = new List<ReminderModal>();
            try
            {
                if (oReminderModal.UserId > 0 && oReminderModal.startDate != null && oReminderModal.EndDate != null)
                {
                    result = objFOSBLL.GetReminderData(oReminderModal);
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetReminderData." + ex.ToString());
            }
            return result;
        }


        [HttpPost]
        public bool SetReminderData([FromBody] ReminderModal oReminderModal)
        {
            bool result = false;
            try
            {
                if (oReminderModal.AppointmentId > 0)
                {
                    result = objFOSBLL.SetReminderData(oReminderModal);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in SetReminderData." + ex.ToString()); 
            }
            return result;
        }

        [HttpPost]
        public bool UpdateReminderData([FromBody] ReminderUpdateModal oReminderModal)
        {
            bool result = false;

            if (oReminderModal != null && oReminderModal.FosReminderId > 0 && oReminderModal.UserId > 0)
            {
                result = objFOSBLL.UpdateReminderData(oReminderModal);
            }
            return result;
        }

        [HttpGet("{LeadId}")]
        public List<CustomerCommentModal> GetAgentCommments(string LeadId)
        {

            List<CustomerCommentModal> result = new List<CustomerCommentModal>();
            try
            {
                if (CoreCommonMethods.IsValidString(LeadId))
                {
                    result = objFOSBLL.GetAgentCommments(LeadId);
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }

        [HttpPost]
        public bool UpdateAppLocation([FromBody] AppointmentLocation oAppLocation)
        {
            bool result = false;


            if (oAppLocation != null && oAppLocation.AppointmentId > 0 && oAppLocation.UserId > 0)
            {
                result = objFOSBLL.UpdateAppLocation(oAppLocation);
            }
            return result;
        }
        [HttpPost]
        public SaveInfo SetAppointmentDataV2([FromBody] AppointmentsDataModel objAppointmentData)
        {
            SaveInfo saveInfo = null;
            if (objAppointmentData != null && CoreCommonMethods.IsValidString(objAppointmentData.EncryptedLeadId) && CoreCommonMethods.IsValidString(objAppointmentData.EncryptedCustomerId))
            {
                objAppointmentData.ParentId = objAppointmentData.EncryptedLeadId != null ? CommonMethods.GetDecryptIdBySource(objAppointmentData.EncryptedLeadId) : objAppointmentData.ParentId;
                objAppointmentData.CustomerId = objAppointmentData.EncryptedCustomerId != null ? CommonMethods.GetDecryptIdBySource(objAppointmentData.EncryptedCustomerId) : objAppointmentData.CustomerId;
            }
            objAppointmentData.Source = string.IsNullOrEmpty(objAppointmentData.Source) ? Request.Headers["source"] : objAppointmentData.Source;
            Int16 AgentProcessId = (short)(Request != null && CoreCommonMethods.IsValidString(Convert.ToString(Request.Headers["ProcessId"])) && JsonConvert.DeserializeObject(Request.Headers["ProcessId"]) != string.Empty ? Convert.ToInt16(Request.Headers["ProcessId"]) : 0);


            if (objAppointmentData != null && objAppointmentData.ParentId > 0)
                saveInfo = objFOSBLL.SetAppointmentDataV2(objAppointmentData, AgentProcessId);

            return saveInfo;
        }

        [HttpGet("{UserId}")]
        public List<LeadAttributesModel> GetLeadAttributeDetails(long UserId)
        {
            List<LeadAttributesModel> result = null;
            if (UserId > 0)
            {
                result = objFOSBLL.GetLeadAttributes(UserId);
            }
            return result;
        }

        [HttpPost]
        public SaveInfo SaveLeadsSubStatusV2(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            SaveInfo result = null;
            if (oLeadsSubStatusModel != null && oLeadsSubStatusModel.ParentID > 0)
            {
                oLeadsSubStatusModel.Source = string.IsNullOrEmpty(oLeadsSubStatusModel.Source) ? Request.Headers["source"] : oLeadsSubStatusModel.Source;
                result = objFOSBLL.SaveLeadsSubStatusV2(oLeadsSubStatusModel);
            }
            return result;
        }
        [HttpGet("{AppointmentId}/{Type}")]
        public AppLocationsModal GetAppointnmentLocations(long AppointmentId, Int16 Type)
        {
            AppLocationsModal result = null;
            if (AppointmentId > 0)
            {
                result = objFOSBLL.GetAppointnmentLocations(AppointmentId, Type);
            }
            return result;
        }


        [HttpGet("{LeadId}")]
        public List<CustomerCommentModal> GetActivityHistory(string LeadId)
        {

            List<CustomerCommentModal> result = new List<CustomerCommentModal>();
            try
            {
                if (CoreCommonMethods.IsValidString(LeadId))
                {
                    result = objFOSBLL.GetActivityHistory(LeadId);
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }


        [HttpGet]
        public List<ReasonCancelModal> GetCancelReasons()
        {
            List<ReasonCancelModal> response = new List<ReasonCancelModal>();
            response = objFOSBLL.GetCancelReasons();
            return response;
        }
        [HttpGet("{CustomerId}/{ParentId}/{AppointmentId}/{BookingId}")]
        public AppointmentsDataModel GetAppointmentDataV2(string CustomerId, string ParentId, string AppointmentId, string BookingId)
        {
            AppointmentsDataModel result = new AppointmentsDataModel();
            if (CoreCommonMethods.IsValidString(CustomerId) && CoreCommonMethods.IsValidString(ParentId) && CoreCommonMethods.IsValidString(AppointmentId))
            {
                result = objFOSBLL.GetAppointmentDetailsV2(CustomerId, ParentId, AppointmentId, BookingId);
            }
            return result;
        }

        [HttpGet("{Key}")]
        public bool removeCache(string Key)
        {
            if (MemoryCache.Default[Key] != null)
            {
                MemoryCache.Default.Remove(Key);
                return true;
            }
            return false;
        }

        [HttpGet]
        public string getVersion()
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string devEnviornment = con.GetSection("Communication").GetSection("Environment").Value.ToString();
            string getVersion = "VersionNo".AppSettings() + " Env Var." + Environment.GetEnvironmentVariable("MATRIX_ASPNETCORE_ENVIRONMENT") + " dev env" + devEnviornment;
            return getVersion;

        }

        [HttpGet("{CustomerID}")]
        public List<CustLeadInfo> getCustomerActiveLeads(string CustomerID)
        {
            List<CustLeadInfo> info = objFOSBLL.getCustLeadInfo(Convert.ToInt64(CustomerID));
            return info;

        }
        [HttpGet("{UserId}")]
        public SurveyInfo CheckSurveyAgent(string UserId)
        {
            SurveyInfo Survey = objFOSBLL.CheckSurveyAgent(Convert.ToInt64(UserId));
            return Survey;

        }
        [HttpPost]
        public ResponseAPI ValidateQRCode(AppointmentsDataModel oAppointmentsDataModel)
        {
            ResponseAPI result = null;
            string UserId = Request != null ? Request.Headers["AgentId"] : string.Empty;
            string Source = Request != null ? Request.Headers["source"] : string.Empty;

            if (oAppointmentsDataModel != null && oAppointmentsDataModel.AppointmentId > 0 && CoreCommonMethods.IsValidString(oAppointmentsDataModel.QRCode) && CoreCommonMethods.IsValidInteger(UserId) > 0)
            {
                oAppointmentsDataModel.UserId = Convert.ToInt64(UserId);
                oAppointmentsDataModel.Source = Source;

                result = objFOSBLL.ValidateQRCode(oAppointmentsDataModel);
            }

            return result;
        }
        [HttpPost]
        public QRCodeModel GenerateQRCode(string AppointmentId)
        {
            QRCodeModel result = null;

            result = objFOSBLL.GenerateQRCode(AppointmentId);

            return result;
        }

        [HttpGet]
        public List<AppointmentsDataModel> GetAppointmentSummary(string InputParm, Int16 Type, Int16 NoOfDays)
        {
            List<AppointmentsDataModel> result = null;

            if (CoreCommonMethods.IsValidString(InputParm) && Type > 0)
                result = objFOSBLL.GetAppointmentSummary(InputParm, Type, NoOfDays);

            return result;
        }

        [HttpGet]
        public List<PriorityModel> GetAgentAssignedLeads(Int16 NoOfDays)
        {
            long agentId = Request != null ? CoreCommonMethods.IsValidInteger(Request.Headers["AgentId"]) : 0;
            List<PriorityModel> objAssignedLeads = objFOSBLL.GetAgentAssignedLeads(agentId, Convert.ToInt16(NoOfDays));
            return objAssignedLeads;
        }

        [HttpPost]
        public Envelope<bool> LinkAppointment(LinkAppointmentRequest request)
        {
            return objFOSBLL.LinkAppointment(request, Request.Headers["AgentId"]);
        }
        [HttpGet("{PlaceId}")]
        public PlaceLatLongModel GetLatLongByPlaceId(string PlaceId)
        {
            return objFOSBLL.GetLatLongByPlaceId(PlaceId);
        }

        [HttpPost]
        public ResponseData<string> SaveCustomerLocationData([FromBody] CustomerLocationModel customerLocationObj)
        {

            if (customerLocationObj != null && CoreCommonMethods.IsValidString(customerLocationObj.EncryptedLeadId))
            {
                customerLocationObj.LeadId = customerLocationObj.EncryptedLeadId != null ? CommonMethods.GetDecryptIdBySource(customerLocationObj.EncryptedLeadId) : customerLocationObj.LeadId;
            }
            return objFOSBLL.SaveCustomerLocationData(customerLocationObj);


        }
        [HttpPost]
        public ResponseData<string> TriggerSaveCustomerLocationData([FromBody] CustomerLocationModel customerLocationObj)
        {

            if (customerLocationObj != null && CoreCommonMethods.IsValidString(customerLocationObj.EncryptedLeadId))
            {
                customerLocationObj.LeadId = customerLocationObj.EncryptedLeadId != null ? CommonMethods.GetDecryptIdBySource(customerLocationObj.EncryptedLeadId) : customerLocationObj.LeadId;
            }
            return objFOSBLL.TriggerSaveCustomerLocationData(customerLocationObj);


        }


        [HttpPost]

        public ResponseData<CustomerLocationModel> GetCustomerLocationData([FromBody] AppointmentsDataModel objAppointmentData)
        {
            if (objAppointmentData != null && CoreCommonMethods.IsValidString(objAppointmentData.EncryptedLeadId) && CoreCommonMethods.IsValidString(objAppointmentData.EncryptedCustomerId))
            {
                objAppointmentData.ParentId = objAppointmentData.EncryptedLeadId != null ? CommonMethods.GetDecryptIdBySource(objAppointmentData.EncryptedLeadId) : objAppointmentData.ParentId;
                objAppointmentData.CustomerId = objAppointmentData.EncryptedCustomerId != null ? CommonMethods.GetDecryptIdBySource(objAppointmentData.EncryptedCustomerId) : objAppointmentData.CustomerId;
            }
            return objFOSBLL.GetCustomerLocationData(objAppointmentData);

        }

        [HttpGet("{LeadId}")]
        public bool CheckCustomerLocationAvailable(long LeadId)
        {
            return objFOSBLL.CheckCustomerLocationAvailable(LeadId);
        }

        [HttpPost]
        public ResponseData<dynamic> IsAppointmentCreated([FromBody] LeadIdModel LeadIdModel)
        {
            ResponseData<dynamic> res = new ResponseData<dynamic>();
            if (LeadIdModel != null && CoreCommonMethods.IsValidString(LeadIdModel.EncryptedLeadId))
            {
                LeadIdModel.LeadId = LeadIdModel.EncryptedLeadId != null ? CommonMethods.GetDecryptIdBySource(LeadIdModel.EncryptedLeadId) : LeadIdModel.LeadId;

            }
            if (LeadIdModel != null)
                return objFOSBLL.IsAppointmentCreated(LeadIdModel.LeadId);
            else
                return res;
        }

        [HttpGet("{LeadId}/{CityId}/{SlotId}/{AppointmentDateTime}/{UserId}")]
        public bool CheckAgentAvailabilityInCity(long LeadId, Int32 CityId, Int16 SlotId, DateTime AppointmentDateTime, long UserId)
        {
            return objFOSBLL.CheckAgentAvailabilityInCity(LeadId, CityId, SlotId, AppointmentDateTime, UserId);
        }
        [HttpGet("{CityId}")]
        public ResponseData<List<DateAvailableModel>> getAgentAvailabilityInCityMaster(Int32 CityId)
        {
            return objFOSBLL.getAgentAvailabilityInCityMaster(CityId);
        }



        [HttpGet]
        public bool SendCommToOfflineLeadsInCity()
        {
            return objFOSBLL.SendCommToOfflineLeadsInCity();
        }
        [HttpPost]
        public bool PushAppDataToKafka([FromBody] AppointmentsDataModel appointmentsDataModel)
        {
            appointmentsDataModel.UserId = Convert.ToInt64(Request.Headers["AgentId"]);
            return objFOSBLL.PushAppDataToKafka(appointmentsDataModel);

        }
        [HttpPost]
        public ResponseData<EnumAppValidation> SendCustLocationOnWhatsapp([FromBody] LeadLatLong latLong)
        {
            ResponseData<EnumAppValidation> oResponseAPI = new ResponseData<EnumAppValidation>() { Status = false, Message = "Something went wrong", Data = EnumAppValidation.SOMETHINGWRONG };
            if (CoreCommonMethods.IsValidInteger(latLong.LeadId) > 0)
            {
                oResponseAPI = objFOSBLL.SendCustLocationOnWhatsapp(latLong.LeadId, latLong.latlong, latLong.Address);
            }
            return oResponseAPI;
        }

        [HttpPost]
        public bool SaveCoreAddressUsage([FromBody] FOSCoreAddressModel FOSCoreAddressModel)
        {
            bool result = false;
            if (CoreCommonMethods.IsValidInteger(FOSCoreAddressModel.LeadId) > 0)
            {
                result = objFOSBLL.SaveCoreAddressUsage(FOSCoreAddressModel);
            }
            return result;
        }
        [HttpGet("{CityId}")]
        public ResponseData<List<DateAvailableModel>> GetTotalAppointmentsByCityId(Int32 CityId)
        {

            return objFOSBLL.GetTotalAppointmentsByCityId(CityId);

        }
        [HttpGet("{LeadId}")]
        public ResponseData<CarDataModel> GetCarDetails(long LeadId)
        {
            return objFOSBLL.GetCarDetails(LeadId);
        }

        [HttpGet("{LeadId}")]
        public ResponseData<bool> IsActiveAppointment(long LeadId)
        {
            return objFOSBLL.IsActiveAppointment(LeadId);
        }

        [HttpGet("{LeadId}/{CustomerId}/{ProductID}/{Source}")]
        public ResponseData<AppointmentURLData> GetAppointmentURL(long LeadId, long CustomerId = 0, Int16 ProductID = 0, string Source = "")
        {
            return objFOSBLL.GetAppointmentURL(LeadId, CustomerId, ProductID, Source);
        }

        [HttpGet("{Key}")]

        public ResponseData<dynamic> GetDataByKey(string Key)
        {
            ResponseData<dynamic> res = new();
            if (CoreCommonMethods.IsValidString(Key))
            {
                return objFOSBLL.GetDataByKey(Key);
            }
            return res;
        }

        [HttpGet]
        public ResponseData<List<string>> GetAllKeys()
        {
            return objFOSBLL.GetAllKeys();
        }

        [HttpGet("{LeadId}")]
        public ResponseData<bool> SaveFOSIntent(long LeadId)
        {
            string source = Request != null && Request.Headers != null ? Request.Headers["source"] : "";
            return objFOSBLL.SaveFOSIntent(LeadId, source);
        }
        [HttpGet]
        public ResponseData<List<AppointmentDataModelAI>> GetAppointmentDetailsforAI()
        {
            return objFOSBLL.GetAppointmentDetailsforAI();
        }

        [HttpGet("{LeadId}")]
        public ResponseData<dynamic> GetAppointmentExistanceStatus(long LeadId)
        {
            return objFOSBLL.GetAppointmentExistanceStatus(LeadId);
        }

        [HttpPost]
        public ResponseData<string> RescheduleAppointmentCustWA([FromBody] RescheduleApptData rescheduleAppt)
        {
            ResponseData<string> result = new() {
                Status = false,
                Message = "LeadId, AppointmentDateTime and SlotId must have some value" 
            };

            if (rescheduleAppt != null && rescheduleAppt.LeadID > 0 && rescheduleAppt.AppointmentDateTime != DateTime.MinValue && rescheduleAppt.SlotId > 0)
            {
                result = objFOSBLL.RescheduleAppointmentCustWA(rescheduleAppt);
            }
            return result;
        }

        [HttpPost]
        public CallIdResult GetCallId([FromBody] CallIdModel callId)
        {
            CallIdResult result = new() {
                Status = false,
                Message = "ParentID, ProductID, EmpCode are required" 
            };
            if (callId != null && callId.ParentID > 0 && callId.ProductID > 0 && callId.EmpCode != "")
            {
                result = objFOSBLL.GetCallId(callId);
            }
            return result;
        }
    }
}