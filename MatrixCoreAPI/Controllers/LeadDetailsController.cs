﻿using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using EmailCommunicationBLL;
using PropertyLayers;
using Helper;
using System.Text;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Http;

namespace MatrixCoreAPI.Controllers
{

    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class LeadDetailsController : Controller
    {

        ILeadDetailsBLL objLeadetailsBLL;      
        public LeadDetailsController(ILeadDetailsBLL _objLeadetailsBLL)
        {
            objLeadetailsBLL = _objLeadetailsBLL;
            //httpContextAccessor = _httpContextAccessor;
        }

        [HttpGet("{LeadId}")]
        public Response PushRenewalLeadToCJ(string LeadId)
        {
            return objLeadetailsBLL.PushRenewalLeadToCJ(LeadId);
        }

        [HttpPost]
        public Response GetHealthRenewalLead(GetRenewalLead objRenewalLead)
        {
            return objLeadetailsBLL.GetHealthRenewalLead(objRenewalLead);
        }

        [HttpGet]
        public string checkLeadAssignmentDetails()
        {
            string LeadId = "";
            string result = string.Empty;
            try
            {
                LeadId = HttpContext.Request.Query["LeadId"];
                 result = objLeadetailsBLL.checkLeadAssignmentDetails(LeadId);
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in checkLeadAssignmentDetails In LD." + ex.ToString());
            }
            return result;
        }



        [HttpGet]
        public CustomerBookingDocumentDetails GetCustomerBookingDocumentURL(string LeadId)
        {
            return objLeadetailsBLL.GetCustomerBookingDocumentURL(LeadId, Request?.Headers?["AgentId"]);
        }

        [HttpGet("{PolicyNo}")]
        public Response GetRenewalleadByPolicyNo(string PolicyNo)
        {
            return objLeadetailsBLL.GetRenewalleadByPolicyNo(PolicyNo);

        }

        [HttpGet("{userId}")]
        public string GetAgentCSATScore(string userId)
        {
            string result = string.Empty;

            if (CoreCommonMethods.IsValidString(userId) && Convert.ToInt32(userId) > 0)
                result = objLeadetailsBLL.GetAgentCSATScore(Convert.ToInt32(userId));

            return result;

        }

        [HttpGet("{BookingID}/{UserId}/{Source}")]
        public string GetbmsLink(long BookingID, long UserId, string Source)
        {
            StringBuilder sb = new StringBuilder();
            string Origin = Request.Headers["Origin"].FirstOrDefault();
            string ClientIPAddr = HttpContext.Connection.RemoteIpAddress?.ToString();
            string BMSAuthToken;
            string bmsurl = objLeadetailsBLL.GetbmsLink(BookingID, UserId, Source, HttpContext, Request?.Headers?["token"].ToString(),Origin, ClientIPAddr, out BMSAuthToken);
            //httpContextAccessor.HttpContext.Response.Cookies.Append("BMSAuthToken", BMSAuthToken, new CookieOptions()
            //{
            //    Secure = true,
            //    HttpOnly = true,
            //    Expires = DateTimeOffset.UtcNow.AddHours(13.5),
            //    Path = "/",
            //    IsEssential = true,
            //    Domain = ".policybazaar.com",
            //    MaxAge = new TimeSpan(8, 0, 0)
            //});
            return bmsurl;

        }

        [HttpGet("{Empcode}")]
        public bool IsUserKYAEligible(string Empcode)
        {
            return objLeadetailsBLL.IsUserKYAEligible(Empcode);
        }

        [HttpGet("{LeadId}")]
        public LeadInfo GetLeadStatusDetails(string LeadId)
        {
            return objLeadetailsBLL.GetLeadStatusDetails(LeadId);
        }

        [HttpGet]
        public List<CityModal> GetCityList()
        {
            List<CityModal> oCity = objLeadetailsBLL.GetCityList();
            return oCity;
        }

        [HttpGet]
        public List<SubStatusModal> GetSubStatusDetails()
        {
            List<SubStatusModal> oSubStatus = objLeadetailsBLL.GetSubStatusDetails();
            return oSubStatus;
        }
        [HttpGet]
        public LeadInfo GetRenewalLeaddetails(string BookingId)
        {
            LeadInfo oRenewalLeadDetails = objLeadetailsBLL.GetRenewalLeaddetails(BookingId);
            return oRenewalLeadDetails;
        }

        [HttpGet("{UserId}")]
        public bool DkdLottery(string UserId)
        {
            return objLeadetailsBLL.DkdLottery(UserId);
        }
        [HttpPost]
        public List<GroupList> GetUniqueGroups([FromForm] GroupReq objGroupReq)
        {
            return objLeadetailsBLL.GetUniqueGroups(objGroupReq.UserId, objGroupReq.ProductId);
        }

        [HttpGet("{LeadID}")]
        public List<CustomerSelection> GetCustomerSelection(Int64 LeadID)
        {
            return objLeadetailsBLL.GetCustomerSelection(LeadID);
        }

        [HttpPost]
        public UserInfoModel InsertUserInfo([FromBody] UserInfo userInfo)
        {
            UserInfoModel userInfoModel = new UserInfoModel();
            if (Math.Floor(Math.Log10(userInfo.Mobile) + 1) > 6 && userInfo.CustId > 0 && userInfo.NoOfProfile < 10)
            {
                userInfoModel = objLeadetailsBLL.InsertUserInfo(userInfo);
            }

            return userInfoModel;

        }

        [HttpPost]
        public LeadDeatils GetLeadDetails([FromBody]GetLeadDetails getLeadDetails)
        {
            return objLeadetailsBLL.GetLeadDetails(getLeadDetails);
        }

        [HttpPost]
        public LeadResponse CreateLeadByReferralId(LeadRequest leadRequest)
        {
            LeadResponse leadResponse = objLeadetailsBLL.CreateLeadByReferralId(leadRequest);
            return leadResponse;
        }

        [HttpGet]
        public Remarks GetCustomerPitchedRemarks(string LeadId)
        {
            Remarks remarks = objLeadetailsBLL.GetCustomerPitchedRemarks(LeadId);

            return remarks; 
        }

        [HttpGet]
        public UpsellStatus GetUpsellClickStatus(string LeadId)
        {
            UpsellStatus upsellStatus = objLeadetailsBLL.GetUpsellClickStatus(LeadId);
            return upsellStatus;
        }        

        [HttpPost]
        public ResponseAPI InsertCallBack(string ecode, long leadId, string callBackDateTime, int callBackTypeId)
        {
            var response = new ResponseAPI();

            if (!string.IsNullOrEmpty(ecode) && leadId > 0 && !string.IsNullOrEmpty(callBackDateTime) && callBackTypeId >= 0)
            {
                response = objLeadetailsBLL.InsertCallBack(ecode, leadId, callBackDateTime, callBackTypeId);
            }
            else
            {
                response.status = false;
                response.message = "Invalid inputs!";
            }
            return response;
        }

        [HttpPost]
        public ResponseAPI SetCallBack(CallBack callBack)
        {
            var response = new ResponseAPI();

            if (callBack != null && !string.IsNullOrEmpty(callBack.ProcessName))
            {
                response = objLeadetailsBLL.SetCallBack(callBack);
            }
            else
            {
                response.status = false;
                response.message = "Invalid inputs!";
            }
            return response;
        }

        [HttpGet]
        public LeadDataModel GetLeadAssignedAgent(string MobileNo, Int16 ProductID)
        {         
            return objLeadetailsBLL.GetLeadAssignedAgent(MobileNo, ProductID); 
        }
        [HttpGet]
        public bool AssignLead(Int64 LeadID, string EmployeeID, string Source)
        {
            return objLeadetailsBLL.AssignLead(LeadID, EmployeeID, Source, Request?.Headers?["AgentId"]);
        }

        [HttpGet]
        public AgentDetails GetAgentDetails(string encLeadId,string? Process)
        {
            DateTime req = DateTime.Now;
            string EncKey = string.Empty;
            string EncIV = string.Empty;
            AgentDetails agentDetails = new AgentDetails();
            try
            {
                EncKey = Request != null ? (Request.Headers["EncKey"]).ToString() : string.Empty;
                EncIV = Request != null ? (Request.Headers["EncIV"]).ToString() : string.Empty;

                agentDetails = objLeadetailsBLL.GetAgentDetails(encLeadId, EncKey, EncIV, Process);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(encLeadId, Convert.ToInt64(encLeadId), ex.ToString(), "GetAgentDetails", "MatrixCore", "LeadDetailsController", encLeadId, ex.ToString(), req, DateTime.Now);
            }


            return agentDetails;
        }

        [HttpPost]
        public LeadResponse GetRenewalBookingStatus(RenewalBookingData request)
        {
            long custId = 0;
            var response = new LeadResponse();
            try
            {
                if (request != null)
                {
                    var refId = Convert.ToInt64(Crypto.Decrytion_Payment_AES(request.ReferralId, "Core", 256, 128, Request?.Headers["EncKey"].ToString(), Request?.Headers["EncIV"].ToString(), true));
                    custId = Convert.ToInt64(Crypto.Decrytion_Payment_AES(request.CustomerId, "Core", 256, 128, Request?.Headers["EncKey"].ToString(), Request?.Headers["EncIV"].ToString(), true));

                    response.LeadID = objLeadetailsBLL.GetRenewalBookingStatus(refId, request.ProductId, custId);
                    response.Error = "Success";
                }
                else
                {
                    response.Error = "Invalid request";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(custId.ToString(), custId, ex.ToString(),
                                                          "GetRenewalBookingStatus", "MatrixCore", "LeadDetailsController",
                                                          JsonConvert.SerializeObject(request), ex.ToString(), DateTime.Now, DateTime.Now);
                response.Error = "Error occured";
            }
            return response;
        }

        [HttpGet]
        public NewSVURLModel GetCJExitPointUrl(long LeadId, int ProductId)
        {
            return objLeadetailsBLL.GetCJExitPointUrl(LeadId, ProductId, Request?.Headers?["AgentId"]);
        }

        [HttpGet]
        public string GetCJUrl(string LeadID, int ProductId, int SupplierId, string? LeadSource, string? Process)
        {
            string AgentID = Request?.Headers?["AgentId"].ToString();
            return objLeadetailsBLL.GetCJUrl(LeadID, ProductId, SupplierId, LeadSource, Process, AgentID);
        }

        [HttpGet]
        public UrlResponse GetCommonCJUrl(string LeadID, int ProductId, int SupplierId, string? LeadSource, string? Process, string? LeadCreationSource, string? EnquiryId)
        {
            string AgentID = Request?.Headers?["AgentId"].ToString();
            return objLeadetailsBLL.GetCommonCJUrl(LeadID, ProductId, SupplierId, LeadSource, Process, LeadCreationSource, EnquiryId, AgentID);
        }

        [HttpGet]
        public UrlResponse GetJourneyLink(string LeadID, string? Process)
        {
            string AgentID = Request?.Headers?["AgentId"].ToString();
            return objLeadetailsBLL.GetJourneyLink(LeadID, Process,AgentID);
        }
        [HttpPost]
        public GenericAPIResponse CallTransferSalesIVR(CallTransferRequest ReqCTR)
        {
            GenericAPIResponse response = new() { status = 200, message = "Invalid Request" };
            if (!string.IsNullOrEmpty(ReqCTR.bookingid) 
                && Convert.ToInt64(ReqCTR.bookingid) > 0
                && !string.IsNullOrEmpty(ReqCTR.transfer_type) && !string.IsNullOrEmpty(ReqCTR.agent))
            {
                response = objLeadetailsBLL.CallTransferSalesIVR(ReqCTR);
            }
            return response;
        }
        [HttpGet("{LeadID}")]
        public string IsUnfreezeCjURL(string LeadID)
        {
            if (CoreCommonMethods.IsValidInteger(LeadID) > 0)
                return objLeadetailsBLL.IsEnableRollback(LeadID);

            return string.Empty;
        }
        [HttpGet("{CustomerID}")]
        public bool IsAppInstalled(string CustomerID)
        {
            if (CoreCommonMethods.IsValidInteger(CustomerID) > 0)
                return objLeadetailsBLL.IsAppInstalled(CustomerID);

            return false;
        }
        [HttpGet]
        public UrlResponse SmartInvestQuesURL(string CustomerID, string LeadID)
        {
            UrlResponse objUrlResponse = new UrlResponse();
            if (CoreCommonMethods.IsValidInteger(CustomerID) > 0 && CoreCommonMethods.IsValidInteger(LeadID) > 0)
                return objLeadetailsBLL.SmartInvestQuesURL(CustomerID, LeadID);

            return objUrlResponse;
        }
        [HttpGet]
        public CustomerInvestResponse SmartInvestViewResponse(string CustomerID, string LeadID)
        {
            CustomerInvestResponse objResponse = new CustomerInvestResponse();
            if (CoreCommonMethods.IsValidInteger(CustomerID) > 0 && CoreCommonMethods.IsValidInteger(LeadID) > 0)
                return objLeadetailsBLL.SmartInvestViewResponse(CustomerID, LeadID);

            return objResponse;
        }

        [HttpGet]
        public LeadDeatils GetLeadBasicInfo(long LeadId)
        {
            return objLeadetailsBLL.GetLeadBasicInfo(LeadId);
        }

        [HttpGet("{leadId}")]
        public List<ParentChildLeadData> GetParentChildLeadData(long leadId)
        {
            return objLeadetailsBLL.GetParentChildLeadData(leadId);
        }

        [HttpGet("{UserId}")]
        public UserProductList GetUserProductList(long UserId)
        {
            return objLeadetailsBLL.GetUserProductList(UserId);
        }

        [HttpPost]
        public CustCallDetails GetAgentCallDetailsV2([FromBody] CallInfo getAgentCallDetails)
        {
            string Source = Request != null ? Request.Headers["source"] : string.Empty;
            getAgentCallDetails.Source = Source;
            return objLeadetailsBLL.GetAgentCallDetailsV2(getAgentCallDetails);
        }
        [HttpGet]
        public LeadDetailsEntity GetLeadInfoByLeadId(long LeadId)
        {
            return objLeadetailsBLL.GetLeadInfoByLeadId(LeadId);
        }


        [HttpGet("{LeadId}")]
        public BasicLeadDetails GetLeadDetailsByLeadID(string LeadId)
        {
            BasicLeadDetails obj = new BasicLeadDetails();
            if (CoreCommonMethods.IsValidInteger(LeadId) > 0)
            {
                obj = objLeadetailsBLL.GetLeadDetailsByLeadID(Convert.ToInt64(LeadId));
            }
            return obj;
        }
        
        [HttpGet]
        public List<ScheduledCallbackData> GetCallBackDataByAgentId(long AgentId)
        {
            string Source = Request != null ? Request.Headers["source"] : string.Empty;
            return objLeadetailsBLL.GetCallBackDataByAgentId(AgentId, Source);
        }

        [HttpGet]
        public AssignedLeadData AssignLeadToAgent (long LeadId)
        {
            return objLeadetailsBLL.AssignLeadToAgent(LeadId);
        }


        [HttpGet("{CustomerId}")]
        public ResponseData<List<LeadDetailsForCustId>> GetActiveLeadsByCustId(long CustomerId)
        {
            return objLeadetailsBLL.GetActiveLeadsByCustId(CustomerId);
        }

        [HttpGet]
        //This api is for sme - whatsapp
        public LeadDetailsResponse GetLeadDetailsWithAgentInfo(long leadId)
        {
           return objLeadetailsBLL.GetLeadDetailsWithAgentInfo(leadId);
        }

        [HttpGet]
        public long FetchParentLead(long LeadID, int ProductID, long CustomerID)
        {
            return objLeadetailsBLL.FetchParentLead(LeadID, ProductID, CustomerID);
        }
    }
}
