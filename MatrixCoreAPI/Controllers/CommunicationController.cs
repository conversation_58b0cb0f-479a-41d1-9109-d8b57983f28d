﻿using Microsoft.AspNetCore.Mvc;
using MatrixCoreAPI.Helpers;
using EmailCommunicationBLL;
using PropertyLayers;
using System.Collections.Generic;
using System;

namespace MatrixCoreAPI.Controllers
{
    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class CommunicationController : Controller
    {
        readonly ICommunicationBLL communicationBll;
        public CommunicationController(ICommunicationBLL _communicationBll)
        {
            communicationBll = _communicationBll;
        }

        [HttpPost]
        public CtpResponse GetLeadDetails(CtpRequest request)
        {
            return communicationBll.GetLeadDetails(request);
        }

        [HttpGet]
        public List<AgentCommLogs> GetAgentCommLogs(long UserId)
        {
            return communicationBll.GetAgentCommLogs(UserId);
        }

        [HttpGet]
        public List<CTCSchdular> CallSchedular(long LeadId)
        {
            return communicationBll.CallSchedular(LeadId);
        }

        [HttpPost]
        public string SubscribeCustomer(SubscribeCustomer subscribeCustomer)
        {
            subscribeCustomer.SubSource = subscribeCustomer.SubSource == null ? "Onelead": subscribeCustomer.SubSource;
            return communicationBll.SubscribeCustomer(subscribeCustomer);
        }

        [HttpGet("{CustomerId}")]
        public List<CTCSchdular> GetCallSchedularByCustId(long CustomerId)
        {
            return communicationBll.GetCallSchedularByCustId(CustomerId);
        }
    }
}