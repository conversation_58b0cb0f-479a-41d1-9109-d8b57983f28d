﻿using EmailCommunicationBLL;
using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace MatrixCoreAPI.Controllers
{
    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ChatController : Controller
    {

        IChatBLL objChatBLL;
        public ChatController(IChatBLL _objChatBLL)
        {
            objChatBLL = _objChatBLL;
        }
        [HttpPost]
        public bool SyncChatData(List<ChatDataModel> _ChatDataModellst)
        {
            string result = string.Empty;
            return objChatBLL.SyncChatData(_ChatDataModellst);
        }
        [HttpGet("{LeadID}")]
        public LeadData GetcustInfo(string LeadID, Int16 isservice, string? mobileno, string? product)
        {
            return objChatBLL.GetcustInfo(LeadID, isservice, mobileno, product);
        }
        [HttpGet("{LeadID}/{CustId}/{ChatuserID}")]
        public string getMatrixURL(string? LeadID, string? CustId, string? ChatuserID, string? empid)
        {
            return objChatBLL.getSalesViewURl(LeadID, CustId, ChatuserID, empid);
        }
        [HttpGet]
        public bool IsChatAllowed(long leadid, string? IP)
        {
            return objChatBLL.IsChatAllowed(leadid, IP);
        }
        [HttpGet("{LeadID}")]
        public CarInfo getCarInfo(string LeadID)
        {
            return objChatBLL.getCarInfo(Convert.ToInt64(LeadID));
        }
        [HttpGet("{LeadID}")]
        public ChatHealthInfo getHealthInfo(string LeadID)
        {
            return objChatBLL.getHealthInfo(Convert.ToInt64(LeadID));
        }
        [HttpGet("{LeadID}/{UserID}")]
        public ResponseData<string> IsWhatsappAllowed(string LeadID, string UserID)
        {
            return objChatBLL.IsWhatsappAllowed(LeadID, UserID);
        }
        [HttpGet("{leadId}")]
        public LeadData GetAssignDataforchat(string leadId)
        {
            return objChatBLL.GetAssignDataforchat(leadId);
        }

        
        [HttpGet("{CustomerID}")]
        public ResponseData<CustomerActiveLeads> GetCustomerActiveLeads(string CustomerID)
        {
            return objChatBLL.GetCustomerActiveLeads(CustomerID);
        }

        [HttpGet("{LeadID}")]
        public ResponseData<LeadTT> GetLeadTT(string LeadID)
        {
            return objChatBLL.GetLeadTT(LeadID);
        }

        [HttpGet("{LeadId}/{ProductId}")]
        public ResponseData<AdditionalInfo> GetAdditionalInfo(long LeadId, int ProductId)
        {
            return objChatBLL.GetAdditionalInfo(LeadId, ProductId);
        }

        [HttpGet]
        public ChatFilterData ChatfilterData(string leadid, string mobileno)
        {
            return objChatBLL.ChatfilterData(Convert.ToInt64(leadid), mobileno);
        }

        [HttpPost]
        public Envelope<bool> SetCustInteraction(Envelope<InteractionModel> item)
        {
            if(item.Data != null)
            {
                return new Envelope<bool> { Data = objChatBLL.SetCustInteraction(item.Data) };
            }
            else
            {
                return new Envelope<bool> { Data = false, Error = "Please provide Valid Data.", ErrorCode = 1456 };
            }
        }

        [HttpGet("{LeadId}")]
        public ResponseData<EmployeeDetails> GetLastActiveAgentDetails(long LeadId)
        {
            return objChatBLL.GetLastActiveAgentDetails(LeadId);
        }

        [HttpGet("{CustomerId}/{Type}")]
        public ResponseAPI SetCustomerNITrigger(long CustomerId, int Type)
        {
            return objChatBLL.SetCustomerNITrigger(CustomerId, Type);
        }

        [HttpGet]
        public ResponseData<LeadData> GetleadInfo()
        {
            ResponseData<LeadData> response = new ResponseData<LeadData>();
            long leadID = 0; int productId = 0;

            long.TryParse(HttpContext.Request.Query["leadId"], out leadID);
            int.TryParse(HttpContext.Request.Query["productId"], out productId);
            if (leadID <= 0)
            {
                response.Message = "Invalid lead";
                return response;
            }
            return objChatBLL.GetleadInfo(leadID, productId);

        }

                
        [HttpGet("{leadId}/{productId}")]
        public LeadAgentDetailsCHAT GetAssignedLeadAgentDetails(long leadId, short productId)
        {
            return objChatBLL.GetAssignedLeadAgentDetails(leadId, productId);
        }
    }
}
