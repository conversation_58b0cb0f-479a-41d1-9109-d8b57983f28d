﻿using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using EmailCommunicationBLL;
using PropertyLayers;
using System;
using Helper;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace MatrixCoreAPI.Controllers
{
    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]

    public class UserDetailsController : Controller
    {
        IUserDetailsBLL objUserDetailsBLL;
        public UserDetailsController(IUserDetailsBLL _objUserDetails)
        {
            objUserDetailsBLL = _objUserDetails;
        }

        [HttpPost]
        public UserDetailsResponse UpdateUserCertificateDetails(UserCertificationDetails objuserCertDetails)
        {
            UserDetailsResponse resultObj = new UserDetailsResponse();
            string strexception = string.Empty;
            try
            {
                if (objuserCertDetails != null)
                {
                    if (objuserCertDetails.EmployeeId == "")
                    {
                        resultObj.message = "Invalid EmployeeID";
                        return resultObj;
                    }
                    else if (objuserCertDetails.ExamMode == "")
                    {
                        resultObj.message = "Invalid ExamMode";
                        return resultObj;
                    }
                    else if (objuserCertDetails.CertificationDate < 900000000)
                    {
                        resultObj.message = "Invalid CertificationDate";
                        return resultObj;
                    }
                    else if (objuserCertDetails.CertificationEndDate < objuserCertDetails.CertificationDate)
                    {
                        resultObj.message = "CertificationEndDate should be greather than CertificationDate";
                        return resultObj;
                    }
                    else if (string.IsNullOrEmpty(objuserCertDetails.CertificateType))
                    {
                        resultObj.message = "CertificateType missing";
                        return resultObj;
                    }
                    else if (objuserCertDetails.CertificateType != "1")
                    {
                        resultObj.message = "Invalid Certificate Type";
                        return resultObj;
                    }
                    resultObj = objUserDetailsBLL.UpdateUserCertificateDetails(objuserCertDetails);
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objuserCertDetails.EmployeeId.ToString(), 0, strexception, "UpdateUserCertificateDetails", "UserDetailsController", "124", JsonConvert.SerializeObject(objuserCertDetails).ToString(), JsonConvert.SerializeObject(resultObj).ToString(), DateTime.Now, DateTime.Now);
            }
            return resultObj;
        }

        [HttpGet("{EmployeeIdOrName}")]
        public SalesPartnerAndSalesSpecialist GetSalesPartnerAndSalesSpecialist(string EmployeeIdOrName)
        {
            var response = new SalesPartnerAndSalesSpecialist();
            try
            {
                response = UserDetailsBLL.GetSalesPartnerAndSalesSpecialist(EmployeeIdOrName);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.Message, "GetSalesPartnerAndSalesSpecialist",
                                                          "GetSalesPartnerAndSalesSpecialist", "", JsonConvert.SerializeObject(response).ToString(),
                                                          JsonConvert.SerializeObject(response).ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        [HttpGet("{leadId}")]
        public string GetPrimaryAgent(long leadId)
        {
            var response = string.Empty;
            try
            {
                response = UserDetailsBLL.GetPrimaryAgent(leadId);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.Message, "GetPrimaryAgent",
                                                          "GetPrimaryAgent", "", JsonConvert.SerializeObject(response).ToString(),
                                                          JsonConvert.SerializeObject(response).ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        [HttpPost]
        public OtpResult GenerateLoginOtp([FromBody] GetOtpRequest request)
        {
            var response = new OtpResult()
            {
                Message = "Something went wrong, please relogin"
            };
            if (request != null && !string.IsNullOrEmpty(request.MobileNo) && CoreCommonMethods.IsValidInteger(request.UserId) > 0)
            {
                response = objUserDetailsBLL.GenerateLoginOtp(request, HttpContext);
            }
            return response;
        }

        [HttpPost]
        public OtpResult VerifyLoginOtp([FromBody] ValidateOtpRequest request)
        {
            var response = new OtpResult()
            {
                Message = "Something went wrong, please relogin"
            };
            if (request != null && !string.IsNullOrEmpty(request.MobileNo) && CoreCommonMethods.IsValidInteger(request.OTP) > 0)
            {
                response = objUserDetailsBLL.VerifyLoginOtp(request, HttpContext);
            }
            return response;
        }

        [HttpPost]
        public List<ResponseData<string>> SetAgentStatus([FromBody] UserDetails details)
        {
            return objUserDetailsBLL.SetAgentStatus(details.EmployeeIds, details.IsActive);
        }
    }
}
