﻿using Amazon.Auth.AccessControlPolicy;
using EmailCommunicationBLL;
using Helper;
using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace MatrixCoreAPI.Controllers
{
    [CommAuth]
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class BmsController : Controller
    {

        IBmsBLL objBmsBLL;     

        public BmsController(IBmsBLL _objBmsBLL)
        {
            objBmsBLL = _objBmsBLL;
            
        }

        [HttpGet("{CustomerId}")]
        public string GetRMDetails(Int64 CustomerId)
        {
            if (CustomerId <= 0)
            {
                return "No DATA FOUND";
            }
            return objBmsBLL.GetRMDetails(CustomerId);
        }

        [HttpGet("{AgentId}/{BookingId}")]
        public bookingModel GetAgentTypeByBooking(Int64 AgentId, Int64 BookingId)
        {
            bookingModel obookingModel = null;
            long bookingDate = 0;
            if (AgentId > 0)
            {
                string Source = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["source"]) ? Request.Headers["source"].ToString().ToLower() : string.Empty;
                string EncKey = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["EncKey"]) ? Request.Headers["EncKey"].ToString() : string.Empty;
                string EncIV = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["EncIV"]) ? Request.Headers["EncIV"].ToString() : string.Empty;
                obookingModel = objBmsBLL.GetAgentTypeByBooking(AgentId, BookingId, bookingDate, EncKey, EncIV);
            }

            return obookingModel;
        }

        [HttpGet("{AgentId}/{BookingId}/{bookingDate}")]
        public bookingModel GetAgentTypeByBooking(Int64 AgentId, Int64 BookingId, long bookingDate)
        {
            bookingModel obookingModel = null;
            if (AgentId >= 0)
            {

                string Source = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["source"]) ? Request.Headers["source"].ToString().ToLower() : string.Empty;
                string EncKey = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["EncKey"]) ? Request.Headers["EncKey"].ToString() : string.Empty;
                string EncIV = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["EncIV"]) ? Request.Headers["EncIV"].ToString() : string.Empty;
                obookingModel = objBmsBLL.GetAgentTypeByBooking(AgentId, BookingId, bookingDate, EncKey, EncIV);
            }

            return obookingModel;
        }
        [HttpPost]
        public SaveInfo UpdateLeadAttribute([FromBody] LeadFlags oLeadFlags)
        {
            SaveInfo oSaveInfo = new SaveInfo();

            if (oLeadFlags != null && oLeadFlags.AttributeID > 0)
            {
                oSaveInfo = objBmsBLL.UpdateLeadFlags(oLeadFlags);
            }
            return oSaveInfo;
        }

        [HttpGet("{LeadId}/{UserId}/{Source}")]
        public NewSVURLModel GetNewSVURL(Int64 LeadId, Int64 UserId, string Source)
        {
            NewSVURLModel oNewSVURLModel = new NewSVURLModel();
            if (LeadId > 0 && UserId > 0)
            {

                oNewSVURLModel = objBmsBLL.GetNewSVURL(LeadId, UserId, Source);

            }

            return oNewSVURLModel;
        }

        [HttpPut("{LeadId}/{UserId}/{NoCostEMI}")]
        public SaveInfo UpdateNoCostEMI(Int64 LeadId, Int64 UserId, bool NoCostEMI)
        {
            SaveInfo saveInfo = new SaveInfo();
            if (LeadId > 0 && UserId > 0)
            {

                saveInfo = objBmsBLL.UpdateNoCostEMI(LeadId, UserId, NoCostEMI);

            }
            return saveInfo;

        }

        [HttpGet("{UserID}")]
        public AgentSupervisor GetAgentSupervisor(int UserID)
        {
            AgentSupervisor agentSupervisor = new AgentSupervisor();
            if (UserID > 0)
                agentSupervisor = objBmsBLL.GetAgentSupervisor(UserID);
            return agentSupervisor;
        }
        [HttpPost]
        public string GetInternalEmailURL([FromBody] GenerateUrlModel ogenerateUrlModel)
        {
            string result = string.Empty;
            ogenerateUrlModel.UserId = CoreCommonMethods.IsValidInteger(Request?.Headers?["AgentId"]);//consider from header
            
            if (ogenerateUrlModel.UserId > 0 && ogenerateUrlModel.TypeId > 0)
                result = objBmsBLL.GetInternalEmailURL(ogenerateUrlModel);

            return result;
        }

        [HttpGet("{ParentId}")]
        public bool GetNoCostEMI(long ParentId)
        {
            bool result = false;
            if (ParentId > 0)
            {
                result = objBmsBLL.GetNoCostEMI(ParentId);
            }
            return result;
        }
        [HttpPost]
        public List<RMCommentsDetailsResponse> GetRMComments([FromBody] ReqRMComment oReqRMComment)
        {
            List<RMCommentsDetailsResponse> comments = objBmsBLL.GetRMComments(oReqRMComment.LeadIds, oReqRMComment.EmployeeID);
            return comments;
        }
        [HttpGet]
        public CallTransferLeadInfo GetLeadCallTransferInfo(string LeadID, string? TransferType)
        {
            CallTransferLeadInfo objCallTransferLeadInfo = new CallTransferLeadInfo();
            if (!string.IsNullOrEmpty(LeadID) && Convert.ToInt64(LeadID) > 0)
            {
                objCallTransferLeadInfo = objBmsBLL.GetleadDetailsForCalltranfer(Convert.ToInt64(LeadID), TransferType);
            }
            return objCallTransferLeadInfo;
        }

        [HttpPost]
        public SaveInfo CreateBooking([FromBody] BookingDetailsModel reqData)
        {
            return objBmsBLL.CreateBooking(reqData, Request?.Headers?["AgentId"], Request?.Headers?["source"]);
        }

        [HttpPost]
        public int UpdateLeadDetails([FromBody] LeadDetailsEntity reqData)
        {
            return objBmsBLL.UpdateLeadDetails(reqData);
        }

        [HttpGet]
        public LeadDetailsEntity GetLeadDetailsByLeadId(long LeadId)
        {

            string EncKey = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["EncKey"]) ? Request.Headers["EncKey"].ToString() : string.Empty;
            string EncIV = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["EncIV"]) ? Request.Headers["EncIV"].ToString() : string.Empty;

            return objBmsBLL.GetLeadDetailsByLeadId(LeadId, EncKey, EncIV);
        }


        [HttpPost]
        public string SalesLogin(dynamic details)
        {
            var Token = CoreCommonMethods.IsValidString(Request?.Headers?["Token"]) ? Request?.Headers?["Token"].ToString() : null; //consider from header

            if (Token != null)
            {

                string BMSAuthToken;
                string bmsurl = objBmsBLL.SalesLogin(details, Token, out BMSAuthToken);
                return bmsurl;
            }
            return null;
        }
        [HttpGet]
        public AdditionalLeadDetails GetAdditionalLeadDetails(string LeadId, string Source)
        {
            AdditionalLeadDetails objALD = new AdditionalLeadDetails();
            if (!string.IsNullOrEmpty(LeadId) && Convert.ToInt64(LeadId) > 0)
            {
                objALD = objBmsBLL.GetAdditionalLeadDetails(Convert.ToInt64(LeadId), Source);
            }
            return objALD;
        }

        [HttpGet("{LeadID}")]
        public BMSRenewDetails GetRenewalDetails(string LeadID)
        {
            string Source = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["source"]) ? Request.Headers["source"].ToString().ToLower() : string.Empty;

            BMSRenewDetails renewDetails = objBmsBLL.GetRenewalDetails(LeadID,Source);
            return renewDetails;
        }

        [HttpPost]
        public Envelope<bool> InsertUpdateLeadStatus([FromBody] StampingRequestModel reqData)
        {
            return objBmsBLL.InsertUpdateLeadStatus(reqData);
        }
        [HttpGet("{LeadID}")]
        public ResponseData<List<LeadHistoryResponse>> GetLeadHistory(long LeadID)
        {
            ResponseData<List<LeadHistoryResponse>> objLeadHistoryResponse = new ResponseData<List<LeadHistoryResponse>>();
            if (LeadID == null || LeadID <=0)
            {
                objLeadHistoryResponse.Status = false;
                objLeadHistoryResponse.Message = "Invalid LeadID";
                objLeadHistoryResponse.Data = null;
                return objLeadHistoryResponse;
            }
            objLeadHistoryResponse = objBmsBLL.GetLeadHistory(LeadID);
            return objLeadHistoryResponse;
        }

        [HttpGet]
        public LeadStatusResponseModel GetLeadLogs(long leadId)
        {
            return objBmsBLL.GetLeadLogs(leadId);
        }

        [HttpGet]
        public List<LeadDetailsByAgent> GetBookingDetailsByAgentId(long FromDate, long ToDate)
        {
            string UserId = Request != null && Request.Headers != null && !string.IsNullOrEmpty(Request.Headers["AgentId"]) ? Request.Headers["AgentId"].ToString() : string.Empty;
            return objBmsBLL.GetBookingDetailsByAgentId(UserId, FromDate, ToDate);
        }
        [HttpGet("{LeadID}")]
        public ResponseData<BookedAndRenewalLeadData> GetBookedAndRenewalLeadData(long LeadID)
        {
            ResponseData<BookedAndRenewalLeadData> objData = new ResponseData<BookedAndRenewalLeadData>();
            if (LeadID <= 0)
            {
                objData.Status = false;
                objData.Message = "Invalid LeadID";
                objData.Data = null;
                return objData;
            }
            objData = objBmsBLL.GetBookedAndRenewalLeadData(LeadID);
            return objData;
        }

        [HttpGet("{CustomerId}/{LeadId}")]
        public string GetRMDetailsForTermSavings(long CustomerId, long LeadId)
        {
            if (CustomerId <= 0)
            {
                return "No DATA FOUND";
            }
            return objBmsBLL.GetRMDetailsForTermSavings(CustomerId, LeadId);
        }

        [HttpGet]
        public MailboxResponse GetMailURL()
        {
            return objBmsBLL.GetMailURL(Request?.Headers?["AgentId"], HttpContext);
        }
    }
}