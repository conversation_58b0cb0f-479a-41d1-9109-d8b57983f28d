﻿using EmailCommunicationBLL;
using Helper;
using MatrixCoreAPI.Helpers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;
using System;
using System.Collections.Generic;
using DataAccessLayer;
using System.Text;
using Newtonsoft.Json;
using MongoConfigProject;

namespace MatrixCoreAPI.Controllers
{

    [CommAuth]
    [Route("api/[controller]/[action]")]
    public class SalesViewController : Controller
    {
        private ISalesViewBLL objSalesViewBLL;

        public SalesViewController(ISalesViewBLL _objSalesViewBLL)
        {
            objSalesViewBLL = _objSalesViewBLL;
        }

        [HttpGet("{LeadId}")]
        public string testing(string LeadId)
        {
            // Console.WriteLine("testing in SL.");
            LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), "testing Error", "testing", "SalesviewBLL", "testing", "1234", "", DateTime.Now, DateTime.Now);
            return "testing done";
        }

        [HttpGet]
        public List<PriorityModel> GetUserAssignedLeadsV2()
        {
            List<PriorityModel> objPriorityModel = new List<PriorityModel>();
            try
            {
                string UserId = Request.Headers["AgentId"];
                string token = Request.Headers["Token"];
                if (CoreCommonMethods.IsValidString(UserId) && CoreCommonMethods.IsValidString(token))
                {
                    objPriorityModel = objSalesViewBLL.GetUserAssignedLeadsV2(UserId);
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetUserAssignedLeadsV2 in SL." + ex.ToString());
            }
            return objPriorityModel;
        }

        [HttpGet]
        public List<ReassignedLead> GetReassignedLeads()
        {
            List<ReassignedLead> objReassignedLead = new List<ReassignedLead>();
            try
            {
                string UserId = Request.Headers["AgentId"];

                if (CoreCommonMethods.IsValidString(UserId))
                    objReassignedLead = objSalesViewBLL.GetReassignedLeads(Convert.ToInt64(UserId));
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in GetReassignedLeads in SL." + ex.ToString());
            }
            return objReassignedLead;
        }

        [HttpGet("{LeadId}/{AssignedTo}")]
        public bool ReAssignedPODLead(string leadId, string AssignedTo)
        {
            bool result = false;
            try
            {
                string UserId = Request.Headers["AgentId"];
                if (CoreCommonMethods.IsValidString(leadId) && CoreCommonMethods.IsValidString(AssignedTo) && CoreCommonMethods.IsValidString(UserId))
                {
                    result = objSalesViewBLL.ReAssignedPODLead(Convert.ToInt64(leadId), Convert.ToInt64(UserId), Convert.ToInt32(AssignedTo));
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in ReAssignedPODLead in SL." + ex.ToString());
            }
            return result;
        }

        [HttpGet("{LeadId}")]
        public string ViewPhoneNo(string LeadId)
        {
            string result = string.Empty;
            try
            {

                string UserId = Request.Headers["AgentId"];
                string token = Request.Headers["Token"];
                string mobileNo = Request.Headers["mobileNo"];
                if (CoreCommonMethods.IsValidString(LeadId) && CoreCommonMethods.IsValidString(token) && CoreCommonMethods.IsValidString(mobileNo))
                {
                    result = objSalesViewBLL.ViewPhoneNo(LeadId, UserId, token, mobileNo);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in ViewPhoneNo in SL." + ex.ToString());

            }
            return result;
        }

        [HttpGet("{LeadId}/{type}/{ProductId}/{CustomerId}")]
        public string getWhatsAppURL(string LeadId, int type, int ProductId = 0, long CustomerId = 0)
        {
            string result = string.Empty;
            try
            {
                string UserId = Request.Headers["AgentId"];
                if (CoreCommonMethods.IsValidString(LeadId) && CoreCommonMethods.IsValidString(UserId) && type > 0)
                {
                    result = objSalesViewBLL.getWhatsAppURL(LeadId, UserId, type, ProductId, CustomerId);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in getWhatsAppURL in SL." + ex.ToString());
            }
            return result;
        }

        [HttpPost]
        public bool SetAppointmentData([FromBody] AppointmentsDataModel objAppointmentData)
        {
            bool result = false;
            if (objAppointmentData != null && objAppointmentData.ParentId > 0)
            {
                result = objSalesViewBLL.SetAppointmentData(objAppointmentData);
            }
            return result;
        }

        [HttpPost]
        public AppointmentsDataModel GetAppointmentData([FromBody] AppointmentsDataModel _appointmentDataModel)
        {
            AppointmentsDataModel AppointmentsDataModel = null;
            StringBuilder sb = new StringBuilder();
            
            try
            {
                string agentId = Request != null ? Request.Headers["AgentId"] : string.Empty;

                sb.Append("AgentId" + agentId + "\r\n");
                sb.Append("enter in GetAppointmentData1 " + JsonConvert.SerializeObject(_appointmentDataModel) + "\r\n");

                if (_appointmentDataModel != null && CoreCommonMethods.IsValidString(_appointmentDataModel.EncryptedLeadId) && CoreCommonMethods.IsValidString(_appointmentDataModel.EncryptedCustomerId))
                {
                    _appointmentDataModel.CustomerId = CommonMethods.GetDecryptIdBySource(_appointmentDataModel.EncryptedCustomerId);
                    _appointmentDataModel.ParentId = CommonMethods.GetDecryptIdBySource(_appointmentDataModel.EncryptedLeadId);
                    sb.Append(" out AppointmentsDataModel" + "\r\n");
                }
                if (_appointmentDataModel != null && _appointmentDataModel.ParentId > 0 && _appointmentDataModel.CustomerId > 0)
                    AppointmentsDataModel = objSalesViewBLL.GetAppointmentData(_appointmentDataModel.CustomerId, _appointmentDataModel.ParentId);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetAppointmentDataController", "MatrixCore", "SalesViewController", sb.ToString(), JsonConvert.SerializeObject(AppointmentsDataModel), DateTime.Now, DateTime.Now);
            }
            //finally
            //{
            //    LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(_appointmentDataModel.ParentId), error, "GetAppointmentDataController", "MatrixCore", "SalesViewController", sb.ToString(), JsonConvert.SerializeObject(AppointmentsDataModel), DateTime.Now, DateTime.Now);

            //}
            return AppointmentsDataModel;
        }

        //[HttpGet]
        //public string GetPaymentDetails()
        //{
        //    string key = ""; string param = "";
        //    string result = string.Empty;
        //    try
        //    {
        //        param = HttpContext.Request.Query["param"];
        //        key = HttpContext.Request.Query["key"];


        //        if (!CoreCommonMethods.IsValidString(key) && !CoreCommonMethods.IsValidString(param))
        //            return "";


        //        result = objSalesViewBLL.GetPaymentDetails(key, param);
        //    }
        //    catch (Exception ex)
        //    {
        //        //Console.WriteLine("Exception in GetPaymentDetails in SL." + ex.ToString());
        //    }
        //    return result;
        //}

        [HttpGet("{CustId}")]
        public OnCallCustomerModal IsCustomerOnCall(string CustId)
        {
            OnCallCustomerModal response = new OnCallCustomerModal();
            if (CoreCommonMethods.IsValidString(CustId))
            {
                response = objSalesViewBLL.IsCustomerOnCall(CustId);
            }
            return response;
        }
        [HttpGet("{leadId}")]
        public OnCallCustomerModal CheckCustomerOnCallByLeadId(long leadId)
        {
            OnCallCustomerModal response = new OnCallCustomerModal();
            if (leadId>0)
            {
                response = objSalesViewBLL.IsCustomerOnCallByLeadId(leadId);
            }
            return response;
        }


        [HttpGet("{LeadId}/{Type}/{ProductId}")]
        public List<CityMasterModal> GetOfflineCities(string LeadId, string Type, string ProductId)
        {
            List<CityMasterModal> response = new List<CityMasterModal>();
            try
            {
                if (CoreCommonMethods.IsValidString(LeadId) && CoreCommonMethods.IsValidString(Type) && CoreCommonMethods.IsValidString(ProductId))
                {
                    string UserId = Request.Headers["AgentId"];
                    response = objSalesViewBLL.GetOfflineCities(Convert.ToInt64(LeadId), Convert.ToInt16(Type), Convert.ToInt16(ProductId), Convert.ToInt64(UserId));
                }
            }
            catch (Exception ex)
            {

            }
            return response;
        }

        [HttpGet("{LeadId}/{ProductId}")]
        public FOSModal GetAppointmentTypeList(string LeadId, string ProductId)
        {
            FOSModal response = new FOSModal();
            try
            {
                if (CoreCommonMethods.IsValidString(LeadId) && CoreCommonMethods.IsValidString(ProductId))
                {
                    string UserId = Request != null ? Request.Headers["AgentId"] : string.Empty;
                    string ProcessId = Request != null ? Request.Headers["ProcessId"] : string.Empty;

                    response = objSalesViewBLL.GetAppointmentTypeList(Convert.ToInt64(LeadId), Convert.ToInt16(ProductId), Convert.ToInt64(UserId), Convert.ToInt16(ProcessId));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetAppointmentTypeList in SL." + ex.ToString());
            }
            return response;
        }

        [HttpGet("{LeadId}")]
        public List<InsurerQuotePrice> GetSMEQuotesReport(string LeadId)
        {
            List<InsurerQuotePrice> response = new List<InsurerQuotePrice>();
            if (CoreCommonMethods.IsValidString(LeadId))
            {
                response = objSalesViewBLL.GetSMEQuotesReport(Convert.ToInt64(LeadId));
            }
            return response;
        }

        [HttpGet("{ProductId}/{SubStatusId}/{Source?}")]
        public List<ReasonMasterModel> GetReasonMaster(string ProductId, string SubStatusId, string? Source)
        {
            List<ReasonMasterModel> response = new List<ReasonMasterModel>();
            if (CoreCommonMethods.IsValidString(ProductId) && CoreCommonMethods.IsValidString(SubStatusId))
            {
                response = objSalesViewBLL.GetReasonMaster(Convert.ToInt32(ProductId), Convert.ToInt32(SubStatusId),Source);
            }
            return response;
        }



        [HttpPost]
        public bool SaveAppCancelReason([FromBody] AppCancelReasonModel oAppCancelReasonModel)
        {
            bool result = false;
            if (oAppCancelReasonModel != null && oAppCancelReasonModel.SubStatusId > 0)
            {
                result = objSalesViewBLL.SaveAppCancelReason(oAppCancelReasonModel);
            }
            return result;
        }

        [HttpGet]
        public string GetTicketServiceURL(string UId, string ECode, string Name, string Type, string Source, string LId, string TId)
        {
            string result = string.Empty;

            result = objSalesViewBLL.GetTicketServiceURL(UId, ECode, Name, Type, Source, LId, TId);
            return result;
        }
        [HttpGet("{EmpCode}")]
        public bool GetAdvisorInfo(string EmpCode)
        {

            bool response = objSalesViewBLL.GetAdvisorInfo(EmpCode);
            return response;
        }

        [HttpGet]
        public ResponseAPI HealthCheck()
        {
            ResponseAPI Response = new ResponseAPI();
            Response.status = true;
            Response.message = "Success";

            return Response;
        }

        #region SME
        [HttpPost]
        public SaveInfo AddUpdateSMEQuotes([FromBody] SMEQuoteModal oSMEQuoteModal)
        {
            SaveInfo result = null;
            try
            {
                if (oSMEQuoteModal != null && oSMEQuoteModal.LeadID > 0 && CoreCommonMethods.IsValidString(oSMEQuoteModal.Path))
                {
                    result = objSalesViewBLL.AddUpdateSMEQuotes(oSMEQuoteModal);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in AddUpdateSMEQuote in SL." + ex.ToString());
            }
            return result;
        }

        [HttpGet("{LeadId}")]
        public List<SMELeadQuote> GetSMEQuotes(long LeadId)
        {
            List<SMELeadQuote> oCustomerSurvey = null;
            try
            {
                if (LeadId > 0)
                {
                    oCustomerSurvey = objSalesViewBLL.GetSMEQuotes(Convert.ToInt64(LeadId));
                }
            }
            catch (Exception ex)
            {
            }
            return oCustomerSurvey;
        }

        [HttpPost]
        public SaveInfo DeleteSMEQuoteData([FromBody] SMEQuoteDetailModel oSMEQuoteDetailModel)
        {
            SaveInfo result = null;
            try
            {
                if (oSMEQuoteDetailModel != null && oSMEQuoteDetailModel.LeadID > 0)
                {
                    result = objSalesViewBLL.DeleteSMEQuoteData(oSMEQuoteDetailModel);
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in AddUpdateSMEQuote in SL." + ex.ToString());
            }
            return result;
        }

        [HttpPost]
        public SaveInfo UpdateSMEQuoteList([FromBody] SMEQuoteModal oSMEQuoteModal)
        {
            SaveInfo result = null;
            try
            {
                if (oSMEQuoteModal != null && oSMEQuoteModal.LeadID > 0)
                {
                    result = objSalesViewBLL.UpdateSMEQuoteList(oSMEQuoteModal);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in AddUpdateSMEQuote in SL." + ex.ToString());
            }
            return result;
        }


        [HttpPost]
        public SaveInfo UpdateSMEQuotesStatusV2([FromBody] SMEQuoteModal oSMEQuoteModal)
        {
            SaveInfo result = null;
            try
            {
                if (oSMEQuoteModal != null && oSMEQuoteModal.LeadID > 0)
                {
                    //This is called by Quote Agent, no email to be sent by Quote agent to Quote agent (case handle)
                    oSMEQuoteModal.QuoteSource = "QuoteAgent";
                    result = objSalesViewBLL.UpdateSMEQuotesStatusV2(oSMEQuoteModal);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in UpdateSMEQuotesStatusV2 in SL." + ex.ToString());
            }
            return result;
        }

        [HttpPost]
        public SaveInfo SMEQuotesAdditionalUploadV2([FromBody] SMEAdditionalFileModal oSMEAdditionalFileModal)
        {
            SaveInfo result = null;
            try
            {
                if (oSMEAdditionalFileModal != null && oSMEAdditionalFileModal.LeadID > 0 && CoreCommonMethods.IsValidString(oSMEAdditionalFileModal.filePath) && CoreCommonMethods.IsValidString(oSMEAdditionalFileModal.FileName))
                {
                    result = objSalesViewBLL.SMEQuotesAdditionalUploadV2(oSMEAdditionalFileModal);
                }
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in SMEQuotesAdditionalUploadV2 in SL." + ex.ToString());
            }
            return result;
        }
        #endregion

        [HttpGet]
        public Result GetAgentStory()
        {
            Result respone = new Result();
            try
            {
                string UserId = Request.Headers["AgentId"];
                string token = Request.Headers["Token"];
                if (CoreCommonMethods.IsValidString(UserId) && CoreCommonMethods.IsValidString(token))
                {
                    respone = objSalesViewBLL.GetAgentStory(Convert.ToInt64(UserId));
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception in GetAgentStory in SL." + ex.ToString());
            }
            return respone;
        }

        [HttpPost]
        public SaveInfo UpdateAgentStoryResponse([FromBody] AgentStory storyResponse)
        {
            SaveInfo result = null;
            try
            {
                string UserId = Request.Headers["AgentId"];
                string token = Request.Headers["Token"];
                if (CoreCommonMethods.IsValidString(UserId) && CoreCommonMethods.IsValidString(token))
                {
                    storyResponse.AgentId = Convert.ToInt64(UserId);
                    result = objSalesViewBLL.UpdateAgentStoryResponse(storyResponse);
                }
            }
            catch (Exception ex)
            {

                Console.WriteLine("Exception in UpdateAgentStoryResponse in SL." + ex.ToString());
            }
            return result;
        }

        [HttpGet("{custId}")]
        public string GetCustAddress(string custId)
        {
            string response = string.Empty;
            if (CoreCommonMethods.IsValidString(custId) && CoreCommonMethods.IsValidInteger(custId) > 0)
                response = objSalesViewBLL.GetCustAddress(custId);

            return response;
        }



        [HttpPost]
        public SaveInfo SaveAddressInfo([FromBody] CustAddModal oCustAddressModal)
        {
            SaveInfo result = null;
            try
            {
                if (oCustAddressModal != null && oCustAddressModal.CustomerId > 0)
                {
                    result = null;
                    result = objSalesViewBLL.saveAddressInfo(oCustAddressModal);
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }


        [HttpGet("{LeadId}")]
        public TTModal GetCallDetails(long LeadId)
        {
            TTModal response = null;
            response = objSalesViewBLL.GetCallDetails(LeadId);
            return response;
        }

        [HttpPost]
        public string GenerateComBoxUrl([FromBody] CommBoxModal oCommBoxModal)
        {
            string result = string.Empty;
            try
            {
                string UserId = Request.Headers["AgentId"];
                if (oCommBoxModal != null && oCommBoxModal.LeadId > 0 && Convert.ToInt64(UserId) > 0)
                {
                    oCommBoxModal.UserId = Convert.ToInt64(UserId);
                    result = objSalesViewBLL.GenerateComBoxUrl(oCommBoxModal);
                }
                else
                {
                    result = "Invalid Request";
                }
            }
            catch (Exception ex)
            {
            }
            return result;
        }

        [HttpGet("{ParentId}/{ProductId}")]
        public ResponseData<dynamic> GetAssignmentTypeByCityId(string ParentId, string ProductId)
        {
            ResponseData<dynamic> res = new() { Data = null, Message = "Something went wrong", Status = false };
            if (CoreCommonMethods.IsValidString(ParentId) && CoreCommonMethods.IsValidString(ProductId))
            {

                return objSalesViewBLL.GetAssignmentTypeByCityId(Convert.ToInt64(ParentId), Convert.ToInt32(ProductId));
            }
            return res;
        }


        [HttpGet("{ParentId}")]
        public bool IsAppointmentDone(string ParentId)
        {
            bool response = false;
            if (CoreCommonMethods.IsValidString(ParentId))
            {

                response = objSalesViewBLL.IsAppointmentDone(Convert.ToInt64(ParentId));
            }
            return response;
        }

        [HttpPost]
        public SaveInfo SaveFosPitchedData([FromBody] FOSPitchedModel oFOSPitchedModel)
        {
            SaveInfo result = new SaveInfo();
            try
            {
                if (oFOSPitchedModel != null && oFOSPitchedModel.LeadId > 0)
                {
                    result = objSalesViewBLL.SaveFosPitchedData(oFOSPitchedModel);
                }
            }
            catch (Exception ex)
            {
            }
            return result;
        }

        [HttpGet]
        public Dictionary<string, object> GetConfig()
        {
            Dictionary<string, object> response = new Dictionary<string, object>();
            string Key = HttpContext.Request.Query["Key"];
            if (CoreCommonMethods.IsValidString(Key))
            {
                response = objSalesViewBLL.GetConfigfromMongo(Key);
            }
            return response;
        }


        [HttpGet("{UserId}/{Token}")]
        public SVModal GetUserInfo(string UserId, string Token)
        {
            SVModal response = null;
            if (CoreCommonMethods.IsValidString(UserId) && CoreCommonMethods.IsValidString(Token))
            {
                response = objSalesViewBLL.GetUserObj(UserId, Token);
            }
            return response;
        }
        [HttpGet("{UserId}/{Token}")]
        public SVModal GetUserInfoV2(string UserId, string Token)
        {
            SVModal response = null;
            if (CoreCommonMethods.IsValidString(UserId) && CoreCommonMethods.IsValidString(Token))
            {
                response = objSalesViewBLL.GetUserObjV2(UserId, Token);
            }
            return response;
        }

        [HttpGet("{AgentId}")]
        public bookingModel GetAgentTypeByAgentId(Int64 AgentId)
        {
            bookingModel obookingModel = null;
            if (AgentId > 0)
            {
                obookingModel = objSalesViewBLL.GetAgentTypeByAgentId(AgentId);
            }

            return obookingModel;
        }

        [HttpPost]
        public DocumentUrlResponse GetDocumentUrl([FromBody] DocumentUrlRequest request)
        {
            DocumentUrlResponse response;
            try
            {
                if (request != null &&
                    CoreCommonMethods.IsValidString(request.CustomerId) &&
                    CoreCommonMethods.IsValidString(request.DocumentId))
                {
                    response = LeadDetailsBLL.GetDocumentUrl(request);
                }
                else
                {
                    throw new Exception("Invalid Input Data.");
                }
            }
            catch (Exception ex)
            {
                response = new DocumentUrlResponse
                {
                    StatusMessage = ex.Message
                };
            }
            return response;
        }

        [HttpGet("{EmployeeId}")]
        public string hangupCall(string EmployeeId)
        {
            string result = string.Empty;

            if (CoreCommonMethods.IsValidString(EmployeeId))
                result = objSalesViewBLL.hangupCall(EmployeeId);

            return result;
        }


        [HttpPost]
        public ResultLeadDetailsByCustIDAndProdID GetLeadDetailsByCustIDAndProdID([FromBody] LeadDataModel request)
        {
            ResultLeadDetailsByCustIDAndProdID obj = null;

            if (request != null && request.CustomerId > 0 && request.LeadId > 0)
            {
                string userId = Request.Headers["AgentId"];
                request.UserId = Convert.ToInt64(userId);
                List<SVLeadDetails> response = objSalesViewBLL.GetLeadDetailsByCustIDAndProdID(request);

                obj = response != null ? new()
                {
                    GetLeadDetailsByCustIDAndProdIDResult = new LeadDetailsByCustIDAndProdIDResult()
                    {
                        Data = response
                    }
                } : null;

            }

            return obj;
        }



        [HttpGet("{LeadId}")]
        public ResponseAPI GetFOSChurnLogicMsg(long LeadId)
        {
            ResponseAPI result = null;

            if (LeadId > 0)
                result = objSalesViewBLL.GetChurnLogicMsg(LeadId);

            return result;
        }

        [HttpPost]
        public string GeneratePbmeetLink([FromBody] DailerData oDailerData)
        {
            string result = string.Empty;
            if (oDailerData != null && oDailerData.LeadID > 0)
            {
                bool removeCustLink = true;
                result = objSalesViewBLL.GeneratePbmeetLink(oDailerData, removeCustLink);
            }

            return result;
        }

        [HttpPost]
        public string GenerateVerificationVCLink([FromBody] DailerData dialerData)
        {
            string result = string.Empty;
            if (dialerData != null && dialerData.LeadID > 0)
            {
                result = objSalesViewBLL.GenerateVerificationVCLink(dialerData);
            }

            return result;
        }

        [HttpPost]
        public string GeneratePbmeetLinkV2([FromBody] DailerData oDailerData)
        {
            string result = string.Empty;
            if (oDailerData != null && oDailerData.LeadID > 0)
                result = objSalesViewBLL.GeneratePbmeetLinkV2(oDailerData);

            return result;
        }

        [HttpPost]
        public string GeneratePbmeetLinkV3([FromBody] DailerData oDailerData)
        {
            string result = string.Empty;
            if (oDailerData != null && oDailerData.LeadID > 0)
                result = objSalesViewBLL.GeneratePbmeetLinkV3(oDailerData);

            return result;
        }
        [HttpPost]
        public string GetAgentURLByMeetingId([FromBody] DailerData oDailerData)
        {
            string result = string.Empty;
            if (oDailerData != null && oDailerData.LeadID > 0)
                result = objSalesViewBLL.GetAgentURLByMeetingId(oDailerData);

            return result;
        }

        [HttpPost]
        public ResponseAPI ReAssignChurnLead([FromBody] LeadDetailsRequest oLeadDeatils)
        {
            ResponseAPI result = null;
            string UserId = Request.Headers["AgentId"];

            if (oLeadDeatils.LeadID > 0 && CoreCommonMethods.IsValidString(UserId) && Convert.ToInt64(UserId) > 0)
                result = objSalesViewBLL.ReAssignChurnLead(oLeadDeatils.LeadID, Convert.ToInt64(UserId));

            return result;
        }

        [HttpGet]
        public string GetClaimDetailsURL(string CustomerID)
        {
            string result = string.Empty;
            result = objSalesViewBLL.GetClaimDetailsURL(CustomerID);

            return result;
        }

        [HttpGet("{LeadId}")]
        public List<AppointmentsDataModel> GetAppointmentHistory(string LeadId)
        {
            List<AppointmentsDataModel> result = null;

            if (CoreCommonMethods.IsValidInteger(LeadId) > 0)
                result = objSalesViewBLL.GetAppointmentHistory(Convert.ToInt64(LeadId));

            return result;
        }
        [HttpGet("{LeadId}")]
        public ResponseAPI IsAppMarkCancel(string LeadId)
        {
            ResponseAPI result = null;

            if (CoreCommonMethods.IsValidInteger(LeadId) > 0)
                result = objSalesViewBLL.IsAppMarkCancel(Convert.ToInt64(LeadId));

            return result;
        }



        [HttpPost]
        public ResponseAPI SendAdditionalNoOTP([FromBody] OTPModel otpModel)
        {
            ResponseAPI oResponseAPI = new() { message = "Required fields are mandatory" };
            string Source = Request != null ? Request.Headers["source"] : string.Empty;
            if (otpModel != null && CoreCommonMethods.IsValidInteger(otpModel.CustId) > 0 && CoreCommonMethods.IsValidInteger(otpModel.LeadId) > 0)
                oResponseAPI = objSalesViewBLL.SendAdditionalNoOTP(otpModel,  Source);

            return oResponseAPI;
        }

        [HttpPost]
        public ResponseAPI SetCustContactInfo([FromBody] CustContactInfo objCustDetails)
        {
            ResponseAPI oResponseAPI = new() { message = "Required fields are mandatory" };
            string AllowAdditionalNoProducts = "SecOTPProducts".AppSettings();
            string Source = Request != null ? Request.Headers["source"] : string.Empty;

            if (AllowAdditionalNoProducts.Contains("," + objCustDetails.ProductId + ","))
                objCustDetails.IsOTPVerified = true;

            if (objCustDetails != null && objCustDetails.CustomerId > 0 && CoreCommonMethods.IsValidInteger(objCustDetails.MobileNo) > 0)
                oResponseAPI = objSalesViewBLL.SetCustContactInfo(objCustDetails, Source);

            return oResponseAPI;
        }
        [HttpGet]
        public string GetBusinessHealthRatingPercentage(string AgentId, string? ProductId)
        {
            string result = string.Empty;
            result = objSalesViewBLL.GetBusinessHealthRatingPercentage(AgentId, ProductId).ToString();
            return result;
        }

        [HttpGet]
        public string GetConsolidatedBusinessHealthRating(string AgentId, string? ProductId)
        {
            string objBusinessHealthRating = objSalesViewBLL.GetConsolidatedPolicyInforcementData(AgentId, ProductId);
            return objBusinessHealthRating;
        }

        [HttpGet]
        public string GetUserSuperGroup(string AgentId, string ProductId)
        {
            string objBusinessHealthRating = objSalesViewBLL.GetUserSuperGroup(AgentId, ProductId);
            return objBusinessHealthRating;
        }

        [HttpGet]
        public string GetUserGroup(string ProductID, string SourcePage)
        {
            string AgentId = Request != null ? (Request.Headers["AgentId"]).ToString() : string.Empty;
            string objUserGroup = objSalesViewBLL.GetUserGroup(ProductID, SourcePage, AgentId);
            return objUserGroup;
        }

        [HttpGet]
        public string GetBHRDeduction(double BHR, double ShareMonthlyMode)
        {
            string objBHRDeduction = objSalesViewBLL.GetBHRDeduction(BHR, ShareMonthlyMode);
            return objBHRDeduction;
        }
        [HttpPost]
        public List<PaymentFailedTicketResponse> FetchPaymentFailedFeedbackTickets([FromBody] FeedbackTicket objFeedbackTicket)
        {
            List<PaymentFailedTicketResponse> response = new List<PaymentFailedTicketResponse>();
            long AgentId = Convert.ToInt64(Request.Headers["AgentId"]);
            if (objFeedbackTicket.ParentIds != null && objFeedbackTicket.ParentIds.Length > 0)
                response = objSalesViewBLL.GetPaymentFailedFeedbackTickets(objFeedbackTicket.ParentIds, AgentId, objFeedbackTicket.IssueId);
            return response;
        }
        [HttpGet]
        public int GetPaymentFailedCasesCount()
        {
            int PaymentFailedCasesCount = 0;
            long AgentId = Convert.ToInt64(Request.Headers["AgentId"]);
            PaymentFailedCasesCount = objSalesViewBLL.GetPaymentFailedCasesCount(AgentId);
            return PaymentFailedCasesCount;
        }
        [HttpGet]
        public SelfEnforcementRatingData GetSelfEnforcementRatingData()
        {
            SelfEnforcementRatingData objSelfEnforcementRatingData = new SelfEnforcementRatingData();
            long UserId = Convert.ToInt64(Request.Headers["AgentId"]);
            objSelfEnforcementRatingData = objSalesViewBLL.GetSelfEnforcementRatingData(UserId);
            return objSelfEnforcementRatingData;
        }
        [HttpGet]
        public string GetInforcementRatingHierarchialData(string UserId,string Role, string ?ProductID)
        {
            string InforcementRatingHierarchialData = "InvalidUserId";
            if (!string.IsNullOrEmpty(UserId))
            {
                InforcementRatingHierarchialData = objSalesViewBLL.GetInforcementRatingHierarchialData(Convert.ToInt64(UserId), Role, ProductID);
            }
            return InforcementRatingHierarchialData;
        }
        [HttpGet]
        public SelfEnforcementRatingDataMonthWise GetSelfEnforcementRatingDataMonthWise(int month, int year,string ?ProductID)
        {
            SelfEnforcementRatingDataMonthWise objSelfEnforcementRatingDataMonthWise = new SelfEnforcementRatingDataMonthWise();
            long UserId = Convert.ToInt64(Request.Headers["AgentId"]);
            objSelfEnforcementRatingDataMonthWise = objSalesViewBLL.GetSelfEnforcementRatingDataMonthWise(UserId , month, year, ProductID);
            return objSelfEnforcementRatingDataMonthWise;
        }
        [HttpPost]
        public RMDetails IsRMVirtuallyAllocated([FromBody] ReqRMDetails oReqRMDetails)
        {
            RMDetails objRMDetails = new RMDetails();
            if (oReqRMDetails.CustomerID <= 0 || oReqRMDetails.LeadID <= 0)
            {
                return objRMDetails;
            }
            objRMDetails = objSalesViewBLL.IsRMVirtuallyAllocated(oReqRMDetails);
            return objRMDetails;
        }
        [HttpPost]
        public RMDetails AllocateRMBeforeIssuance([FromBody] ReqRMDetails oReqRMDetails)
        {
            RMDetails objRMDetails = new RMDetails();
            if (oReqRMDetails.CustomerID <= 0 || oReqRMDetails.LeadID<=0)
            {
                return objRMDetails;
            }
            objRMDetails = objSalesViewBLL.AllocateRMBeforeIssuance(oReqRMDetails);
            return objRMDetails;
        }

        [HttpGet("{LeadID}")]
        public string GetCustomerPlansFromCJ(long LeadID)
        {
            string Response = "";
            long AgentId = Convert.ToInt64(Request.Headers["AgentId"]);
            if(LeadID > 0)
            {
                Response = objSalesViewBLL.GetCustomerPlansFromCJ(LeadID, AgentId);
            }
            return Response;
        }

        [HttpGet]
        public string GetCustomerPlansFromCJv2([FromQuery] long LeadID, [FromQuery] int? ProductID = null)

        {
            string Response = "";
            long AgentId = Convert.ToInt64(Request.Headers["AgentId"]);
            if (LeadID > 0)
            {
                int productIdValue = ProductID ?? 0; 
                Response = objSalesViewBLL.GetCustomerPlansFromCJv2(LeadID, AgentId, productIdValue);
            }
            return Response;
        }


        [HttpPost]
        public string AvailDiscountonCJ([FromBody] object oReqDiscountDetails)
        {
            string objResponse = "{'Message':No Data Found,'Success':false}";
            if (oReqDiscountDetails != null)
            {
                objResponse = objSalesViewBLL.AvailDiscountonCJ(oReqDiscountDetails);
            }
            else 
            {
                objResponse = "{'Message':Invalid input,'Success':false}";
            }
            return objResponse;
        }

        [HttpPost]
        public string AvailDiscountonCJv2([FromBody] object oReqDiscountDetails)
        {
            string objResponse = "{'Message':No Data Found,'Success':false}";
            if (oReqDiscountDetails != null)
            {
                objResponse = objSalesViewBLL.AvailDiscountonCJv2(oReqDiscountDetails);
            }
            else
            {
                objResponse = "{'Message':Invalid input,'Success':false}";
            }
            return objResponse;
        }

        [HttpPost]
        public List<Paymentstatus> GetPaymentAttemptStatus([FromBody] ReqPaymentsAttempts oReqPaymentsAttempts)
        {
            List<Paymentstatus> paymentStatus = new List<Paymentstatus>();
            if (!string.IsNullOrEmpty(Request.Headers["AgentId"]) && oReqPaymentsAttempts != null)
            {
               paymentStatus = objSalesViewBLL.GetPaymentAttemptStatus(oReqPaymentsAttempts.LeadIds, Convert.ToInt64(Request.Headers["AgentId"]));
            }
            return paymentStatus;
        }
       
        [HttpGet]
        public string GetUpsellModalURL(string LeadId)  
        {
            string response = string.Empty;
            if(!string.IsNullOrEmpty(LeadId) && Convert.ToInt64(LeadId) > 0)
            {
                 response = objSalesViewBLL.GetUpsellModalURL(Convert.ToInt64(LeadId));
            }
            return response;
        }

        [HttpPost]
        public object? GetCustomerIntentsAI([FromBody] object payload)
        {
            return objSalesViewBLL.GetCustomerIntentsAI(payload);
        }

        [HttpPost]
        public object? SaveIntentFeedbackAI([FromBody] object feedbackPayload)
        {
            return objSalesViewBLL.SaveIntentFeedbackAI(feedbackPayload);
        }

        [HttpPost]
        public object? GetCustomerIntentsAI_V2([FromBody] object payload)
        {
            return objSalesViewBLL.GetCustomerIntentsAIV2(payload);
        }

        [HttpPost]
        public object? SaveIntentFeedbackAI_V2([FromBody] object feedbackPayload)
        {
            return objSalesViewBLL.SaveIntentFeedbackAIV2(feedbackPayload);
        }

        [HttpGet("{LeadId}")]
        public string ViewEmailId(string LeadId)
        {
            try
            {
                return objSalesViewBLL.ViewEmailId(LeadId, Request?.Headers?["AgentId"], Request?.Headers?["Email"]);
            }
            catch
            {
                return string.Empty;
            }
        }

        [HttpGet]
        public List<ResponseAPI> SendPaymentCommunication(string LeadID, string PendingAmount, string ProductID, string DueDate, string ?Source, int GroupId, bool ?IsHealthPersistency, string ?UserId, string ?EmployeeId)
        {
            List<ResponseAPI> result = new List<ResponseAPI>();
            ResponseAPI res = new ResponseAPI();
            if (!CoreCommonMethods.IsValidString(LeadID) && !CoreCommonMethods.IsValidString(PendingAmount))
            {
                res.status = false;
                res.message = "Invalid Request";
                result.Add(res);
                return result;
            }

            result = objSalesViewBLL.SendPaymentCommunication(LeadID, PendingAmount, ProductID, DueDate, Source, GroupId, IsHealthPersistency == true ? true : false, string.IsNullOrEmpty(UserId) ? "" : UserId, string.IsNullOrEmpty(EmployeeId) ? "" : EmployeeId);
            return result;
        }

        [HttpGet]
        public List<PaymentFailedCasesModel> GetPaymentFailedCasesInfo(string ProductID, string? UserId)
        {
            string agentId = Request.Headers["AgentId"];
            try
            {
                if (!string.IsNullOrEmpty(UserId))
                {
                    var valueBytes = Convert.FromBase64String(UserId);
                    agentId = Encoding.UTF8.GetString(valueBytes);
                }
            }
            catch
            {

            }
            if (string.IsNullOrEmpty(ProductID))
                ProductID = "115";
            return objSalesViewBLL.GetPaymentFailedCasesInfo(agentId, ProductID);
        }

        [HttpPost]
        public List<LatestPaymentStatusforLead> GetUpdatedPaymentStatusForBookedLeads([FromBody] RequestPaymentStatusData objRequestPaymentStatusData)
        {
            return objSalesViewBLL.UpdatePaymentStatusForBookedLeads(objRequestPaymentStatusData);
        }
        [HttpPost]
        public DuePaymentResponse SendPaymentFailedCommunication([FromBody] RequestSendCommForPaymentFailedCust objReq)
        {
            DuePaymentResponse objDuePaymentResponse = new DuePaymentResponse();
            objDuePaymentResponse =  objSalesViewBLL.SendPaymentFailedCommunication(objReq, Request?.Headers?["AgentId"]);
            return objDuePaymentResponse;
        }

        [HttpGet]
        public ResponseData<long> GetLeadAssignedAgent(long LeadId)
        {
            return objSalesViewBLL.GetLeadAssignedAgent(LeadId);
        }

        [HttpGet]
        public UserDetails GetLeadAssignedAgentDetails(long LeadId)
        {
            return objSalesViewBLL.GetLeadAssignedAgentDetails(LeadId);
        }

        [HttpPost]
        public string GetClaimDetailsInbound([FromBody]ClaimDetails claimDetails)
        {
            return objSalesViewBLL.GetClaimDetailsInbound(claimDetails);
        }
        [HttpGet]
        public ResponseAPI SendEmandateEnableCommunication(string LeadID, string? Source)
        {
            return objSalesViewBLL.SendEmandateEnableCommunication(LeadID, Source);
        }

        [HttpPost]
        public object? CallSummaryV2([FromBody] object request)
        {
            return objSalesViewBLL.CallSummaryV2(request);
        }

        [HttpPost]
        public object? SaveFeedbackV2([FromBody] object request)
        {
            return objSalesViewBLL.SaveFeedbackV2(request);
        }

        [HttpGet("{LeadId}")]
        public AdditonalDetailsRequest GetAdditionalDetails(long LeadId)
        {
            return objSalesViewBLL.GetAdditionalDetails(LeadId);
        }

        [HttpPost]
        public ResponseData<List<LeadDocument>> AddUpdateUploadedDocs([FromBody] DocsUpload docs)
        {
            return objSalesViewBLL.AddUpdateUploadedDocs(docs, Request?.Headers?["AgentId"]);
        }

        public List<QuestionModel> GetFeedBackMaster()
        {
            return objSalesViewBLL.GetFeedBackMaster();
        }

        [HttpPost]
        public ResponseAPI SaveFOSFeedback([FromBody] FOSAppCompeleModel request)
        {
            string UserId = Request.Headers["AgentId"];
            return objSalesViewBLL.SaveFOSFeedback(request, Convert.ToInt64(UserId));
        }

        [HttpGet("{LeadId}/{CustomerId}")]
        public string GetActiveLives(long LeadId, long CustomerId)
        {
            return objSalesViewBLL.GetActiveLives(LeadId, CustomerId);
        }

        [HttpGet("{LeadId}/{CustomerId}")]
        public string GetClaims(long LeadId, long CustomerId)
        {
            return objSalesViewBLL.GetClaims(LeadId, CustomerId);
        }
        
        [HttpPost]
        public string SetCustContactEmailInfo([FromBody]CustContactInfo objCustDetails)
        {
            string response = objSalesViewBLL.SetCustContactEmailInfo(objCustDetails);
            return response;
        }

        [HttpPost]
        public string GetEmailMobileStatus([FromBody]CustContactInfo objCustDetails)
        {
            string response = objSalesViewBLL.GetEmailMobileStatus(objCustDetails);
            return response;
        }

        [HttpGet("{agentId}")]
        public List<AgentAPEDetails> GetAgentAPE(long agentId)
        {
            return objSalesViewBLL.GetAgentAPE(agentId);
        }
        [HttpPost]
        public object RemoveModeratorFromRoomPbmeet([FromBody] object request)
        {
            return objSalesViewBLL.RemoveModeratorFromRoomPbmeet(request);
        }

        [HttpGet("{LeadId}")]
        public List<string> GetLeadIds(long LeadId)
        {
            return objSalesViewBLL.GetLeadIds(LeadId);
        }

        [HttpGet("{LeadId}")]
        public object GetLeadStatus(long LeadId)
        {
            return objSalesViewBLL.GetLeadStatus(LeadId);
        }

        [HttpGet("{LeadId}")]
        public PEDinfo GetSegmentQuestionAnswers(long LeadId)
        {
            return objSalesViewBLL.GetSegmentQuestionAnswers(LeadId);
        }

        [HttpPost]
        public bool SendVCTemplate([FromBody] SendTemplateRequest requesData)
        {
            string CustId = requesData.CustId;
            string URL = requesData.URL;
            string LeadId = requesData.LeadId;
            string productId = requesData.productId;
            string process = requesData.process;
            return objSalesViewBLL.SendVCTemplate(CustId, URL, LeadId, productId, process);
        }
        
        [HttpPost]
        public bool SendVCTemplateV1([FromBody] DailerData oDailerData)
        {
            return objSalesViewBLL.SendVCTemplateV1(oDailerData);
        }

        [HttpGet("{LeadId}/{PropertyId}/{SubStatusId}")]
        public short GetSubStatusReason(long LeadId, short PropertyId, int SubStatusId)
        {
            return objSalesViewBLL.GetSubStatusReason(LeadId, PropertyId, SubStatusId);
        }
        [HttpGet]
        public string TestIsRedisConnected()
        {
            return objSalesViewBLL.IsRedisConnected();
        }


        [HttpGet]
        public List<PaymentInfo> getAllPaymentDetails()
        {
            string key = ""; string param = "";
            string result = string.Empty;

            param = HttpContext.Request.Query["param"];
            key = HttpContext.Request.Query["key"];


            if (!CoreCommonMethods.IsValidString(key) && !CoreCommonMethods.IsValidString(param))
                return null;


            return objSalesViewBLL.getAllPaymentDetails(key, param);
            
        }
        
        [HttpPost]
        public string changeCustomerEmail([FromBody] CustContactInfo objCustDetails)
        {
            return objSalesViewBLL.changeCustomerEmail(objCustDetails);            
        }

        [HttpGet("{CustId}/{MobileNo}/{CountryID}")]
        public bool UpdatePrimaryMob(string CustId, string MobileNo, string CountryID)
        {
            return objSalesViewBLL.UpdatePrimaryMob(CustId, MobileNo, CountryID);
        }
        [HttpPost]
        public string UpdateCallableNumber([FromBody] CustContactInfo objCustDetails)
        {        
            return objSalesViewBLL.UpdateCallableNumber(objCustDetails);
        }
        [HttpPost]
        public NewSVURLModel GetUploadQcrUrl([FromBody] LeadRfqData data)
        {
            return objSalesViewBLL.GetUploadQcrUrl(data);
        }

        [HttpGet]
        public bool GetIsPotentialBuyer(long CustomerId)
        {
            return objSalesViewBLL.GetIsPotentialBuyer(CustomerId);
        }

        [HttpGet]
        public object GetSetRfqPolicyTypeData(long LeadId, short PolicyTypeId, short PolicySubTypeId, bool FetchData)
        {
            return objSalesViewBLL.GetSetRfqPolicyTypeData(LeadId, PolicyTypeId, PolicySubTypeId, FetchData, Request?.Headers?["AgentId"]);
        }

        [HttpPost]
        public bool SaveCallIntent([FromBody] CallIntentData callData)
        {
            bool result = false;

            if (callData != null && callData.LeadId > 0)
            {
                callData.Source = Request.Headers["source"];

                result = objSalesViewBLL.SaveCallIntent(callData);
            }
            return result;
        }
    
        [HttpGet("{LeadId}")]
        public claimcomments GetClaimComments(string LeadId)
        {
            claimcomments response = objSalesViewBLL.GetClaimComments(LeadId);
            return response;
        }
        [HttpGet]
        public string GetCumulativeAgentBHRData(string EmployeeID, string Role, string? ProductID)
        {
            string CumulativeAgentBHRData = "InvalidEmployeeID";
            if (!string.IsNullOrEmpty(EmployeeID))
            {
                CumulativeAgentBHRData = objSalesViewBLL.GetCumulativeAgentBHRData(EmployeeID, Role, ProductID);
            }
            return CumulativeAgentBHRData;
        }

        [HttpGet]
        public List<HWData> GetHWEligibleData(long AgentId, string SupervisorIds)
        {
            List<HWData> objHWData = new List<HWData>();
            try
            {
                string UserId = Request.Headers["AgentId"];
                objHWData = objSalesViewBLL.GetHWEligibleData(AgentId, SupervisorIds, Convert.ToInt64(UserId));
            }
            catch (Exception ex){

            }
            return objHWData;
        }
        [HttpGet("{LeadId}")]
        public LeadDetailResponse GetLeadCustomerDetails(string LeadId)
        {
            return objSalesViewBLL.GetLeadCustomerDetails(Convert.ToInt64(LeadId));
        }

        [HttpGet("{LeadId}")]
        public ResponseAPI SendCouponTrigger(long LeadId)
        {
            return objSalesViewBLL.SendCouponTrigger(LeadId);
        }

        [HttpGet]
        public string GetPBMeetConferenceLink(long LeadId)
        {
            string AgentId = Request != null ? Request.Headers?["AgentId"].ToString() : string.Empty;
            return objSalesViewBLL.GetPBMeetConferenceLink(LeadId, AgentId);
        }

        [HttpGet("{ProductId}")]
        public (List<CreditChangeResponse> List, int IsAuthorisedCreator, bool IsBulkUploadAuthorized) GetCreditChangeRequests(int ProductId)
        {
            string UserId = Request.Headers["AgentId"];
            return objSalesViewBLL.GetCreditChangeRequests(Convert.ToInt64(UserId), ProductId);
        }
        [HttpGet]
        public string GetBookingDetailsForCreditChange(long leadId, int ProductId)
        {
            return objSalesViewBLL.GetBookingDetailsForCreditChange(Convert.ToInt64(leadId), ProductId);
        }
        [HttpGet]
        public List<UserDetails> GetHierarchialAgentList(string ProductID)
        {
            string UserId = Request.Headers["AgentId"];
            return objSalesViewBLL.GetHierarchialAgentList(Convert.ToInt32(ProductID),Convert.ToInt64(UserId));
        }
        [HttpGet]
        public List<CreditChangeReasonMaster> GetCreditChangeReasonMaster()
        {
            return objSalesViewBLL.GetCreditChangeReasonMaster();
        }
        [HttpPost]
        public ResponseAPI SetUpdateCreditChangeRequest([FromBody] CreditChangeRequest objCreditChangeRequest)
        {
            ResponseAPI objResponseAPI = new ResponseAPI();
            string UserId = Request.Headers["AgentId"];
            if (!string.IsNullOrEmpty(UserId))
            {
                objCreditChangeRequest.RequestorUserID = Convert.ToInt64(UserId);
                objResponseAPI =  objSalesViewBLL.SetUpdateCreditChangeRequest(objCreditChangeRequest,0);
            }
            return objResponseAPI;
        }
        [HttpGet]
        public bool IsAuthorisedCreditChangeApprover(long BookingID, int AgentTypeID)
        {
            bool result = false;
            string UserId = Request.Headers["AgentId"];
            if (!string.IsNullOrEmpty(UserId))
            {
                result = objSalesViewBLL.IsAuthorisedCreditChangeApprover(Convert.ToInt64(UserId), BookingID, AgentTypeID);
            }
            return result;
        }
        [HttpGet]
        public List<CreditChangeLogs> GetCreditChangeLogs(long BookingID, int AgentTypeID, int RequestID)
        {
            List<CreditChangeLogs> result = new List<CreditChangeLogs>();
            result = objSalesViewBLL.GetCreditChangeLogs(BookingID, AgentTypeID, RequestID);
            return result;
        }
        [HttpGet("{BookingId}")]
        public List<CreditChangeResponse> GetCreditChangeBookingHistory(long BookingId)
        {
            return objSalesViewBLL.GetCreditChangeBookingHistory(BookingId);
        }


        [HttpPost]
        public List<PauseSelfCallingResponse> PauseFosSelfCalling([FromBody] LogInDTO objAgentData)
        {
            string UserId = Request.Headers["AgentId"];
            List<PauseSelfCallingResponse> result = new List<PauseSelfCallingResponse>();
            if (!string.IsNullOrEmpty(UserId))
            {
                objAgentData.UserId = Convert.ToInt64(UserId);
                result = objSalesViewBLL.PauseFosSelfCalling(objAgentData);

            }
            return result;
        }

        [HttpPost]
        public bool ResumeFosSelfCalling([FromBody] LogInDTO objFOSAgentData)
        {
            string UserId = Request.Headers["AgentId"];
            if (!string.IsNullOrEmpty(UserId))
            {
                objFOSAgentData.UserId = Convert.ToInt64(UserId);
                var result = objSalesViewBLL.ResumeFosSelfCalling(objFOSAgentData);
                return result;
            }
            return false;
        }
        
        [HttpGet("{BookingId}/{ReferenceId}")]
        public bool ValidateSalesCreditReferenceLead(long BookingId,long ReferenceId)
        {
            return objSalesViewBLL.ValidateSalesCreditReferenceLead(BookingId, ReferenceId);
        }

        [HttpGet]
        public LeadDataModel CheckLeadPrevAssignToUser(long leadId)
        {
            LeadDataModel leadData = new();
            string UserId = Request.Headers["AgentId"];
            if (CoreCommonMethods.IsValidString(UserId))
            {
                leadData = objSalesViewBLL.CheckLeadPrevAssignToUser(Convert.ToInt64(UserId), leadId);
            }
           
            return leadData;
        }
        [HttpPost]
        public List<BulkUploadResponseList> BulkUploadSalesCreditChange([FromForm] InsertS3UploadTable requestDto)
        {
            string UserId = Request.Headers["AgentId"];
            return objSalesViewBLL.BulkUploadSalesCreditChange(requestDto, Convert.ToInt64(UserId));
        }

        [HttpPost]
        public LeadVerifyDataResponse LeadVerifyOtp([FromBody] OTPModel obj)
        {
            string UserId = Request.Headers["AgentId"];
            return objSalesViewBLL.LeadVerifyOtp(obj,UserId);
        }

        [HttpPost]
        public object? LeadSummaryAI([FromBody] object request)
        {
            return objSalesViewBLL.LeadSummaryAI(request);
        }

        [HttpPost]
        public object? SaveSummaryFeedbackAI([FromBody] object request)
        {
            return objSalesViewBLL.SaveSummaryFeedbackAI(request);
        }
        
        [HttpGet]
        public List<VirtualNumberModel> GetVirtualNumberList(long LeadId)
        {
            List<VirtualNumberModel> virtualNumber = new();
            string UserId = Request.Headers["AgentId"];
            if (CoreCommonMethods.IsValidString(UserId))
            {
                virtualNumber = objSalesViewBLL.GetVirtualNumberList(LeadId, Convert.ToInt64(UserId));
            }
            return virtualNumber;
        }

        [HttpGet("{BookingId}/{ReferenceId}/{ProductId}/{SelectedAgent}/{AgentTypeId}")]
        public ResponseAPI IsUserEligibleForCredit(long BookingId, string ReferenceId, int ProductId, long SelectedAgent, int AgentTypeId)
        {
            return objSalesViewBLL.IsUserEligibleForCredit(BookingId, ReferenceId, ProductId, SelectedAgent, AgentTypeId);
        }

        [HttpGet]
        public BookingDetailsModel GetSmeRenewalLeadBookingDetails(long LeadId)
        {
            return objSalesViewBLL.GetSmeRenewalLeadBookingDetails(LeadId);
        }

        [HttpPost]
        public ResponseAPI SendEmandateEnableCommunicationTermInv([FromBody] Inputdata obj)
        {
            return objSalesViewBLL.SendEmandateEnableCommunicationTermInv(obj);
        }

        [HttpGet("{ParentId}")]
        public bool InvAdvisorVerify(long ParentId)
        {
            return objSalesViewBLL.InvAdvisorVerify(ParentId);
        }

        [HttpPost]
        public ResponseData<bool> GetPrefComm([FromBody] CommPreferenceData data)
        {
            return objSalesViewBLL.GetPrefComm(data);
        }
        
        [HttpPost]
        public ResponseAPI SendOptinLinktoCustomer([FromBody] SendWAOptInData data)
        {
            return objSalesViewBLL.SendOptinLinktoCustomer(data);
        }

        [HttpGet("{AgentId}/{ProductId}")]
        public ResponseData<BHRResponse> GetBHRPercentageAndColor(long AgentId, int ProductId)
        {
            return objSalesViewBLL.GetBHRPercentageAndColor(AgentId, ProductId);
        }

        [HttpGet("{BookingID}/{AgentTypeID}/{CurrentStatus}/{UserID}/{Actions}/{RequestID}")]
        public void SendCreditChangeCommunicationEP(long BookingID, int AgentTypeID, int CurrentStatus, long UserID, int Actions, int RequestID)
        {
            objSalesViewBLL.SendCreditChangeCommunicationEP(BookingID, AgentTypeID, CurrentStatus, UserID, Actions, RequestID);
        }

        [HttpPost]
        public ResponseAPI SendEmandateEnableCommunicationTermInvTest([FromBody] Inputdata obj)
        {
            return objSalesViewBLL.SendEmandateEnableCommunicationTermInvTest(obj);
        }

        [HttpPost]
        public NewSVURLModel GetUploadRFQUrl([FromBody] LeadRfqData data)
        {
            string agentId = Request != null ? Request.Headers["AgentId"] : string.Empty;
            return objSalesViewBLL.GetUploadRFQUrl(data, agentId);
        }

        [HttpPost]
        public bool MotorRenewalSecondaryNumSwitch([FromBody] SecondaryNumberSwitchRequest request)
        {
            return objSalesViewBLL.MotorRenewalSecondaryNumSwitch(request);
        }

        [HttpPost]
        public bool MotorRenewalLastCallableNumSwitch([FromBody] LastCallableNumSwitchRequest request)
        {
            return objSalesViewBLL.MotorRenewalLastCallableNumSwitch(request);
        }

        [HttpPost]
        public ResponseAPI CreateCreditChangeRequest([FromBody] CreditChangeRequest objCreditChangeRequest)
        {
            ResponseAPI objResponseAPI = new ResponseAPI();
            string UserId = Request.Headers["AgentId"];
            if (!string.IsNullOrEmpty(UserId))
            {
                objCreditChangeRequest.RequestorUserID = Convert.ToInt64(UserId);
                objResponseAPI = objSalesViewBLL.CreateCreditChangeRequest(objCreditChangeRequest, 0);
            }
            return objResponseAPI;
        }

        [HttpGet("{ParentId}")]
        public bool MotorAdvisorVerify(long ParentId)
        {
            bool response = false;
            string UserId = Request.Headers["AgentId"];
            if (!string.IsNullOrEmpty(UserId))
            {
                response = objSalesViewBLL.MotorAdvisorVerify(ParentId);
            }
            return response;
        }

        [HttpGet("{BookingID}/{CustomerId}/{ProductId}")]
        public ResponseData<List<EndorsementData>> GetEndorsementData(long BookingId, long CustomerId, long ProductId)
        {
            ResponseData<List<EndorsementData>> list = new ResponseData<List<EndorsementData>>() { Status = false, Message = "No Data Found", Data = new List<EndorsementData>() };
            string UserId = Request.Headers["AgentId"];
            if (!string.IsNullOrEmpty(UserId))
            {
                list = objSalesViewBLL.GetEndorsementData(BookingId, CustomerId, ProductId);
            }
            return list;
        }

        [HttpPost]
        public ClaimCallTransferAgentDetailsResponse GetClaimTransferInfoOrScheduleCB([FromBody] ClaimCallTransferAgentDetailsRequest input)
        {
            return objSalesViewBLL.GetClaimTransferInfoOrScheduleCB(input);
        }
        
        [HttpPost]
        public CustomerBookingsResponse GetCustomerBookingsByCustomerId([FromBody] CustomerBookingsRequest request)
        {
            var result = objSalesViewBLL.GetCustomerBookingsByCustomerId(request);
            return result;
        }

        [HttpPost]
        public ServiceCallTransferInfoResponse GetBookingInfoByLeadIdCRT([FromBody] ServiceCallTransferInfoRequest request)
        {
            return objSalesViewBLL.GetBookingInfoByLeadIdCRT(request);
        }

        [HttpGet("{LeadId}")]
        public ResponseData<bool> CheckWhatsAppOptinSent(long LeadId)
        {
            return objSalesViewBLL.CheckWhatsAppOptinSent(LeadId);
        }


        [HttpPost]
        public IActionResult GetAssignedAgentDetails([FromBody] AssignedAgentDetailRequest request)
        {
            if (request == null || request.CustomerId <= 0 || request.ProductId <= 0)
                return BadRequest("Invalid request parameters.");

            var result = objSalesViewBLL.GetAssignedAgentDetails(request);
            return Ok(result);
        }

        [HttpGet("{EmployeeId}/{EmployeeRole}/{ProductId}")]
        public string FetchBHRBookingDataTLWise(string EmployeeId, string EmployeeRole, int ProductId)
        {
            string objBusinessHealthRating = objSalesViewBLL.FetchBHRBookingDataTLWise(EmployeeId, EmployeeRole, ProductId);
            return objBusinessHealthRating;
        }
    }
}
