﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net5.0</TargetFramework>
		<Platforms>AnyCPU;x86</Platforms>
		<Nullable>enable</Nullable>
		<ImplicitUsings>disable</ImplicitUsings>
		<ThreadPoolMinThreads>3000</ThreadPoolMinThreads>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<WarningLevel>5</WarningLevel>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
		<WarningLevel>5</WarningLevel>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\EmailCommunicationBLL\EmailCommunicationBLL.csproj" />
		<ProjectReference Include="..\PropertyLayers\PropertyLayers.csproj">
			<GlobalPropertiesToRemove></GlobalPropertiesToRemove>
		</ProjectReference>
	</ItemGroup>



	<ItemGroup>
		<PackageReference Include="AWSSDK.Extensions.NETCore.Setup" Version="3.7.1" />
		<PackageReference Include="AWSSDK.S3" Version="3.7.7.14" />
		<PackageReference Include="ExcelDataReader" Version="3.6.0" />
		<PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="5.0.7" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="6.0.0" />
		<PackageReference Include="System.Net.Http.WinHttpHandler" Version="5.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
	</ItemGroup>


	<ItemGroup>
		<None Remove="Swashbuckle.AspNetCore" />
	</ItemGroup>
	<ItemGroup>

		<None Update="QA_ConfigValues1.xml">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>

		<None Update="ConfigValues1.xml">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="AuthToken.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="ValidationConfig.xml">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
	</ItemGroup>

</Project>
