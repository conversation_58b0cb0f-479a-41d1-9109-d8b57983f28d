﻿using DataAccessLayer;
using DataAccessLibrary;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Helper;
using System.Text;

namespace EmailCommunicationBLL
{
    public class UserDetailsBLL : IUserDetailsBLL
    {
        public UserDetailsResponse UpdateUserCertificateDetails(UserCertificationDetails objuserdetails)
        {
            string strexception = string.Empty;
            UserDetailsDLL detailsDLL = new UserDetailsDLL();
            UserDetailsResponse objuserRes = new UserDetailsResponse();
            try
            {
                (int res, string Message) = detailsDLL.UpdateUserCertificationDetails(objuserdetails);
                objuserRes.message = Message;
                if (res > 0)
                {
                    objuserRes.status = true;
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objuserdetails.EmployeeId.ToString(), 0, strexception, "UpdateUserCertificateDetails", "UpdateUserCertificationDetailsBLL", "124", JsonConvert.SerializeObject(objuserdetails).ToString(), JsonConvert.SerializeObject(objuserRes).ToString(), DateTime.Now, DateTime.Now);
            }
            return objuserRes;
        }

        public static SalesPartnerAndSalesSpecialist GetSalesPartnerAndSalesSpecialist(string employeeIdOrName)
        {
            var response = new SalesPartnerAndSalesSpecialist();
            try
            {
                string employeeId = string.Empty;
                string name = string.Empty;

                if (employeeIdOrName.Any(letter => char.IsDigit(letter)))
                {
                    employeeId = employeeIdOrName;
                }
                else
                {
                    name = employeeIdOrName;
                }

                var data = UserDetailsDLL.GetSalesPartnerAndSalesSpecialist(employeeId, name);

                if (data != null && data.Tables.Count > 0)
                {
                    response.SalesPartners = data.Tables[0].AsEnumerable().ToList().Select(dr => new UserDetails()
                    {
                        UserId = Convert.ToInt64(dr["UserId"]),
                        UserName = string.Format("{0} ({1})", Convert.ToString(dr["UserName"]), Convert.ToString(dr["EmployeeId"]))
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0,
                                                          ex.Message, "GetSalesPartnerAndSalesSpecialist",
                                                          "GetSalesPartnerAndSalesSpecialist", "",
                                                          "", "", DateTime.Now, DateTime.Now);
                response.Message = ex.Message;
                response.SalesPartners = null;
            }
            return response;
        }

        public static string GetPrimaryAgent(long leadId)
        {
            var response = string.Empty;
            try
            {
                response = UserDetailsDLL.GetPrimaryAgent(leadId);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId,
                                                          ex.Message, "GetPrimaryAgent",
                                                          "GetPrimaryAgent", "",
                                                          "", "", DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public OtpResult GenerateLoginOtp(GetOtpRequest otpModel, HttpContext httpContext)
        {
            var response = new OtpResult();
            var error = string.Empty;
            string apiResponse = string.Empty;
            dynamic jsonObject = new ExpandoObject();
            bool IsValidRequest = true;
            try
            {
                var header = new Dictionary<object, object>
                {
                    { "authKey", "coreAPIauthKey".AppSettings() },
                    { "clientKey", "coreAPIclientKey".AppSettings() }
                };

                jsonObject.mobile = otpModel.MobileNo;
                jsonObject.countryCode = otpModel.CountryCode;
                jsonObject.agentId = otpModel.UserId;
                jsonObject.email = otpModel.Email;
                jsonObject.onSms = true;
                jsonObject.onWa = false;
                jsonObject.onEmail = true;
                jsonObject.smsType = "GEN_OTP_SMS_NEW";
                jsonObject.waType = "GEN_OTP_WA_NEW";
                jsonObject.emailType = "GET_OTP_EMAIL_NEW";


                if (otpModel.Source != null && otpModel.Source == "MTXPlusLogin")
                {
                    if (httpContext.Request.Cookies.ContainsKey("TempAuthToken"))
                    {
                        string cookieValue = httpContext.Request.Cookies["TempAuthToken"];
                        if (CoreCommonMethods.IsValidString(cookieValue))
                        {
                            var decodedByteArray = Convert.FromBase64String(cookieValue);
                            var decodedJsonString = Encoding.UTF8.GetString(decodedByteArray);
                            decodedJsonString = Crypto.Decrytion_Payment_AES(decodedJsonString, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings());
                            LDAPUserDetails? userdet = !string.IsNullOrEmpty(decodedJsonString) ? JsonConvert.DeserializeObject<LDAPUserDetails>(decodedJsonString) : null;
                            DateTime LDAPAuthTime = userdet != null ? userdet.LDAPSetTime : default;
                            if (otpModel.UserId == userdet.UserID.ToString() && userdet.IsLDAPEnabled == true && userdet.IsOTPRequired == true)
                            {
                                if ((DateTime.Now - LDAPAuthTime).TotalMinutes <= 5)
                                {
                                    jsonObject.agentId = userdet != null ? userdet.UserID.ToString() : string.Empty;
                                    jsonObject.mobile = userdet != null ? userdet.ContactNo.ToString() : string.Empty;
                                    jsonObject.email = userdet != null ? userdet.EmailID.ToString() : string.Empty;
                                }
                                else
                                {
                                    response.Message = "Session expired, please re-login again";
                                    response.IsSuccess = false;
                                    IsValidRequest = false;
                                }
                            }
                            else
                            {
                                response.Message = "Unauthorised attempt to generate OTP ";
                                response.IsSuccess = false;
                                IsValidRequest = false;
                            }

                        }
                        else
                        {
                            response.Message = "UnAuthorised attempt to send the verification code.";
                            response.IsSuccess = false;
                            IsValidRequest = false;
                        }
                    }
                    else
                    {
                        response.Message = "UnAuthorised attempt to send the verification code.";
                        response.IsSuccess = false;
                        IsValidRequest = false;
                    }
                }


                if (IsValidRequest == true)
                {
                    var request = JsonConvert.SerializeObject(jsonObject);
                    apiResponse = CommonAPICall.CallAPI("coreAPI".AppSettings() + "cs/otp/send/v4", request, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                    var result = JsonConvert.DeserializeObject<CoreOTPResponse>(apiResponse);

                    if (result != null && Convert.ToInt16(result.statusCode) == 1)
                    {
                        response.Message = result.statusMsg;
                        response.IsSuccess = true;
                    }
                    else if (result != null && Convert.ToInt16(result.statusCode) == 2)
                    {
                        response.Message = "You have exceeded the maximum number of Verification code requests";
                    }
                    else
                    {
                        response.Message = "Verification code sent failed";
                    }
                }
            }
            catch (Exception ex)
            {
                response.Message = "Something went wrong. Please resend the verification code.";
                error = ex.Message;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(otpModel.UserId, Convert.ToInt64(otpModel.UserId), error, "GenerateLoginOtp", "MatrixCore", "UserDetailsBLL", JsonConvert.SerializeObject(jsonObject), apiResponse, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public OtpResult VerifyLoginOtp(ValidateOtpRequest otpModel, HttpContext httpContext)
        {
            var response = new OtpResult();
            var error = string.Empty;
            string apiResponse = string.Empty;
            dynamic jsonObject = new ExpandoObject();
            bool IsValidRequest = true;
            bool IsMarkOTPVerified = false;

            try
            {
                var header = new Dictionary<object, object>
                {
                    { "authKey", "coreAPIauthKey".AppSettings() },
                    { "clientKey", "coreAPIclientKey".AppSettings() }
                };

                jsonObject.mobile = otpModel.MobileNo;
                jsonObject.countryCode = otpModel.CountryCode;
                jsonObject.otp = otpModel.OTP;

                // Validate the OTP for MTXPlusLogin
                if (otpModel.Source != null && otpModel.Source == "MTXPlusLogin")
                {
                    var cookieValidationResult = ValidateTempAuthToken(httpContext);

                    if (!cookieValidationResult.IsValid)
                    {
                        response.Message = cookieValidationResult.ErrorMessage;
                        response.IsSuccess = false;
                        IsValidRequest = false;
                    }
                    else
                    {
                        IsMarkOTPVerified = cookieValidationResult.IsMarkOTPVerified;
                    }
                }

                if (IsValidRequest)
                {
                    var request = JsonConvert.SerializeObject(jsonObject);
                    apiResponse = CommonAPICall.CallAPI("coreAPI".AppSettings() + "cs/otp/verify/v3", request, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                    var result = JsonConvert.DeserializeObject<CoreOTPResponse>(apiResponse);

                    if (result != null && Convert.ToInt16(result.statusCode) == 1)
                    {
                        response.Message = result.statusMsg;
                        response.IsSuccess = true;

                        if (IsMarkOTPVerified)
                        {
                            UpdateTempAuthToken(httpContext);
                        }
                    }
                    else if (result != null && !string.IsNullOrEmpty(result.statusMsg))
                    {
                        response.Message = result.statusMsg;
                    }
                    else
                    {
                        response.Message = "Verification code sent failed";
                    }
                }
            }
            catch (Exception ex)
            {
                response.Message = "Something went wrong. Please resend the verification code.";
                error = ex.Message;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(otpModel.TrackingId, Convert.ToInt64(otpModel.OTP), error, "VerifyLoginOtp", "MatrixCore", "UserDetailsBLL", JsonConvert.SerializeObject(jsonObject), apiResponse, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        private (bool IsValid, string ErrorMessage, bool IsMarkOTPVerified) ValidateTempAuthToken(HttpContext httpContext)
        {
            if (httpContext.Request.Cookies.ContainsKey("TempAuthToken"))
            {
                string cookieValue = httpContext.Request.Cookies["TempAuthToken"];
                if (CoreCommonMethods.IsValidString(cookieValue))
                {
                    var decodedByteArray = Convert.FromBase64String(cookieValue);
                    var decodedJsonString = Encoding.UTF8.GetString(decodedByteArray);
                    decodedJsonString = Crypto.Decrytion_Payment_AES(decodedJsonString, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings());
                    var userdet = !string.IsNullOrEmpty(decodedJsonString)
                        ? JsonConvert.DeserializeObject<LDAPUserDetails>(decodedJsonString)
                        : null;

                    if (userdet != null && userdet.IsLDAPEnabled && userdet.IsOTPRequired)
                    {
                        DateTime LDAPAuthTime = userdet.LDAPSetTime;
                        if ((DateTime.Now - LDAPAuthTime).TotalMinutes < 5)
                        {
                            return (true, string.Empty, true);
                        }
                        else
                        {
                            return (false, "This OTP has been expired, please resend the OTP and try again.", false);
                        }
                    }
                    return (false, "Unauthorized attempt to verify OTP.", false);
                }
                return (false, "Unauthorized attempt.", false);
            }
            return (false, "Unauthorized attempt.", false);
        }

        private void UpdateTempAuthToken(HttpContext httpContext)
        {
            string cookieValue = httpContext.Request.Cookies["TempAuthToken"];
            var decodedByteArray = Convert.FromBase64String(cookieValue);
            var decodedJsonString = Encoding.UTF8.GetString(decodedByteArray);
            decodedJsonString = Crypto.Decrytion_Payment_AES(decodedJsonString, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings());
            var userdet = JsonConvert.DeserializeObject<LDAPUserDetails>(decodedJsonString);

            if (userdet != null)
            {
                // Update IsOTPVerified property
                userdet.IsOTPVerified = true;

                var updatedJsonString = JsonConvert.SerializeObject(userdet);
                string encryptjsonString = Crypto.Encrytion_Payment_AES(updatedJsonString, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings(), false);
                var updatedBase64String = Convert.ToBase64String(Encoding.UTF8.GetBytes(encryptjsonString));

                httpContext.Response.Cookies.Append("TempAuthToken", updatedBase64String, new CookieOptions
                {
                    HttpOnly = true,
                    Secure = true,
                    SameSite = SameSiteMode.Strict,
                    Expires = DateTime.Now.AddMinutes(1440),
                    Domain = ".policybazaar.com"
                });
            }
        }

        public List<ResponseData<string>> SetAgentStatus(List<string> employeeIds, bool isActive)
        {
            List<ResponseData<string>> response = new List<ResponseData<string>>();
            ResponseData<string> responseapi = new ResponseData<string>()
            {
                Status = false,
                Message = string.Empty,
                Data = string.Empty
            };
            DateTime reqTime = DateTime.Now;
            try
            {
                if (employeeIds != null && employeeIds.Count > 0)
                {
                    if(employeeIds.Count > 50)
                    {
                        responseapi.Status = false;
                        responseapi.Message = "EmployeeId List containes more than 50 Users";
                        response.Add(responseapi);

                        return response;
                    }
                    foreach (string employeeId in employeeIds)
                    {
                        responseapi = new ResponseData<string>()
                        {
                            Status = false,
                            Message = string.Empty,
                            Data = string.Empty
                        };

                        if (!string.IsNullOrEmpty(employeeId))
                        {
                            DataSet ds = UserDetailsDLL.SetAgentStatus(employeeId, isActive == true ? 1 : 0);
                            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                            {
                                DataRow dr = ds.Tables[0].Rows[0];
                                responseapi.Status = dr["Result"] != null && dr["Result"] != DBNull.Value ? Convert.ToBoolean(dr["Result"]) : false;
                                responseapi.Message = dr["Message"] != null && dr["Message"] != DBNull.Value ? Convert.ToString(dr["Message"]) : string.Empty;
                                responseapi.Data = employeeId;
                                response.Add(responseapi);
                            }
                        }
                        else
                        {
                            responseapi.Status = false;
                            responseapi.Message = "EmployeeId is undefined";
                            responseapi.Data = employeeId;
                            response.Add(responseapi);
                        }
                    }
                }
                else
                {
                    responseapi.Status = false;
                    responseapi.Message = "EmployeeId List is empty";
                    response.Add(responseapi);
                }
            }
            catch (Exception ex)
            {
                responseapi.Status = false;
                responseapi.Message = ex.Message;
                response.Add(responseapi);

                LoggingHelper.LoggingHelper.AddloginQueue(employeeIds[0], 0, ex.Message, "SetAgentInactive", "MRSCore", "AgentBLL", JsonConvert.SerializeObject(employeeIds) + ", isactive - " +  isActive.ToString(), JsonConvert.SerializeObject(response), reqTime, DateTime.Now);
            }
            return response;
        }


    }
}
