using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface IFOSBLL
    {
        bool SaveLeadsSubStatus(LeadsSubStatusModel oLeadsSubStatusModel);
        List<AppointmentsDataModel> GetAgentAppointments(string userId);
        CustInfoModel GetFOSCustInfo(string leadId);
        List<ActvityHistoryModel> GetFOSActivityHistory(string LeadId,string AppointmentId);
        APIResponse MarkAttendance(LogInDTO oLogInDTO);
        bool IsMarkAttendance(long userId);
        bool SaveComments(CommentModal oCommentModal);
        bool UpdateAppointmentStatus(LeadsSubStatusModel oLeadsSubStatusModel);
        AppointmentsDataModel GetAppointmentDetails(string customerId, string parentId,string AppointmentId);
        string GetAppToken(string userId);
        Data ldapValidationV2(EmpData oEmpData);
        List<ReminderModal> GetReminderData(ReminderModal oReminderModal);
        bool SetReminderData(ReminderModal oReminderModal);
        bool UpdateReminderData(ReminderUpdateModal oReminderModal);
        List<CustomerCommentModal> GetAgentCommments(string leadId);
        bool UpdateAppLocation(AppointmentLocation oAppLocation);
        SaveInfo SetAppointmentDataV2(AppointmentsDataModel objAppointmentData, Int16 AgentProcessId);
        List<LeadAttributesModel> GetLeadAttributes(long UserId);
        SaveInfo SaveLeadsSubStatusV2(LeadsSubStatusModel oLeadsSubStatusModel);
        AppLocationsModal GetAppointnmentLocations(long appointmentId, short type);

        List<CustomerCommentModal> GetActivityHistory(string leadId);

        List<ReasonCancelModal> GetCancelReasons();

        AppointmentsDataModel GetAppointmentDetailsV2(string customerId, string parentId, string AppointmentId,string BookingId);
        List<CustLeadInfo> getCustLeadInfo(Int64 CustomerID);
        SurveyInfo CheckSurveyAgent(Int64 UserId);
        Int16 IsUserActive(long userId);
        ResponseAPI ValidateQRCode(AppointmentsDataModel oAppointmentsDataModel);
        QRCodeModel GenerateQRCode(string appointmentId);
        List<AppointmentsDataModel> GetAppointmentSummary(string InputParm, Int16 Type, Int16 NoOfDays);
        List<PriorityModel> GetAgentAssignedLeads(long AgentId, Int16 NoofDays);
        Envelope<bool> LinkAppointment(LinkAppointmentRequest request, string agentId);
        PlaceLatLongModel GetLatLongByPlaceId(string PlaceId);

        ResponseData<string> SaveCustomerLocationData(CustomerLocationModel customerLocationObj);

        ResponseData<string> TriggerSaveCustomerLocationData(CustomerLocationModel customerLocationObj);

        ResponseData<CustomerLocationModel> GetCustomerLocationData(AppointmentsDataModel objAppointmentData);
        bool CheckCustomerLocationAvailable(long LeadId);
        ResponseData<dynamic> IsAppointmentCreated(long LeadId);

        bool CheckAgentAvailabilityInCity(long LeadId, Int32 CityId, Int16 SlotId, DateTime AppointmentDateTime, long UserId);

        ResponseData<List<DateAvailableModel>> getAgentAvailabilityInCityMaster(Int32 CityId);
        bool SendCommToOfflineLeadsInCity();
        bool PushAppDataToKafka(AppointmentsDataModel appointmentsDataModel);


        ResponseData<EnumAppValidation> SendCustLocationOnWhatsapp(string LeadId, LatLongModel latLong, string Address);
        ResponseData<List<DateAvailableModel>> GetTotalAppointmentsByCityId(Int32 CityId);

       bool SaveCoreAddressUsage(FOSCoreAddressModel FOSCoreAddressModel);

       ResponseData<CarDataModel> GetCarDetails(long LeadId);
        ResponseData<bool> IsActiveAppointment(long LeadId);
        ResponseData<AppointmentURLData> GetAppointmentURL(long LeadId, long CustomerId = 0, Int16 ProductID = 0, string Source = "");

        ResponseData<dynamic> GetDataByKey(string Key);
        ResponseData<List<string>> GetAllKeys();
        ResponseData<bool> SaveFOSIntent(long LeadId, string source);

        ResponseData<List<AppointmentDataModelAI>> GetAppointmentDetailsforAI();

        ResponseData<dynamic> GetAppointmentExistanceStatus(long LeadId);

        ResponseData<string> RescheduleAppointmentCustWA(RescheduleApptData rescheduleAppt);

        CallIdResult GetCallId(CallIdModel callId);

    }
}