﻿using PropertyLayers;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface ISME
    {
        List<SubProductListModal> GetSMETransferSubProducts();
        SaveInfo ReAssignTransferLead(AssignLeadModal oAssignLeadModal);
        List<SmeIndustryType> GetSmeIndustryTypes();
        QuickSightUrl GetQuickSightURL(string userId);
        List<MyLeadData> GetMyLeadsData(string agentId, long LeadId);
        List<MyLeadData> GetMyRenewalData(string agentId);
        SaveInfo UpdateSmeLeadDetails(MyLeadData data, string UserId);
        SMEMasterList GetSmeInsurerMaster();
        SaveInfo CreateSmeMom(MOMData data, string UserId);
        List<MOMData> GetSmeMomData(long CustomerId, string UserId);
        SaveInfo SaveSmeFeedback(FeedbackData data);
        SalesPartnerAndSalesSpecialist GetAgentsByType(string AgentType);
        string GetMobileNo(long LeadId, long UserId, int ActionType, long CustomerId, long PrimaryId);
        NewSVURLModel GeneratePerLifeRateURL(GeneratePerLifeRateRequest request);
        List<Master> GetMappingValues();
        Response GetCustomerLeadBySubProduct(string encCustId, int subProductId, string encKey, string encIV);
        List<LeadInfo> GetCustomerOpenLeads(long customerId, int subProductId, long userId);
    }
}