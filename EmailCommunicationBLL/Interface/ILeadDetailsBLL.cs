﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface ILeadDetailsBLL
    {
        Response PushRenewalLeadToCJ(string LeadId);

        Response GetHealthRenewalLead(GetRenewalLead objRenewalLead);

        string checkLeadAssignmentDetails(string LeadId);

        CustomerBookingDocumentDetails GetCustomerBookingDocumentURL(string LeadId, string userId);


        Response GetRenewalleadByPolicyNo(string PolicyNo);

        string GetAgentCSATScore(long userId);

        string GetbmsLink(long BookingID, long UserId, string Source, HttpContext httpContext, string Token,string Origin,string ClientIPAddr, out string BMSAuthToken);

        bool IsUserKYAEligible(String Empcode);

        List<CityModal> GetCityList();
        List<SubStatusModal> GetSubStatusDetails();
        LeadInfo GetLeadStatusDetails(string LeadId);
        LeadInfo GetRenewalLeaddetails(string BookingId);

        bool DkdLottery(string UserId);
        List<GroupList> GetUniqueGroups(string UserId, string ProductId);
        List<CustomerSelection> GetCustomerSelection(Int64 LeadID);
        UserInfoModel InsertUserInfo(UserInfo userInfo);

        LeadDeatils GetLeadDetails(GetLeadDetails getLeadDetails);
        LeadResponse CreateLeadByReferralId(LeadRequest leadRequest);

        Remarks GetCustomerPitchedRemarks(string LeadId);
        UpsellStatus GetUpsellClickStatus(string LeadId);
        public ResponseAPI InsertCallBack(string ecode, long leadId, string callBackDateTime, int callBackTypeId);

        ResponseAPI SetCallBack(CallBack ObjCallBack);

        LeadDataModel GetLeadAssignedAgent(string MobileNo,Int16 ProductID);

        bool AssignLead(Int64 LeadID, string EmployeeID,string Source, StringValues? agentId);

        AgentDetails GetAgentDetails(string encLeadId, string EncKey, string EncIV, string Process);

        long GetRenewalBookingStatus(long referralId, int productId, long customerId);
        string GetCJUrl(string leadID, int productId, int supplierId, string leadSource, string process, string AgentID);
        NewSVURLModel GetCJExitPointUrl(long LeadId, int ProductId, string agentId);

        UrlResponse GetCommonCJUrl(string leadID, int productId, int supplierId, string LeadSource, string process , string LeadCreationSource, string EnquiryId, string AgentId);

        UrlResponse GetJourneyLink(string leadID,string process, string AgentId);
        GenericAPIResponse CallTransferSalesIVR(CallTransferRequest ReqCTR);
        string IsEnableRollback(string leadID);
        bool IsAppInstalled(string CustomerID);
        UrlResponse SmartInvestQuesURL(string CustomerID, string LeadID);
        CustomerInvestResponse SmartInvestViewResponse(string CustomerID, string LeadID);
        LeadDeatils GetLeadBasicInfo(long leadId);
        List<ParentChildLeadData> GetParentChildLeadData(long leadId);
        UserProductList GetUserProductList(long userId);

        CustCallDetails GetAgentCallDetailsV2(CallInfo getAgentCallDetails);
        LeadDetailsEntity GetLeadInfoByLeadId(long LeadId);

        BasicLeadDetails GetLeadDetailsByLeadID(long LeadId);
        List<ScheduledCallbackData> GetCallBackDataByAgentId(long AgentId, string Source);
        AssignedLeadData AssignLeadToAgent(long LeadId);
        ResponseData<List<LeadDetailsForCustId>> GetActiveLeadsByCustId(long customerId);
        LeadDetailsResponse GetLeadDetailsWithAgentInfo(long leadId);

        long FetchParentLead(long LeadID, int ProductID, long CustomerID);
    }
}
