﻿using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface IChatBLL
    {
        bool SyncChatData(List<ChatDataModel> _ChatDataModellst);
        LeadData GetcustInfo(string EncodedLeadID, Int16 isservice, string mobileno, string product);
        string getSalesViewURl(string LeadID, string CustId, string ChatuserID, string EmployeeId);
        bool IsChatAllowed(long LeadID, string IP);
        CarInfo getCarInfo(Int64 LeadID);
        ChatHealthInfo getHealthInfo(Int64 LeadID);
        ResponseData<string> IsWhatsappAllowed(string LeadID, string UserID);
        public LeadData GetAssignDataforchat(string leadId);
        ResponseData<CustomerActiveLeads> GetCustomerActiveLeads(string CustomerID);
        ResponseData<LeadTT> GetLeadTT(string LeadID);
        ResponseData<AdditionalInfo> GetAdditionalInfo(long leadId, int productId);
        ChatFilterData ChatfilterData(long LeadID, string mobileno);
        bool SetCustInteraction(InteractionModel data);
        ResponseData<EmployeeDetails> GetLastActiveAgentDetails(long leadId);
        ResponseAPI SetCustomerNITrigger(long customerId, int type);

        ResponseData<LeadData> GetleadInfo(long LeadID, int productId);
        
        LeadAgentDetailsCHAT GetAssignedLeadAgentDetails(long leadId, short productId);
    }
}
