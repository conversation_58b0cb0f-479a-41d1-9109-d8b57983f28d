﻿using DataAccessLayer;
using Microsoft.Extensions.Primitives;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;


namespace EmailCommunicationBLL
{
    public interface IWebSiteServiceBLL
    {
        public SaveInfo SaveSelectedQuotes(SelectedQuote obj);

        public Response SaveCustomerIntent(List<CustomerIntent> customerIntent);

        public Response SaveLeadAction(LeadAction leadAction);

        public LeadStatusResponse GetLeadQuoteUploadStatus(long leadId);

        public SaveInfo UploadSmeRequestForQuotes(List<SmeRequestForQuotesEntity> rfqEntities);

        public string GetOfflineBookedLeads(string date, int productId);

        public string GetLeadDetailsPcd(long leadId);

        public LeadDetailsForCustIdResponse GetLeadDetailsForCustId(long customerId);

        public CallDataResponse GetCallData(long leadId, long creationDate, string callType);
        public LeadDeatils GetAllBasicLeadDetails(string LeadId, string Source, string EncKey, string EncIV);

        public int getPreBookingTalkTime(long LeadId);
        public bool AssignLead(long LeadID, string EmpCode, string Source, Int16 LeadType, String CallID, string callDate, string TransferType);

        public bool SendCustCommtoUnansLeads();


        public UrlResponse GetExitPointURL(long LeadId, long CustomerId, short ProductId);
        public AgentProfileData GetAgentProfileData(long LeadID);


        ResponseAPI GetCityIdByPincode(int Pincode, long LeadId);

        EmployeeDetails GetAgentDetails(string userId, StringValues? encKey, StringValues? encIV);

        LeadDeatils GetParentLeadSource(string leadId);

        CreateLeadResponse CreateLead(CreateLeadRequest request);

        List<StoreDetails> GetAvailableStore(string CityID);
        ResponseAPI UploadUTMCampaign(UTMCampaignDetails request);

        StoreAndAdvisorInfoModel GetStoreAndAdvisorInfo(Int32 CityID);
        List<ProductModel> GetFOSProductMaster(string Source);
        List<FOSCityMaster> GetFOSCityMaster(Int32 ProductId);

        List<CustomerLeads> GetCustomerLeads(CustomerLeadsRequest request, string encKey, string encIV, out string message);
        List<CustomerLeads> GetLeadsByCustId(CustomerLeadsRequest request, string encKey, string encIV, out string message);
        List<LeadInfo> GetActiveLeadAppSet(string CustId, out bool status, string EncKey, string EncIV);

        SaveInfo SetCsatAssignLead(CSATModel oCSATModel);

        ResponseData<string> AddUpdateCallAnalysis(CallAnalyserRequest request);
        ResponseData<Dictionary<string, string>> GetCityByPinCodeProduct(Int32 Pincode, Int16 ProductId, string Source);
        SaveInfo SavePlanRecommendation(PlanRecommendationModel planRecommModel);
        PlanRecommendationModel GetPlanRecommendation(long leadId, out bool message);
        //List<KnowYoutAdvisorModel> GetProductListByEmpId(string empId, out bool status);
        ResponseData<string> PushNotification(NotificationRequest request);
        ResponseData<string> PushReadNotification(ReadNotificationRequest request);
        NotificationsResponse GetNotificationsData(string userId, short? numberOfDays);
        ResponseData<string> ValidateCustomerContact(CustomerContactDetails contactDetails, string encKey, string encIV);

        ResponseData<string> SaveAIAudioData(AIAudioModel aiModel);

        List<KnowYoutAdvisorModel> GetProductListByEmpId(KnowYoutAdvisorModel KnowYoutAdvisorModel, out bool status, out Int16 code);

        ResponseData<string> GetPGPaymentLink(string leadId, string Source);
        ResponseData<string> GetEMandateEnableLink(string leadId);
        ResponseData<string> NotifyAgent(NotifyAgentRequest NotifyAgentReq);
        ResponseData<string> InsertAgentAPEData(InsertAgentAPEDataRequest objreq);
        ApplicationMonitorMaster GetApplicationMonitoringMaster();
        SaveInfo PushApplicationMonitoringDetails(AppMonitorReqModel objAppMonitorReqModel);
        List<AppMonitorData> GetApplicationMonitoringData();
        bool DeleteApplicationData(int ID);
        string GetRenewalLeadData(long leadId);
        void SetPetDetails(PetDetails petDetail);
        ResponseData<SmeData> GetSmeLeadData(long leadId);
        ResponseData<List<SupplierDTO>> GetSuppliersByLeadId(long leadId, string productId, string leadSource);
        ResponseData<List<Plans>> GetPlansByLeadId(long leadId, short supplierId, string productId, string leadSource);
        UserDetails GetAgentDetailsByECode(string EmployeeCode);
        ResponseAPI UpdateSelfiesData(SelfieModel request);
        bool SetCustomerCtcClick(long LeadId, bool IsCallBackScheduled);
        bool InsertShortCallData(ShortCallData obj);
        bool PushHWEligibleData(HWEligibleData obj);
        bool SetCouponRedeemValue(long customerId);
        bool GetCouponRedeemValue(long customerId);
        CouponRedeemModel SetCouponRaiseRequest(CouponRedeemModel obj);
        List<CouponRedeemModel> GetCouponRaiseRequest(string UserId);
        bool UpdateCouponRaiseRequest(CouponRedeemModel onj);
        bool PushVCPitchData(VCPitchData obj);

        ResponseData<string> SaveFeatureData(FeatureData featureData);

        ResponseData<List<LeadInfo>> GetActiveLeads(string InputValue, int TypeId, string EncKey, string EncIV, string source);

        List<AgentCallDetails> GetAgentCallDetails(string pbcentrallogin, string MobileNo);
        List<Response> RejectAllLeads(string ECode, string source, List<LeadInfo> rejectLeadInfo);
        Response ProcessLeadByCustomerId(long customerId, string regNo, string type, string source, int ReasonId);
        bool IsCustomerDNC(long mobileNo);
        bool SaveAssistanceData(AssistanceData obj);
        CouponRedeemModel GetCouponDataByLeadId(long leadId);
        bool InsertIVRFeedBack(string agentid, string leadid, string rating, string source, string process, long MobileNo, int ProductId, long CallDataId, long AppointmentId = 0);

        ResponseData<LeadAssignDetails> AssignLeadWithDetails(long LeadID, string EmpCode, string Source, Int16 LeadType, String CallID, string callDate, string TransferType, string leadRank);

        bool IsValidMobileNo(long MobileNo);

        ResponseData<Dictionary<object, object>> GetIncomeFromPayUTerm(IncomeFromPayUData obj);
        public ResponseData<AgentProfileData> GetAgentProfileDataByECode(string ECode);
        Response CTCCallBack(CTCSchdular _CTCSchdular, string source = null);

        ResponseData<string> SaveFOSApptCallAnalysisAI(FOSApptCallDataAI objFosApptCallData);
        ResponseData<string> InsertFOSApptCallAnalysisAI(FOSApptCallDataAI objFosApptCallData);
        ResponseAPI VerifyCallBackDateTime(ShortCallData obj);

        public long CreateCustomer(long mobileNo, string email, string name, string countryId);

        Response SaveFollowUpComment(FollowUpComment obj);
        Response DisableScheduledC2C(long leadId, string source);
        List<LeadDeatils> GetActiveLeadsForCustomer(string mobileNo, short subProductId, string source, string encKey, string encIV);

        ResponseUltraHNICust CheckUltraHNICustomer(long customerId, string mobileNoHash, long leadId, bool skipPayuApiCall);
        ResponseData<object> LeadDetailsForWhatsappBot(long customerId, short productId);

        ResponseData<object> GetLeadDetailsForAI(long leadId);
        ResponseData<LeadAssignResp> AssignLeadToGroup(long CustomerID, int ProductId, string identifier, int? GroupID, string ApiSource =null);

        bool CheckIsLeadInvalidAI(long leadId);
        ResponseData<List<KeyValuePair<string, string>>> GetAdditionalLeadDetailsFromCJ(long LeadId);

        ResponseData<object> GetCustomerLeadCityforAI(long customerId);

        SaveInfo GetCustRecordingConcern(LeadsSubStatusModel CustResponse);
    }
}