﻿using Microsoft.AspNetCore.Http;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface IMTXPlusLoginBLL
    {
        ResponseData<LDAPUserDetails> LDAPAuthCheck(string UserName, string password, HttpContext httpContext, string origin);
        Result LogoutFromMtxplus(MTXPlusLogInDTO objlogindet, HttpContext httpContext);
        MTXPlusLoginResponse GetUserDetails(long UserID , HttpContext httpContext);
        MTXPlusLoginResponse SetLoggedinUserDetails(MTXPlusLogInDTO objlogindet, string origin, HttpContext httpContext);
        List<PreLoginSurvey> GetSurveyByLocation();
        ResponseData<List<PreLoginSurvey>> GetPreLoginSurveyRequirement(long userId);
        void InsertEmployeeSurvey(PreLoginSurveyResult obj);
        void UpdateUserPinnedMenu(long userId, int menuId, bool isPin);
        bool VerifyToken(string UserId, string token);
    }
}
