﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface IBmsBLL
    {
        string GetRMDetails(Int64 CustomerId);
        bookingModel GetAgentTypeByBooking(Int64 AgentId, Int64 BookingId, long bookingDate, string EncKey, string EncIV);
        SaveInfo UpdateLeadFlags(LeadFlags oLeadFlags);

        NewSVURLModel GetNewSVURL(Int64 LeadId, Int64 UserId, string Source);

        SaveInfo UpdateNoCostEMI(Int64 LeadId, Int64 UserId, bool NoCostEMI);

        AgentSupervisor GetAgentSupervisor(int UserID);
        string GetInternalEmailURL(GenerateUrlModel ogenerateUrlModel);

        bool GetNoCostEMI(long ParentId);
        List<RMCommentsDetailsResponse> GetRMComments(string LeadIds, string EmployeeID);
        CallTransferLeadInfo GetleadDetailsForCalltranfer(long LeadID, string TransferType);
        SaveInfo CreateBooking(BookingDetailsModel bookingData, string userId, string source);
        int UpdateLeadDetails(LeadDetailsEntity reqData);
        LeadDetailsEntity GetLeadDetailsByLeadId(long leadId, string EncKey, string EncIV);
        string SalesLogin(dynamic details, string Token, out string BMSAuthToken);
        AdditionalLeadDetails GetAdditionalLeadDetails(long LeadId, string Source);
        BMSRenewDetails GetRenewalDetails(string LeadID,string Source);
        public Envelope<bool> InsertUpdateLeadStatus(StampingRequestModel reqData);
        ResponseData<List<LeadHistoryResponse>> GetLeadHistory(long LeadID);
        LeadStatusResponseModel GetLeadLogs(long leadId);
        List<LeadDetailsByAgent> GetBookingDetailsByAgentId(string userId, long FromDate, long ToDate);
        ResponseData<BookedAndRenewalLeadData> GetBookedAndRenewalLeadData(long LeadID);
        string GetRMDetailsForTermSavings(long customerId, long LeadId);
        public MailboxResponse GetMailURL(StringValues? agentId, HttpContext httpContext);
    }
}