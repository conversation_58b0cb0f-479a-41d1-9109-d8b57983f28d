﻿using Microsoft.AspNetCore.Http;
using PropertyLayers;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface ISalesViewBLL
    {
        List<ReassignedLead> GetReassignedLeads(long UserID);
        List<PriorityModel> GetUserAssignedLeadsV2(string _UserID);

        string ViewPhoneNo(string leadId, string userId, string token, string mobileNo);
        bool ReAssignedPODLead(long LeadId, long AssignBy, int AssignTo);

        string getWhatsAppURL(string leadId, string userId, int type, int ProductId, long customerId);
        bool SetAppointmentData(AppointmentsDataModel objAppointmentData);
         AppointmentsDataModel  GetAppointmentData(long customerId, long parentId);
        string GetPaymentDetails(string key, string param);
        OnCallCustomerModal IsCustomerOnCall(string custId);
        List<CityMasterModal> GetOfflineCities(long LeadId, int Type, int ProductId, long UserId);
        FOSModal GetAppointmentTypeList(long LeadId, int ProductId, long UserId, int ProcessId);
        List<InsurerQuotePrice> GetSMEQuotesReport(long LeadId);
        List<ReasonMasterModel> GetReasonMaster(int ProductId, int SubStatusId,string Source);
        bool SaveAppCancelReason(AppCancelReasonModel oAppCancelReasonModel);

        string GetTicketServiceURL(string UId, string ECode, string Name, string Type, string Source, string LId, string TId);
        bool GetAdvisorInfo(string EmpCode);
        #region SME
        SaveInfo AddUpdateSMEQuotes(SMEQuoteModal oSMEQuoteModal);
        List<SMELeadQuote> GetSMEQuotes(long LeadId);
        SaveInfo DeleteSMEQuoteData(SMEQuoteDetailModel oSMEQuoteDetailModel);
        SaveInfo UpdateSMEQuoteList(SMEQuoteModal oSMEQuoteModal);

        SaveInfo UpdateSMEQuotesStatusV2(SMEQuoteModal oSMEQuoteModal);
        SaveInfo SMEQuotesAdditionalUploadV2(SMEAdditionalFileModal oSMEAdditionalFileModal);
        #endregion

        Result GetAgentStory(long userId);
        SaveInfo UpdateAgentStoryResponse(AgentStory storyResponse);
        string GetCustAddress(string custId);
        SaveInfo saveAddressInfo(CustAddModal oCustAddressModal);
        TTModal GetCallDetails(long leadId);
        string GenerateComBoxUrl(CommBoxModal oCommBoxModal);
        ResponseData<dynamic> GetAssignmentTypeByCityId(long parentId, int productId);
        bool IsAppointmentDone(long ParentId);
        SaveInfo SaveFosPitchedData(FOSPitchedModel oFOSPitchedModel);
        Dictionary<string, object> GetConfigfromMongo(string key);
        SVModal GetUserObj(string UserId, string Token);
        SVModal GetUserObjV2(string UserId, string Token);
        bookingModel GetAgentTypeByAgentId(long agentId);
        string hangupCall(string employeeId);

        string GeneratePbmeetLink(DailerData oDailerData, bool removeCustLink);

        string GenerateVerificationVCLink(DailerData dialerData);

        string GeneratePbmeetLinkV2(DailerData oDailerData);
        string GeneratePbmeetLinkV3(DailerData oDailerData);
        string GetAgentURLByMeetingId(DailerData oDailerData);

        ResponseAPI GetChurnLogicMsg(long leadId);
        ResponseAPI ReAssignChurnLead(long leadId, long UserId);


        List<SVLeadDetails> GetLeadDetailsByCustIDAndProdID(LeadDataModel LeadData);

        List<AppointmentsDataModel> GetAppointmentHistory(long leadId);

        string GetClaimDetailsURL(string CustID);
        ResponseAPI IsAppMarkCancel(long LeadId);
        ResponseAPI SendAdditionalNoOTP(OTPModel otpModel, string Source);

        ResponseAPI SetCustContactInfo(CustContactInfo objCustDetails, string Source);
        string GetBusinessHealthRatingPercentage(string AgentId, string ProductID);

        string GetConsolidatedPolicyInforcementData(string AgentId, string ProductId);

        string GetBHRDeduction(double BHR, double ShareMonthlyMode);

        
        //BusinessHealthRating GetConsolidatedBusinessHealthRating(string AgentId);
        
        string GetUserSuperGroup(string AgentId, string ProductId);

        string GetUserGroup(string ProductID, string SourcePage, string AgentId);


        List<PaymentFailedTicketResponse> GetPaymentFailedFeedbackTickets(long[] ParentIds, long AgentId, int IssueId);
        int GetPaymentFailedCasesCount(long AgentId);
        SelfEnforcementRatingData GetSelfEnforcementRatingData(long UserId);
        string GetInforcementRatingHierarchialData(long UserId, string Role, string ProductID);
        SelfEnforcementRatingDataMonthWise GetSelfEnforcementRatingDataMonthWise(long UserId, int month, int year, string ProductID);
        RMDetails AllocateRMBeforeIssuance(ReqRMDetails oReqRMDetails);
        RMDetails IsRMVirtuallyAllocated(ReqRMDetails oReqRMDetails);
        string GetCustomerPlansFromCJ(long LeadID, long AgentID);
        string GetCustomerPlansFromCJv2(long LeadID, long AgentID, int ProductID);
        string AvailDiscountonCJ(object dataToPost);
        string AvailDiscountonCJv2(object dataToPost);
        List<Paymentstatus> GetPaymentAttemptStatus(string LeadIds, long AgentId);
        string GetUpsellModalURL(long LeadId);
        object GetCustomerIntentsAI(object payload);
        object SaveIntentFeedbackAI(object feedbackPayload);
        object GetCustomerIntentsAIV2(object payload);
        object SaveIntentFeedbackAIV2(object feedbackPayload);
        string ViewEmailId(string leadId, string userId, string email);
        List<ResponseAPI> SendPaymentCommunication(string LeadID, string PendingAmount, string ProductID, string DueDate, string Source, int GroupId, bool IsHealthPersistency, string userId, string employeeId);
        List<PaymentFailedCasesModel> GetPaymentFailedCasesInfo(string AgentId, string ProductID);
        List<LatestPaymentStatusforLead> UpdatePaymentStatusForBookedLeads(RequestPaymentStatusData objRequestPaymentStatusData);
        DuePaymentResponse SendPaymentFailedCommunication(RequestSendCommForPaymentFailedCust objReq, string UserId);
        ResponseData<long> GetLeadAssignedAgent(long leadId);
        UserDetails GetLeadAssignedAgentDetails(long leadId);
        string GetClaimDetailsInbound(ClaimDetails claimDetails);
        ResponseAPI SendEmandateEnableCommunication(string LeadID, string Source);
        object CallSummaryV2(object request);
        object SaveFeedbackV2(object request);
        AdditonalDetailsRequest GetAdditionalDetails(long leadId);
        List<QuestionModel> GetFeedBackMaster();
        ResponseAPI SaveFOSFeedback(FOSAppCompeleModel request, long UserId);
        ResponseData<List<LeadDocument>> AddUpdateUploadedDocs(DocsUpload docs, string agentId);
        List<AgentAPEDetails> GetAgentAPE(long agentId);
        string GetActiveLives(long leadId, long customerId);
        string GetClaims(long leadId, long customerId);
        string SetCustContactEmailInfo(CustContactInfo objCustDetails);
        string GetEmailMobileStatus(CustContactInfo objCustDetails);
        object RemoveModeratorFromRoomPbmeet(object request);
        List<string> GetLeadIds(long leadId);
        object GetLeadStatus(long leadId);
        PEDinfo GetSegmentQuestionAnswers(long LeadId);
        bool SendVCTemplate(string custId, string uRL, string leadId, string productId, string process);
        bool SendVCTemplateV1(DailerData oDailerData);
        short GetSubStatusReason(long leadId, short propertyId, int subStatusId);
        string IsRedisConnected();
        List<PaymentInfo> getAllPaymentDetails(string key, string param);
        string changeCustomerEmail(CustContactInfo objCustDetails);
        bool UpdatePrimaryMob(string CustId, string MobileNo, string CountryID);
        OnCallCustomerModal IsCustomerOnCallByLeadId(long leadId);
        NewSVURLModel GetUploadQcrUrl(LeadRfqData data);
        bool GetIsPotentialBuyer(long customerId);
        object GetSetRfqPolicyTypeData(long leadId, short policyTypeId, short policySubTypeId, bool fetchData, string agentId);
        bool SaveCallIntent(CallIntentData callData);
        claimcomments GetClaimComments(string leadId);
        string GetCumulativeAgentBHRData(string EmployeeID, string Role, string ProductID);
        List<HWData> GetHWEligibleData(long AgentId, string SupervisorIds, long UserID);
        LeadDetailResponse GetLeadCustomerDetails(long LeadId);
        string UpdateCallableNumber(CustContactInfo objCustDetails);
        ResponseAPI SendCouponTrigger(long leadId);
        string GetPBMeetConferenceLink(long LeadId, string AgentId);
        (List<CreditChangeResponse> List, int IsAuthorisedCreator, bool IsBulkUploadAuthorized) GetCreditChangeRequests(long UserID, int ProductId);
        string GetBookingDetailsForCreditChange(long leadId, int ProductId);
        List<UserDetails> GetHierarchialAgentList(int ProductID, long UserID);
        List<CreditChangeReasonMaster> GetCreditChangeReasonMaster();
        ResponseAPI SetUpdateCreditChangeRequest(CreditChangeRequest objCreditChangeRequest,int IsBulkUpload);
        bool IsAuthorisedCreditChangeApprover(long UserID, long BookingID, int AgentTypeID);
        List<CreditChangeLogs> GetCreditChangeLogs(long BookingID, int AgentTypeID, int RequestID);
        List<CreditChangeResponse> GetCreditChangeBookingHistory(long bookingId);
        List<PauseSelfCallingResponse> PauseFosSelfCalling(LogInDTO objAgentData);

        bool ResumeFosSelfCalling(LogInDTO objFOSAgentData);
        bool ValidateSalesCreditReferenceLead(long bookingId, long referenceId);

        LeadDataModel CheckLeadPrevAssignToUser(long UserId, long LeadId);
        List<BulkUploadResponseList> BulkUploadSalesCreditChange(InsertS3UploadTable requestDto, long v);
        LeadVerifyDataResponse LeadVerifyOtp(OTPModel obj, string UserId);

        object LeadSummaryAI(object request);

        object SaveSummaryFeedbackAI(object request);
        List<VirtualNumberModel> GetVirtualNumberList(long LeadId, long UserId);
        ResponseAPI IsUserEligibleForCredit(long bookingId, string referenceId, int productId, long selectedAgent, int agentTypeId);
        BookingDetailsModel GetSmeRenewalLeadBookingDetails(long LeadId);
        ResponseAPI SendEmandateEnableCommunicationTermInv(Inputdata obj);
        bool InvAdvisorVerify(long parentId);
        ResponseData<bool> GetPrefComm(CommPreferenceData data);
        ResponseAPI SendOptinLinktoCustomer(SendWAOptInData data);
        ResponseData<BHRResponse> GetBHRPercentageAndColor(long userId, int productId);
        void SendCreditChangeCommunicationEP(long bookingID, int agentTypeID, int currentStatus, long userID, int action, int requestID);
        ResponseAPI SendEmandateEnableCommunicationTermInvTest(Inputdata obj);
        NewSVURLModel GetUploadRFQUrl(LeadRfqData data, string AgentId);

        bool MotorRenewalSecondaryNumSwitch(SecondaryNumberSwitchRequest request);

        bool MotorRenewalLastCallableNumSwitch(LastCallableNumSwitchRequest request);
        ResponseAPI CreateCreditChangeRequest(CreditChangeRequest objCreditChangeRequest, int IsBulkUpload);
        bool MotorAdvisorVerify(long parentId);
        ResponseData<List<EndorsementData>> GetEndorsementData(long bookingId, long customerId, long productId);
        ClaimCallTransferAgentDetailsResponse GetClaimTransferInfoOrScheduleCB(ClaimCallTransferAgentDetailsRequest input);
        CustomerBookingsResponse GetCustomerBookingsByCustomerId(CustomerBookingsRequest request);
        ServiceCallTransferInfoResponse GetBookingInfoByLeadIdCRT(ServiceCallTransferInfoRequest request);

        ResponseData<bool> CheckWhatsAppOptinSent(long LeadId);
        AssignedAgentDetails GetAssignedAgentDetails(AssignedAgentDetailRequest request);
        string FetchBHRBookingDataTLWise(string employeeId, string employeeRole, int productId);
    }
}