﻿using PropertyLayers;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface IHealthRenewalBLL
    {
        bool UpdateExclusiveBenifit(ExclusiveBenifit exclusiveBenifit);
        ExclusiveBenifit GetExclusiveBenifit(long LeadId);
        Loading FetchLoading(long LeadId);
        bool UpdateLoading(long LeadId);
        bool UpdatePED(long LeadId);
        dynamic AddOrUpdateDocTtlLimit(AddOrUpdateDocTtlLimit addOrUpdateDocTtlLimit);
        dynamic SetCustomerCallbackByRM(CustomerCallbackByRM customerCallbackByRM);
        dynamic GetSetPortDetails(long LeadID, int type);
        string GeneratePGLink(PGLink pgLink);
        AHCDetails GetAHCURL(long LeadId, long UserID);
        string GetPortSelectionURL(PortSelection portSelection);
        void PushPehchanToBMS(long LeadId, string PehchanID);
        List<PortDetails> GetESQuickQuotesForPortV3(long LeadId);
        bool SetInceptionBookingID(long LeadId);
        string FetchProposalForm(long LeadId, long CustomerID);
        bool CreateRenewalManualPaymentLink(RenewalManualPaymentLink renewalManualPaymentLink, long UserId);
        bool UpdateRenewalRiders(RiderDetails riderDetails);

    }
}
