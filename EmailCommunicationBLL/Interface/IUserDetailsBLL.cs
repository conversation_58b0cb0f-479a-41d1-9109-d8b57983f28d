﻿using Microsoft.AspNetCore.Http;
using PropertyLayers;
using System.Collections.Generic;

namespace EmailCommunicationBLL
{
    public interface IUserDetailsBLL
    {
        UserDetailsResponse UpdateUserCertificateDetails(UserCertificationDetails objuserCertDetails);
        OtpResult GenerateLoginOtp(GetOtpRequest otpModel, HttpContext httpContext);
        OtpResult VerifyLoginOtp(ValidateOtpRequest otpModel, HttpContext httpContext);
        List<ResponseData<string>> SetAgentStatus(List<string> employeeIds, bool isActive);
    }
}
