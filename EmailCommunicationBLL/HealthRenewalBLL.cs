using DataAccessLayer;
using DataAccessLibrary;
using Helper;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using MongoConfigProject;
using System.Text;
using System.Linq;

namespace EmailCommunicationBLL
{
    public class HealthRenewalBLL : IHealthRenewalBLL
    {
        public bool UpdateExclusiveBenifit(ExclusiveBenifit exclusiveBenefit)
        {
            try

            {
                return HealthRenewalDLL.SetExclusiveBenifit(exclusiveBenefit.LeadId, exclusiveBenefit.isExclusiveBenifit) > 0 ? true : false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, exclusiveBenefit.LeadId, ex.ToString(), "UpdateExclusiveBenifit", "HealthRenewalBLL", "MatrixCore", exclusiveBenefit.ToString(), null, DateTime.Now, DateTime.Now);
                return false;
            }

        }

        public ExclusiveBenifit GetExclusiveBenifit(long ParentId)
        {
            try
            {
                DataTable dt = HealthRenewalDLL.GetExclusiveBenifit(ParentId);
                if (dt != null && dt.Rows.Count > 0)
                {
                    return new ExclusiveBenifit
                    {
                        isExclusiveBenifit = Convert.ToInt16(dt.Rows[0]["isExclusiveBenifit"]),
                        LeadId = Convert.ToInt64(dt.Rows[0]["LeadId"])
                    };
                }
                else return null;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, ParentId, ex.ToString(), "GetExclusiveBenifit", "HealthRenewalBLL", "MatrixCore", ParentId.ToString(), null, DateTime.Now, DateTime.Now);
                return new ExclusiveBenifit();
            }
        }

        public bool UpdateLoading(long RenewalLeadId)
        {
            long OldBookingId, LeadId = RenewalLeadId;
            bool flag = true;
            try
            {
                UpdatePED(RenewalLeadId);
                var counter = 0;
                while (counter < 6)
                {
                    DataTable dt = HealthRenewalDLL.UpdateLoading(RenewalLeadId, LeadId, flag);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        OldBookingId = CoreCommonMethods.IsValidString(dt.Rows[0]["OldBookingId"].ToString()) ? Convert.ToInt32(dt.Rows[0]["OldBookingId"].ToString()) : RenewalLeadId;
                        if (OldBookingId > 1)
                        {
                            Loading loading = FetchLoading(OldBookingId);
                            if (loading != null && (loading.IsLoading == 1 || loading.LoadingPremium > 0) && loading.PremiumWaivedOff == 3)
                            {
                                int result = HealthRenewalDLL.UpdateLoading(LeadId, loading);
                                if (result > 0) return true;
                                else return false;
                            }
                            else
                            {
                                flag = false;
                                RenewalLeadId = OldBookingId;
                                counter++;
                                continue;
                            }
                        }
                        else return true;
                    }
                    else return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, ex.ToString(), "AlterLoading", "HealthRenewalBLL", "MatrixCore", LeadId.ToString(), null, DateTime.Now, DateTime.Now);
                return false;
            }
        }

        public bool UpdatePED(long RenewalLeadId)
        {
            long OldBookingId, LeadId = RenewalLeadId;
            int SupplierID = 0;
            try
            {
                var counter = 0;
                while (counter < 5)
                {
                    DataTable dt = HealthRenewalDLL.UpdatePED(RenewalLeadId, LeadId);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        OldBookingId = CoreCommonMethods.IsValidString(dt.Rows[0]["OldBookingId"].ToString()) ? Convert.ToInt32(dt.Rows[0]["OldBookingId"].ToString()) : RenewalLeadId;
                        SupplierID = CoreCommonMethods.IsValidString(dt.Rows[0]["SupplierID"].ToString()) ? Convert.ToInt32(dt.Rows[0]["SupplierID"].ToString()) : 0;
                        if (OldBookingId > 1)
                        {
                            Loading loading = FetchLoading(OldBookingId);
                            if (loading != null && ((SupplierID != 17 && (loading.NSTPReason != null || loading.STPStatus != null || loading.PEDInfo != null)) || (SupplierID == 17 && !string.IsNullOrEmpty(loading.PEDInfo))))
                            {
                                int result = HealthRenewalDLL.UpdatePED(LeadId, loading);
                                if (result > 0) return true;
                                else return false;
                            }
                            else
                            {
                                RenewalLeadId = OldBookingId;
                                counter++;
                                continue;
                            }
                        }
                        else return true;
                    }
                    else return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, ex.ToString(), "AlterLoading", "HealthRenewalBLL", "MatrixCore", LeadId.ToString(), null, DateTime.Now, DateTime.Now);
                return false;
            }
        }

        public Loading FetchLoading(long LeadId)
        {
            dynamic requestTime = DateTime.Now;
            string json = string.Empty;
            dynamic LoadingDetails;
            try
            {
                if (LeadId > 0)
                {
                    json = "{\"LeadId\":\"" + LeadId + "\"}";
                    Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"AppId", "Matrix1.0"},{ "AppKey","BMSToken".AppSettings()}};
                    var url = "bmsApiServiceurl".AppSettings() + "booking/GetRenewalForBookingDetails";
                    string response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json-patch+json", header);
                    if (!string.IsNullOrEmpty(response))
                    {
                        LoadingDetails = JObject.Parse(response);
                        if (LoadingDetails != null)
                        {
                            return new Loading()
                            {
                                LoadingPercentage = Convert.ToInt16(LoadingDetails.LoadingPercentage),
                                LoadingPremium = Convert.ToDecimal(LoadingDetails.LoadingPremium),
                                LoadingReason = LoadingDetails.LoadingReason,
                                IsLoading = (short)(Convert.ToDecimal(LoadingDetails.LoadingPremium) > 100 ? 1 : 0),
                                PEDInfo = LoadingDetails.PEDInfo,
                                STPStatus = LoadingDetails.STPStatus,
                                NSTPReason = LoadingDetails.NSTPReason,
                                PremiumWaivedOff = Convert.ToInt16(LoadingDetails.PremiumWaivedOff)
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoadingDetails = ex.ToString();
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, ex.ToString(), "FetchLoading", "HealthRenewalBLL", "MatrixCore", json, LoadingDetails, requestTime, DateTime.Now);
            }

            return null;
        }

        public dynamic AddOrUpdateDocTtlLimit(AddOrUpdateDocTtlLimit addOrUpdateDocTtlLimit)
        {
            dynamic requestTime = DateTime.Now;
            string json = string.Empty;
            dynamic Limit;
            try
            {
                json = " {\"agentId\" :\"" + addOrUpdateDocTtlLimit.agentId + "\",\"others_m_limit\" :\" " + addOrUpdateDocTtlLimit.others_m_limit + "\",\"aadhar_m_limit\" :\" " + addOrUpdateDocTtlLimit.aadhar_m_limit + "\",\"polCopy_m_limit\" :\" " + addOrUpdateDocTtlLimit.polCopy_m_limit + "\",\"endorsement_m_limit\" :\" " + addOrUpdateDocTtlLimit.endorsement_m_limit + "\"}";
                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"clientKey", "coreAPIclientKey".AppSettings()},{ "authKey","coreAPIauthKey".AppSettings()}};
                var url = "coreAPI".AppSettings() + "cs/doc/addOrUpdateAgentDocTtlLimit";
                string response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json-patch+json", header);
                if (!string.IsNullOrEmpty(response))
                {
                    Limit = JObject.Parse(response);
                    return Limit;
                }

            }
            catch (Exception ex)
            {
                Limit = ex.ToString();
                LoggingHelper.LoggingHelper.Log(addOrUpdateDocTtlLimit.agentId, 0, ex.ToString(), "AddOrUpdateDocTtlLimit", "HealthRenewalBLL", "MatrixCore", json, Limit, requestTime, DateTime.Now);
            }

            return null;
        }

        public dynamic SetCustomerCallbackByRM(CustomerCallbackByRM customerCallbackByRM)
        {
            DateTime requestTime = DateTime.Now;
            string json = string.Empty;
            string url = string.Empty;
            string Result = null;
            try
            {
                json = " {\"UserId\" :" + customerCallbackByRM.UserId + ",\"UserName\" :\"" + customerCallbackByRM.UserName + "\",\"LeadId\" :\"" + customerCallbackByRM.LeadId + "\",\"ProductId\" :" + customerCallbackByRM.ProductId + ",\"Remarks\" :\"" + customerCallbackByRM.Remarks + "\"}";

                string ClaimKey = "ClaimKey".AppSettings();
                string ClaimIV = "ClaimIV".AppSettings();
                string enc = Crypto.encrypt_AES(customerCallbackByRM.LeadId.ToString(), ClaimKey, ClaimIV, 256, 128);

                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"SecretKey", enc},{ "SourceKey","SourceKey".AppSettings()},{ "ValidationKey","ValidationKey".AppSettings()}};

                url = "ClaimURL".AppSettings() + "/ExternalClaim/SetCustomerCallbackByRM";
                string response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);
                if (!string.IsNullOrEmpty(response))
                {
                    Result = JsonConvert.DeserializeObject(response).ToString();
                }

            }
            catch (Exception ex)
            {
                Result = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(customerCallbackByRM.LeadId.ToString(), customerCallbackByRM.LeadId, null, "SetCustomerCallbackByRM", "HealthRenewalBLL", "MatrixCore", "url - " + url + " , json - " + json, Result, requestTime, DateTime.Now);
            }

            return Result;
        }

        public dynamic GetSetPortDetails(long LeadId, int type)
        {
            DateTime requestTime = DateTime.Now;
            JObject json = null;
            string url = string.Empty;
            dynamic Result = null;
            try
            {
                if (type == 1)
                {
                    string enc = Crypto.Encrytion_Payment_AES(LeadId.ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings());
                    Dictionary<object, object> header = new()
                    {
                        { "clientKey", "coreAPIclientKey".AppSettings() },
                        { "authKey", "coreAPIauthKey".AppSettings() }
                    };

                    url = "corePCDAPI".AppSettings() + "cs/pcd/detail?bookingId=" + enc + "&health=1";
                    string response = CommonAPICall.CallAPI(url, null, "GET", Convert.ToInt32("MRSTimeout".AppSettings()), header);
                    if (!string.IsNullOrEmpty(response))
                    {
                        json = JObject.Parse(response);

                        if (json.ContainsKey("policyDetails"))
                        {
                            JObject policyDetails = json.Value<JObject>("policyDetails");
                            JObject policyHealthAdditionDetails = json.Value<JObject>("policyHealthAdditionDetails");
                            if (policyDetails != null)
                            {
                                RenewalPortDetails details = new()
                                {
                                    LeadId = LeadId,
                                    PED = policyDetails.GetValue("isPED").ToString() != string.Empty ? policyDetails.Value<bool>("isPED") : default,
                                    Claim = policyDetails.GetValue("isClaim").ToString() != string.Empty ? policyDetails.Value<bool>("isClaim") : default,
                                    PreviousPolicyNo = policyDetails.GetValue("previousPolicyNumber").ToString() != string.Empty ? policyDetails.Value<string>("previousPolicyNumber") : default,
                                    PreviousInsurer = policyDetails.GetValue("previousSupplierName").ToString() != string.Empty ? policyDetails.Value<string>("previousSupplierName") : default,
                                    PreviousSI = policyDetails.GetValue("previousSumInsured").ToString() != string.Empty ? policyDetails.Value<decimal>("previousSumInsured") : default,
                                    PreviousTerm = policyDetails.GetValue("previousTerm").ToString() != string.Empty ? policyDetails.Value<short>("previousTerm") : default,
                                    PreviousNCB = policyDetails.GetValue("previousNCB").ToString() != string.Empty ? policyDetails.Value<decimal>("previousNCB") : default,
                                    EnrollmentDate = policyDetails.GetValue("previousPolicyStartDate").ToString() != string.Empty ? policyDetails.Value<string>("previousPolicyStartDate") : default,
                                };
                                if (policyHealthAdditionDetails != null)
                                {
                                    details.PortabilityReason = policyHealthAdditionDetails.GetValue("portabilityReason").ToString() != string.Empty ? policyHealthAdditionDetails.Value<string>("portabilityReason") : default;
                                }
                                HealthRenewalDLL.SetPortDetails(details);
                                Result = details;
                            }
                            else
                            {
                                Result = "No Data Found";
                            }
                        }
                    }
                }
                else
                {
                    DataTable dt = HealthRenewalDLL.GetPortDetails(LeadId);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        return new RenewalPortDetails
                        {
                            PED = dt.Rows[0]["PED"] != DBNull.Value && Convert.ToBoolean(dt.Rows[0]["PED"]),
                            Claim = dt.Rows[0]["Claim"] != DBNull.Value && Convert.ToBoolean(dt.Rows[0]["Claim"]),
                            PreviousPolicyNo = dt.Rows[0]["PreviousPolicyNo"] == DBNull.Value ? default : Convert.ToString(dt.Rows[0]["PreviousPolicyNo"]),
                            PreviousInsurer = dt.Rows[0]["PreviousInsurer"] == DBNull.Value ? default : Convert.ToString(dt.Rows[0]["PreviousInsurer"]),
                            PreviousSI = dt.Rows[0]["PreviousSI"] == DBNull.Value ? default : Convert.ToDecimal(dt.Rows[0]["PreviousSI"]),
                            PreviousTerm = dt.Rows[0]["PreviousTerm"] == DBNull.Value ? default : Convert.ToInt16(dt.Rows[0]["PreviousTerm"]),
                            PreviousNCB = dt.Rows[0]["PreviousNCB"] == DBNull.Value ? default : Convert.ToDecimal(dt.Rows[0]["PreviousNCB"]),
                            EnrollmentDate = dt.Rows[0]["EnrollmentDate"] == DBNull.Value ? default : Convert.ToString(dt.Rows[0]["EnrollmentDate"]),
                            PortabilityReason = dt.Rows[0]["PortabilityReason"] == DBNull.Value ? default : Convert.ToString(dt.Rows[0]["PortabilityReason"]),
                            NewPolicyNo = dt.Rows[0]["OldPolicyNo"] == DBNull.Value ? default : Convert.ToString(dt.Rows[0]["OldPolicyNo"])
                        };
                    }
                    else return null;
                }

            }
            catch (Exception ex)
            {
                Result = ex.ToString();
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, null, "GetSetPortDetails", "HealthRenewalBLL", "MatrixCore", "url - " + url, Result, requestTime, DateTime.Now);
            }

            return Result;
        }

        public string GeneratePGLink(PGLink pgLink)
        {
            DateTime requestTime = DateTime.Now;
            dynamic json = string.Empty;
            string url = string.Empty;
            string Result = null;
            JObject res = null;
            dynamic exception = null;
            try
            {
                Dictionary<object, object> header = new()
                {
                    { "AppKey", "bmsServiceToken".AppSettings() },
                    { "AppId", "Matrix1.0" }
                };

                json = " {\"LeadId\" :\"" + pgLink.LeadId + "\",\"Amount\" :\"" + pgLink.Amount + "\",\"UserId\" :\"" + pgLink.UserId + "\",\"PaymentReason\" :\"" + pgLink.PaymentReason + "\",\"EmployeeID\" :\"" + pgLink.EmployeeID + "\",\"PolicyNumber\" :\"" + pgLink.PolicyNumber + "\"}";

                url = "bmsApiServiceurl2".AppSettings() + "api/Partner/GeneratePGLink";

                string response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                if (!string.IsNullOrEmpty(response))
                {
                    res = JObject.Parse(response);
                }
                if (res.ContainsKey("PGLink"))
                {
                    Result = res.GetValue("PGLink").ToString();
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(pgLink.LeadId.ToString(), pgLink.LeadId, exception, "GeneratePGLink", "HealthRenewalBLL", "MatrixCore", "url - " + url + " , json - " + json, JsonConvert.SerializeObject(res), requestTime, DateTime.Now);
            }

            return Result;
        }

        public AHCDetails GetAHCURL(long LeadId, long UserID)
        {
            DateTime requestTime = DateTime.Now;
            dynamic json = string.Empty, url = string.Empty, response = string.Empty;
            JObject res = null;
            dynamic exception = null;
            AHCDetails aHCDetails = new();
            try
            {
                Dictionary<object, object> header = new()
                {
                    { "x-api-key", "AHCKey".AppSettings() }
                };
                DataRow dr = CreateTicketForCare(LeadId, UserID);
                string encBookingId = Crypto.Encrytion_Payment_AES(dr["OldBookingId"].ToString(), "Core", 256, 128, "AHCencKey".AppSettings(), "AHCencIV".AppSettings(), true);
                encBookingId = Crypto.EncryptString(encBookingId);
                encBookingId = Crypto.UrlEncode(encBookingId);

                string encCustomerId = Crypto.Encrytion_Payment_AES(dr["CustomerID"].ToString(), "Core", 256, 128, "AHCencKey".AppSettings(), "AHCencIV".AppSettings(), true);
                encCustomerId = Crypto.EncryptString(encCustomerId);
                encCustomerId = Crypto.UrlEncode(encCustomerId);

                json = " {\"BookingID\":0,\"CustomerID\": 0 ,\"EncryptedBookingID\": \" " + encBookingId + "\",\"EncryptedCustomerID\":\" " + encCustomerId + "\",\"SourceID\": 8,\"ProductID\": " + dr["ProductID"] + ",\"UserID\": " + UserID + ",\"UserName\": \"" + dr["UserName"] + "\",\"UserCode\": \"" + dr["EmployeeId"] + "\",\"TicketID\": " + dr["TicketId"] + ",\"TicketNo\": \"" + dr["TicketDetailsId"] + "\"}";
                url = "AHCUrl".AppSettings() + "AHC/GetAHCEligibilityNPortalURLV1";

                response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                if (!string.IsNullOrEmpty(response))
                {
                    res = JObject.Parse(response);
                }
                if (res.ContainsKey("ahcPortalURL"))
                {
                    aHCDetails.URL = res.GetValue("ahcPortalURL").ToString();
                }
                if (res.ContainsKey("message"))
                {
                    aHCDetails.Message = res.GetValue("message").ToString();
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, exception, "GetAHCURL", "HealthRenewalBLL", "MatrixCore", "url - " + url + " , json - " + json, response, requestTime, DateTime.Now);
            }

            return aHCDetails;
        }

        public DataRow CreateTicketForCare(long LeadId, long UserID)
        {
            DateTime requestTime = DateTime.Now;
            dynamic json = string.Empty, url = string.Empty, response = string.Empty;
            dynamic res = null;
            dynamic exception = null;
            DataRow dr = null;
            try
            {
                Dictionary<object, object> header = new()
                {
                    { "token", "TicketToken".AppSettings() },
                    { "App", "Matrix" }
                };

                DataTable dt = HealthRenewalDLL.GetAHCURL(LeadId, UserID);
                dt.Columns.Add("TicketId", typeof(System.Int32));
                dt.Columns.Add("TicketDetailsId", typeof(string));
                dr = dt.Rows[0];
                json = " {\"Source\": \"Matrix\",\"ProductID\": 2,\"Name\": \"{customerName}\",\"Comments\": \"{remarks regarding the ticket}\",\"EmailID\": \"<EMAIL>\",\"LeadID\": " + dr["OldBookingId"] + ",\"CustomerID\": 0,\"SubIssueCode\": \"MANAGEANNUALHEALTHCHECKUPREQUEST\",\"AutoClosure\": 1,\"CreatedBy\": 124,\"SubSource\": \"AHC\",\"InitiatedBy\": \"Customer\"}";

                url = "TicketService".AppSettings() + "CreateTicketForCare";

                response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                if (!string.IsNullOrEmpty(response))
                {
                    res = JObject.Parse(response);
                }
                if (res.Data != null)
                {
                    dr["TicketId"] = Convert.ToInt32(res.Data.TicketId);
                    dr["TicketDetailsId"] = Convert.ToString(res.Data.TicketDetailsId);
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, exception, "CreateTicketForCare", "HealthRenewalBLL", "MatrixCore", "url - " + url + " , json - " + json, response, requestTime, DateTime.Now);
            }

            return dr;
        }

        public void PushPehchanToBMS(long LeadId, string PehchanID)
        {

            DateTime requestTime = DateTime.Now;
            dynamic json = string.Empty, url = string.Empty, response = string.Empty;
            dynamic exception = null;
            try
            {
                Dictionary<object, object> header = new()
                {
                    { "TOKEN", "PIVCToken".AppSettings() }
                };

                json = "{\"LeadId\": " + LeadId.ToString() + ",\"PehechaanID\": \"" + PehchanID.ToString() + "\"}";
                url = "PIVC".AppSettings() + "UpdateBasicPolicyDetails";

                response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, exception, "PushPehchanToBMS", "HealthRenewalBLL", "MatrixCore", "url - " + url + " , json - " + json, response, requestTime, DateTime.Now);
            }

        }

        public List<PortDetails> GetESQuickQuotesForPortV3(long LeadId)
        {

            string response = string.Empty;
            DateTime requestTime = DateTime.Now;
            dynamic json = string.Empty, url = string.Empty, res = null;
            dynamic exception = null;
            List<PortDetails> portDetailsList = new List<PortDetails>();
            PortDetails portDetails = new PortDetails();
            try
            {
                Dictionary<object, object> header = new()
                {
                    { "authkey", "CJAuthToken".AppSettings() }
                };

                json = "{\"matrixLeadId\": " + LeadId.ToString() + ",\"source\": \"matrix\",\"healthQuotesForPortRequest\": null,\"isCorporatePolicy\": false,\"isInternal\": false,\"proposerId\": 0,\"planId\": 0,\"sumInsured\": 0}";

                url = "HealthRenewalCJUrl".AppSettings() + "/api/Renewal/GetESQuickQuotesForPortV3";

                response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json-patch+json", header);
                if (!string.IsNullOrEmpty(response))
                {
                    res = JsonConvert.DeserializeObject<dynamic>(response);
                }
                if (res != null && res.data != null && res.errorCode == "200" && res.data.quotes != null)
                {
                    foreach (var data in res.data.quotes)
                    {
                        portDetails = new PortDetails()
                        {
                            PlanName = Convert.ToString(data.planName),
                            PlanId = Convert.ToInt32(data.planId),
                            Premium = Convert.ToString(data.finalPremium),
                            SumInsured = Convert.ToString(data.sumInsured),
                            SupplierId = Convert.ToString(data.supplierId),
                            proposerId = Convert.ToInt32(res.data.proposerId),
                            enquiryId = Convert.ToInt32(res.data.enquiryId)
                        };
                        portDetailsList.Add(portDetails);
                    }
                }

            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, exception, "GetESQuickQuotesForPortV3", "HealthRenewalBLL", "MatrixCore", "url - " + url + " , json - " + json, response, requestTime, DateTime.Now);
            }
            return portDetailsList;

        }

        public string GetPortSelectionURL(PortSelection portSelection)
        {
            DateTime requestTime = DateTime.Now;
            dynamic json = string.Empty, url = string.Empty, response = string.Empty;
            JObject res = null;
            dynamic exception = null;
            string RedirectionURL = string.Empty;
            try
            {
                Dictionary<object, object> header = new()
                {
                    { "authkey", "CJAuthToken".AppSettings() }
                };

                json = "{ \"proposerID\": " + portSelection.proposerID + ", \"planId\":" + portSelection.planId + ", \"enquiryID\": " + portSelection.enquiryID + ", \"sumInsured\": " + portSelection.sumInsured + " }";


                url = "HealthRenewalCJUrl".AppSettings() + "/api/Renewal/CreateSelection";

                response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                if (!string.IsNullOrEmpty(response))
                {
                    res = JObject.Parse(response);
                }
                if (res.ContainsKey("RedirectionURL"))
                {
                    RedirectionURL = res.GetValue("RedirectionURL").ToString();
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(portSelection.enquiryID.ToString(), portSelection.enquiryID, exception, "GetPortSelectionURL", "HealthRenewalBLL", "MatrixCore", "url - " + url + " , json - " + json, response, requestTime, DateTime.Now);
            }

            return RedirectionURL;
        }

        public bool SetInceptionBookingID(long LeadId)
        {
            DateTime requestTime = DateTime.Now;
            dynamic exception = null;
            long RenewalLead = LeadId, OldbookingID, InceptionID;
            try
            {
                var counter = 0;
                while (counter < 6)
                {
                    DataTable dt = HealthRenewalDLL.GetInceptionBooking(LeadId);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        OldbookingID = CoreCommonMethods.IsValidString(dt.Rows[0]["OldBookingId"].ToString()) ? Convert.ToInt32(dt.Rows[0]["OldBookingId"].ToString()) : 0;
                        InceptionID = CoreCommonMethods.IsValidString(dt.Rows[0]["InceptionID"].ToString()) ? Convert.ToInt32(dt.Rows[0]["InceptionID"].ToString()) : 0;
                        if (OldbookingID > 1 && InceptionID == 0)
                        {
                            LeadId = OldbookingID;
                            counter++;
                            continue;
                        }
                        else
                        {
                            HealthRenewalDLL.SetInceptionBooking(RenewalLead, InceptionID != 0 ? InceptionID : LeadId);
                            return true;
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(RenewalLead.ToString(), RenewalLead, exception, "SetInceptionBookingID", "HealthRenewalBLL", "MatrixCore", RenewalLead.ToString(), null, requestTime, DateTime.Now);
            }

            return false;
        }

        public string FetchProposalForm(long LeadID, long CustomerID)
        {
            DateTime requestTime = DateTime.Now;
            string RedirectionURL = string.Empty;
            string PFLinkDocId = string.Empty;
            try
            {
                // Fetch Document ID

                string BmsCromaURL = "BmsCromaURL".AppSettings() + "api/Matrix/GetPFDoc";

                Dictionary<object, object> header = new()
                {
                    { "accept", "text/plain" },
                    { "source", "matrix" },
                    { "clientKey", "PBCromaclientkey".AppSettings() },
                    { "authKey", "PBCromaAuthkey".AppSettings() }
                };

                var payload = new { LeadId = LeadID };
                string dataToPost = JsonConvert.SerializeObject(payload);

                var data = CommonAPICall.CallAPI(BmsCromaURL, dataToPost, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                if (!string.IsNullOrEmpty(data))
                {
                    JObject details = JObject.Parse(data);
                    if (details != null)
                    {
                        PFLinkDocId = details["PFLinkDocId"]?.ToString();
                    }
                }

                if (!string.IsNullOrEmpty(PFLinkDocId))
                {

                    // Fetch the document 

                    string coreAPIencKey = "coreAPIencKey".AppSettings();
                    string coreAPIivKey = "coreAPIivKey".AppSettings();

                    string encDocID = Crypto.Encrytion_Payment_AES(PFLinkDocId.ToString(), "Core", 256, 128, coreAPIencKey, coreAPIivKey, false);
                    string encCustID = Crypto.Encrytion_Payment_AES(CustomerID.ToString(), "Core", 256, 128, coreAPIencKey, coreAPIivKey, false);

                    header = new()
                    {
                        { "clientKey", "coreAPIclientKey".AppSettings() },
                        { "authKey", "coreAPIauthKey".AppSettings() }
                    };

                    string json = "{ \"docId\": \"" + encDocID + "\", \"custId\":\"" + encCustID + "\" }";

                    var response = CommonAPICall.CallAPI("DocumentUrlEndpointFetch".AppSettings(), json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                    if (!string.IsNullOrEmpty(response))
                    {
                        JObject res = JObject.Parse(response);
                        if (res.ContainsKey("ttlDocUrl"))
                        {
                            RedirectionURL = res.GetValue("ttlDocUrl").ToString();
                        }
                        else
                        {
                            return "No Document Found!";
                        }
                    }
                }
                else
                {
                    return "No Document Found!";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), LeadID, ex.ToString(), "FetchProposalForm", "MatrixCore", "HealthRenewalBLL", LeadID.ToString(), string.Empty, requestTime, DateTime.Now);
            }

            return RedirectionURL;
        }
    
        public bool CreateRenewalManualPaymentLink(RenewalManualPaymentLink renewalManualPaymentLink, long UserId)
        {
            DateTime requestTime = DateTime.Now;
            string response = string.Empty;
            string url = string.Empty, requestJsonData = string.Empty;
            try
            {
                DataSet ds = HealthRenewalDLL.FetchHealthRenewalPaymentLink(renewalManualPaymentLink.ID, UserId);
                string pgManualEncKey = "pgManualEncKey".AppSettings();
                string pgManualIVKey = "pgManualIVKey".AppSettings();
                string jsonData = string.Empty, paymentlinkurl = string.Empty;
                if (ds != null)
                {
                    if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            string encMobile = Crypto.encrypt_AES(dr["MobileNo"].ToString(), pgManualEncKey, pgManualIVKey, 256, 128);
                            string encEmail = renewalManualPaymentLink.IsEmailchanged ?
                                    Crypto.encrypt_AES(renewalManualPaymentLink.emailId, pgManualEncKey, pgManualIVKey, 256, 128)
                                    : Crypto.encrypt_AES(dr["EmailID"].ToString(), pgManualEncKey, pgManualIVKey, 256, 128);

                            jsonData = "{" +
                                "\"productId\":\"" + 147 + "\"" +
                                ",\"srcSupplierId\":\"" + dr["SupplierID"].ToString() + "\"" +
                                ",\"planId\":\"" + renewalManualPaymentLink.PlanID + "\"" +
                                ",\"amount\":\"" + dr["PremiumAmount"].ToString() + "\"" +
                                ",\"firstName\":\"" + dr["ProposerName"].ToString() + "\"" +
                                ",\"mobileNo\":\"" + encMobile + "\"" +
                                ",\"emailId\":\"" + encEmail + "\"" +
                                ",\"txTypeId\":\"" + renewalManualPaymentLink.txTypeId + "\"" +
                                ",\"parentLeadId\":\"" + dr["ParentID"].ToString() + "\"" +
                                ",\"proposalNo\":\"" + renewalManualPaymentLink.proposalNo + "\"" +
                                ",\"holdPayment\":\"" + renewalManualPaymentLink.HoldPayment + "\"" +
                                ",\"nstp\":\"" + renewalManualPaymentLink.NSTP + "\"" +
                                ",\"paymentReason\":\"" + dr["PaymentReason"].ToString() + "\"" +
                                ",\"leadId\":\"" + dr["LeadID"].ToString() + "\"" +
                                ",\"customerId\":\"" + dr["CustomerID"].ToString() + "\"" +
                                ",\"policyNo\":\"" + dr["PolicyNo"].ToString() + "\"" +
                                ",\"premiumPayingTerm\":\"\"" +
                                ",\"maxRecurringAmount\":\"" + 0 + "\"";
                            if (renewalManualPaymentLink.Emandate)
                            {
                                jsonData = jsonData + ",\"displayMode\":\"" + (8) + "\"";
                            }

                        }
                    }

                    if (ds.Tables.Count > 1 && ds.Tables[1].Rows.Count > 0)
                    {
                        foreach (DataRow dr in ds.Tables[1].Rows)
                        {
                            jsonData = jsonData +
                                ",\"loginUser\":\"" + dr["EmployeeId"].ToString() + "\"";
                            requestJsonData = jsonData;
                            jsonData += "}";
                        }
                    }

                    if (!string.IsNullOrEmpty(jsonData))
                    {
                        JObject jsonObject = JObject.Parse(jsonData);
                        var sortedProperties = jsonObject.Properties().OrderBy(p => p.Name).ToList();
                        JObject sortedObject = new(sortedProperties);
                        string sortedJson = sortedObject.ToString(Formatting.None);
                        JObject sortedjsonObject = JObject.Parse(sortedJson);

                        StringBuilder stringBuilder = new();
                        foreach (var property in sortedjsonObject.Properties())
                        {
                            stringBuilder.Append($"{property.Name}={property.Value};");
                        }
                        string commaSeparatedString = stringBuilder.ToString();
                        string checksum = "pghash_manual_client_key".AppSettings() + "|" + commaSeparatedString + "|" + "pghash_manual_secret_key".AppSettings();
                        string shachecksum = CoreCommonMethods.ComputeSha256Hash(checksum);

                        requestJsonData = requestJsonData +
                            ",\"checksum\":\"" + shachecksum + "\"" +
                                "}";
                        url = "pgAPI".AppSettings() + "pgi/create/instantPayment/link";
                        Dictionary<object, object> header = new()
                        {
                            { "mid", "pgMID".AppSettings() },
                            { "auth", "pgAuthKey".AppSettings() }
                        };
                        response = CommonAPICall.CallAPI(url, requestJsonData, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);
                        if (!string.IsNullOrEmpty(response))
                        {
                            JObject details = JObject.Parse(response);

                            if (details.ContainsKey("url"))
                            {
                                paymentlinkurl = details["url"]?[0]?.ToString();
                            }
                        }
                        if (!string.IsNullOrEmpty(paymentlinkurl))
                        {
                            return HealthRenewalDLL.SetHealthRenewalPaymentLink(renewalManualPaymentLink.ID, UserId, paymentlinkurl);
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(renewalManualPaymentLink.LeadId.ToString(), renewalManualPaymentLink.LeadId, ex.Message, "CreateRenewalManualPaymentLink", "MatrixCore", "HealthRenewalBLL", JsonConvert.SerializeObject(renewalManualPaymentLink), "url - " + url + ", json - " + requestJsonData , requestTime, DateTime.Now);
            }

            return false;
        }

        public bool UpdateRenewalRiders(RiderDetails riderDetails)
        {
            DateTime requestTime = DateTime.Now;
            string response = string.Empty;
            bool result = false;
            try
            {
                result = Convert.ToBoolean(HealthRenewalDLL.UpdateRenewalRiders(riderDetails.LeadID, riderDetails.Riders, riderDetails.PremiumCJ));

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(riderDetails.LeadID.ToString(), riderDetails.LeadID, ex.ToString(), "UpdateRenewalRiders", "HealthRenewalBLL", "MatrixCore", riderDetails.LeadID.ToString(), response, requestTime, DateTime.Now);
            }

            return result;
        }
    }
}
