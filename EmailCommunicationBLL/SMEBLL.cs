﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.Caching;
using DataAccessLibrary;
using Helper;
using MongoConfigProject;
using Newtonsoft.Json;
using PropertyLayers;

namespace EmailCommunicationBLL
{
    public class SMEBLL : ISME
    {
        public List<SubProductListModal> GetSMETransferSubProducts()
        {
            List<SubProductListModal> lstSubProductListModal = null;
            try
            {
                DataSet oDataSet = SMEDLL.GetSMETransferSubProducts();
                if (oDataSet != null && oDataSet.Tables.Count > 0)
                {
                    lstSubProductListModal = new List<SubProductListModal>();
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        SubProductListModal oGroupListModal = new SubProductListModal();
                        oGroupListModal.SubProductId = row["SubProductId"] == DBNull.Value ? 0 : Convert.ToInt64(row["SubProductId"]);
                        oGroupListModal.SubProductName = row["SubProductName"] == DBNull.Value ? "" : Convert.ToString(row["SubProductName"]);
                        oGroupListModal.QueueName = row["QueueName"] == DBNull.Value ? "" : Convert.ToString(row["QueueName"]);

                        lstSubProductListModal.Add(oGroupListModal);
                    }
                }
            }
            catch (Exception ex)
            {
                //LoggingHelper.LoggingHelper.AddloginQueue("", "", ex.Message.ToString(), "GetGroupList", "SalesViewBLL", "MatrixCoreAPI", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return lstSubProductListModal;
        }

        public SaveInfo ReAssignTransferLead(AssignLeadModal oAssignLeadModal)
        {
            SaveInfo odata = new SaveInfo();
            DateTime dt = DateTime.Now;
            try
            {
                var ds = SMEDLL.getGroupNameandID(oAssignLeadModal.AssignTo);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    Int16 GroupId = Convert.ToInt16(ds.Tables[0].Rows[0]["UserGroupID"]);

                    AllocationDetails _AllocationDetails = new AllocationDetails();
                    _AllocationDetails.LeadID = oAssignLeadModal.LeadId;
                    _AllocationDetails.GroupID = GroupId;
                    _AllocationDetails.JobID = oAssignLeadModal.JobId;
                    _AllocationDetails.AssignbyUserID = oAssignLeadModal.AssignBy;
                    _AllocationDetails.AssigntoUserID = oAssignLeadModal.AssignTo;

                    SMEDLL.ReAssignLead(_AllocationDetails);
                    SMEDLL.UnAssignLead(oAssignLeadModal.LeadId, GroupId, oAssignLeadModal.AssignTo);
                    odata.IsSaved = true;
                    odata.StatusCode = 200;

                }
            }

            catch (Exception ex)
            {
                odata.IsSaved = false;
                odata.StatusCode = 404;
                odata.Message = Convert.ToString(ex.Message);
                LoggingHelper.LoggingHelper.AddloginQueue("", oAssignLeadModal.LeadId, ex.ToString(), "ReAssignTransferLead", "MatrixCore", "SalesViewBLL", JsonConvert.SerializeObject(oAssignLeadModal), string.Empty, dt, DateTime.Now);
                return null;
            }

            return odata;
        }


        public List<SmeIndustryType> GetSmeIndustryTypes()
        {
            DateTime dt = DateTime.Now;
            List<SmeIndustryType> industryTypesList = null;
            try
            {
                string Key = $"{RedisCollection.IndustryTypesList()}";
                if (MemoryCache.Default[Key] != null)
                {
                    industryTypesList = new List<SmeIndustryType>();
                    industryTypesList = (List<SmeIndustryType>)MemoryCache.Default.Get(Key);
                }
                else
                {
                    DataSet oDataSet = SMEDLL.GetSmeIndustryTypes();
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        industryTypesList = (from dr in oDataSet.Tables[0].AsEnumerable()
                                             select new SmeIndustryType()
                                             {
                                                 Id = dr["ID"] != null && dr["ID"] != DBNull.Value ? Convert.ToInt32(dr["ID"]) : default,
                                                 Name = dr["Name"] != null && dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : default
                                             }).ToList();
                        CommonCache.GetOrInsertIntoCache(industryTypesList, Key, 8 * 60);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetSmeIndustryTypes", "MatrixCore", "SmeBLL", string.Empty, string.Empty, dt, DateTime.Now);
            }
            return industryTypesList;
        }

        public QuickSightUrl GetQuickSightURL(string userId)
        {
            var response = new QuickSightUrl();
            try
            {
                string EmpId = "";
                var dataSet = LeadPrioritizationDLL.GetUserDetails(Convert.ToInt64(userId));
                if (dataSet != null && dataSet.Tables != null && dataSet.Tables[0].Rows.Count > 0)
                {
                    var row = dataSet.Tables[0].Rows[0];
                    EmpId = row["EmployeeId"] != DBNull.Value ? row["EmployeeId"].ToString() : default;
                }
                if (!string.IsNullOrEmpty(EmpId))
                {
                    var dataToSend = new
                    {
                        EmployeeId = EmpId,
                        DashboardId = "SMEQuickSightDashboardId".AppSettings()
                    };
                    string jsonData = JsonConvert.SerializeObject(dataToSend);
                    var result = CommonAPICall.CallAPI("QuickSightUrl".AppSettings(), jsonData, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", null);
                    var res = JsonConvert.DeserializeObject<dynamic>(result);
                    if (res != null)
                    {
                        response.Url = res.QuicksightUrl;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetQuickSightURL", "MatrixCore", "SmeBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }
        public List<MyLeadData> GetMyLeadsData(string userId, long LeadId)
        {
            var response = new List<MyLeadData>();
            try
            {
                if (!string.IsNullOrEmpty(userId))
                {
                    var data = SMEDLL.GetMyLeadsData(Convert.ToInt64(userId), Convert.ToInt64(LeadId));
                    if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0)
                    {
                        response = data.Tables[0].AsEnumerable().Select(dr => new MyLeadData

                        {
                            LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : default,
                            CustomerId = dr["CustomerId"] != DBNull.Value ? Convert.ToInt64(dr["CustomerId"]) : default,
                            LeadSource = dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : default,
                            CreatedOn = dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : default,
                            ParentId = dr["ParentId"] != DBNull.Value ? Convert.ToInt64(dr["ParentId"]) : default,
                            SubProductId = dr["SubProductId"] != DBNull.Value ? Convert.ToInt16(dr["SubProductId"]) : default,
                            CompanyName = dr["CompanyName"] != DBNull.Value ? Convert.ToString(dr["CompanyName"]) : default,
                            Premium = dr["Premium"] != DBNull.Value ? Convert.ToDecimal(dr["Premium"]) : default,
                            CIN = dr["CIN"] != DBNull.Value ? Convert.ToString(dr["CIN"]) : default,
                            StatusId = dr["StatusId"] != DBNull.Value ? Convert.ToInt16(dr["StatusId"]) : default,
                            StatusName = dr["StatusName"] != DBNull.Value ? Convert.ToString(dr["StatusName"]) : default,
                            SubProductName = dr["SubProductName"] != DBNull.Value ? Convert.ToString(dr["SubProductName"]) : default,
                            RFQStatusId = dr["RFQStatusId"] != DBNull.Value ? Convert.ToInt16(dr["RFQStatusId"]) : default,
                            RFQStatus = dr["RFQStatus"] != DBNull.Value ? Convert.ToString(dr["RFQStatus"]) : default,
                            QuoteAgentId = dr["QuoteAgentId"] != DBNull.Value ? Convert.ToInt64(dr["QuoteAgentId"]) : default,
                            QuoteAgentName = dr["QuoteAgentName"] != DBNull.Value ? Convert.ToString(dr["QuoteAgentName"]) : default,
                            AssignedAgentId = dr["AssignedAgentId"] != DBNull.Value ? Convert.ToInt64(dr["AssignedAgentId"]) : default,
                            AssignedAgentName = dr["AssignedAgentName"] != DBNull.Value ? Convert.ToString(dr["AssignedAgentName"]) : default,
                            Name = dr["ContactPersonName"] != DBNull.Value ? Convert.ToString(dr["ContactPersonName"]) : default,
                            MobileNo = dr["MobileNo"] != DBNull.Value ? Crypto.MaskMobileNo(Convert.ToString(dr["MobileNo"])) : default,
                            EmailId = dr["EmailId"] != DBNull.Value ? Crypto.MaskEmailID(Convert.ToString(dr["EmailId"])) : default,
                            LinkedinConnection = dr["LinkedinConnection"] != DBNull.Value ? Convert.ToInt16(dr["LinkedinConnection"]) : default,
                            LinkedinLink = dr["LinkedinLink"] != DBNull.Value ? Convert.ToString(dr["LinkedinLink"]) : default,
                            ClientCityId = dr["ClientCityId"] != DBNull.Value ? Convert.ToString(dr["ClientCityId"]) : default,
                            PolicyDate = dr["PolicyStartDate"] != DBNull.Value ? Convert.ToString(dr["PolicyStartDate"]) : default,
                            PolicyType = dr["PolicyType"] != DBNull.Value ? Convert.ToInt16(dr["PolicyType"]) : default,
                            IndustryTypeId = dr["IndustryTypeId"] != DBNull.Value ? Convert.ToInt32(dr["IndustryTypeId"]) : default,
                            ClaimHistory = dr["ClaimHistory"] != DBNull.Value ? Convert.ToString(dr["ClaimHistory"]) : default,
                            ExistingBroker = dr["ExistingBroker"] != DBNull.Value ? Convert.ToInt32(dr["ExistingBroker"]) : default,
                            ExistingInsurer = dr["ExistingInsurer"] != DBNull.Value ? Convert.ToInt32(dr["ExistingInsurer"]) : default,
                            ExistingTPA = dr["ExistingTPA"] != DBNull.Value ? Convert.ToInt32(dr["ExistingTPA"]) : default,
                            Probability = dr["Probability"] != DBNull.Value ? Convert.ToInt32(dr["Probability"]) : default,
                            SumInsured = dr["SumInsured"] != DBNull.Value ? Convert.ToInt64(dr["SumInsured"]) : default,
                            NoOfLives = dr["NoOfLives"] != DBNull.Value ? Convert.ToInt32(dr["NoOfLives"]) : default,
                            UtmSource = dr["UtmSource"] != DBNull.Value ? Convert.ToString(dr["UtmSource"]) : default,
                            UtmMedium = dr["UtmMedium"] != DBNull.Value ? Convert.ToString(dr["UtmMedium"]) : default,
                            CrossSellSubProductIds = dr["CrossSellSubProductIds"] != DBNull.Value ? Convert.ToString(dr["CrossSellSubProductIds"]) : default,
                            WinningStrategy = dr["WinningStrategy"] != DBNull.Value ? Convert.ToString(dr["WinningStrategy"]) : default,
                            PolicyTypeID = dr["PolicyType"] != DBNull.Value ? Convert.ToString(dr["PolicyType"]) : default,
                            DecisionMakerCityId = dr["DecisionMakerCityId"] != DBNull.Value ? Convert.ToString(dr["DecisionMakerCityId"]) : default,
                            ExecutiveRole = dr["ExecutiveRole"] != DBNull.Value ? Convert.ToString(dr["ExecutiveRole"]) : default,
                            ParentCompany = dr["ParentCompany"] != DBNull.Value ? Convert.ToString(dr["ParentCompany"]) : default,
                            OtherExistingBroker = dr["OtherExistingBroker"] != DBNull.Value ? Convert.ToString(dr["OtherExistingBroker"]) : default,
                            OtherExistingInsurer = dr["OtherExistingInsurer"] != DBNull.Value ? Convert.ToString(dr["OtherExistingInsurer"]) : default,
                            OtherExistingTPA = dr["OtherExistingTPA"] != DBNull.Value ? Convert.ToString(dr["OtherExistingTPA"]) : default,
                            SubCIN = dr["SubCIN"] != DBNull.Value ? Convert.ToString(dr["SubCIN"]) : default,
                            AltContactInformation = new List<AltContactInformationDetails>()
                        }).ToList();

                        if (data.Tables[1].Rows != null && data.Tables[1].Rows.Count > 0)
                        {
                            var altContactInfo = data.Tables[1].AsEnumerable().Select(dr => new
                            {
                                PrimaryId = dr["PrimaryId"] != DBNull.Value ? Convert.ToInt64(dr["PrimaryId"]) : 0,
                                LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                                AltContactPersonName = dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : null,
                                AltMobileNo = dr["MobileNo"] != DBNull.Value ? Crypto.MaskMobileNo(Convert.ToString(dr["MobileNo"])) : null,
                                AltEmailId = dr["EmailId"] != DBNull.Value ? Crypto.MaskEmailID(Convert.ToString(dr["EmailId"])) : null
                            }).ToList();

                            foreach (var lead in response)
                            {
                                lead.AltContactInformation = altContactInfo
                                    .Where(ac => ac.LeadId == lead.LeadId) // Match by LeadId
                                    .Select(ac => new AltContactInformationDetails
                                    {
                                        PrimaryId = ac.PrimaryId,
                                        LeadId = ac.LeadId,
                                        AltContactPersonName = ac.AltContactPersonName,
                                        AltMobileNo = ac.AltMobileNo,
                                        AltEmailId = ac.AltEmailId
                                    }).ToList();
                            }
                        }


                    }


                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(userId, 0, ex.ToString(), "GetMyLeadsData", "MatrixCore", "SmeBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public List<MyLeadData> GetMyRenewalData(string userId)
        {
            var response = new List<MyLeadData>();
            try
            {
                if (!string.IsNullOrEmpty(userId))
                {
                    var data = SMEDLL.GetMyRenewalData(Convert.ToInt64(userId));
                    if (data != null && data.Tables != null && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0)
                    {
                        response = (from dr in data.Tables[0].AsEnumerable()
                                    select new MyLeadData()
                                    {
                                        LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : default,
                                        CustomerId = dr["CustomerId"] != DBNull.Value ? Convert.ToInt64(dr["CustomerId"]) : default,
                                        LeadSource = dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : default,
                                        CreatedOn = dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : default,
                                        ParentId = dr["ParentId"] != DBNull.Value ? Convert.ToInt64(dr["ParentId"]) : default,
                                        SubProductId = dr["SubProductId"] != DBNull.Value ? Convert.ToInt16(dr["SubProductId"]) : default,
                                        CompanyName = dr["CompanyName"] != DBNull.Value ? Convert.ToString(dr["CompanyName"]) : default,
                                        Premium = dr["Premium"] != DBNull.Value ? Convert.ToDecimal(dr["Premium"]) : default,
                                        CIN = dr["CIN"] != DBNull.Value ? Convert.ToString(dr["CIN"]) : default,
                                        StatusId = dr["StatusId"] != DBNull.Value ? Convert.ToInt16(dr["StatusId"]) : default,
                                        StatusName = dr["StatusName"] != DBNull.Value ? Convert.ToString(dr["StatusName"]) : default,
                                        SubProductName = dr["SubProductName"] != DBNull.Value ? Convert.ToString(dr["SubProductName"]) : default,
                                        RFQStatusId = dr["RFQStatusId"] != DBNull.Value ? Convert.ToInt16(dr["RFQStatusId"]) : default,
                                        RFQStatus = dr["RFQStatus"] != DBNull.Value ? Convert.ToString(dr["RFQStatus"]) : default,
                                        QuoteAgentId = dr["QuoteAgentId"] != DBNull.Value ? Convert.ToInt64(dr["QuoteAgentId"]) : default,
                                        QuoteAgentName = dr["QuoteAgentName"] != DBNull.Value ? Convert.ToString(dr["QuoteAgentName"]) : default,
                                        AssignedAgentId = dr["AssignedAgentId"] != DBNull.Value ? Convert.ToInt64(dr["AssignedAgentId"]) : default,
                                        AssignedAgentName = dr["AssignedAgentName"] != DBNull.Value ? Convert.ToString(dr["AssignedAgentName"]) : default,
                                        RenewalLeadId = dr["RenewalLeadId"] != DBNull.Value ? Convert.ToInt64(dr["RenewalLeadId"]) : default,
                                        ExpiryDate = dr["ExpiryDate"] != DBNull.Value ? Convert.ToString(dr["ExpiryDate"]) : default,
                                        PolicyNo = dr["PolicyNo"] != DBNull.Value ? Convert.ToString(dr["PolicyNo"]) : default,
                                        LastYearInsurer = dr["LastYearInsurer"] != DBNull.Value ? Convert.ToString(dr["LastYearInsurer"]) : default,

                                    }).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(userId, 0, ex.ToString(), "GetMyRenewalData", "MatrixCore", "SmeBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public SaveInfo UpdateSmeLeadDetails(MyLeadData data, string UserId)
        {
            SaveInfo response = new SaveInfo();
            DateTime dt = DateTime.Now;
            try
            {
                var ds = SMEDLL.UpdateSmeLeadDetails(data, Convert.ToInt64(UserId));
                if (ds != null)
                {
                    var row = ds.Tables[0].Rows[0];
                    response = new SaveInfo
                    {
                        IsSaved = true,
                        ReferralId = row["LeadId"] != DBNull.Value ? Convert.ToString(row["LeadId"]) : default,

                    };
                    response.Message = response.ReferralId + " Updated Successfully";
                }
                else
                {
                    response = new SaveInfo
                    {
                        IsSaved = false,
                    };
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "UpdateSmeLeadDetails", "MatrixCore", "SmeBLL", string.Empty, string.Empty, dt, DateTime.Now);
            }
            return response;
        }
        public SMEMasterList GetSmeInsurerMaster()
        {
            DateTime dt = DateTime.Now;
            SMEMasterList SmeInsurerMasterList = new SMEMasterList();
            List<Master> TPA = new List<Master>();
            List<Master> Insurer = new List<Master>();
            List<Master> Broker = new List<Master>();

            try
            {
                string Key = $"{RedisCollection.SmeInsurerMasterList()}";
                if (MemoryCache.Default[Key] != null)
                {
                    SmeInsurerMasterList = new SMEMasterList();
                    SmeInsurerMasterList = (SMEMasterList)MemoryCache.Default.Get(Key);
                }
                else
                {
                    DataSet oDataSet = SMEDLL.GetSmeInsurerMaster(1);
                    if (oDataSet != null && oDataSet.Tables.Count > 0)
                    {
                        if (oDataSet.Tables[0].Rows.Count > 0)
                        {
                            Insurer = (from dr in oDataSet.Tables[0].AsEnumerable()
                                       select new Master()
                                       {
                                           Id = dr["ID"] != null && dr["ID"] != DBNull.Value ? Convert.ToInt32(dr["ID"]) : default,
                                           Name = dr["Name"] != null && dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : default
                                       }).ToList();
                        }
                        if (oDataSet.Tables[1].Rows.Count > 0)
                        {
                            TPA = (from dr in oDataSet.Tables[1].AsEnumerable()
                                   select new Master()
                                   {
                                       Id = dr["ID"] != null && dr["ID"] != DBNull.Value ? Convert.ToInt32(dr["ID"]) : default,
                                       Name = dr["Name"] != null && dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : default
                                   }).ToList();
                        }
                        if (oDataSet.Tables[2].Rows.Count > 0)
                        {
                            Broker = (from dr in oDataSet.Tables[2].AsEnumerable()
                                      select new Master()
                                      {
                                          Id = dr["ID"] != null && dr["ID"] != DBNull.Value ? Convert.ToInt32(dr["ID"]) : default,
                                          Name = dr["Name"] != null && dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : default
                                      }).ToList();
                        }
                        SmeInsurerMasterList.Broker = Broker;
                        SmeInsurerMasterList.TPA = TPA;
                        SmeInsurerMasterList.Insurer = Insurer;
                        CommonCache.GetOrInsertIntoCache(SmeInsurerMasterList, Key, 8 * 60);
                    }

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetSmeInsurerMaster", "MatrixCore", "SmeBLL", string.Empty, string.Empty, dt, DateTime.Now);
            }

            return SmeInsurerMasterList;
        }
        public SaveInfo CreateSmeMom(MOMData data, string userId)
        {
            SaveInfo response = new SaveInfo();
            DateTime dt = DateTime.Now;
            try
            {
                response.IsSaved = SMEDLL.CreateSmeMom(data, Convert.ToInt64(userId));
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(data.LeadId), data.LeadId, ex.ToString(), "CreateSmeMom", "MatrixCore", "SmeBLL", string.Empty, string.Empty, dt, DateTime.Now);
            }
            return response;
        }

        public List<MOMData> GetSmeMomData(long CustomerId, string UserId)
        {
            var response = new List<MOMData>();
            try
            {
                if (CustomerId > 0)
                {
                    var data = SMEDLL.GetSmeMomData(Convert.ToInt64(CustomerId), Convert.ToInt64(UserId));
                    if (data != null && data.Tables != null && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0)
                    {
                        response = (from dr in data.Tables[0].AsEnumerable()
                                    select new MOMData()
                                    {
                                        CustomerId = dr["CustomerID"] != DBNull.Value ? Convert.ToInt64(dr["CustomerID"]) : default,
                                        LeadId = dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : default,
                                        MeetingDate = dr["MeetingDate"] != DBNull.Value ? Convert.ToDateTime(dr["MeetingDate"]) : default,
                                        FollowUpDate = dr["FollowUpDate"] != DBNull.Value ? Convert.ToDateTime(dr["FollowUpDate"]) : default,
                                        MeetingType = dr["MeetingType"] != DBNull.Value ? Convert.ToString(dr["MeetingType"]) : default,
                                        CityId = dr["CityId"] != DBNull.Value ? Convert.ToString(dr["CityId"]) : default,
                                        MeetingStatus = dr["MeetingStatus"] != DBNull.Value ? Convert.ToInt32(dr["MeetingStatus"]) : default,
                                        ClientName = dr["ClientName"] != DBNull.Value ? Convert.ToString(dr["ClientName"]) : default,
                                        ClientDesignation = dr["ClientDesignation"] != DBNull.Value ? Convert.ToString(dr["ClientDesignation"]) : default,
                                        MeetingAgenda = dr["MeetingAgenda"] != DBNull.Value ? Convert.ToString(dr["MeetingAgenda"]) : default,
                                        PbAttendees = dr["PbAttendees"] != DBNull.Value ? Convert.ToString(dr["PbAttendees"]) : default,
                                        CreatedOn = dr["CreatedOn"] != DBNull.Value ? Convert.ToString(dr["CreatedOn"]) : default,
                                        LOB = dr["LOB"] != DBNull.Value ? Convert.ToString(dr["LOB"]) : default,
                                        CallPriority = dr["CallPriority"] != DBNull.Value ? Convert.ToString(dr["CallPriority"]) : default
                                    }).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(CustomerId), 0, ex.ToString(), "GetSmeMomData", "MatrixCore", "SmeBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }
        public SaveInfo SaveSmeFeedback(FeedbackData data)
        {
            SaveInfo response = new SaveInfo();
            DateTime dt = DateTime.Now;
            try
            {
                response.IsSaved = SMEDLL.SaveSmeFeedback(data);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "SaveSmeFeedback", "MatrixCore", "SmeBLL", string.Empty, string.Empty, dt, DateTime.Now);
            }
            return response;
        }
        public SalesPartnerAndSalesSpecialist GetAgentsByType(string AgentType)
        {
            var response = new SalesPartnerAndSalesSpecialist();
            try
            {
                if (!string.IsNullOrEmpty(AgentType))
                {
                    var data = SMEDLL.GetAgentsByType(AgentType);

                    if (data != null && data.Tables.Count > 0)
                    {
                        response.SalesPartners = data.Tables[0].AsEnumerable().ToList().Select(dr => new UserDetails()
                        {
                            UserId = Convert.ToInt64(dr["UserId"]),
                            UserName = string.Format("{0} ({1})", Convert.ToString(dr["UserName"]), Convert.ToString(dr["EmployeeId"])),
                            EmployeeId = Convert.ToString(dr["EmployeeId"])
                        }).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0,
                                                          ex.Message, "GetAgentsByType",
                                                          "GetAgentsByType", "",
                                                          "", "", DateTime.Now, DateTime.Now);
                response.Message = "Error occured";
            }
            return response;
        }
        public NewSVURLModel GeneratePerLifeRateURL(GeneratePerLifeRateRequest request)
        {
            NewSVURLModel response = new();

            try
            {
                if (request != null && request.LeadId > 0)
                {
                    var dataToSend = new
                    {
                        LeadId = request.LeadId,
                        CustomerId = request.CustomerId,
                        Token = request.Token ?? string.Empty
                    };
                    string url = "SmeCJURL".AppSettings() + "matrix/GeneratePerLifeRateURL";
                    var header = new Dictionary<object, object>
                    {
                        { "Access-Token", "SmeQcrUploadToken".AppSettings() }
                    };
                    var result = CommonAPICall.CallAPI(url,
                                                       JsonConvert.SerializeObject(dataToSend),
                                                       "POST",
                                                       Convert.ToInt32("CJTimeout".AppSettings()),
                                                       "application/json",
                                                       header);
                    if (result != null)
                    {
                        dynamic parsedResult = JsonConvert.DeserializeObject(result);
                        if (parsedResult != null && parsedResult.RedirectURL != null)
                        {
                            response.StatusCode = 1;
                            response.StatusMessage = "Success";
                            response.URL = parsedResult.RedirectURL;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    Convert.ToString(request.LeadId),
                    Convert.ToInt64(request.LeadId),
                    ex.ToString(),
                    "GeneratePerLifeRateURL",
                    "MatrixCore",
                    "SmeBLL",
                    string.Empty,
                    string.Empty,
                    DateTime.Now,
                    DateTime.Now
                );
            }
            return response;
        }

        public string GetMobileNo(long LeadId, long UserId, int ActionType, long CustomerId, long PrimaryId)
        {
            string response = string.Empty;

            try
            {
                // Fetch data from the database
                var dataSet = SMEDLL.GetMobileNo(LeadId, UserId, ActionType, CustomerId, PrimaryId);

                // Check if the dataset is valid and contains data
                if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                {
                    var row = dataSet.Tables[0].Rows[0];

                    // Extract the MobileNo column value
                    response = row["response"] != DBNull.Value ? row["response"].ToString() : string.Empty;
                }
            }

            catch (Exception ex)
            {
                // Log any unexpected exceptions
                LoggingHelper.LoggingHelper.AddloginQueue(
                    "",
                    0,
                    ex.ToString(),
                    "GetMobileNo",
                    "MatrixCore",
                    "SMEBLL",
                    string.Empty,
                    string.Empty,
                    DateTime.Now,
                    DateTime.Now);
            }
            return response;
        }
        public List<Master> GetMappingValues()
        {
            DateTime dt = DateTime.Now;
            List<Master> mappings = new List<Master>();

            try
            {
                DataSet oDataSet = SMEDLL.GetSmeInsurerMaster(2);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    mappings = (from dr in oDataSet.Tables[0].AsEnumerable()
                                select new Master()
                                {
                                    Id = dr["ID"] != DBNull.Value ? Convert.ToInt32(dr["ID"]) : default,
                                    Value = dr["Value"] != DBNull.Value ? Convert.ToDecimal(dr["Value"]) : default
                                }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetMappingValues", "MatrixCore", "SmeBLL", string.Empty, string.Empty, dt, DateTime.Now);
            }

            return mappings;
        }
        public List<LeadInfo> GetCustomerOpenLeads(long customerId, int subProductId, long userId)
        {
            DateTime dt = DateTime.Now;
            List<LeadInfo> leadIds = new List<LeadInfo>();

            try
            {
                DataSet oDataSet = SMEDLL.GetCustomerOpenLeads(customerId, subProductId, userId);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    leadIds = (from dr in oDataSet.Tables[0].AsEnumerable()
                               select new LeadInfo()
                               {
                                   LeadID = dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : default,
                                   LeadStatus = dr["StatusName"] != DBNull.Value ? Convert.ToString(dr["StatusName"]) : default,
                                   LastUpdatedOn = dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : default,
                                   CompanyName = dr["CompanyName"] != DBNull.Value ? Convert.ToString(dr["CompanyName"]) : default,
                               }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    "", 0, ex.ToString(),
                    "GetCustomerOpenLeads", "MatrixCore", "SmeBLL", string.Empty, string.Empty,
                    dt, DateTime.Now);
            }

            return leadIds;
        }


        public Response GetCustomerLeadBySubProduct(string encCustId, int subProductId, string encKey, string encIV)
        {
            var res = new Response();
            try
            {
                var custId = Crypto.Decrytion_Payment_AES(encCustId, "Core", 256, 128, encKey, encIV, true);
                if (!string.IsNullOrEmpty(custId))
                {
                    DataSet data = SMEDLL.GetCustomerLeadBySubProduct(Convert.ToInt64(custId), subProductId);
                    if (data != null && data.Tables != null && data.Tables[0].Rows.Count > 0)
                    {
                        var row = data.Tables[0].Rows[0];
                        res.LeadId = row["LeadId"] != DBNull.Value ? row["LeadId"].ToString() : default;
                        res.status = row["IsActive"] != DBNull.Value && Convert.ToBoolean(row["IsActive"]);
                        res.message = res.status ? "IsActive" : "Inactive";
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetCustomerLeadBySubProduct", "MatrixCore", "SmeBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return res;
        }

    }

}