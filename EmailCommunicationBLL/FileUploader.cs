﻿using System;
using Helper;
using PropertyLayers;
using MongoConfigProject;
using Newtonsoft.Json;
using System.Net.Http;
using DataAccessLibrary;
using System.Collections.Generic;
using System.Text;

namespace EmailCommunicationBLL
{
    public class FileUploader : IFileUploader
    {
        public PolicyUploadResponse UploadPolicy(PolicyUploadRequest request)
        {
            PolicyUploadResponse response = null;
            try
            {
                long custId = 0;
                var data = SalesViewDLL.GetBasicLeadDetails(request.LeadId);
                if (data != null && data.Tables.Count > 0)
                {
                    custId = data.Tables[0].Rows[0]["CustomerId"] == DBNull.Value ? 0 : Convert.ToInt64(data.Tables[0].Rows[0]["CustomerId"]);
                }
                if (request.CustomerId <= 0 || request.CustomerId != custId)
                {
                    string custLog = request.CustomerId + ", " + custId;
                    request.CustomerId = custId > 0 ? custId : request.CustomerId;
                    LoggingHelper.LoggingHelper.AddloginQueue(request.LeadId.ToString(), request.LeadId, "", "UploadPolicy_V2", "MatrixCore", "", "", custLog, DateTime.Now, DateTime.Now);
                }

                GetConfigValue("uploadPolicyCopy/v2", out string endpointUrl, out string accessKey, out string secretKey, out string authKey, out string clientKey);

                var signatureKey = $"{accessKey}|clientKey={clientKey};customerId={request.CustomerId};fileName={request.UploadedFile.FileName};" +
                                   $"leadId={request.LeadId};productId={request.ProductId};|{secretKey}";

                var payloadJSON = new
                {
                    leadId = request.LeadId,
                    customerId = request.CustomerId,
                    productId = request.ProductId,
                    fileName = request.UploadedFile.FileName,
                    signature = CoreCommonMethods.ComputeSha256Hash(signatureKey)
                };

                var content = new ByteArrayContent(CoreCommonMethods.GetBytesOfFile(request.UploadedFile));
                content.Headers.Add("Content-Type", request.UploadedFile.ContentType);

                var multipartContent = new MultipartFormDataContent
                {
                    { content, "file", request.UploadedFile.FileName },
                    { new StringContent(JsonConvert.SerializeObject(payloadJSON)), "payloadJSON" }
                };

                var headerParams = new Dictionary<string, string>
                {
                    { "authKey", authKey },
                    { "clientKey", clientKey }
                };

                var result = CommonAPICall.PostApiCallSync(endpointUrl, 3000, multipartContent, headerParams);

                if (!string.IsNullOrEmpty(result))
                    response = JsonConvert.DeserializeObject<PolicyUploadResponse>(result);
                else
                    throw new OperationCanceledException("PolicyUploadResponse Empty!");
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("",
                                                          request != null ? request.LeadId : 0,
                                                          "Invalid file",
                                                          "UploadPolicy",
                                                          "MatrixCore",
                                                          "",
                                                          ex.Message,
                                                          "Invalid file", DateTime.Now, DateTime.Now);

            }
            return response;
        }

        public BasicPolicyDetailsResponse UpdateBasicPolicyDetails(long leadId, string docId)
        {
            BasicPolicyDetailsResponse response = null;
            try
            {
                var headerParams = new Dictionary<object, object>
                {
                    { "authKey", "coreAPIauthKey".AppSettings() },
                    { "clientKey", "coreAPIclientKey".AppSettings() },
                    { "Token", "PIVCToken".AppSettings() }
                };
                var Request = new
                {
                    LeadId = leadId,
                    DocUploadId = docId
                };


                var result = CommonAPICall.CallAPI("PIVC".AppSettings() + "UpdateBasicPolicyDetails",
                                                 JsonConvert.SerializeObject(Request),
                                                 "POST",
                                                 3000,
                                                 "application/json",
                                                 headerParams);
                if (!string.IsNullOrEmpty(result))
                    response = JsonConvert.DeserializeObject<BasicPolicyDetailsResponse>(result);
                else
                    throw new OperationCanceledException("BasicPolicyDetails Empty!");
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("",
                                                          leadId,
                                                          "Invalid data",
                                                          "UpdateBasicPolicyDetails",
                                                          "MatrixCore",
                                                          "",
                                                          ex.Message,
                                                          "Invalid data", DateTime.Now, DateTime.Now);
            }
            return response;
        }

        private static void GetConfigValue(string endPoint,
                                           out string endpointUrl,
                                           out string accessKey,
                                           out string secretKey,
                                           out string authKey,
                                           out string clientKey)
        {
            endpointUrl = "CoreDocUploadBaseUrl".AppSettings() + endPoint;
            accessKey = "accessKey".AppSettings();
            secretKey = "secretKey".AppSettings();
            authKey = "coreAPIauthKey".AppSettings();
            clientKey = "coreAPIclientKey".AppSettings();
        }

        public MiscDocUploadResponse UploadMiscDocument(MiscDocUploadRequest request)
        {
            MiscDocUploadResponse response = null;
            try
            {
                GetConfigValue("uploadMiscDoc", out string endpointUrl, out string accessKey, out string secretKey, out string authKey, out string clientKey);

                var checkSumParams = new SortedList<string, string>
                {
                    { "clientKey", clientKey },
                    { "customerId", request.CustomerId.ToString() },
                    { "type", request.Type},
                    { "enquiryId", request.EnquiryId},
                    { "leadId", request.LeadId.ToString()},
                    { "productId", request.ProductId.ToString()}
                };

                var checkSum = GenerateCheckSum(checkSumParams, accessKey, secretKey);

                var payloadJSON = new
                {
                    clientKey,
                    customerId = request.CustomerId,
                    enquiryId = request.EnquiryId,
                    leadId = request.LeadId,
                    productId = request.ProductId,
                    type = request.Type,
                    signature = CoreCommonMethods.ComputeSha256Hash(checkSum)
                };

                var content = new ByteArrayContent(CoreCommonMethods.GetBytesOfFile(request.UploadedFile));
                content.Headers.Add("Content-Type", request.UploadedFile.ContentType);

                var multipartContent = new MultipartFormDataContent
                {
                    { content, "file", request.UploadedFile.FileName },
                    { new StringContent(JsonConvert.SerializeObject(payloadJSON)), "payloadJSON" }
                };

                var headerParams = new Dictionary<string, string>
                {
                    { "authKey", authKey },
                    { "clientKey", clientKey }
                };

                if (request.Type == "Invoice")
                    endpointUrl += "?docTypeId=67";

                var result = CommonAPICall.PostApiCallSync(endpointUrl, 3000, multipartContent, headerParams);

                if (!string.IsNullOrEmpty(result))
                    response = JsonConvert.DeserializeObject<MiscDocUploadResponse>(result);
                else
                    throw new OperationCanceledException("MiscDocUploadResponse Empty!");
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("",
                                                          request.LeadId,
                                                          ex.Message,
                                                          "UploadMiscDocument",
                                                          "MatrixCore",
                                                          "",
                                                          "",
                                                          "Invalid data", DateTime.Now, DateTime.Now);
            }

            return response;
        }

        public MiscDocUploadResponse UploadCustomerProof(MiscDocUploadRequest request)
        {
            var response = new MiscDocUploadResponse();
            string docCategory = string.Empty;
            string docCategoryId = string.Empty;
            string docType = string.Empty;
            string docTypeId = string.Empty;
            try
            {
                var refId = string.Format("{0}{1}", request.LeadId, Guid.NewGuid());
                GetConfigValue("customerProofDocs/v2", out string endpointUrl, out string accessKey, out string secretKey, out string authKey, out string clientKey);
                if (request.Type == "PANCopy")
                {
                    docCategory = "ID Proof";
                    docCategoryId = "7";
                    docType = "Pan Card";
                    docTypeId = "3";
                }
                if (request.Type == "GSTCopy")
                {
                    docCategory = "Income Proof";
                    docCategoryId = "8";
                    docType = "GST";
                    docTypeId = "774";
                }
                var mappingData = new SortedList<string, string>
                {
                    { "idx", "1" },
                    { "docCategoryId", docCategoryId },
                    { "docTypeId", docTypeId },
                    { "docCategory", docCategory},
                    { "docType", docType}
                };
                var mappingDataObject = GenerateCheckSum(mappingData);
                var mappingDataList = "[{" + mappingDataObject + "}]";
                var checkSumParams = new SortedList<string, string>
                {
                    { "cId", request.CustomerId.ToString() },
                    { "clientKey", clientKey },
                    { "mapping",  mappingDataList},
                    { "refId", refId},
                    { "src", "matrix"}
                };
                var checkSum = GenerateCheckSum(checkSumParams, accessKey, secretKey);
                var payload = new
                {
                    refId,
                    clientKey,
                    signature = CoreCommonMethods.ComputeSha256Hash(checkSum),
                    src = "matrix",
                    cId = request.CustomerId,
                    mapping = new List<dynamic> { mappingData }
                };
                var content = new ByteArrayContent(CoreCommonMethods.GetBytesOfFile(request.UploadedFile));
                content.Headers.Add("Content-Type", request.UploadedFile.ContentType);

                var payloadJSON = JsonConvert.SerializeObject(payload);

                var multipartContent = new MultipartFormDataContent
                {
                    { content, "file", request.UploadedFile.FileName },
                    { new StringContent(payloadJSON), "payloadJSON" }
                };
                var headerParams = new Dictionary<string, string>
                {
                    { "authKey", authKey },
                    { "clientKey", clientKey }
                };
                var result = CommonAPICall.PostApiCallSync(endpointUrl, 3000, multipartContent, headerParams);

                if (!string.IsNullOrEmpty(result))
                {
                    dynamic res = JsonConvert.DeserializeObject<dynamic>(result);
                    if (res != null)
                    {
                        response.StatusSuccessful = (short)res.ok > 0;
                        response.DocId = res.docId;
                        response.Message = res.msg;
                    }
                    else
                    {
                        throw new OperationCanceledException("Response cannot be parsed!");
                    }
                }
                else
                {
                    throw new OperationCanceledException("UploadCustomerProof Empty!");
                }
            }
            catch (Exception ex)
            {
                response.Message = ex.Message;
                LoggingHelper.LoggingHelper.AddloginQueue("",
                                                          request.LeadId,
                                                          ex.Message,
                                                          "UploadCustomerProof",
                                                          "MatrixCore",
                                                          "",
                                                          "",
                                                          "Invalid data", DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static string GenerateCheckSum(SortedList<string, string> keyValuePairs, string accessKey = null, string secretKey = null)
        {
            var checkSum = new StringBuilder();

            if (keyValuePairs != null && keyValuePairs.Count > 0)
            {
                if (!string.IsNullOrEmpty(accessKey))
                    checkSum.Append(accessKey + "|");

                foreach (var pair in keyValuePairs)
                {
                    checkSum.Append(pair.Key + "=" + pair.Value + ";");
                }

                if (!string.IsNullOrEmpty(secretKey))
                    checkSum.Append("|" + secretKey);
            }
            return checkSum.ToString();
        }
    }

}
