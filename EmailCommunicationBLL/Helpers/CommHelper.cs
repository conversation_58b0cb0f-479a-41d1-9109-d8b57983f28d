﻿using PropertyLayers;
using System;
using System.Collections.Generic;

namespace EmailCommunicationBLL.Helpers
{
    public class CommHelper
    {
        public static string GetCommValue(EnumCommTypes types)
        {
            string CommValue = string.Empty;
            switch (types)
            {
                case EnumCommTypes.Email:
                    CommValue = "Email";
                    break;
                case EnumCommTypes.SMS:
                    CommValue = "SMS";
                    break;
                case EnumCommTypes.OB:
                    CommValue = "Outbound Call";
                    break;
                case EnumCommTypes.Chat:
                    CommValue = "Chat";
                    break;
                case EnumCommTypes.OTP:
                    CommValue = "OTP";
                    break;
                case EnumCommTypes.IB:
                    CommValue = "Inbound Call";
                    break;
                case EnumCommTypes.C2C:
                    CommValue = "CTC";
                    break;
                case EnumCommTypes.WA:
                    CommValue = "WhatsApp";
                    break;
                case EnumCommTypes.VIDEOMEET:
                    CommValue = "VIDEOMEET";
                    break;
                case EnumCommTypes.TRANSFERCALL:
                    CommValue = "Transfer Call";
                    break;
                case EnumCommTypes.FOSVisit:
                    CommValue = "FOS Visit";
                    break;
            }
            return CommValue;
        }

        public static List<Disposition> GetDispositions(DateTime CreatedON, DateTime Updatedtime, int callstatus, EnumCommTypes commtype, string CallStatusName)
        {
            List<Disposition> _displist = new();
            if (commtype == EnumCommTypes.OB || commtype == EnumCommTypes.IB || commtype == EnumCommTypes.C2C || commtype == EnumCommTypes.SCREENSHARE || commtype == EnumCommTypes.VIDEOMEET || commtype == EnumCommTypes.TRANSFERCALL)
            {
                _displist.Add(new Disposition { Status = "-1", CreatedOn = CreatedON.Kind == DateTimeKind.Utc ? CreatedON.ToLocalTime() : CreatedON, StatusName = "Outbound Call INITIATED" });
                if (callstatus > 0)
                {
                    _displist.Add(new Disposition { Status = Convert.ToString(callstatus), CreatedOn = Updatedtime, StatusName = CallStatusName });
                }
            }
            return _displist;
        }

        public static List<Disposition> DefaultDisp(DateTime CreatedON, EnumCommTypes commtype, string CallStatusName)
        {
            List<Disposition> _displist = new();

            if (commtype == EnumCommTypes.OB)
            {
                _displist.Add(new Disposition { Status = "-1", CreatedOn = CreatedON.Kind == DateTimeKind.Utc ? CreatedON.ToLocalTime() : CreatedON, StatusName = CallStatusName });
            }

            return _displist;
        }

    }
}