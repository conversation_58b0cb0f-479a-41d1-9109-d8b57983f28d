﻿using DataAccessLibrary;
using System;
using System.Data;
using MongoConfigProject;
using Newtonsoft.Json;
using System.Text;
using PropertyLayers;
using System.Net;
using System.Linq;
using Microsoft.AspNetCore.Http;
using System.Net.Sockets;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.IO;
using Helper;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;

namespace EmailCommunicationBLL
{
    public class MTXPlusLoginBLL : IMTXPlusLoginBLL
    {
        public ResponseData<LDAPUserDetails> LDAPAuthCheck(string UserName, string password, HttpContext httpContext, string origin)
        {
            DateTime StartTime = DateTime.Now;
            StringBuilder logBuilder = new StringBuilder();
            string error = string.Empty;
            ResponseData<LDAPUserDetails> response = new ResponseData<LDAPUserDetails>();
            response.Data = new LDAPUserDetails();
            SetLdapErrorResponse(response, EnumLdapVerified.ErrorOccured, "LDAPAuthCheck", logBuilder);
            logBuilder.AppendLine("Starting LDAP Authentication Check");

            try
            {
                DataSet ds = MTXPlusLoginDLL.GetLDapEmployeeDetails(Convert.ToString(UserName));
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    logBuilder.AppendLine("--GetLDapEmployeeDetails--");
                    DataTable dt = ds.Tables[0];
                    bool ISLDapEnabled = Convert.ToBoolean(dt.Rows[0]["ISLDapEnabled"] != DBNull.Value ? dt.Rows[0]["ISLDapEnabled"] : false);
                    DateTime PasswordExpDate = Convert.ToDateTime(dt.Rows[0]["PasswordExpDate"] != DBNull.Value ? dt.Rows[0]["PasswordExpDate"] : null);
                    long UserID = Convert.ToInt64(dt.Rows[0]["UserID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["UserID"]) : 0);
                    string DomainUserName = string.Empty;

                    if (ISLDapEnabled == true)
                        DomainUserName = dt.Rows[0]["UserName"].ToString();

                    DomainUserName = !string.IsNullOrEmpty(DomainUserName) ?  DomainUserName.Trim() : "";
                    password = !string.IsNullOrEmpty(password) ? password.Trim() : "";

                    if (ISLDapEnabled == true && !string.IsNullOrEmpty(DomainUserName))
                    {
                        logBuilder.AppendLine("--Perform LDAP Authentication starts--");
                        var ldapResult = PerformLdapAuthentication(DomainUserName, password, logBuilder);

                        logBuilder.AppendLine("--Handle LDAP Authentication starts--");
                        response = HandleLdapAuthenticationResult(ldapResult, UserName, logBuilder);

                        if (response.Code == Convert.ToInt16(EnumLdapVerified.SuccessValidateldap))
                        {
                            HandleSuccessfulLdapLogin(response, UserName, UserID, httpContext, origin, logBuilder);
                        }
                        else
                        {
                            response = CheckFailedLdapLogin(response, UserName, password, PasswordExpDate, logBuilder, httpContext);
                        }
                    }
                    else
                    {
                        SetLdapErrorResponse(response, EnumLdapVerified.LDAPInfoIncorrect, "LDAP information is incorrect.", logBuilder);
                    }
                }
                else
                {
                    SetLdapErrorResponse(response, EnumLdapVerified.LDAPInfoIncorrect, "LDAP information is incorrect.", logBuilder);
                }

            }
            catch (Exception ex)
            {
                error = ex.ToString();
                logBuilder.Append("Exception: " + ex);
                response.Code = (short)EnumLdapVerified.ErrorOccured;
                response.Message = "error occured";
                response.Status = false;
                Console.WriteLine("Error in LDAPAuthCheckMTXBLL- for user" + UserName + " -- " + ex.Message.ToString() + " - " + DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(UserName.ToString(), 0, error.ToString(), "LDAPAuthCheck", "MTXPlusLoginBLL", "MatrixCoreAPI", UserName.ToString(), logBuilder.ToString(), StartTime, DateTime.Now);
            }

            return response;
        }

        public LdapModel PerformLdapAuthentication(string domainUserName, string password, StringBuilder logBuilder)
        {
            try
            {
                if (!string.IsNullOrEmpty(password))
                {
                    password = DecryptData(password, logBuilder);
                    string url = "LdapUrl".AppSettings().ToString();
                    logBuilder.Append("LDAP URL: " + url);
                    string requestJson = $"{{\"username\":\"{domainUserName}\",\"password\":\"{password}\"}}";
                    string result = CommonAPICall.CallAPI(url, requestJson, "POST", 5000, "application/json", null);

                    logBuilder.Append("LDAP API Response: " + result);
                    return !string.IsNullOrEmpty(result) ? JsonConvert.DeserializeObject<LdapModel>(result) : null;
                }
                else
                {
                    logBuilder.Append("LDAP API Response:Empty Password ");
                    return null;
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(domainUserName.ToString(), 0, ex.ToString(), "PerformLdapAuthentication", "MTXPlusLoginBLL", "MatrixCoreAPI", domainUserName.ToString(), "", DateTime.Now, DateTime.Now);
            }
            return null;
        }

        public static string DecryptData(string base64EncryptedData, StringBuilder logBuilder)
        {
            try
            {
                // Load private key from PEM file or store as a string
                var privateKeyPem = "MtxPlusDecPrivateKey".AppSettings();
                if (!string.IsNullOrEmpty(privateKeyPem))
                {
                    privateKeyPem = privateKeyPem.Replace("\\n", "\n");

                    // Convert PEM to RSA parameters
                    RSAParameters rsaParameters = PemToRSAParameters(privateKeyPem);
                    using (RSA rsa = RSA.Create())
                    {
                        rsa.ImportParameters(rsaParameters);
                        byte[] encryptedBytes = Convert.FromBase64String(base64EncryptedData);
                        byte[] decryptedBytes = rsa.Decrypt(encryptedBytes, RSAEncryptionPadding.OaepSHA1);
                        return Encoding.UTF8.GetString(decryptedBytes);
                    }
                }
                else
                {
                    logBuilder.Append("DecryptData - Empty PEM KEY FROM MONGO");
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", 0, ex.ToString(), "DecryptData", "MTXPlusLoginBLL", "MatrixCoreAPI", "", "", DateTime.Now, DateTime.Now);
            }
            return "";
        }

        private static RSAParameters PemToRSAParameters(string pem)
        {
            try
            {
                var rsa = RSA.Create();
                rsa.ImportFromPem(pem.ToCharArray());
                return rsa.ExportParameters(true);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", 0, ex.ToString(), "PemToRSAParameters", "MTXPlusLoginBLL", "MatrixCoreAPI", pem.ToString(), "", DateTime.Now, DateTime.Now); 
                return default(RSAParameters);
            }
        }

        private ResponseData<LDAPUserDetails> HandleLdapAuthenticationResult(LdapModel ldapModel, string userName, StringBuilder logBuilder)
        {
            var response = new ResponseData<LDAPUserDetails>();
            response.Data = new LDAPUserDetails();
            if (ldapModel != null && ldapModel?.message.ToLower() == "success")
            {
                response.Code = (short)EnumLdapVerified.SuccessValidateldap;
                logBuilder.Append("IsLDAPValidate" + true);
                if (ldapModel.user != null && !string.IsNullOrEmpty(ldapModel.user.givenName))
                {
                    DateTime? PassLastSet = DateTime.FromFileTime(Convert.ToInt64(ldapModel.user.pwdLastSet));
                    bool isUpdated = MTXPlusLoginDLL.UpdateUserExpPassword(userName, PassLastSet);
                    logBuilder.Append("Password Last Set Updated: " + isUpdated);
                    response.Status = true;
                    response.Message = "LDAP Authentication Successful.";
                }
            }
            else
            {
                response.Status = true;
                response.Message = "Invalid UserName/Password";
                response.Code = (short)EnumLdapVerified.InvalidUserNamePass;
                logBuilder.Append("--Not Validated ");
            }
            return response;
        }
        private void HandleSuccessfulLdapLogin(ResponseData<LDAPUserDetails> response, string UserName, long userId, HttpContext httpContext, string origin, StringBuilder logBuilder)
        {
            response.Data.IsLDAPEnabled = true;
            response.Data.EmployeeID = UserName;
            response.Data.UserID = userId;
            response.Data.LDAPSetTime = DateTime.Now;

            DataTable userInfo = MTXPlusLoginDLL.GetBasicUserInfo(userId);
            if (userInfo != null && userInfo.Rows.Count > 0)
            {
                PopulateAdditionalUserInfo(response, userInfo, httpContext, origin);
            }

            var secureToken = GenerateSecureToken(response.Data);
            SetAuthCookie(httpContext, secureToken);

            logBuilder.AppendLine("--Handled Successful LDAP Login--");
        }

        private string GenerateSecureToken(LDAPUserDetails userDetails)
        {
            var jsonString = JsonConvert.SerializeObject(userDetails);
            string encryptjsonString = Crypto.Encrytion_Payment_AES(jsonString, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings(), false);
            var byteArray = Encoding.UTF8.GetBytes(encryptjsonString);
            return Convert.ToBase64String(byteArray);
        }
        private void SetAuthCookie(HttpContext httpContext, string secureToken)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.Strict,
                Expires = DateTime.UtcNow.AddMinutes(1440),
                Domain = ".policybazaar.com"
            };
            httpContext?.Response.Cookies.Append("TempAuthToken", secureToken, cookieOptions);
        }
        private void PopulateAdditionalUserInfo(ResponseData<LDAPUserDetails> response, DataTable userInfo, HttpContext httpContext, string origin)
        {
            var row = userInfo.Rows[0];
            response.Data.RoleID = Convert.ToInt32(row["RoleID"] ?? 0);
            long mobileNo = Convert.ToInt64(row["ContactNo"] ?? 0);
            if (mobileNo > 0)
            {
                response.Data.ContactNo = Crypto.Encrytion_Payment_AES(mobileNo.ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false);
                response.Data.MaskedContactNo = Crypto.MaskMobileNo(mobileNo.ToString());
            }
            response.Data.EmailID = row["EmailID"]?.ToString();
            response.Data.IsLoginActive = Convert.ToBoolean(row["IsLoginActive"] ?? false);
            response.Data.IsLDAPAuthorised = true;
            response.Data.IsOTPRequired = ShouldGenerateOTP(response.Data.RoleID, httpContext, origin);
            response.Data.IsOTPVerified = false;
        }
        private bool ShouldGenerateOTP(int RoleID, HttpContext httpContext, string origin)
        {
            bool result = false;
            List<string> OTPRoleIDs = "OTPRoleIDs".AppSettings().Split(',').ToList();

            if ((OTPRoleIDs.Contains(RoleID.ToString())) || origin.Contains("mobilematrix.policybazaar.com"))
            {
                result = true;
            }
            return result;
        }
        private ResponseData<LDAPUserDetails> CheckFailedLdapLogin(ResponseData<LDAPUserDetails> response, string userName, string password, DateTime passwordExpDate, StringBuilder logBuilder, HttpContext httpContext)
        {
            response.Status = true;
            response.Code = (short)EnumLdapVerified.InvalidUserNamePass;
            response.Message = "Invalid Username/Password.";

            int passwordExpiredDays = (DateTime.Now - passwordExpDate).Days;
            if (passwordExpiredDays >= 30)
            {
                response.Code = (short)EnumLdapVerified.PasswordExp;
                response.Message = "Your Password has expired. Change it via ‘Change Password’ button on the Matrix Login Page.";
                logBuilder.Append("Password expired");
            }
            else
            {
                long loginAttemptStatus = MTXPlusLoginDLL.Check_LoginForLdapUser(userName, password, GetClientIpAddress(httpContext), true);
                if (loginAttemptStatus == -2)
                {
                    response.Code = (short)EnumLdapVerified.AccountBlocked;
                    response.Message = "Your account has been blocked due to multiple invalid attempts. Please ask your Team Leader to connect with MIS team to unblock.";
                    logBuilder.Append("Account Blocked");
                }
            }
            return response;
        }

        private void SetLdapErrorResponse(ResponseData<LDAPUserDetails> response, EnumLdapVerified statusCode, string message, StringBuilder logBuilder)
        {
            response.Status = true;
            response.Message = message;
            response.Code = (short)statusCode;
            response.Data.IsLDAPEnabled = false;
            logBuilder.AppendLine(message);
        }
        public enum EnumLdapVerified
        {
            ErrorOccured = 0,
            SuccessValidateldap = 1,
            InvalidUserNamePass = 2,
            LDAPInfoIncorrect = 3,
            AccountBlocked = 4,
            PasswordExp = 5

        }

        public static MatrixPlusUserInfo PrepareUserModel(DataSet objDsUserdata, HttpContext httpContext)
        {
            MatrixPlusUserInfo objMatrixUserInfo = new MatrixPlusUserInfo();
            string TrackingID = "1234098";
            try
            {
                if (objDsUserdata != null && objDsUserdata.Tables.Count > 0)
                {
                    DataTable dtUserdata = objDsUserdata.Tables[0];
                    objMatrixUserInfo.UserId = dtUserdata.Rows[0]["UserID"] != null && dtUserdata.Rows[0]["UserID"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["UserID"]) : "";
                    TrackingID = objMatrixUserInfo.UserId;
                    objMatrixUserInfo.IpAddress = GetClientIpAddress(httpContext);
                    objMatrixUserInfo.UserName = dtUserdata.Rows[0]["UserName"] != null && dtUserdata.Rows[0]["UserName"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["UserName"]) : "";
                    objMatrixUserInfo.RoleName = dtUserdata.Rows[0]["RoleName"] != null && dtUserdata.Rows[0]["RoleName"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["RoleName"]) : "";
                    objMatrixUserInfo.Email = dtUserdata.Rows[0]["Email"] != null && dtUserdata.Rows[0]["Email"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["Email"]) : "";
                    objMatrixUserInfo.Address = dtUserdata.Rows[0]["Address"] != null && dtUserdata.Rows[0]["Address"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["Address"]) : "";
                    objMatrixUserInfo.IsOneLead = dtUserdata.Rows[0]["IsOneLead"] != null && dtUserdata.Rows[0]["IsOneLead"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsOneLead"]) : false;
                    objMatrixUserInfo.EmployeeId = dtUserdata.Rows[0]["EmployeeId"] != null && dtUserdata.Rows[0]["EmployeeId"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["EmployeeId"]) : "";
                    objMatrixUserInfo.IsActive = dtUserdata.Rows[0]["IsActive"] != null && dtUserdata.Rows[0]["IsActive"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsActive"]) : false;
                    objMatrixUserInfo.CompanyId = dtUserdata.Rows[0]["CompanyId"] != null && dtUserdata.Rows[0]["CompanyId"] != DBNull.Value ? Convert.ToInt32(dtUserdata.Rows[0]["CompanyId"]) : default;
                    objMatrixUserInfo.CreatedOn = dtUserdata.Rows[0]["CreatedON"] != null && dtUserdata.Rows[0]["CreatedON"] != DBNull.Value ? Convert.ToDateTime(dtUserdata.Rows[0]["CreatedON"]) : default;
                    objMatrixUserInfo.QuickView = dtUserdata.Rows[0]["QuickView"] != null && dtUserdata.Rows[0]["QuickView"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["QuickView"]) : default;
                    objMatrixUserInfo.MenuList = objDsUserdata.Tables.Count > 1 ? objDsUserdata.Tables[1] : default;
                    objMatrixUserInfo.RoleId = objDsUserdata.Tables.Count > 2 && objDsUserdata.Tables[2].Rows.Count > 0 ? objDsUserdata.Tables[2].Rows[0]["RoleId"].ToString() : default;
                    objMatrixUserInfo.GroupId = objDsUserdata.Tables.Count > 3 && objDsUserdata.Tables[3].Rows.Count > 0 ? Convert.ToInt32(objDsUserdata.Tables[3].Rows[0]["groupid"]) : default;
                    objMatrixUserInfo.GroupList = objDsUserdata.Tables.Count > 3 ? objDsUserdata.Tables[3] : default;
                    objMatrixUserInfo.UserBUMapping = objDsUserdata.Tables.Count > 6 ? objDsUserdata.Tables[6] : default;
                    objMatrixUserInfo.IsCreateLead = dtUserdata.Rows[0]["IsCreateLead"] != null && dtUserdata.Rows[0]["IsCreateLead"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsCreateLead"]) : default;
                    objMatrixUserInfo.processId = dtUserdata.Rows[0]["processId"] != null && dtUserdata.Rows[0]["processId"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["processId"]) : default;
                    objMatrixUserInfo.CampaignName = dtUserdata.Rows[0]["Campaign"] != null && dtUserdata.Rows[0]["Campaign"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["Campaign"]) : default;
                    objMatrixUserInfo.IsAsterickDialer = objDsUserdata.Tables.Count > 5 && objDsUserdata.Tables[5].Rows.Count > 0 ? Convert.ToBoolean(objDsUserdata.Tables[5].Rows[0]["IsAsterick"]) : default;
                    objMatrixUserInfo.IsLoginActive = dtUserdata.Rows[0]["IsLoginActive"] != null && dtUserdata.Rows[0]["IsLoginActive"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsLoginActive"]) : default;
                    objMatrixUserInfo.ExistSessionId = dtUserdata.Rows[0]["ExistSessionId"] != null && dtUserdata.Rows[0]["ExistSessionId"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["ExistSessionId"]) : default;
                    objMatrixUserInfo.LogInURL = dtUserdata.Rows[0]["LoginURL"] != null && dtUserdata.Rows[0]["LoginURL"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["LoginURL"]) : default;
                    objMatrixUserInfo.Grade = dtUserdata.Rows[0]["Grade"] != null && dtUserdata.Rows[0]["Grade"] != DBNull.Value ? Convert.ToInt16(dtUserdata.Rows[0]["Grade"]) : default;
                    objMatrixUserInfo.IsProgressive = dtUserdata.Rows[0]["IsProgressive"] != null && dtUserdata.Rows[0]["IsProgressive"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsProgressive"]) : false;
                    objMatrixUserInfo.IsWFH = dtUserdata.Rows[0]["IsWFH"] != null && dtUserdata.Rows[0]["IsWFH"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsWFH"]) : false;
                    objMatrixUserInfo.Asterisk_IP = dtUserdata.Rows[0]["Asterisk_IP"] != null && dtUserdata.Rows[0]["Asterisk_IP"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["Asterisk_IP"]) : null;
                    objMatrixUserInfo.Asterisk_Url = dtUserdata.Rows[0]["Asterisk_Url"] != null && dtUserdata.Rows[0]["Asterisk_Url"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["Asterisk_Url"]) : null;
                    objMatrixUserInfo.DialerPWD = dtUserdata.Rows[0]["DialerPWD"] != null && dtUserdata.Rows[0]["DialerPWD"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["DialerPWD"]) : null;
                    objMatrixUserInfo.CallingCompany = dtUserdata.Rows[0]["CallingCompany"] != null && dtUserdata.Rows[0]["CallingCompany"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["CallingCompany"]) : null;
                    objMatrixUserInfo.DIDNo = dtUserdata.Rows[0]["DIDNo"] != null && dtUserdata.Rows[0]["DIDNo"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["DIDNo"]) : null;
                    objMatrixUserInfo.ContactNo = dtUserdata.Rows[0]["ContactNo"] != null && dtUserdata.Rows[0]["ContactNo"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["ContactNo"]) : null;
                    objMatrixUserInfo.IsWebphone = dtUserdata.Rows[0]["IsWebphone"] != null && dtUserdata.Rows[0]["IsWebphone"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsWebphone"]) : false;
                    objMatrixUserInfo.IsSOSGroup = dtUserdata.Rows[0]["IsSOSGroup"] != null && dtUserdata.Rows[0]["IsSOSGroup"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsSOSGroup"]) : false;
                    objMatrixUserInfo.AvgRating = dtUserdata.Rows[0]["AvgRating"] != null && dtUserdata.Rows[0]["AvgRating"] != DBNull.Value ? Convert.ToDecimal(dtUserdata.Rows[0]["AvgRating"]) : 0;
                    objMatrixUserInfo.ResRating = dtUserdata.Rows[0]["ResRating"] != null && dtUserdata.Rows[0]["ResRating"] != DBNull.Value ? Convert.ToDecimal(dtUserdata.Rows[0]["ResRating"]) : 0;
                    objMatrixUserInfo.NorRating = dtUserdata.Rows[0]["NorRating"] != null && dtUserdata.Rows[0]["NorRating"] != DBNull.Value ? Convert.ToDecimal(dtUserdata.Rows[0]["NorRating"]) : 0;
                    objMatrixUserInfo.QualityScore = dtUserdata.Rows[0]["QualityScore"] != null && dtUserdata.Rows[0]["QualityScore"] != DBNull.Value ? Convert.ToDecimal(dtUserdata.Rows[0]["QualityScore"]) : 0;
                    objMatrixUserInfo.IssuanceScore = dtUserdata.Rows[0]["IssuanceScore"] != null && dtUserdata.Rows[0]["IssuanceScore"] != DBNull.Value ? Convert.ToDecimal(dtUserdata.Rows[0]["IssuanceScore"]) : 0;

                    objMatrixUserInfo.IsSOSAgent = dtUserdata.Rows[0]["IsSOSAgent"] != null && dtUserdata.Rows[0]["IsSOSAgent"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsSOSAgent"]) : default;
                    objMatrixUserInfo.IsNewSV = dtUserdata.Rows[0]["IsNewSV"] != null && dtUserdata.Rows[0]["IsNewSV"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsNewSV"]) : default;
                    objMatrixUserInfo.IsEnableVC = dtUserdata.Rows[0]["IsEnableVC"] != null && dtUserdata.Rows[0]["IsEnableVC"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsEnableVC"]) : default;
                    objMatrixUserInfo.IsEnableChat = dtUserdata.Rows[0]["IsEnableChat"] != null && dtUserdata.Rows[0]["IsEnableChat"] != DBNull.Value ? Convert.ToBoolean(dtUserdata.Rows[0]["IsEnableChat"]) : default;
                    objMatrixUserInfo.UserBand = dtUserdata.Rows[0]["UserBand"] != null && dtUserdata.Rows[0]["UserBand"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["UserBand"]) : default;

                    objMatrixUserInfo.Landingurl = dtUserdata.Rows[0]["Landingurl"] != null && dtUserdata.Rows[0]["Landingurl"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["Landingurl"]) : default;
                    objMatrixUserInfo.MinVoucherVal = dtUserdata.Rows[0]["MinVoucherVal"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(dtUserdata.Rows[0]["MinVoucherVal"]);
                    objMatrixUserInfo.MaxVoucherVal = dtUserdata.Rows[0]["MaxVoucherVal"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(dtUserdata.Rows[0]["MaxVoucherVal"]);
                    objMatrixUserInfo.IsVoucherEnable = dtUserdata.Rows[0]["IsVoucherEnable"] == DBNull.Value ? false : Convert.ToBoolean(dtUserdata.Rows[0]["IsVoucherEnable"]);
                    objMatrixUserInfo.ManagerName = dtUserdata.Rows[0]["ManagerName"] != null && dtUserdata.Rows[0]["ManagerName"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["ManagerName"]) : default;
                    objMatrixUserInfo.ManagerEmployeeId = dtUserdata.Rows[0]["ManagerEmployeeId"] != null && dtUserdata.Rows[0]["ManagerEmployeeId"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["ManagerEmployeeId"]) : default;

                    objMatrixUserInfo.ManagerId = Convert.ToInt64(dtUserdata.Rows[0]["ManagerId"]) == 0 ? 0 : Convert.ToInt64(dtUserdata.Rows[0]["ManagerId"]);

                    objMatrixUserInfo.PODManagerId = dtUserdata.Rows[0]["PODManagerId"] != null && dtUserdata.Rows[0]["PODManagerId"] != DBNull.Value ? Convert.ToString(dtUserdata.Rows[0]["PODManagerId"]) : default;
                    objMatrixUserInfo.IsPODUser = Convert.ToBoolean(dtUserdata.Rows[0]["IsPODUser"]);
                    objMatrixUserInfo.GroupProcessId = Convert.ToString(dtUserdata.Rows[0]["GroupProcessId"]);
                    objMatrixUserInfo.UserProcessId = Convert.ToString(dtUserdata.Rows[0]["UserProcessId"]);
                    objMatrixUserInfo.LanguageID = Convert.ToString(dtUserdata.Rows[0]["LanguageID"]);
                    objMatrixUserInfo.IsRenewal = Convert.ToInt32(dtUserdata.Rows[0]["IsRenewal"]);
                    objMatrixUserInfo.IsChat = Convert.ToInt32(dtUserdata.Rows[0]["IsChat"]);
                    objMatrixUserInfo.IsInbound = Convert.ToInt32(dtUserdata.Rows[0]["IsInbound"]);
                    objMatrixUserInfo.PasswordExpDate = Convert.ToDateTime(dtUserdata.Rows[0]["PasswordExpDate"]);
                    objMatrixUserInfo.QueueList = objDsUserdata.Tables[4];
                    objMatrixUserInfo.IsTermBHREnable = 0;
                    objMatrixUserInfo.IsInvBHREnable = 0;

                    if (!objMatrixUserInfo.RoleId.Equals("13"))
                    {
                        DataTable dt = MTXPlusLoginDLL.IsBizRatingDashboardVisible(Convert.ToInt64(objMatrixUserInfo.UserId));
                        if (dt != null && dt.Rows.Count > 0)
                        {
                            objMatrixUserInfo.IsInvBHREnable = Convert.ToInt32(dt.Rows[0]["IsInvestmentVisible"]);
                            objMatrixUserInfo.IsTermBHREnable = Convert.ToInt32(dt.Rows[0]["IsTermVisible"]);
                        }
                    }

                    if (objMatrixUserInfo.MenuList != null && objMatrixUserInfo.MenuList.Rows.Count > 0)
                    {
                        if (!objMatrixUserInfo.MenuList.Columns.Contains("IsPinnedItem"))
                            objMatrixUserInfo.MenuList.Columns.Add("IsPinnedItem", typeof(int));

                        // Use HashSet for O(1) lookup instead of List.Contains (O(n))
                        var userPinnedItems = MTXPlusLoginDLL.GetUserPinnedItems(Convert.ToInt64(objMatrixUserInfo.UserId));
                        var pinnedMenus = new HashSet<int>(userPinnedItems ?? Enumerable.Empty<int>());

                        foreach (DataRow dr in objMatrixUserInfo.MenuList.Rows)
                        {
                            int menuId = dr["MenuId"] != DBNull.Value ? Convert.ToInt32(dr["MenuId"]) : 0;
                            dr["IsPinnedItem"] = pinnedMenus.Contains(menuId) ? 1 : 0;
                        }
                    }


                    if (objMatrixUserInfo.MenuList != null)
                    {
                        objMatrixUserInfo.IsQueueAgent = objMatrixUserInfo.MenuList.Rows.Cast<DataRow>()
                            .Count(dataRow => Convert.ToInt32(dataRow["MenuId"].ToString()) == 154) > 0;
                    }

                    objMatrixUserInfo.Context = Convert.ToString(dtUserdata.Rows[0]["UserContex"]);
                    objMatrixUserInfo.Queue = Convert.ToString(dtUserdata.Rows[0]["UserQueue"]);

                    DataTable Permissiondt = objDsUserdata.Tables.Count > 7 ? objDsUserdata.Tables[7] : null;
                    if (Permissiondt != null && Permissiondt.Rows.Count > 0)
                    {
                        List<ListDTO> PermissionsList = new List<ListDTO>();
                        PermissionsList = Permissiondt.AsEnumerable().Select(row => new ListDTO { ID = Convert.ToString(row.Field<short>("FollowUpMenuID")) }).ToList();
                        if (PermissionsList != null)
                        {
                            var filterdlist = PermissionsList.Where(p => p.ID == ((int)CustomerPermissions.ConfirmPayment).ToString())
                                      .Select(p => p).FirstOrDefault();
                            if (filterdlist != null)
                            {
                                objMatrixUserInfo.IsPayment = 1;
                            }
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(TrackingID, 0, ex.ToString(), "PrepareUserModel", "MTXPlusLoginBLL", "MatrixCoreAPI", TrackingID, "", DateTime.Now, DateTime.Now);
            }
            return objMatrixUserInfo;
        }

        public Result LogoutFromMtxplus(MTXPlusLogInDTO objlogindet, HttpContext httpContext)
        {
            Result res = new Result();
            try
            {
                if (Convert.ToInt64(objlogindet.UserId) > 0 && IsAuthorisedLoggedinRequest(httpContext, objlogindet, "logout"))
                {
                    if(objlogindet.RoleID == 13 && IsAgentOnCall(objlogindet.UserId))
                    {
                        res.IsSuccess = true;
                        res.Message = "Cant logout : User already logged in and is on call";
                        res.Status = false;
                    }
                    else
                    {
                        string status = "LOGOUT";
                        string dataToPost = $"{{\"AgentCode\":\"{objlogindet.EmployeeId}\",\"IsMatrixUat\":true,\"status\":\"{status}\"}}";
                        UpdateAgentLoginStatus(objlogindet.EmployeeId, dataToPost);
                        LogOutfromDB(objlogindet.UserId, 1, objlogindet.URL, objlogindet.IPAddress, 1);
                        res.IsSuccess = true;
                        res.Message = "";
                        res.Status = true;
                    }
                }
                else
                {
                    res.IsSuccess = true;
                    res.Message = "Unauthorised request";
                    res.Status = false;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(objlogindet.UserId.ToString(), 0, ex.ToString(), "LogoutFromMtxplus", "MTXPlusLoginBLL", "MatrixCoreAPI", objlogindet.EmployeeId.ToString(), "", DateTime.Now, DateTime.Now);
                res.IsSuccess = false;
                res.Message = ex.ToString();
                res.Status = false;
            }
            return res;
        }
        public bool IsAgentOnCall(long UserID)
        {
            DateTime reqdt = DateTime.Now;
            bool res = false;
            PredictiveAgentStatus _PredictiveAgentStatus = new PredictiveAgentStatus();
            try
            {
                _PredictiveAgentStatus = PredictiveAgentStatusRedis.GetPredictiveAgents(UserID.ToString());
                if (_PredictiveAgentStatus != null)
                {
                    if (_PredictiveAgentStatus.status == "BUSY" && 
                        (Math.Abs((DateTime.Now - _PredictiveAgentStatus._updatedAt).TotalSeconds) <= 30))
                    {
                        res = true;
                    }
                }
            }
            catch(Exception ex)
            {
                res = false;
                LoggingHelper.LoggingHelper.Log(UserID.ToString(), 0, ex.ToString(), "IsAgentOnCall", "MTXPlusLoginBLL", "MatrixCoreAPI", UserID.ToString(), _PredictiveAgentStatus.ToString(), reqdt, DateTime.Now);
            }
            return res;
        }
        public static string UpdateAgentLoginStatus(string EmployeeId, string dataToPost)
        {
            string response = "";
            try
            {
                string baseUrl = "InternalMatrixApiBaseURL".AppSettings();
                string url = $"{baseUrl}onelead/api/Dialer/updateAgentLoginStatus";

                var headers = new Dictionary<object, object>
                {
                    { "source", "Matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey", "matrixAPIclientKey".AppSettings() }
                };

                int timeout = 3000;
                string contentType = "application/json";

                response = CommonAPICall.CallAPI(url, dataToPost, "POST", timeout, contentType, headers);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(EmployeeId.ToString(), 0, ex.ToString(), "UpdateAgentLoginStatus", "MTXPlusLoginBLL", "MatrixCoreAPI", dataToPost.ToString(), "", DateTime.Now, DateTime.Now);
            }
            return response;
        }
        public static void LogOutfromDB(long userId, int logOutType, string url, string ipAddress, int LoginButtonId)
        {
            try
            {
                var objLogin = new MTXPlusLogInDTO();
                objLogin.UserId = userId;
                objLogin.IsActive = false;
                objLogin.LogOutBy = userId;
                objLogin.LogOutType = logOutType;
                objLogin.URL = url;
                objLogin.IPAddress = ipAddress;
                objLogin.LoginButtonId = LoginButtonId;
                MTXPlusLoginDLL.UpdateLoginDetails(objLogin);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(userId.ToString(), 0, ex.ToString(), "LogOutfromDB", "MTXPlusLoginBLL", "MatrixCoreAPI", ipAddress.ToString(), "", DateTime.Now, DateTime.Now);
            }
        }

        public MTXPlusLoginResponse SetLoggedinUserDetails(MTXPlusLogInDTO objlogindet, string origin, HttpContext httpContext)
        {
            MTXPlusLoginResponse objres = new MTXPlusLoginResponse();
            try
            {
                if (IsAuthorisedLoggedinRequest(httpContext, objlogindet, "login"))
                {
                    objres.Data = new MatrixPlusUserInfo();
                    int ret = 0; string message = string.Empty;
                    string AsteriskToken = string.Empty;

                    objlogindet.IPAddress = GetClientIpAddress(httpContext);
                    Guid newGuid = Guid.NewGuid();
                    AsteriskToken = newGuid.ToString() + "-" + DateTime.Now.ToString("HHmmss");
                    objlogindet.SessionId = newGuid.ToString();
                    DataSet objDsUserdata = MTXPlusLoginDLL.SetLoggedinUserDetails(objlogindet, out ret, out message);
                    if (ret < 0)
                    {
                        objres.status = false;
                        objres.message = message;
                        objres.Data = null;
                    }
                    else
                    {
                        objres.status = true;
                        objres.message = "";
                        objres.Data = null;

                        // Generate JWT Token
                        var token = GenerateJwtToken(objlogindet);

                        // Redirect URL
                        string redirectUrl = "MatrixRedirectURL".AppSettings().ToString();
                        if (!string.IsNullOrEmpty(origin) && (origin.ToUpper().Contains("MOBILEMATRIX")))
                        {
                            redirectUrl = "MobileMatrixRedirectURL".AppSettings().ToString();
                        }

                        MatrixPlusUserInfo objMatrixPlusUserInfo = new MatrixPlusUserInfo();
                        objres.IsMTXPlusUser = false;

                        //Get UserDetails
                        List<string> MTXPlusUser = "MatrixPlusUsers".AppSettings().Split(',').ToList();
                        List<string> ExcludeMTXPlusUser = "ExcludeMatrixPlusUsers".AppSettings().Split(',').ToList();
                        bool IsMTXEnableForAll = (!string.IsNullOrEmpty("MtxPlusEnableForAll".AppSettings()) && "MtxPlusEnableForAll".AppSettings().Equals("true")) ? true : false;
                        if ((IsMTXEnableForAll || MTXPlusUser.Contains(objlogindet.UserId.ToString())) && (!ExcludeMTXPlusUser.Contains(objlogindet.UserId.ToString())))
                        {
                            //To Determine usage of old and new panel based on users
                            objres.IsMTXPlusUser = true;

                            DataSet objUserDet = MTXPlusLoginDLL.GetUserDetails(objlogindet.UserId);
                            if (objUserDet != null && objUserDet.Tables.Count > 0)
                            {
                                objMatrixPlusUserInfo = PrepareUserModel(objUserDet, httpContext);

                                //Safely set MatrixToken in server side cookie 
                                SetMatrixTokenCookie(httpContext, objMatrixPlusUserInfo, AsteriskToken, origin);
                            }

                            // Update user last logout time diffrence
                            MTXPlusLoginDLL.GetUpdateUserLogoutTime(objlogindet.UserId, 0, true);

                            //Insert asterisk token in redis and mark agent as IDLE
                            string requestJSON = "{\"AgentCode\":\"" + objlogindet.EmployeeId.ToUpper() + "\",\"status\":\"IDLE\",\"opensv\":0,\"TotalCalls\":0,\"TotalTalkTime\":0,\"IsProgressive\":false,\"AsteriskToken\":\"" + AsteriskToken + "\",\"AgentIP\":\"" + objlogindet.IPAddress + "\"}";
                            string UpdateAgentLoginStatusRes = UpdateAgentLoginStatus(objlogindet.EmployeeId.ToUpper(), requestJSON);
                            if (objMatrixPlusUserInfo.RoleId == "13" && UpdateAgentLoginStatusRes != "")
                            {
                                DialerAsteriskData res = (DialerAsteriskData)JsonConvert.DeserializeObject(UpdateAgentLoginStatusRes, typeof(DialerAsteriskData));
                                if (res != null && res.asteriskIp != null && res.asteriskUrl != null && res.asteriskIp != "" && res.asteriskUrl != "")
                                {
                                    objMatrixPlusUserInfo.Asterisk_IP = res.asteriskIp;
                                    objMatrixPlusUserInfo.Asterisk_Url = res.asteriskUrl;
                                }
                            }

                            //#### Update The download limit for health Agents ####
                            AddOrUpdateDocTtlLimit(objMatrixPlusUserInfo);

                            objMatrixPlusUserInfo.Token = "cG9saWN5 YmF6YWFy";
                            objMatrixPlusUserInfo.mToken = "cG9saWN5 YmF6YWFy";
                        }

                        //SetIsMTXPlusUserCookie
                        var cookieOptions = new CookieOptions
                        {
                            HttpOnly = true,
                            Secure = true,
                            Expires = DateTime.UtcNow.AddMinutes(1440),
                            Domain = ".policybazaar.com"
                        };
                        httpContext?.Response.Cookies.Append("IsMTXPlusUser", objres.IsMTXPlusUser ? "1" : "0", cookieOptions);

                        objMatrixPlusUserInfo.JWTToken = token;
                        objMatrixPlusUserInfo.RedirectionURL = redirectUrl;
                        objMatrixPlusUserInfo.AsteriskToken = AsteriskToken;
                        objres.Data = objMatrixPlusUserInfo;
                    }
                }
                else
                {
                    objres.status = false;
                    objres.message = "Unauthorised attempt to login";
                    objres.Data = null;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(objlogindet.EmployeeId.ToString(), 0, ex.ToString(), "SetLoggedinUserDetails", "MTXPlusLoginBLL", "MatrixCoreAPI", objlogindet.URL.ToString(), "", DateTime.Now, DateTime.Now);
            }

            return objres;
        }

        public bool IsAuthorisedLoggedinRequest(HttpContext httpContext, MTXPlusLogInDTO objlogindet, string requestType)
        {
            bool result = false;
            string exception = string.Empty;
            try
            {
                if (httpContext.Request.Cookies.ContainsKey("TempAuthToken"))
                {
                    string cookieValue = httpContext.Request.Cookies["TempAuthToken"];
                    if (CoreCommonMethods.IsValidString(cookieValue))
                    {
                        var decodedByteArray = Convert.FromBase64String(cookieValue);
                        var decodedJsonString = Encoding.UTF8.GetString(decodedByteArray);
                        decodedJsonString = Crypto.Decrytion_Payment_AES(decodedJsonString, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings());
                        LDAPUserDetails? userdet = !string.IsNullOrEmpty(decodedJsonString) ? JsonConvert.DeserializeObject<LDAPUserDetails>(decodedJsonString) : null;
                        DateTime LDAPAuthTime = userdet != null ? userdet.LDAPSetTime : default;
                        if ((((DateTime.Now - LDAPAuthTime).TotalMinutes <= 5)
                            && objlogindet.UserId == userdet.UserID
                            && (userdet.IsLDAPAuthorised == true)
                            && ((requestType == "logout")
                            || (userdet.IsOTPRequired == true && userdet.IsOTPVerified == true)
                            || (userdet.IsOTPRequired == false)))
                            || (objlogindet.IsInsideLogout == true)
                            )
                        {
                            result = true;
                            objlogindet.UserId = userdet.UserID;
                            objlogindet.LogInBy = userdet.UserID;
                            objlogindet.LogInType = 3;
                            objlogindet.EmployeeId = userdet.EmployeeID;
                            objlogindet.RoleID = userdet.RoleID;
                        }
                        else
                        {
                            result = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(objlogindet.UserId.ToString(), 0, ex.ToString(), "IsAuthorisedLoggedinRequest", "MTXPlusLoginBLL", "MatrixCoreAPI", objlogindet.UserId.ToString(), "", DateTime.Now, DateTime.Now);
            }
            return result;
        }
        private string GenerateJwtToken(MTXPlusLogInDTO objlogindet)
        {
            var secretKey = "LoginJWTKey".AppSettings().ToString();
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
            var signingCredentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new System.Security.Claims.ClaimsIdentity(new[]
                {
                new System.Security.Claims.Claim("UserID",objlogindet.UserId.ToString() ),
                new System.Security.Claims.Claim("EmployeeId", objlogindet.EmployeeId.ToString()),
                new System.Security.Claims.Claim("RoleID", objlogindet.RoleID.ToString())
            }),
                Expires = DateTime.UtcNow.AddMinutes(5),
                SigningCredentials = signingCredentials
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
        private void SetMatrixTokenCookie(HttpContext httpContext, MatrixPlusUserInfo userInfo, string asteriskToken, string origin)
        {
            if (httpContext == null || userInfo == null || string.IsNullOrEmpty(asteriskToken))
                return;

            var matrixTokenData = new
            {
                UserId = userInfo.UserId,
                EmployeeId = userInfo.EmployeeId,
                AsteriskToken = asteriskToken,
                GroupId = userInfo.GroupId,
                RoleId = userInfo.RoleId
            };
            SetCookie(httpContext, "MatrixToken", matrixTokenData, 1440, true); // 24 hours

            string asteriskTokenData = "token=" + asteriskToken + "&UserID=" + userInfo.UserId + "&EmployeeId=" + userInfo.EmployeeId;
            SetCookie(httpContext, "AsteriskToken", asteriskTokenData, 1080,false); // 18 hours

            var uri = new Uri(origin);
            string cdomain = uri.Host;
            SetCookie(httpContext, "cdomain", cdomain, 1440, false); // 24 hours

            var posPaymentData = new
            {
                EmployeeId = userInfo.EmployeeId.ToString(),
                GroupId = userInfo.GroupId
            };
            SetCookie(httpContext, "posPayment", posPaymentData, 480, true); // 8 hours

            string UserToken = "UserID=" + userInfo.UserId + "&EmployeeId=" + userInfo.EmployeeId;
            SetCookie(httpContext, "UserToken", UserToken, 1440,false); // 24 hours
        }

        private void SetCookie(HttpContext httpContext, string cookieName, object cookieData, int expiryInMinutes, bool isEncode)
        {
            string value = cookieData is string str ? str : JsonConvert.SerializeObject(cookieData);
            if (isEncode)
            {
                value = Convert.ToBase64String(Encoding.UTF8.GetBytes(value));
            }
            string cookieHeader = $"{cookieName}={value}; Domain=.policybazaar.com; Path=/; HttpOnly; Secure; Expires={DateTime.UtcNow.AddMinutes(expiryInMinutes):R};";
            httpContext.Response.Headers.Append("Set-Cookie", cookieHeader);
        }
        public MTXPlusLoginResponse GetUserDetails(long UserID, HttpContext httpContext)
        {
            MTXPlusLoginResponse objres = new MTXPlusLoginResponse();
            try
            {
                objres.Data = new MatrixPlusUserInfo();
                DataSet objDsUserdata = MTXPlusLoginDLL.GetUserDetails(UserID);
                if (objDsUserdata != null && objDsUserdata.Tables.Count > 0)
                {
                    objres.Data = PrepareUserModel(objDsUserdata, httpContext);
                    objres.status = true;

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(UserID.ToString(), 0, ex.ToString(), "GetUserDetails", "MTXPlusLoginBLL", "MatrixCoreAPI", UserID.ToString(), "", DateTime.Now, DateTime.Now);
            }

            return objres;
        }


        public List<PreLoginSurvey> GetSurveyByLocation()
        {
            List<PreLoginSurvey> result = new List<PreLoginSurvey>();
            try
            {

                DataSet ds = MTXPlusLoginDLL.GetSurveyByLocation("login");
                if (ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                    {
                        var row = new PreLoginSurvey
                        {
                            SurveyHtmlUrl = ds.Tables[0].Rows[i]["SurveyHtmlUrl"].ToString(),
                            ContentType = Convert.ToByte(ds.Tables[0].Rows[i]["ContentType"])
                        };
                        result.Add(row);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("login", 0, ex.ToString(), "GetSurveyByLocation", "MTXPlusLoginBLL", "MatrixCoreAPI", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }

            return result;
        }
        public static void AddOrUpdateDocTtlLimit(MatrixPlusUserInfo objMatrixPlusUserInfo)
        {
            HealthRenewalBLL obj = new HealthRenewalBLL();
            try
            {
                if (objMatrixPlusUserInfo.GroupList != null && objMatrixPlusUserInfo.GroupList.Rows.Count > 0)
                {
                    DataTable dtNewt = objMatrixPlusUserInfo.GroupList;
                    var distinctProducts = dtNewt.AsEnumerable()
                                                 .Select(row => new
                                                 {
                                                     ProductId = row["ProductId"].ToString(),
                                                     ProductName = row["ProductName"].ToString()
                                                 })
                                                 .Distinct();

                    // Retrieve configuration settings once
                    int aadharLimit = Convert.ToInt32("aadhar_m_limit".AppSettings());
                    int polCopyLimit = Convert.ToInt32("polCopy_m_limit".AppSettings());
                    int endorsementLimit = Convert.ToInt32("endorsement_m_limit".AppSettings());
                    int othersLimit = Convert.ToInt32("others_m_limit".AppSettings());

                    if (distinctProducts.Any())
                    {
                        {
                            foreach (var product in distinctProducts)
                            {
                                if ((objMatrixPlusUserInfo.RoleId == "13" || objMatrixPlusUserInfo.RoleId == "12") && product.ProductId == "2")
                                {
                                    AddOrUpdateDocTtlLimit addOrUpdateDocTtlLimit = new AddOrUpdateDocTtlLimit
                                    {
                                        agentId = objMatrixPlusUserInfo.UserId,
                                        aadhar_m_limit = aadharLimit,
                                        polCopy_m_limit = polCopyLimit,
                                        endorsement_m_limit = endorsementLimit,
                                        others_m_limit = othersLimit
                                    };
                                    obj.AddOrUpdateDocTtlLimit(addOrUpdateDocTtlLimit);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(objMatrixPlusUserInfo.UserId.ToString(), 0, ex.ToString(), "AddOrUpdateDocTtlLimit", "MTXPlusLoginBLL", "MatrixCoreAPI", objMatrixPlusUserInfo.ToString(), string.Empty, DateTime.Now, DateTime.Now);
            }
        }

        public static string GetClientIpAddress(HttpContext context)
        {
            try
            {
                string ipAddress = null;

                // Check X-Forwarded-For first (used behind proxies/load balancers)
                if (context.Request.Headers.ContainsKey("X-Forwarded-For"))
                {
                    var forwardedIps = context.Request.Headers["X-Forwarded-For"].ToString();
                    if (!string.IsNullOrWhiteSpace(forwardedIps))
                    {
                        // Usually comma-separated list: client, proxy1, proxy2...
                        var firstIp = forwardedIps.Split(',')[0].Trim();
                        if (IPAddress.TryParse(firstIp, out var parsedIp) && parsedIp.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                        {
                            return parsedIp.ToString(); // return if IPv4
                        }
                    }
                }

                // Fall back to direct connection IP
                var remoteIp = context.Connection.RemoteIpAddress;
                if (remoteIp != null && remoteIp.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork)
                {
                    return remoteIp.ToString(); // return IPv4
                }

                // If it's IPv6-mapped IPv4, convert it
                if (remoteIp != null && remoteIp.IsIPv4MappedToIPv6)
                {
                    return remoteIp.MapToIPv4().ToString();
                }

                return remoteIp?.ToString(); // fallback
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", 0, ex.ToString(), "GetClientIpAddress", "MTXPlusLoginBLL", "MatrixCoreAPI", "", "", DateTime.Now, DateTime.Now);
                return string.Empty;
            }
        }


        public ResponseData<List<PreLoginSurvey>> GetPreLoginSurveyRequirement(long userId)
        {
            ResponseData<List<PreLoginSurvey>> response = new ResponseData<List<PreLoginSurvey>>()
            {
                Status = false,
                Message = "",
                Data = new List<PreLoginSurvey>()
            };
            DateTime reqTime = DateTime.Now;

            try
            {
                DataSet ds = MTXPlusLoginDLL.GetPreLoginSurveyRequirement(userId);
                if(ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach(DataRow row in ds.Tables[0].Rows)
                    {
                        var item = new PreLoginSurvey
                        {
                            SurveyName = row["SurveyName"].ToString(),
                            SurveyHtmlUrl = row["SurveyHtmlUrl"].ToString(),
                            ButtonText = row["ButtonText"].ToString(),
                            SurveyId = Convert.ToInt32(row["SurveyId"].ToString()),
                            ContentType = Convert.ToByte(row["ContentType"]),
                            Link = row["Link"].ToString(),
                        };
                        response.Data.Add(item);
                    }
                    response.Status = true;
                    response.Message = "Success!";
                }
                else
                {
                    response.Status = true;
                    response.Message = "No Data Found!";
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", userId, ex.ToString(), "GetPreLoginSurveyRequirement", "MTXPlusLoginBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(response), string.Empty, reqTime, DateTime.Now);
                response.Status = false;
                response.Message = ex.Message;
            }

            return response;
        }

        public void InsertEmployeeSurvey(PreLoginSurveyResult obj)
        {
            DateTime reqTime = DateTime.Now;

            try
            {
                if(obj.UserId > 0)
                {
                    MTXPlusLoginDLL.InsertEmployeeSurvey(obj);
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", obj.UserId, ex.ToString(), "InsertEmployeeSurvey", "MTXPlusLoginBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(obj), string.Empty, reqTime, DateTime.Now);
            }
        }
        public void UpdateUserPinnedMenu(long userId, int menuId, bool isPin)
        {
            DateTime reqTime = DateTime.Now;

            try
            {
                if (userId > 0 && menuId > 0)
                {
                    MTXPlusLoginDLL.UpdateUserPinnedMenu(userId, menuId, isPin);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", userId, ex.ToString(), "UpdateUserPinnedMenu", "MTXPlusLoginBLL", "MatrixCoreAPI", string.Empty, string.Empty, reqTime, DateTime.Now);
            }
            
        }
        public bool VerifyToken(string UserId, string token)
        {
            string url = "InternalVerifyToken".AppSettings() + "customer/verifytoken/";
            int timeout = Convert.ToInt32("DialerAPITimeout".AppSettings());
            string dataToPost = "{\"userid\":\"" + UserId + "\",\"token\":\"" + token + "\"}";

            var data = CommonAPICall.CallAPI(url, dataToPost, "POST", timeout, "application/json", null);
            if (data != null && data == "true")
                return true;
            else
                return false;
        }

    }

}