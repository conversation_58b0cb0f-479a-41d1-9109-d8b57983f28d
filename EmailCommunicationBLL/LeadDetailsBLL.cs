﻿using DataAccessLayer;
using DataAccessLibrary;
using Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using Microsoft.IdentityModel.Tokens;
using Microsoft.VisualBasic;
using MongoConfigProject;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PropertyLayers;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace EmailCommunicationBLL
{
    public class LeadDetailsBLL : ILeadDetailsBLL
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        public Response PushRenewalLeadToCJ(string LeadId)
        {
            Response result = new Response();
            try
            {
                if (CoreCommonMethods.IsValidString(LeadId))
                {
                    DataSet oDataSet = LeadDetailsDLL.GetRenewalLeadDetails(Convert.ToInt64(LeadId));
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        DataRow row = oDataSet.Tables[0].Rows[0];
                        result = SyncRenewalLeadToCJ(Convert.ToInt64(LeadId), row);
                        if (result.status)
                        {
                            LeadDetailsDLL.UpdateRenewalStatus(row["RenewalID"].ToString(), 5, null);
                        }
                        else
                        {
                            if (result.message == "Timeout expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.")
                                LeadDetailsDLL.UpdateRenewalStatus(row["RenewalID"].ToString(), 6, result.message);
                            else
                                LeadDetailsDLL.UpdateRenewalStatus(row["RenewalID"].ToString(), 4, result.message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in PushRenewalLeadToCJ." + ex.ToString());
            }
            return result;
        }

        public Response SyncRenewalLeadToCJ(long leadId, DataRow renewDetails)
        {
            string strexception = string.Empty;
            string requestJson = string.Empty;
            string responseJson = string.Empty;
            Response returnResult = new Response();
            DateTime start = DateTime.Now;
            try
            {

                // RenewalLeadToCJ healthPush = new RenewalLeadToCJ();

                var healthPush = new RenewalLeadToCJ
                {
                    AddOnBasePremium = renewDetails["AddOnBasePremium"].ToString(),
                    AddOnProduct = renewDetails["AddOnProduct"].ToString(),
                    AddOnUPSellPremium = renewDetails["AddOnUPSellPremium"].ToString(),
                    BaseSI = renewDetails["SumInsured"].ToString(),
                    BaseSIPremium = renewDetails["NoticePremium"].ToString(),
                    BatchID = renewDetails["BatchID"].ToString(),
                    CustomerId = renewDetails["CustomerId"].ToString(),
                    Email = renewDetails["EmailID"].ToString(),
                    FamilyType = renewDetails["SupplierId"].ToString() == "18" ? "0" : renewDetails["FamilyType"].ToString(),
                    MobileNo = renewDetails["MobileNo"].ToString(),
                    PlanID = renewDetails["PlanID"].ToString(),
                    PlanName = renewDetails["PlanName"].ToString(),
                    PlanTerm = renewDetails["PlanTerm"].ToString(),
                    PolicyEndDate = Convert.ToDateTime(renewDetails["PolicyExpiryDate"].ToString()).ToString("dd-MM-yyyy"),
                    PolicyNo = renewDetails["OldPolicyNo"].ToString(),
                    Product = renewDetails["ProductName"].ToString(),
                    ProductID = renewDetails["ProductID"].ToString().ToSafeInt(),
                    ProposerDOB = renewDetails["DOB"].ToString(),
                    ProposerName = renewDetails["ProposerName"].ToString(),
                    OneYearPremium = Renewal.FormatPremium(renewDetails["OneYearPremium"].ToString()),
                    OneYearQuote = Renewal.FormatQuote(renewDetails["OneYearQuote"].ToString()),
                    TwoYearPremium = Renewal.FormatPremium(renewDetails["TwoYearPremium"].ToString()),
                    TwoYearQuote = Renewal.FormatQuote(renewDetails["TwoYearQuote"].ToString()),
                    ThreeYearPremium = Renewal.FormatPremium(renewDetails["ThreeYearPremium"].ToString()),
                    ThreeYearQuote = Renewal.FormatQuote(renewDetails["ThreeYearQuote"].ToString()),
                    UPSellSI = renewDetails["UPSellSI"].ToString(),
                    UPSellSIPremium = renewDetails["UPSellSIPremium"].ToString(),
                    MatrixLeadID = Convert.ToInt64(renewDetails["LeadID"].ToString()),
                    UpsellOneYearPremium = Renewal.FormatPremium(renewDetails["UpsellOneYearPremium"].ToString()),
                    UpsellOneYearQuote = Renewal.FormatQuote(renewDetails["UpsellOneYearQuote"].ToString()),
                    UpsellTwoYearPremium = Renewal.FormatPremium(renewDetails["UpsellTwoYearPremium"].ToString()),
                    UpsellTwoYearQuote = Renewal.FormatQuote(renewDetails["UpsellTwoYearQuote"].ToString()),
                    UpsellThreeYearPremium = Renewal.FormatPremium(renewDetails["UpsellThreeYearPremium"].ToString()),
                    UpsellThreeYearQuote = Renewal.FormatQuote(renewDetails["UpsellThreeYearQuote"].ToString()),
                    ProductCode = renewDetails["ProductCode"].ToString(),
                    MasterPolicyNo = renewDetails["MasterPolicyNo"].ToString(),
                    FreeHealthCheckup = renewDetails["FreeHealthCheckup"].ToString(),
                    NoClaimBonus = Convert.ToDecimal(renewDetails["NCB"].ToString()),
                    RenewalYear = Convert.ToInt32(renewDetails["RenewalYear"].ToString()),
                    IsPreviousClaimsTaken = renewDetails["IsPreviousClaimsTaken"].ToString().ToSafeBool(),
                    SpecificDiseaseWaitingPeriod = Convert.ToInt32(renewDetails["SpecificDiseaseWaitingPeriod"].ToString()),
                    PreExistingDiseaseWaitingPeriod = Convert.ToInt32(renewDetails["PreExistingDiseaseWaitingPeriod"].ToString()),
                    AdditionalBenefits = renewDetails["AdditionalBenefits"].ToString(),
                    Wellnesspoint = renewDetails["Wellnesspoint"].ToString().ToSafeInt(),
                    PremiumInflation = string.IsNullOrEmpty(renewDetails["PremiumInflation"].ToString()) ? default : Convert.ToDecimal(renewDetails["PremiumInflation"]),
                    OldBookingId = renewDetails["OldBookingId"].ToString().ToSafeLong(),
                    MonthlyMode = renewDetails["MonthlyMode"].ToString(),
                    ACH = renewDetails["ACH"].ToString(),
                    Remarks = renewDetails["Remarks"].ToString(),
                    SupplierID = renewDetails["SupplierID"].ToString(),
                    Utmsource = renewDetails["Utm_source"].ToString(),

                    CityID = renewDetails["CityID"].ToString().ToSafeInt(),
                    ZipCode = renewDetails["PostCode"].ToString().ToSafeInt(),
                    PremiumWaivedOff = string.IsNullOrEmpty(renewDetails["PremiumWaivedOff"].ToString()) ? default : Convert.ToInt16(renewDetails["PremiumWaivedOff"]),
                    LoadingPercentage = string.IsNullOrEmpty(renewDetails["LoadingPercentage"].ToString()) ? default : Convert.ToInt16(renewDetails["LoadingPercentage"]),
                    IsLoading = !string.IsNullOrEmpty(renewDetails["IsLoading"].ToString()) && Convert.ToBoolean(renewDetails["IsLoading"]),
                    LoadingPremium = string.IsNullOrEmpty(renewDetails["LoadingPremium"].ToString()) ? default : Convert.ToDecimal(renewDetails["LoadingPremium"]),
                    LoadingReason = renewDetails["LoadingReason"].ToString(),
                    WasPort = renewDetails["WasPort"].ToString().ToSafeBool(),
                    IsHealthRenewalFOS = renewDetails["IsHealthRenewalFOS"].ToString().ToSafeBool(),
                    EnrollmentDate = renewDetails["EnrollmentDate"].ToString(),
                    SubStatusCode = renewDetails["SubStatusCode"].ToString(),
                    Country = Convert.ToInt32(renewDetails["Country"]),
                    PEDInfo = renewDetails["PEDInfo"].ToString(),
                    STPStatus = renewDetails["STPStatus"].ToString(),
                    NSTPReason = renewDetails["NSTPReason"].ToString(),
                    UpsellOffer = !string.IsNullOrEmpty(renewDetails["UpsellOffer"].ToString()) && Convert.ToBoolean(renewDetails["UpsellOffer"]),
                    OfferDetails = renewDetails["OfferDetails"].ToString(),
                    DirectLastYear = !string.IsNullOrEmpty(renewDetails["DirectLastYear"].ToString()) && Convert.ToBoolean(renewDetails["DirectLastYear"])
                };


                if (!string.IsNullOrEmpty(renewDetails["PolicyInceptionDate"].ToString()))
                {
                    healthPush.PolicyInceptionDate = Convert.ToDateTime(renewDetails["PolicyInceptionDate"].ToString()).ToString("dd-MM-yyyy");
                }

                requestJson = JsonConvert.SerializeObject(healthPush);
                string url = "RenewalLeadToCJ".AppSettings();

                Dictionary<object, object> header = new Dictionary<object, object>
                {
                    { "authkey", "CJAuthToken".AppSettings() }
                };
                responseJson = CommonAPICall.CallAPI(url, requestJson, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                var resultObj = JsonConvert.DeserializeObject<KVPair>(responseJson);
                returnResult.LeadId = leadId.ToString();
                if (resultObj.Value == "Uploaded")
                {
                    returnResult.status = true;
                }
                else
                {
                    returnResult.message = resultObj.Value;
                }
            }
            catch (Exception ex)
            {
                returnResult.message = ex.ToString();
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(leadId.ToString(), leadId, strexception, "SyncToCJ", "LeadDetailsBLL", "Matrixcore", requestJson, responseJson, start, DateTime.Now);
            }
            return returnResult;
        }


        public Response GetHealthRenewalLead(GetRenewalLead objRenewalLead)
        {

            Response result = new Response();
            try
            {
                if (objRenewalLead.PolicyNo == null && objRenewalLead.CustomerId == null)
                {

                    result.LeadId = "None";
                    result.message = "Failed";
                    result.status = false;

                    return result;
                }

                objRenewalLead.CustomerId = (objRenewalLead.CustomerId == null) ? "0" : objRenewalLead.CustomerId;

                objRenewalLead.PolicyNo = (objRenewalLead.PolicyNo == null) ? "" : objRenewalLead.PolicyNo;

                DataSet oDataSet = LeadDetailsDLL.GetHealthRenewalLead(Convert.ToInt64(objRenewalLead.CustomerId), objRenewalLead.PolicyNo);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    result.LeadId = oDataSet.Tables[0].Rows[0]["LeadID"].ToString();
                    result.message = "Success";
                    result.status = true;

                }
                else
                {
                    result.LeadId = "None";
                    result.message = "Failed";
                    result.status = false;
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetHealthRenewalLead." + ex.ToString());
            }
            return result;
        }

        public string checkLeadAssignmentDetails(string LeadId)
        {
            string AgentId = string.Empty;
            try
            {
                if (CoreCommonMethods.IsValidString(LeadId))
                {
                    DataSet oDataSet = LeadDetailsDLL.checkLeadAssignmentDetails(Convert.ToInt64(LeadId));

                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        AgentId = oDataSet.Tables[0].Rows[0]["AssignedToUserID"].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in checkLeadAssignmentDetails." + ex.ToString());
            }
            return AgentId;
        }

        public CustomerBookingDocumentDetails GetCustomerBookingDocumentURL(string leadId, string userId)
        {
            var customerBookingDocumentDetails = new CustomerBookingDocumentDetails();
            string Error = string.Empty;
            DateTime requesttime = DateTime.Now;
            try
            {
                if (string.IsNullOrEmpty(leadId))
                {
                    customerBookingDocumentDetails.error = "InvalidLeadId";
                }
                else
                {
                    customerBookingDocumentDetails.LeadId = leadId;
                    DataSet dataSet = LeadDetailsDLL.GetCustomerBookingDocumentURL(Convert.ToInt64(leadId), Convert.ToInt64(userId));

                    if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                    {
                        string url = "BmsCromaURL".AppSettings().ToString() + "api/CromaServices/GetBasicPolicyInfo/" + Convert.ToInt64(leadId);
                        Dictionary<object, object> _Dict = new Dictionary<object, object>();
                        _Dict.Add("source", "matrix");
                        _Dict.Add("authKey", "PBCromaAuthkey".AppSettings());
                        _Dict.Add("clientKey", "PBCromaclientkey".AppSettings());

                        var data = CommonAPICall.CallAPI(url, "", "GET", 3000, "application/json", _Dict);
                        if (!string.IsNullOrEmpty(data))
                        {
                            BasicPolicyInfo obj = JsonConvert.DeserializeObject<BasicPolicyInfo>(data);
                            if (obj != null && !string.IsNullOrEmpty(obj.DocUploadId) && Convert.ToInt64(obj.CustomerId) > 0)
                            {
                                var request = new DocumentUrlRequest
                                {
                                    DocumentId = obj.DocUploadId.ToString(),
                                    CustomerId = obj.CustomerId.ToString(),
                                    DocumentTypeId = 56
                                };
                                var response = GetDocumentUrl(request);

                                if (response != null)
                                {
                                    if (!string.IsNullOrEmpty(response.DocumentPath))
                                        customerBookingDocumentDetails.DocumentURL = response.DocumentPath;
                                    else
                                        customerBookingDocumentDetails.error = response.StatusMessage;
                                }
                                else
                                {
                                    customerBookingDocumentDetails.error = "Empty GetDocumentUrl Response.";
                                }
                            }
                            else
                            {
                                customerBookingDocumentDetails.error = "Invalid DocUploadId";
                            }
                        }
                        else
                        {
                            customerBookingDocumentDetails.error = "No Result";
                        }
                    }
                    else
                    {
                        customerBookingDocumentDetails.error = "No Result";
                    }
                }
            }
            catch (Exception ex)
            {
                //Do nothing
                Error = Convert.ToString(ex);
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(leadId, Convert.ToInt64(leadId), Error,
                                               "GetCustomerBookingDocumentURL", "LeadDetailsBLL",
                                               "MatrixCore", leadId, Convert.ToString(customerBookingDocumentDetails),
                                               requesttime, DateTime.Now);
            }
            return customerBookingDocumentDetails;
        }
        public string GetAgentCSATScore(long userId)
        {

            string result = string.Empty;
            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetAgentCSATScore(userId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    result = CoreCommonMethods.DataTableToJSONWithJSONNet(oDataSet.Tables[0]);
                }
                else
                {
                    DataTable dt = new DataTable();
                    result = CoreCommonMethods.DataTableToJSONWithJSONNet(dt);
                }
            }
            catch (Exception ex)
            {
                //  Console.WriteLine("Exception in GetAgentCSATScore." + ex.ToString());
            }
            return result;
        }

        public Response GetRenewalleadByPolicyNo(string PolicyNo)
        {
            string policy = string.Empty;
            Response result = new Response();
            string strexception = string.Empty;
            try
            {
                if (CoreCommonMethods.IsValidString(PolicyNo))
                {
                    policy = WebUtility.UrlDecode(PolicyNo);
                    DataSet oDataSet = LeadDetailsDLL.GetRenewalleadByPolicyNo(policy);
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        result.LeadId = oDataSet.Tables[0].Rows[0]["LeadID"].ToString();
                        result.message = "Success";
                        result.status = true;

                    }
                    else
                    {
                        result.LeadId = "None";
                        result.message = "Failed";
                        result.status = false;
                    }
                }

            }
            catch (Exception ex)
            {
                //  Console.WriteLine("Exception in GetRenewalleadByPolicyNo." + ex.ToString());
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(policy, 0, strexception, "GetRenewalleadByPolicyNo", "LeadDetailsBLL", result.message, result.LeadId, result.status.ToString(), DateTime.Now, DateTime.Now);

            }
            return result;
        }


        public string GetbmsLink(long BookingID, long UserId, string Source, HttpContext httpContext, string Token, string origin, string ClientIPAddr, out string BMSAuthToken)
        {
            string url = string.Empty;
            string Response = string.Empty;
            StringBuilder sb = new StringBuilder();
            sb.Append("enter in API " + "\r\n");
            string api = "bmsServiceurl".AppSettings();
            bool IsInternalNetwork = true;
            string AsteriskToken = string.Empty;
            string content = "";

            string strexception = string.Empty;
            BMSAuthToken = string.Empty;
            try
            {
                //if (IsValidOrigin(origin) && !string.IsNullOrEmpty(ClientIPAddr))
                //    IsInternalNetwork = IsIntrnalIP(ClientIPAddr);
                if (IsValidOrigin(origin))
                    IsInternalNetwork = false;

                sb.Append("origin : " + origin + "\r\n");
                sb.Append("IsInternalNetwork : " + IsInternalNetwork + "\r\n");
                sb.Append(" ClientIPAddr: " + ClientIPAddr + "\r\n");

                if (httpContext.Request.Cookies.ContainsKey("MatrixToken"))
                {
                    string cookieValue = httpContext.Request.Cookies["MatrixToken"];
                    if (CoreCommonMethods.IsValidString(cookieValue))
                    {
                        byte[] data = Convert.FromBase64String(cookieValue);
                        string jsonString = System.Text.Encoding.UTF8.GetString(data);
                        User? user = !string.IsNullOrEmpty(jsonString) ? JsonConvert.DeserializeObject<User>(jsonString) : null;
                        AsteriskToken = user != null ? user.AsteriskToken : string.Empty;
                    }
                }
                AsteriskToken = (string.IsNullOrEmpty(AsteriskToken) && !string.IsNullOrEmpty(Token)) ? Token : AsteriskToken;

                if (!string.IsNullOrEmpty(AsteriskToken))
                {
                    Dictionary<object, object> header = new Dictionary<object, object>();
                    header.Add("AppId", "Matrix");
                    header.Add("AppKey", "bmsAppKey".AppSettings());
                    header.Add("AsteriskToken", AsteriskToken.ToString());

                    content = "{\"UserId\":" + UserId + ",\"LeadId\":" + BookingID + ",\"IsInternalNetwork\":" + (IsInternalNetwork ? 1 : 0) + ",\"Source\":\"" + Source + "\"}";
                    var result = CommonAPICall.CallAPI(api, content, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                    sb.Append("apiurl: " + api + "\r\n");
                    sb.Append(" result :" + JsonConvert.SerializeObject(result) + "\r\n");
                    if (!string.IsNullOrEmpty(result))
                    {

                        BmsLoginUrl res = Newtonsoft.Json.JsonConvert.DeserializeObject<BmsLoginUrl>(result);
                        if (res != null && res.isSuccess)
                        {
                            BMSAuthToken = res.BMSAuthToken;
                            Response = res.RedirectUrl;
                        }
                    }
                }
                else
                {
                    LoggingHelper.LoggingHelper.AddloginQueue(null, BookingID, "Unable to find AsteriskToken", "GetbmsLinkAsteriskToken", "MatrixCore", "LeadDetailsBLL", "", sb.ToString(), DateTime.Now, DateTime.Now);
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, BookingID, strexception, "GetbmsLink", "MatrixCore", "LeadDetailsBLL", content, sb.ToString(), DateTime.Now, DateTime.Now);

            }
            return Response;
        }

        public bool IsUserKYAEligible(string Empcode)
        {
            string result = string.Empty;
            string strexception = string.Empty;
            bool response = false;
            DateTime dt = DateTime.Now;


            try
            {
                if (CoreCommonMethods.IsValidString(Empcode))
                {
                    DataSet oDataSet = LeadDetailsDLL.IsUserKYAEligible(Empcode);

                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        response = true;
                    }
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                // Console.WriteLine("Exception in IsKYAEligible." + ex.ToString());
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(Empcode, 0, strexception, "IsKYAEligible", "MatrixCore", response.ToString(), "", "", dt, DateTime.Now);

            }
            return response;
        }

        public LeadInfo GetLeadStatusDetails(string LeadId)
        {
            LeadInfo objLeadStatusDetails = new LeadInfo();
            string strexception = string.Empty;

            if (LeadId == null)
            {
                objLeadStatusDetails.SearchedLeadId = null;
                objLeadStatusDetails.StatusId = 0;
                objLeadStatusDetails.LeadStatus = null;
                objLeadStatusDetails.AssignToGroupId = 0;
                objLeadStatusDetails.error = "InvalidLeadId";

                return objLeadStatusDetails;
            }
            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetLeadStatusDetails(Convert.ToInt64(LeadId));
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {

                    objLeadStatusDetails.SearchedLeadId = LeadId;
                    objLeadStatusDetails.StatusId = oDataSet.Tables[0].Rows[0]["StatusID"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["StatusID"]);
                    objLeadStatusDetails.LeadStatus = oDataSet.Tables[0].Rows[0]["StatusName"] == DBNull.Value ? "" : oDataSet.Tables[0].Rows[0]["StatusName"].ToString();
                    objLeadStatusDetails.AssignToGroupId = oDataSet.Tables[0].Rows[0]["AssignToGroupId"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["AssignToGroupId"]);
                    objLeadStatusDetails.error = "";
                }
                else
                {
                    objLeadStatusDetails.SearchedLeadId = LeadId;
                    objLeadStatusDetails.StatusId = 0;
                    objLeadStatusDetails.LeadStatus = null;
                    objLeadStatusDetails.AssignToGroupId = 0;
                    objLeadStatusDetails.error = "LeadIdNotFound";

                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(LeadId, 0, strexception, "GetLeadStatusDetails", "MatrixCore", objLeadStatusDetails.LeadStatus, objLeadStatusDetails.error, "", DateTime.Now, DateTime.Now);

            }
            return objLeadStatusDetails;
        }
        public List<CityModal> GetCityList()
        {
            string result = string.Empty;
            List<CityModal> response = new List<CityModal>();
            try
            {
                //string Key = RedisCollection.CityList();
                //string obj = RedisHelper.GetRedisData(Key);
                string obj = null;
                if (obj == null)
                {
                    DataSet oDataSet = LeadDetailsDLL.GetCityList();
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in oDataSet.Tables[0].Rows)
                        {
                            CityModal ores = new CityModal();
                            ores.CityId = row["CityId"] == DBNull.Value ? 0 : Convert.ToInt32(row["CityId"]);
                            ores.city = row["CityName"] == DBNull.Value ? "" : Convert.ToString(row["CityName"]);
                            ores.DisplayName = row["DisplayName"] == DBNull.Value ? "" : Convert.ToString(row["DisplayName"]);
                            ores.StateId = row["StateId"] == DBNull.Value ? 0 : Convert.ToInt32(row["StateId"]);
                            ores.State = row["StateName"] == DBNull.Value ? "" : Convert.ToString(row["StateName"]);
                            response.Add(ores);
                        }
                    }
                    //if (response.Count > 0)
                    //{
                    //    RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(response), new TimeSpan(0, 8, 0, 0));
                    //}
                }

                //else
                //{
                //    response = JsonConvert.DeserializeObject<List<CityModal>>(obj);
                //}
                return response;
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in GetCityList." + ex.ToString());
                return response;
            }
        }


        public List<SubStatusModal> GetSubStatusDetails()
        {
            string result = string.Empty;
            List<SubStatusModal> lstCity = new List<SubStatusModal>();
            try
            {
                // string Key = RedisCollection.substatusList();
                //string obj = RedisHelper.GetRedisData(Key);
                string obj = null;
                if (obj == null)
                {
                    DataSet oDataSet = LeadDetailsDLL.GetSubStatusDetails();
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in oDataSet.Tables[0].Rows)
                        {
                            SubStatusModal ores = new SubStatusModal
                            {
                                SubStatusID = row["SubStatusID"] == DBNull.Value ? 0 : Convert.ToInt32(row["SubStatusID"]),
                                SubStatusName = row["SubStatusName"] == DBNull.Value ? "" : Convert.ToString(row["SubStatusName"]),
                                ShortSubStatus = row["ShortSubStatus"] == DBNull.Value ? "" : Convert.ToString(row["ShortSubStatus"])
                            };

                            lstCity.Add(ores);

                        }
                    }
                    //if (lstCity.Count > 0)
                    //    RedisHelper.SetRedisData(Key, JsonConvert.SerializeObject(lstCity), new TimeSpan(0, 8, 0, 0));

                }
                //else
                //{
                //    lstCity = JsonConvert.DeserializeObject<List<SubStatusModal>>(obj);
                //}

                return lstCity;
            }
            catch (Exception ex)
            {
                //  Console.WriteLine("Exception in GetSubStatusDetails." + ex.ToString());
                return lstCity;
            }
        }

        public LeadInfo GetRenewalLeaddetails(string BookingId)
        {
            LeadInfo objRenewalLeaddetails = new LeadInfo();
            string strexception = string.Empty;

            if (BookingId == null)
            {
                objRenewalLeaddetails.LeadID = 0;
                objRenewalLeaddetails.ParentLeadId = 0;
                objRenewalLeaddetails.AssignedToUserId = 0;
                objRenewalLeaddetails.EmployeeId = "";
                objRenewalLeaddetails.StatusId = 0;
                objRenewalLeaddetails.message = "Invalid Booking Id";

                return objRenewalLeaddetails;
            }
            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetRenewalLeadDetailsforBookingId(Convert.ToInt64(BookingId));
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {

                    objRenewalLeaddetails.LeadID = Convert.ToInt64(oDataSet.Tables[0].Rows[0]["LeadId"]);
                    objRenewalLeaddetails.ParentLeadId = Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentleadId"]);
                    objRenewalLeaddetails.AssignedToUserId = Convert.ToInt32(oDataSet.Tables[0].Rows[0]["AssignToUserID"]);
                    objRenewalLeaddetails.EmployeeId = oDataSet.Tables[0].Rows[0]["EmployeeId"].ToString();
                    objRenewalLeaddetails.StatusId = Convert.ToInt32(oDataSet.Tables[0].Rows[0]["LeadStatusId"]);
                    objRenewalLeaddetails.message = "Success";

                }
                else
                {
                    objRenewalLeaddetails.LeadID = 0;
                    objRenewalLeaddetails.ParentLeadId = 0;
                    objRenewalLeaddetails.AssignedToUserId = 0;
                    objRenewalLeaddetails.EmployeeId = "";
                    objRenewalLeaddetails.StatusId = 0;
                    objRenewalLeaddetails.message = "Booking Id not found";

                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(objRenewalLeaddetails.LeadID.ToString(), Convert.ToInt64(BookingId), strexception, "GetRenewalLeaddetails", "MatrixCore", objRenewalLeaddetails.EmployeeId, objRenewalLeaddetails.message, "", DateTime.Now, DateTime.Now);

            }
            return objRenewalLeaddetails;
        }


        public bool DkdLottery(string UserId)
        {
            string result = string.Empty;
            string strexception = string.Empty;
            bool response = false;
            DateTime dt = DateTime.Now;


            try
            {
                string Quizid = LeadDetailsDLL.Quizid(UserId);

                //DataSet oDataSet = LeadDetailsDLL.DkdLottery(UserId);

                var quiz = Convert.ToInt32(Quizid);
                if (quiz == 0)
                {
                    return false;
                }
                DataSet oDataSet = LeadDetailsDLL.DkdLottery(Convert.ToInt32(Quizid));

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    response = true;
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();

                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), 0, strexception, "DkdLottery", "MatrixCore", response.ToString(), "", "", dt, DateTime.Now);
            }
            return response;
        }

        public List<GroupList> GetUniqueGroups(string UserId, string ProductId)
        {
            string result = string.Empty;
            List<GroupList> lstGroup = new List<GroupList>();
            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetUniqueGroups(UserId, ProductId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        GroupList ores = new GroupList
                        {
                            GroupID = row["groupId"] == DBNull.Value ? 0 : Convert.ToInt32(row["groupId"]),
                            GroupName = row["GroupName"] == DBNull.Value ? "" : Convert.ToString(row["GroupName"]),
                            GroupTypeId = row["GroupTypeId"] == DBNull.Value ? 0 : Convert.ToInt32(row["GroupTypeId"]),
                            PolicyType = row["PolicyType"] == DBNull.Value ? 0 : Convert.ToInt32(row["PolicyType"]),
                        };

                        lstGroup.Add(ores);

                    }
                }
                return lstGroup;
            }
            catch (Exception ex)
            {
                return lstGroup;
            }
        }

        public List<CustomerSelection> GetCustomerSelection(Int64 LeadID)
        {
            string result = string.Empty;
            string strexception = string.Empty;
            List<CustomerSelection> Response = new List<CustomerSelection>();
            DateTime dt = DateTime.Now;

            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetCustomerSelection(LeadID);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        CustomerSelection ores = new CustomerSelection
                        {
                            LeadID = row["LeadID"] == DBNull.Value ? 0 : Convert.ToInt32(row["LeadID"]),
                            QuoteCreatedOn = (DateTime)(row["QuoteCreatedOn"] = Convert.ToDateTime(row["QuoteCreatedOn"])),
                            PlanID = row["PlanID"] == DBNull.Value ? 0 : Convert.ToInt32(row["PlanID"]),
                            PlanName = row["PlanName"] == DBNull.Value ? "" : Convert.ToString(row["PlanName"])
                        };

                        Response.Add(ores);

                    }
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), 0, strexception, "GetCustomerSelection", "MatrixCore", Response.ToString(), "", "", dt, DateTime.Now);
            }

            return Response;
        }

        public UserInfoModel InsertUserInfo(UserInfo userInfo)
        {
            UserInfoModel userInfoModel = new UserInfoModel()
            {
                StatusCode = 404,
                Leads = new List<long>()
            };
            try
            {
                DataSet oDataSet = LeadDetailsDLL.InsertUserInfo(userInfo);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        long LeadID = row["LeadID"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadID"]);
                        userInfoModel.Leads.Add(LeadID);

                    }
                    userInfoModel.Status = "SUCCESS";
                    userInfoModel.StatusCode = 200;
                    userInfoModel.Ok = Convert.ToInt32(true);

                }
            }
            catch (Exception ex)
            {
                userInfoModel.StatusCode = 500;
                userInfoModel.Ok = Convert.ToInt32(false);
                userInfoModel.Status = ex.ToString();


            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(userInfo.CustId.ToString(), 0, "", "InsertUserInfo", "LeadDetailsBLL", userInfo.Source, userInfo.ToString(), userInfoModel.ToString(), DateTime.Now, DateTime.Now);
            }
            return userInfoModel;
        }

        public LeadDeatils GetLeadDetails(GetLeadDetails getLeadDetails)
        {
            var leadDetails = new LeadDeatils();
            long leadId = 0;
            try
            {
                leadId = Convert.ToInt64(Crypto.Decrytion_Payment_AES(getLeadDetails.LeadId, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings()));
                DataSet oDataSet = LeadDetailsDLL.GetLeadDetails(leadId, getLeadDetails.IsBooking);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    leadDetails = new LeadDeatils()
                    {
                        PlanName = oDataSet.Tables[0].Rows[0]["SelectedPlanName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["SelectedPlanName"]),
                        UTMCampaign = oDataSet.Tables[0].Rows[0]["Utm_campaign"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Utm_campaign"]),
                        UTMSource = oDataSet.Tables[0].Rows[0]["Utm_source"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Utm_source"]),
                        UTMTerm = oDataSet.Tables[0].Rows[0]["Utm_term"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Utm_term"]),
                        UTMMedium = oDataSet.Tables[0].Rows[0]["UTM_Medium"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["UTM_Medium"]),
                        Name = oDataSet.Tables[0].Rows[0]["Name"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Name"]),
                        DOB = oDataSet.Tables[0].Rows[0]["DOB"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["DOB"]),
                        MobileNo = oDataSet.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? "" : Crypto.Encrytion_Payment_AES(Convert.ToString(oDataSet.Tables[0].Rows[0]["MobileNo"]), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false),
                        EmailId = oDataSet.Tables[0].Rows[0]["EmailID"] == DBNull.Value ? "" : Crypto.Encrytion_Payment_AES(Convert.ToString(oDataSet.Tables[0].Rows[0]["EmailID"]), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false),
                        CityID = oDataSet.Tables[0].Rows[0]["CityID"] == DBNull.Value ? 0 : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["CityID"]),
                        StateID = oDataSet.Tables[0].Rows[0]["StateID"] == DBNull.Value ? 0 : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["StateID"]),
                        ProductID = oDataSet.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? 0 : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"]),
                        CustomerID = oDataSet.Tables[0].Rows[0]["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["CustomerID"]),
                        SupplierId = oDataSet.Tables[0].Rows[0]["SupplierId"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["SupplierId"]),
                        PlanId = oDataSet.Tables[0].Rows[0]["PlanId"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["PlanId"]),
                        SupplierName = oDataSet.Tables[0].Rows[0]["SupplierName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["SupplierName"]),
                        PolicyNo = oDataSet.Tables[0].Rows[0]["PolicyNo"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["PolicyNo"]),
                        ParentLeadSource = oDataSet.Tables[0].Rows[0]["ParentLeadSource"] == DBNull.Value ? default : Convert.ToString(oDataSet.Tables[0].Rows[0]["ParentLeadSource"]),
                        ParentLeadId = oDataSet.Tables[0].Rows[0]["ParentId"] == DBNull.Value ? default : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentId"])
                    };
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, leadId, ex.ToString(), "GetLeadDetails", "MatrixCore", leadDetails.ToString(), "", "", DateTime.Now, DateTime.Now);
            }
            return leadDetails;
        }

        public LeadResponse CreateLeadByReferralId(LeadRequest leadRequest)
        {
            string strexception = string.Empty;
            LeadResponse leadResponse = new LeadResponse();

            try
            {
                if (!string.IsNullOrEmpty(leadRequest.Name) && !string.IsNullOrEmpty(leadRequest.MobileNo) && leadRequest.CustomerID > 0 && leadRequest.ProductID > 0 && leadRequest.ReferralID > 0 && !string.IsNullOrEmpty(leadRequest.DOB))
                {
                    (Int64 LeadId, string Error) = LeadDetailsDLL.CreateLeadByReferralId(leadRequest);
                    leadResponse.LeadID = LeadId;
                    leadResponse.Error = Error;
                }
                else
                {
                    strexception = "Mandatory fields missing.";
                    leadResponse.Error = strexception;
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.Log(null, leadRequest.ReferralID, strexception, "CreateLeadByReferralId", "MatrixCore", "LeadDetailsBLL", leadRequest.ToString(), leadResponse.ToString(), DateTime.Now, DateTime.Now);
                leadResponse.Error = ex.ToString();
            }

            return leadResponse;
        }

        public Remarks GetCustomerPitchedRemarks(string LeadId)
        {
            Remarks remarks = new Remarks();

            try
            {
                if (!string.IsNullOrEmpty(LeadId))
                {
                    string json = "{\"leadid\":[" + LeadId + "]}";
                    Dictionary<object, object> objHeaders = new Dictionary<object, object>() { { "x-access-token", "RemarksApiAuthKey".AppSettings() } };
                    string response = CommonAPICall.CallAPI("RemarksApi".AppSettings(), json, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", objHeaders);
                    if (!string.IsNullOrEmpty(response))
                    {
                        var Response = JsonConvert.DeserializeObject<HealthRenewalRemarks>(response);
                        if (Response.Remarks != "undefined")
                        {
                            remarks.RemarksList = Response.Remarks?.Split(",").Select(x => x.Trim()).ToList();
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, 0, ex.ToString(), "GetCustomerPitchedRemarks", "MatrixCore", null, LeadId.ToString(), remarks.ToString(), DateTime.Now, DateTime.Now);
            }
            return remarks;

        }

        public static DocumentUrlResponse GetDocumentUrl(DocumentUrlRequest documentRequest)
        {
            var response = new DocumentUrlResponse();
            try
            {
                var headerParams = new Dictionary<string, string>
                {
                    { "authKey", "coreAPIauthKey".AppSettings() },
                    { "clientKey", "coreAPIclientKey".AppSettings() }
                };
                documentRequest.CustomerId = Crypto.Encrytion_Payment_AES(documentRequest.CustomerId, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false);
                documentRequest.DocumentId = Crypto.Encrytion_Payment_AES(documentRequest.DocumentId, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false);

                string jsonData = JsonConvert.SerializeObject(documentRequest);
                var result = CommonAPICall.PostAPICall_Sync("CoreDocUploadBaseUrl".AppSettings() + "ttlDocUrl", 3000, jsonData, headerParams);
                response = JsonConvert.DeserializeObject<DocumentUrlResponse>(result);
            }
            catch (Exception ex)
            {
                response.DocumentPath = string.Empty;
                response.ExpiryTimeInSeconds = 0;
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetDocumentPath", "GetDocumentPath", "SalesViewBLL", documentRequest.DocumentId.ToString(), JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public UpsellStatus GetUpsellClickStatus(string LeadId)
        {
            UpsellStatus upsellStatus = new UpsellStatus();

            try
            {
                if (!string.IsNullOrEmpty(LeadId))
                {
                    string json = "{\"leadIDs\":[" + LeadId + "]}";

                    string response = CommonAPICall.CallAPI("UpsellAPI".AppSettings(), json, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json");
                    if (!string.IsNullOrEmpty(response))
                    {
                        var Response = JsonConvert.DeserializeObject<UpsellStatus>(response);
                        if (Response != null)
                        {
                            return Response;
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, 0, ex.ToString(), "GetUpsellClickStatus", "MatrixCore", null, LeadId.ToString(), upsellStatus.ToString(), DateTime.Now, DateTime.Now);
            }
            return upsellStatus;

        }

        public ResponseAPI InsertCallBack(string ecode, long leadId, string callBackDateTime, int callBackTypeId)
        {
            var response = new ResponseAPI()
            {
                status = false
            };

            try
            {
                if (Convert.ToDateTime(callBackDateTime) > DateTime.Now)
                {
                    long userId = LeadDetailsDLL.GetUserId(ecode);

                    if (userId > 0)
                    {
                        InsertCallBack(response, userId, leadId, callBackDateTime, callBackTypeId);
                    }
                    else
                    {
                        response.message = "Invalid EmployeeId!";
                    }
                }
                else
                {
                    response.message = "CallBackDateTime should be greater than current DateTime!";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId,
                                                          ex.ToString(), "InsertCallBack", "LeadDetailsBLL",
                                                          "MatrixCore", "",
                                                          JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
                response.message = ex.Message;
            }
            return response;
        }

        private static void InsertCallBack(ResponseAPI response, long userId, long leadId, string callBackDateTime, int callBackTypeId)
        {
            var headerParams = new Dictionary<string, string>
                               {
                                   { "authorization", "MRSapiAuthKey".AppSettings() }
                               };
            var request = new CalData()
            {
                InsertCallBack = new InsertCallBackRequest()
                {
                    Data = new CalendarEvent()
                    {
                        AgentId = userId,
                        Id = leadId,
                        EventTypeId = 4,
                        StartDate = Convert.ToDateTime(callBackDateTime).ToString("MM/dd/yyyy HH:mm").Replace('-', '/'),
                        EndDate = Convert.ToDateTime(callBackDateTime).AddMinutes(5).ToString("MM/dd/yyyy HH:mm").Replace('-', '/'),
                        CallBackTypeId = callBackTypeId
                    }
                }
            };

            string jsonRequest = JsonConvert.SerializeObject(request);
            var jsonResult = CommonAPICall.PostAPICall_Sync("InsertCallBackUrl".AppSettings(), 3000, jsonRequest, headerParams);

            if (!string.IsNullOrEmpty(jsonResult))
            {
                dynamic result = JsonConvert.DeserializeObject<dynamic>(jsonResult);

                if (result != null)
                {
                    if (jsonResult.Contains("IsInserted") && (bool)result?.InsertCallSchedulerEventResult?.Data?.IsInserted)
                    {
                        response.message = "Success";
                        response.status = true;
                    }
                    else
                    {
                        response.message = result.InsertCallSchedulerEventResult.Error.ToString();
                    }
                }
                else
                {
                    response.message = "Invalid Response!";
                }
            }
            else
            {
                response.message = "Response Invalid!";
            }
        }

        public ResponseAPI SetCallBack(CallBack callBack)
        {
            Int16 ProductId = 0;
            Int64 AssignedTo = 0;
            Int64 ParentLeadId = 0;
            Int16 GroupID = 0;
            var response = new ResponseAPI();
            try
            {
                if (Convert.ToDateTime(callBack.CallBacktime) > DateTime.Now)
                {
                    DataSet leadDetailsData = LeadDetailsDLL.GetLeadDetailsIsActive(callBack.LeadId);

                    if (leadDetailsData != null && leadDetailsData.Tables.Count > 0 && leadDetailsData.Tables[0].Rows.Count > 0)
                    {
                        ProductId = leadDetailsData.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? default : Convert.ToInt16(leadDetailsData.Tables[0].Rows[0]["ProductID"]);
                        ParentLeadId = leadDetailsData.Tables[0].Rows[0]["ParentID"] == DBNull.Value ? default : Convert.ToInt64(leadDetailsData.Tables[0].Rows[0]["ParentID"]);
                        AssignedTo = leadDetailsData.Tables[0].Rows[0]["assignedTO"] == DBNull.Value ? default : Convert.ToInt64(leadDetailsData.Tables[0].Rows[0]["assignedTO"]);
                    }
                    if (ProductId == 117)
                    {
                        if (AssignedTo == 0)
                        {
                            DataSet dataSet = LeadDetailsDLL.GetAgentID_Allocation(GroupID);
                            if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                            {
                                AssignedTo = dataSet.Tables[0].Rows[0]["UserId"] == DBNull.Value ? default : Convert.ToInt64(dataSet.Tables[0].Rows[0]["UserId"]);
                            }

                            if (AssignedTo > 0)
                            {
                                var allocationData = new AllocationDetails()
                                {
                                    AssigntoUserID = AssignedTo,
                                    AssignbyUserID = 124,
                                    LeadID = ParentLeadId,
                                    ProductID = (byte)ProductId,
                                    GroupID = GroupID,
                                    LeadRank = 0,
                                    AllocationTrackingEntryFlag = 0,
                                    JobID = 53,
                                    SelectionCount = 0
                                };
                                LeadDetailsDLL.AssignLead(allocationData);
                            }
                        }

                        if (AssignedTo > 0)
                        {
                            var headerParams = new Dictionary<string, string>
                            {
                                { "authorization", "MRSapiAuthKey".AppSettings() }
                            };

                            var request = new CalData()
                            {
                                InsertCallBack = new InsertCallBackRequest()
                                {
                                    Data = new CalendarEvent()
                                    {
                                        AgentId = AssignedTo,
                                        Id = ParentLeadId,
                                        EventTypeId = 4,
                                        StartDate = Convert.ToDateTime(callBack.CallBacktime).ToString("MM/dd/yyyy HH:mm").Replace('-', '/'),
                                        EndDate = Convert.ToDateTime(callBack.CallBacktime).AddMinutes(5).ToString("MM/dd/yyyy HH:mm").Replace('-', '/'),
                                        CallBackTypeId = callBack.CallBackTypeId
                                    }
                                }
                            };

                            string jsonRequest = JsonConvert.SerializeObject(request);
                            var jsonResult = CommonAPICall.PostAPICall_Sync("InsertCallBackUrl".AppSettings(), 6000, jsonRequest, headerParams);

                            if (!string.IsNullOrEmpty(jsonResult))
                            {
                                dynamic result = JsonConvert.DeserializeObject<dynamic>(jsonResult);

                                if (result != null)
                                {
                                    if (string.IsNullOrEmpty(result.InsertCallSchedulerEventResult.Error.ToString()))
                                    {
                                        response.message = "Success";
                                        response.status = true;
                                    }
                                    else
                                    {
                                        response.message = result.InsertCallSchedulerEventResult.Error.ToString();
                                        response.status = false;
                                    }
                                }
                                else
                                {
                                    response.message = "Invalid Response!";
                                    response.status = false;
                                }
                            }
                            else
                            {
                                response.message = "Response Invalid!";
                                response.status = false;
                            }
                        }
                        else
                        {
                            response.message = "Invalid User!";
                            response.status = false;
                        }
                    }
                }
                else
                {
                    response.message = "CallBackDateTime should be greater than current DateTime!";
                    response.status = false;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", callBack.LeadId,
                                                          ex.ToString(), "SetCallBack", "LeadDetailsBLL",
                                                          "MatrixCore", "",
                                                          JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
                response.message = ex.Message;
                response.status = false;
            }
            return response;
        }

        public static bool IsValidOrigin(string Origin)
        {
            if (!string.IsNullOrEmpty(Origin))
            {
                List<string> ValidOriginList = "PublicOrigins".AppSettings().Split(',').ToList();
                return ValidOriginList.Contains(Origin.ToLower());
            }
            return false;
        }
        public static bool IsIntrnalIP(string IP)
        {
            string chkIP = string.Empty;
            string IpRange = string.Empty;
            if (!string.IsNullOrEmpty(IP))
            {
                var arr = IP.Split('.');

                if (arr.Length > 2)
                {
                    chkIP = arr[0] + "." + arr[1];
                    IpRange = arr[0] + "." + arr[1] + "." + arr[2];
                }

            }
            List<String> PublicInternalIP = "externalIP".AppSettings().Split(',').ToList();
            List<String> InternalIPRange = "InternalIPRange".AppSettings().Split(',').ToList();
            if (chkIP == "10.0" || InternalIPRange.Contains(IpRange) || PublicInternalIP.Contains(IP) == true)
                return true;
            else
                return false;

        }

        public LeadDataModel GetLeadAssignedAgent(string MobileNo, Int16 ProductID)
        {
            string strexception = string.Empty;
            DateTime strRequestTime = DateTime.Now;
            LeadDataModel _LeadDataModel = new LeadDataModel();
            //MobileNo = Crypto.Encrytion_Payment_AES("9958361783", "Self", 256, 128, "WA_Key".AppSettings(), "WA_IV".AppSettings(),false);
            Int64 MobNo = Convert.ToInt64(Crypto.Decrytion_Payment_AES(MobileNo, "Self", 256, 128, "WA_Key".AppSettings(), "WA_IV".AppSettings()));

            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetLeadAssignedAgent(MobNo, ProductID);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {

                    _LeadDataModel = new LeadDataModel()
                    {
                        LeadId = oDataSet.Tables[0].Rows[0]["LeadID"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["LeadID"]),
                        UserId = oDataSet.Tables[0].Rows[0]["UserID"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["UserID"]),
                        EmployeeID = oDataSet.Tables[0].Rows[0]["EmployeeId"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["EmployeeId"]),
                    };
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.Log(MobileNo, 0, strexception, "GetLeadDetails", "MatrixCore", "", "", "", strRequestTime, DateTime.Now);
            }

            return _LeadDataModel;
        }

        public bool AssignLead(Int64 LeadID, string EmployeeID, string Source, StringValues? agentId)
        {
            string error = string.Empty;
            bool result = false;
            try
            {
                if (!string.IsNullOrEmpty(EmployeeID) && !string.IsNullOrEmpty(Source) && LeadID > 0)
                {
                    long assignTo = 0;
                    result = LeadDetailsDLL.AssignLead(LeadID, EmployeeID, Source, agentId);

                    if (Source == "sme")
                    {
                        DataSet data = FOSDLL.GetLeadBasicInfo(LeadID);
                        if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                        {
                            long ParentId = (data.Tables[0].Rows[0]["ParentID"] != null && data.Tables[0].Rows[0]["ParentID"] != DBNull.Value) ? Convert.ToInt64(data.Tables[0].Rows[0]["ParentID"]) : 0;
                            assignTo = (data.Tables[0].Rows[0]["AssignTo"] != null && data.Tables[0].Rows[0]["AssignTo"] != DBNull.Value) ? Convert.ToInt64(data.Tables[0].Rows[0]["AssignTo"]) : 0;
                        }

                        if (assignTo == 0) //if not assign then assign 
                        {
                            var objToPush = new { LeadId = LeadID, IsLeadCreated = true, ProductId = 131, EmployeeId = EmployeeID };
                            string json = JsonConvert.SerializeObject(objToPush);
                            WebSiteServicDLL.SetReferralLeadCreationData(LeadID, json, 124, "SMEAllocation");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                error = ex.Message;
            }
            finally
            {
                string req = "LeadId-" + LeadID + ", EmployeeID-" + EmployeeID + ", Source-" + Source + ", agentId-" + agentId;
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, error, "AssignLead", "LeadDetailsBLL", "MatrixCore", string.Empty, req, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public AgentDetails GetAgentDetails(string encLeadId, string EncKey, string EncIV, string Process)
        {
            string strexception = string.Empty;
            AgentDetails agentDetails = new AgentDetails();
            Int64 LeadID = Convert.ToInt64(Crypto.Decrytion_Payment_AES(encLeadId, "Core", 256, 128, EncKey, EncIV));

            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetAgentDetails(LeadID, Process);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    agentDetails = new AgentDetails()
                    {
                        EmpCode = oDataSet.Tables[0].Rows[0]["EmployeeId"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["EmployeeId"]),
                        EmpName = oDataSet.Tables[0].Rows[0]["UserName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["UserName"]),
                        CustName = oDataSet.Tables[0].Columns.Contains("ProposerName") ? (oDataSet.Tables[0].Rows[0]["ProposerName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["ProposerName"])) : null,
                        Insurer = oDataSet.Tables[0].Columns.Contains("SupplierName") ? (oDataSet.Tables[0].Rows[0]["SupplierName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["SupplierName"])) : null,
                        PolicyNumber = oDataSet.Tables[0].Columns.Contains("OldPolicyNo") ? (oDataSet.Tables[0].Rows[0]["OldPolicyNo"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["OldPolicyNo"])) : null,
                        UserGroupId = oDataSet.Tables[0].Columns.Contains("UserGroupId") ? (oDataSet.Tables[0].Rows[0]["UserGroupId"] == DBNull.Value ? (short)0 : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["UserGroupId"])) : (short)0,
                        NoticePremium = oDataSet.Tables[0].Columns.Contains("NoticePremium") && oDataSet.Tables[0].Rows[0]["NoticePremium"] != DBNull.Value ? Convert.ToDecimal(oDataSet.Tables[0].Rows[0]["NoticePremium"]) : 0,
                        NCB = oDataSet.Tables[0].Columns.Contains("NCB") && oDataSet.Tables[0].Rows[0]["NCB"] != DBNull.Value ? Convert.ToDecimal(oDataSet.Tables[0].Rows[0]["NCB"]) : 0,
                        BasicSumInsured = oDataSet.Tables[0].Columns.Contains("SumInsured") && oDataSet.Tables[0].Rows[0]["SumInsured"] != DBNull.Value ? Convert.ToDecimal(oDataSet.Tables[0].Rows[0]["SumInsured"]) : 0,
                        PlanName = oDataSet.Tables[0].Columns.Contains("PlanName") && oDataSet.Tables[0].Rows[0]["PlanName"] != DBNull.Value ? Convert.ToString(oDataSet.Tables[0].Rows[0]["PlanName"]) : string.Empty
                    };
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.Log(null, LeadID, strexception, "GetLeadDetails", "MatrixCore", agentDetails.ToString(), "", "", DateTime.Now, DateTime.Now);
            }
            return agentDetails;
        }

        public long GetRenewalBookingStatus(long referralId, int productId, long customerId)
        {
            return LeadDetailsDLL.GetRenewalBookingStatus(referralId, productId, customerId);
        }

        public NewSVURLModel GetCJExitPointUrl(long LeadId, int ProductId, string agentId)
        {
            var result = string.Empty;
            var response = new NewSVURLModel();
            try
            {
                if (LeadId > 0)
                {
                    if (ProductId == 131)
                    {
                        int existingProdId = LeadDetailsDLL.GetProductIdByLeadId(LeadId);
                        if (existingProdId == ProductId)
                        {
                            var header = new Dictionary<object, object>
                                    {
                                        { "Access-Token", "SmeCJToken".AppSettings() }
                                    };
                            result = CommonAPICall.CallAPI("SmeCJURL".AppSettings() + "matrix/CreateOfflineLead?Leadid=" + LeadId,
                                                             string.Empty, "POST", Convert.ToInt32("CJTimeout".AppSettings()),
                                                             string.Empty, header);
                            dynamic dynamicResult = JsonConvert.DeserializeObject<dynamic>(result);

                            if (dynamicResult != null && (bool)dynamicResult.IsSuccess)
                            {
                                response.URL = dynamicResult.RedirectURL;
                            }
                            else
                            {
                                response.StatusMessage = "No Continue Journey found";
                            }
                        }
                        else
                        {
                            response.StatusMessage = "No Continue Journey found";
                        }

                        LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), LeadId, string.Empty, "GetCJExitPointUrl", "LeadDetailsBLL", "MatrixCoreAPI",
                                                                  string.Format("LeadId {0}, PassedProductId {1}, ExistingProdId {2}", LeadId, ProductId, existingProdId),
                                                                  string.Empty, DateTime.Now, DateTime.Now);

                    }
                    else if (ProductId == 2)
                    {
                        response.URL = "https://health.policybazaar.com/";
                    }
                    else if (ProductId == 3)
                    {
                        response.URL = "https://travel.policybazaar.com/";
                    }
                    else if (ProductId == 115)
                    {
                        response.URL = "https://investmentlife.policybazaar.com/";
                    }
                    else if (ProductId == 117)
                    {
                        response.URL = "https://ci.policybazaar.com/";
                    }

                    if (!string.IsNullOrEmpty(response.URL))
                    {
                        LeadDetailsDLL.UpdateCJExitPointUrl(LeadId, response.URL);
                        if (ProductId == 131 && !response.URL.Contains("&AgentId="))
                        {
                            response.URL = response.URL + "&AgentId=" + Crypto.EncryptString(agentId);
                        }
                    }
                    else
                    {
                        response.StatusMessage = "Continue Journey URL not found";
                    }
                }
                else
                {
                    response.StatusMessage = "Invalid LeadId";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(string.Empty, LeadId, ex.Message,
                                                "GetCJExitPointUrl", "MatrixCore",
                                                string.Empty, string.Empty, result,
                                                DateTime.Now, DateTime.Now);
                response.StatusMessage = ex.Message;
            }
            return response;
        }

        public string GetCJUrl(string leadID, int productId, int supplierId, string leadSource, string process, string AgentID)
        {
            string result = null, json = null;
            try
            {
                if (!string.IsNullOrEmpty(leadID))
                {
                    if (productId == 2 || productId == 106 || productId == 118 || productId == 130)
                    {
                        json = "{\"continueId\":\"" + Convert.ToBase64String(Encoding.UTF8.GetBytes(leadID)) + "\",\"productID\":" + productId + ",\"supplierID\":" + supplierId + "}";
                        var header = new Dictionary<object, object>()
                                    {
                                        {"authkey", "CJAuthToken".AppSettings()}
                                    };
                        if (AgentID != string.Empty)
                        {
                            header.Add("source", "matrixagent");
                        }
                        string response = CommonAPICall.CallAPI("HealthCJUrl".AppSettings(), json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json-patch+json", header);
                        if (!string.IsNullOrEmpty(response))
                        {
                            result = JsonConvert.DeserializeObject<CJUrlModel>(response)?.redirectionUrl;
                        }
                    }
                    else if (productId == 7 || productId == 115)
                    {
                        result = LeadDetailsDLL.GetCJUrl(leadID);
                    }
                }
            }
            catch (Exception ex)
            {
                result = ex.Message;
                LoggingHelper.LoggingHelper.Log(leadID, Convert.ToInt64(leadID), ex.ToString(),
                                                "GetCJUrl", "LeadDetailsBLL", "MatrixCore",
                                                json, null, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public UrlResponse GetCommonCJUrl(string leadID, int productId, int supplierId, string leadSource, string process, string LeadCreationSource, string EnquiryId, string AgentId)
        {
            string json = null, reqjson = string.Empty;
            UrlResponse result = new UrlResponse();
            DateTime reqdt = DateTime.Now;
            result.StatusMessage = "Success";
            long ProposerID = 0;
            try
            {
                reqjson = "{\"LeadId\":\"" + leadID + "\",\"ProductId\":" + productId + ",\"LeadSource\":" + leadSource + ",\"SupplierId\":" + supplierId + ",\"Process\":" + process + ",\"LeadCreationSource\":" + LeadCreationSource + "}";
                if (!string.IsNullOrEmpty(leadID))
                {
                    if (leadSource.ToLower() == "renewal" && (productId == 2 || productId == 106 || productId == 118 || productId == 130))
                    {
                        json = "{\"continueId\":\"" + Convert.ToBase64String(Encoding.UTF8.GetBytes(leadID)) + "\",\"productID\":" + productId + ",\"supplierID\":" + supplierId + "}";
                        var header = new Dictionary<object, object>()
                                    {
                                        {"authkey", "CJAuthToken".AppSettings()}
                                    };
                        string response = CommonAPICall.CallAPI("HealthCJUrl".AppSettings(), json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json-patch+json", header);
                        if (!string.IsNullOrEmpty(response))
                        {
                            result.ExitPointURL = JsonConvert.DeserializeObject<CJUrlModel>(response)?.redirectionUrl;
                        }
                    }
                    else if (!string.IsNullOrEmpty(LeadCreationSource) && LeadCreationSource.ToLower() == "pbcromaservicev2" && (productId == 2 || productId == 106 || productId == 118 || productId == 130))
                    {
                        Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"apikey", "CJHealthFreshHeaderKey".AppSettings()}};
                        string Healthfreshurl = "CJHealthFreshURL".AppSettings() + "HealthMasterServices.svc/GetExitPointUrl?leadId=" + leadID;
                        string response = CommonAPICall.CallAPI(Healthfreshurl, "", "GET", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);
                        if (!string.IsNullOrEmpty(response))
                        {
                            result.ExitPointURL = JsonConvert.DeserializeObject<CJUrlModel>(response)?.ExitPointURL;
                        }
                    }
                    else if (!string.IsNullOrEmpty(LeadCreationSource) && LeadCreationSource.ToLower() == "pbcromaservicev2" && (productId == 3))
                    {
                        DataRow dataRow = LeadDetailsDLL.GetLeadBasicInfo(Convert.ToInt64(leadID));
                        if (dataRow != null)
                        {
                            ProposerID = Convert.ToInt32(dataRow["ProposerId"]);
                            if (ProposerID > 0)
                            {
                                string EncryptKey = ConnectionClass.getCommonSecretKeyFromAWS("Enc_aes128Key", "TravelAWSEnv".AppSettings().ToString());
                                string EncryptIV = ConnectionClass.getCommonSecretKeyFromAWS("Enc_aes128IV", "TravelAWSEnv".AppSettings().ToString());
                                string RsaPrivateKey = ConnectionClass.getCommonSecretKeyFromAWS("RsaPrivateKey", "TravelAWSEnv".AppSettings().ToString());
                                string RsaPublicKey = ConnectionClass.getCommonSecretKeyFromAWS("RsaPublicKey", "TravelAWSEnv".AppSettings().ToString());

                                string EncryptedProposerId = Crypto.EncryptString(ProposerID.ToString(), EncryptKey, EncryptIV, 128, 128);

                                if (ProposerID.ToString() == EncryptedProposerId)//if encryption doesn't happen
                                {
                                    result.ExitPointURL = "https://travel.policybazaar.com/";
                                }
                                else
                                {
                                    //int ExpiryInMinutes = 43800;
                                    //result.ExitPointURL = "TravelContJourneyURL".AppSettings().ToString() + EncryptedProposerId + "?expiry=" + ExpiryInMinutes + "&token=" + JWTToken;

                                    string exitPointurl = dataRow["ExitPointURL"] != DBNull.Value ? dataRow["ExitPointURL"].ToString() : default;
                                    if (!string.IsNullOrEmpty(exitPointurl))
                                    {
                                        var url = exitPointurl.Substring(0, exitPointurl.IndexOf("?")) + EncryptedProposerId + "?";
                                        var uri = new Uri(exitPointurl);
                                        var qs = HttpUtility.ParseQueryString(uri.Query);
                                        string JWTToken = CreateJWTToken(RsaPrivateKey, RsaPublicKey, Convert.ToInt32(qs.Get("expiry")), AgentId);
                                        qs.Set("token", JWTToken);
                                        result.ExitPointURL = url + qs.ToString();
                                    }
                                }
                            }
                        }
                    }
                    else if ((productId == 117) && ((!string.IsNullOrEmpty(LeadCreationSource) && LeadCreationSource.ToLower() == "pbcromaservicev2" && Convert.ToInt64(EnquiryId) > 0) || (Convert.ToInt64(EnquiryId) > 0)))
                    {
                        string EncryptKey = ConnectionClass.getCommonSecretKeyFromAWS("CJEncryptKey", "Prod_Car");//QA_Car for QA
                        string EncryptIV = ConnectionClass.getCommonSecretKeyFromAWS("CJEncryptIV", "Prod_Car");//QA_Car for QA

                        string NewEnquiryId = EnquiryId.ToString();

                        //NewCJURL requirements
                        if (!String.IsNullOrEmpty(AgentId))
                        {
                            NewEnquiryId = EnquiryId.ToString() + "|Matrix";
                        }

                        string EncryptedEnquiryId = Crypto.EncryptString(NewEnquiryId, EncryptKey, EncryptIV, 256, 128);
                        if (EncryptedEnquiryId == EnquiryId)//if encryption doesnt happen
                        {
                            result.ExitPointURL = "https://ci.policybazaar.com/";
                        }
                        else
                        {
                            //string dec = Crypto.decrypt_AES(EncryptedLeadID.ToString(), 256, 128, EncryptKey, EncryptIV, true);
                            string CIJWT = GetCIJwt(Convert.ToInt64(EnquiryId));
                            string exitPointURL = "MotorCJBaseURL".AppSettings() + "&id2=" + EncryptedEnquiryId + "&t=" + CIJWT;
                            if (!string.IsNullOrEmpty(exitPointURL))
                            {
                                result.ExitPointURL = exitPointURL;
                            }
                        }
                    }
                    else
                    {
                        result.ExitPointURL = LeadDetailsDLL.GetCJUrl(leadID);
                    }
                }
                if (result != null && !string.IsNullOrEmpty(result.ExitPointURL))
                {
                    result.ExitPointURL = result.ExitPointURL.Contains("https") == true ? result.ExitPointURL : "";
                }
            }
            catch (Exception ex)
            {
                result.StatusMessage = ex.ToString();
                LoggingHelper.LoggingHelper.Log(leadID, Convert.ToInt64(leadID), ex.ToString(), "GetCommonCJUrl", "LeadDetailsBLL", "MatrixCore", reqjson, null, reqdt, DateTime.Now);
            }
            return result;
        }
        public string GetCIJwt(long enquiryId)
        {
            string SecretKey = "";
            try
            {
                SecretKey = ConnectionClass.getCommonSecretKeyFromAWS("JwtSecret", "Prod_Car");//QA_Car for QA
                var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(SecretKey));
                var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

                var tokenDescriptor = new SecurityTokenDescriptor
                {
                    Issuer = "matrix",
                    Expires = DateTime.Now.AddMonths(2),
                    IssuedAt = DateTime.Now,
                    SigningCredentials = credentials,
                    Claims = new Dictionary<string, object>
                    {
                        { "enquiryId", enquiryId },
                        { "nbf", DateTime.UtcNow },
                        { "exp", DateTime.UtcNow.AddMonths(2) },
                        { "iat", DateTime.UtcNow },
                        { "iss", "matrix" }
                    }
                };

                var tokenHandler = new JwtSecurityTokenHandler();
                var token = tokenHandler.CreateToken(tokenDescriptor);
                return tokenHandler.WriteToken(token);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(enquiryId.ToString(), Convert.ToInt64(enquiryId), ex.ToString(),
                                                "GetCIJwt", "LeadDetailsBLL", "MatrixCore",
                                                "", "", DateTime.Now, DateTime.Now);
            }
            return "";
        }
        public string CreateJWTToken(string RSAprivateKey, string RSApublicKey, int ExpiryInMinutes, string AgentId)
        {
            using RSA rsa = RSA.Create();
            rsa.ImportRSAPrivateKey(Convert.FromBase64String(RSAprivateKey), out _);

            var signingCredentials = new SigningCredentials(new RsaSecurityKey(rsa), SecurityAlgorithms.RsaSha256)
            {
                CryptoProviderFactory = new CryptoProviderFactory { CacheSignatureProviders = false }
            };

            Claim[] claims = null;

            if (!string.IsNullOrEmpty(AgentId))
            {
                claims = new[]
                {
                    new Claim("ProposerID", "travel.policybazaar.com"),
                    new Claim("role","matrix")
                };
            }
            else
            {
                claims = new[]
                {
                    new Claim("ProposerID", "travel.policybazaar.com")
                };
            }

            var token = new JwtSecurityToken(
                issuer: "travel.policybazaar.com",
                audience: "travel",
                claims: claims,
                expires: DateTime.UtcNow.AddMinutes(ExpiryInMinutes),
                signingCredentials: signingCredentials
            );

            var tokenHandler = new JwtSecurityTokenHandler();
            var tokenString = tokenHandler.WriteToken(token);
            return tokenString.ToString();
        }

        public GenericAPIResponse CallTransferSalesIVR(CallTransferRequest ReqCTR)
        {
            string url = string.Empty;
            var response = new GenericAPIResponse();
            DateTime dtreq = DateTime.Now;
            try
            {
                url = "dialerapi".AppSettings() + "api/dialer/multi_conference.php?transfer_agents=" + ReqCTR.transfer_agents
                    + "&campaign=" + ReqCTR.campaign + "&bookingid=" + Convert.ToInt64(ReqCTR.bookingid)
                    + "&action=" + ReqCTR.action + "&transfer_type=" + ReqCTR.transfer_type + "&agent=" + ReqCTR.agent;
                int timeout = Convert.ToInt32("DialerAPITimeout".AppSettings());
                var data = CommonAPICall.CallAPI(url, "", "GET", timeout, "application/json", null);
                if (CoreCommonMethods.IsValidString(data))
                {
                    response = JsonConvert.DeserializeObject<GenericAPIResponse>(data);
                }
            }
            catch (Exception ex)
            {
                response.message = ex.Message;
                response.status = 400;
                LoggingHelper.LoggingHelper.Log(ReqCTR.bookingid, Convert.ToInt64(ReqCTR.bookingid), ex.ToString(),
                                                "CallTransferSalesIVR", "LeadDetailsBLL", "MatrixCore",
                                                url, response.ToString(), dtreq, DateTime.Now);
            }
            return response;
        }

        public string IsEnableRollback(string LeadID)
        {
            string response = string.Empty;
            DateTime reqTime = DateTime.Now;
            try
            {
                string api = "healthProposerEditURL".AppSettings() + "/HealthMasterServices.svc/EnableProposerEdit?LeadID=" + LeadID;
                Dictionary<object, object> header = new();
                header.Add("apikey", "healthProposerEditKey".AppSettings());
                response = CommonAPICall.CallAPI(api, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), 0, ex.ToString(), "IsEnableRollback", "Leaddetails", "MatrixCoreAPI", LeadID.ToString(), "", reqTime, DateTime.Now);
            }
            return response;
        }
        public bool IsAppInstalled(string CustomerID)
        {
            string response = string.Empty;
            DateTime reqTime = DateTime.Now;
            bool IsAppinstalled = false;
            try
            {
                string EncCustomerID = Crypto.encrypt_AES(CustomerID.ToString(), 256, 128, "MobileAppAPIencKey".AppSettings(), "MobileAppAPIIVKey".AppSettings());
                //string DecCustid = Crypto.decrypt_AES(EncCustomerID, 256, 128, "MobileAppAPIencKey".AppSettings(), "MobileAppAPIIVKey".AppSettings());
                string URL = "MobileAppAPIURL".AppSettings() + "/customerExistInApp";
                string json = "{\"CustID\":\"" + EncCustomerID + "\"}";
                var result = CommonAPICall.CallAPI(URL, json, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
                response = JsonConvert.SerializeObject(result);
                if (!string.IsNullOrEmpty(result))
                {
                    MobileAppInstallationStatus res = Newtonsoft.Json.JsonConvert.DeserializeObject<MobileAppInstallationStatus>(result);
                    if (res != null && res.success && res.appCust)
                    {
                        IsAppinstalled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(CustomerID.ToString(), 0, ex.ToString(), "IsAppInstalled", "Leaddetails", "MatrixCoreAPI", CustomerID.ToString(), response.ToString(), reqTime, DateTime.Now);
            }
            return IsAppinstalled;
        }

        public UrlResponse SmartInvestQuesURL(string CustomerID, string LeadID)
        {
            UrlResponse objUrlResponse = new UrlResponse();
            DateTime dtreq = DateTime.Now;
            dynamic result = "";
            try
            {
                string URLExpiryTime = DateTime.Today.AddDays(1).ToString("dd/MM/yyyy").Replace('/', '-'); ;
                string api = "InvestmentAPI".AppSettings() + "/enqapi/Prequote/MakeFosPQURL";
                Dictionary<object, object> header = new();
                header.Add("Authorization", "InvestmentAuthKey".AppSettings());

                string content = "{\"LeadID\":" + Convert.ToInt64(LeadID) + ",\"CustID\":" + Convert.ToInt64(CustomerID) + ",\"Expire\":\"" + URLExpiryTime + "\"}";
                result = CommonAPICall.CallAPI(api, content, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                if (!string.IsNullOrEmpty(result))
                {
                    InvestmentQuesURL res = Newtonsoft.Json.JsonConvert.DeserializeObject<InvestmentQuesURL>(result);
                    if (res != null)
                    {
                        objUrlResponse.StatusMessage = res.HasError.ToString();
                        objUrlResponse.ExitPointURL = res.ReturnValue;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(CustomerID.ToString(), Convert.ToInt64(LeadID), ex.ToString(), "SmartInvestQuesURL", "Leaddetails", "MatrixCoreAPI", LeadID.ToString(), result.ToString(), dtreq, DateTime.Now);
            }

            return objUrlResponse;
        }
        public CustomerInvestResponse SmartInvestViewResponse(string CustomerID, string LeadID)
        {
            CustomerInvestResponse res = new CustomerInvestResponse();
            DateTime dtreq = DateTime.Now;
            var response = "";
            string Input = "";
            try
            {
                string EncLeadID = Crypto.encrypt_AES(LeadID.ToString(), 256, 128, "InvestmentAPIencKey".AppSettings(), "InvestmentAPIIVKey".AppSettings());
                //string DecLeadid = Crypto.decrypt_AES(EncLeadID, 256, 128, "InvestmentAPIencKey".AppSettings(), "InvestmentAPIIVKey".AppSettings());
                EncLeadID = Crypto.EncryptBase64(Crypto.EncryptBase64(EncLeadID));

                string EncCustID = Crypto.encrypt_AES(CustomerID.ToString(), 256, 128, "InvestmentAPIencKey".AppSettings(), "InvestmentAPIIVKey".AppSettings());
                //string DecCustid = Crypto.decrypt_AES(EncCustID, 256, 128, "InvestmentAPIencKey".AppSettings(), "InvestmentAPIIVKey".AppSettings());
                EncCustID = Crypto.EncryptBase64(Crypto.EncryptBase64(EncCustID));

                string ExpiryTime = DateTime.Today.AddDays(1).ToString("dd/MM/yyyy").Replace('/', '-');
                string verify = string.Format("{0}_{1}", CustomerID, ExpiryTime);
                verify = Crypto.EncryptBase64(Crypto.EncryptBase64(Crypto.encrypt_AES(verify, 256, 128, "InvestmentAPIencKey".AppSettings(), "InvestmentAPIIVKey".AppSettings())));

                Input = "EncLeadID : " + EncLeadID + "&EncCustID : " + EncCustID + "&ExpiryTime : " + ExpiryTime + "&verify : " + verify;

                string api = "InvestmentAPI".AppSettings() + "/enqapi/Prequote/GetFosEnquiryData?leadKey=" + EncLeadID + "&custKey=" + EncCustID + "&verify=" + verify;
                Dictionary<object, object> header = new();
                header.Add("Authorization", "InvestmentAuthKey".AppSettings());

                response = CommonAPICall.CallAPI(api, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                if (!string.IsNullOrEmpty(response))
                {
                    dynamic result = JsonConvert.DeserializeObject<dynamic>(response);
                    if (result != null && !(bool)result?.HasError)
                    {
                        res.HasError = false;
                        res.StatusMessage = "Success";
                        res = Newtonsoft.Json.JsonConvert.DeserializeObject<CustomerInvestResponse>(response);
                    }
                    else
                    {
                        res.HasError = true;
                        res.StatusMessage = (string)result?.ReturnValue;
                        res.ReturnValue = null;
                    }
                }
                else
                {
                    res.HasError = true;
                    res.StatusMessage = "Invalid result";
                    res.ReturnValue = null;
                }

            }
            catch (Exception ex)
            {
                res.HasError = true;
                res.StatusMessage = ex.ToString();
                res.ReturnValue = null;
                LoggingHelper.LoggingHelper.Log(CustomerID.ToString(), Convert.ToInt64(LeadID), ex.ToString(), "SmartInvestViewResponse", "Leaddetails", "MatrixCoreAPI", Input.ToString(), response.ToString(), dtreq, DateTime.Now);
            }
            return res;
        }

        public UrlResponse GetJourneyLink(string leadID, string process, string AgentId)
        {
            string reqjson = string.Empty;
            UrlResponse result = new();
            DateTime reqdt = DateTime.Now;
            try
            {
                DataRow dr = LeadDetailsDLL.GetJourneyLink(leadID);
                reqjson = "{\"LeadId\":\"" + leadID + ",\"Process\":" + process + "}";
                result.StatusMessage = "Success";
                if (dr != null)
                    result = GetCommonCJUrl(leadID, Convert.ToInt32(dr["ProductID"].ToString()), 0, dr["LeadSource"].ToString(), process, dr["LeadCreationSource"].ToString(), dr["EnquiryId"].ToString(), AgentId);

            }
            catch (Exception ex)
            {
                result.StatusMessage = ex.ToString();
                LoggingHelper.LoggingHelper.Log(leadID, Convert.ToInt64(leadID), ex.ToString(), "GetJourneyLink", "LeadDetailsBLL", "MatrixCore", reqjson, null, reqdt, DateTime.Now);
            }
            return result;
        }

        public LeadDeatils GetLeadBasicInfo(long leadId)
        {
            var response = new LeadDeatils();
            try
            {
                DataRow dataRow = LeadDetailsDLL.GetLeadBasicInfo(leadId);
                if (dataRow != null)
                {
                    response.ProductID = Convert.ToInt32(dataRow["ProductID"]);
                    response.Name = (dataRow["Name"] != DBNull.Value && dataRow["Name"] != null) ? dataRow["Name"].ToString() : string.Empty;
                    response.ParentLeadId = (dataRow["ParentID"] != DBNull.Value && dataRow["ParentID"] != null) ? Convert.ToInt64(dataRow["ParentID"]) : 0;
                    response.CustomerID = (dataRow["CustomerID"] != DBNull.Value && dataRow["CustomerID"] != null) ? Convert.ToInt64(dataRow["CustomerID"]) : 0;
                    response.UTMMedium = (dataRow["UTM_Medium"] != DBNull.Value && dataRow["UTM_Medium"] != null) ? Convert.ToString(dataRow["UTM_Medium"]) : string.Empty;
                    response.UTMSource = (dataRow["Utm_source"] != DBNull.Value && dataRow["Utm_source"] != null) ? Convert.ToString(dataRow["Utm_source"]) : string.Empty;
                    response.LeadSource = (dataRow["LeadSource"] != DBNull.Value && dataRow["LeadSource"] != null) ? Convert.ToString(dataRow["LeadSource"]) : string.Empty;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(leadId.ToString(), leadId, ex.ToString(), "GetLeadBasicInfo", "LeadDetailsBLL", "MatrixCore", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public List<ParentChildLeadData> GetParentChildLeadData(long leadId)
        {
            DateTime reqDate = DateTime.Now;
            List<ParentChildLeadData> lParentChildLeads = new List<ParentChildLeadData>();

            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetParentChildLeadData(leadId);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in oDataSet.Tables[0].Rows)
                    {
                        ParentChildLeadData obj = new ParentChildLeadData()
                        {
                            LeadId = dr["LeadId"] != null && dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                            Name = dr["Name"] != null && dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : string.Empty,
                            IsParent = dr["IsParent"] != null && dr["IsParent"] != DBNull.Value && Convert.ToBoolean(dr["IsParent"]),
                        };

                        lParentChildLeads.Add(obj);
                    }
                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(leadId.ToString(), leadId, ex.ToString(), "GetParentChildLeadData", "LeadDetailsBLL", "MatrixCore", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }

            return lParentChildLeads;
        }

        public UserProductList GetUserProductList(long userId)
        {
            DateTime reqTime = DateTime.Now;
            var obj = new UserProductList
            {
                Products = new List<Product>(),
                RoleSuperIds = new List<int>()
            };
            try
            {
                DataSet oDataSet = LeadDetailsDLL.GetUserProductList(userId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0] != null && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in oDataSet.Tables[0].Rows)
                    {
                        Product oProduct = new Product()
                        {
                            ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                            ProductName = dr["ProductDisplayName"] != null && dr["ProductDisplayName"] != DBNull.Value ? Convert.ToString(dr["ProductDisplayName"]) : string.Empty
                        };

                        obj.Products.Add(oProduct);
                    }
                }
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[1] != null && oDataSet.Tables[1].Rows.Count > 0)
                {
                    foreach (DataRow dr in oDataSet.Tables[1].Rows)
                    {
                        obj.RoleSuperIds.Add(Convert.ToInt32(dr["RoleSuperId"]));
                    }

                    obj.IsAgent = oDataSet.Tables[1].Rows[0]["RoleId"] != DBNull.Value && Convert.ToInt32(oDataSet.Tables[1].Rows[0]["RoleId"]) == 13;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(userId.ToString(), 0, ex.ToString(), "GetUserProductList", "LeadDetailsBLL", "MatrixCore", string.Empty, string.Empty, reqTime, DateTime.Now);
            }
            return obj;
        }

        public CustCallDetails GetAgentCallDetailsV2(CallInfo getAgentCallDetails)
        {
            string Input = getAgentCallDetails.pbcentrallogin;
            //string MobileNumber = getAgentCallDetails.MobileNo;
            string exception = string.Empty;
            string MobileNumber = string.Empty;
            CustCallDetails objCustCallDetails = new CustCallDetails();

            DateTime requestTime = DateTime.Now;
            try
            {
                if (getAgentCallDetails.Source == "pg")
                    MobileNumber = getAgentCallDetails.MobileNo;
                else if (!string.IsNullOrEmpty(Input))
                {
                    string EncKey = "MyAccEncKey".AppSettings();
                    string IVKey = "MyAccIVKey".AppSettings();
                    string data = Crypto.decrypt_AES(Input, 256, 128, EncKey, IVKey);
                    dynamic obj = JsonConvert.DeserializeObject(data);
                    MobileNumber = obj.MobileNo;
                }

                if (!string.IsNullOrEmpty(MobileNumber))
                {
                    DataSet oDataSet = LeadPrioritizationDLL.GetCallDetailsByMobile(MobileNumber);

                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        objCustCallDetails.objAgentCallDetails = oDataSet.Tables[0].AsEnumerable()
                        .Select(dr => new AgentCallDetails()
                        {
                            EmployeeID = dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty,
                            Name = dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                            CallDate = Convert.ToString(dr["CallDate"]),
                            Duration = dr["Duration"] != DBNull.Value ? Convert.ToInt32(dr["Duration"]) : 0,
                            ProductID = dr["ProductID"] != DBNull.Value ? Convert.ToInt32(dr["ProductID"]) : 0,
                            ProductName = dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : string.Empty,
                            CallingNo = dr["CallingNo"] != DBNull.Value ? Convert.ToInt64(dr["CallingNo"]) : 0,
                            UserId = dr["UserId"] != DBNull.Value ? Convert.ToInt32(dr["UserId"]) : 0,
                            LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToString(dr["LeadId"]) : string.Empty,
                            AVCertified = dr["AVCertified"] != DBNull.Value ? Convert.ToBoolean(dr["AVCertified"]) : false,
                            CallType = dr["CallType"] != DBNull.Value ? Convert.ToString(dr["CallType"]) : string.Empty,
                        }).ToList();

                    }
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[1].Rows.Count > 0)
                    {
                        objCustCallDetails.CustomerId = oDataSet.Tables[1].Rows[0]["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[1].Rows[0]["CustomerID"]);

                        if (objCustCallDetails.CustomerId > 0)
                        {
                            DataSet dt = LeadPrioritizationDLL.GetAppointmentsByCustId(objCustCallDetails.CustomerId);

                            if (dt != null && dt.Tables.Count > 0 && dt.Tables[0].Rows.Count > 0)
                            {
                                objCustCallDetails.oAppointmentData = dt.Tables[0].AsEnumerable()
                               .Select(dr => new AppointmentsDataModel()
                               {
                                   EmployeeId = dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty,
                                   UserName = dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                                   AppointmentDateTime = dr["AppointmentDateTime"] != DBNull.Value ? Convert.ToDateTime(dr["AppointmentDateTime"]) : DateTime.MinValue,
                                   Status = dr["SubStatusName"] != DBNull.Value ? Convert.ToString(dr["SubStatusName"]) : string.Empty,
                                   StatusId = dr["SubStatusID"] != DBNull.Value ? Convert.ToInt16(dr["SubStatusID"]) : Convert.ToInt16(0),
                                   City = dr["City"] != DBNull.Value ? Convert.ToString(dr["City"]) : string.Empty,
                                   ProductName = dr["ProductDisplayName"] != DBNull.Value ? Convert.ToString(dr["ProductDisplayName"]) : string.Empty,
                                   ProductId = dr["ProductID"] != DBNull.Value ? Convert.ToInt32(dr["ProductID"]) : 0,

                               }).ToList();
                            }
                        }
                    }
                    //check in redis agent is on call or not
                    if (objCustCallDetails != null && objCustCallDetails.objAgentCallDetails != null)
                    {
                        var leadData = objCustCallDetails.objAgentCallDetails.Where(item => item.Duration == 0).ToList();
                        if (leadData != null && leadData.Count > 0)
                        {
                            PredictiveAgentStatus objPredictiveAgentDetails = PredictiveAgentStatusRedis.GetAgentDetails(Convert.ToString(leadData[0].UserId));
                            if (objPredictiveAgentDetails != null && leadData[0].LeadId != null && objPredictiveAgentDetails.LeadId == leadData[0].LeadId && objPredictiveAgentDetails.status != null && objPredictiveAgentDetails.status.ToLower() != "busy")
                            {
                                objCustCallDetails.objAgentCallDetails.RemoveAll((x) => x.LeadId == leadData[0].LeadId && x.Duration == 0);
                            }
                        }

                        objCustCallDetails.objAgentCallDetails = AddClaimData(objCustCallDetails.objAgentCallDetails, MobileNumber);
                    }


                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(MobileNumber, 0, exception, "GetAgentCallDetailsV2", "MatrixCore", "LeadDetailsBLL", Input, JsonConvert.SerializeObject(objCustCallDetails), requestTime, DateTime.Now);
            }
            return objCustCallDetails;

        }

        public List<AgentCallDetails> AddClaimData(List<AgentCallDetails> objAgentCallDetails, string MobileNo)
        {
            try
            {
                if (objAgentCallDetails == null)
                    objAgentCallDetails = new List<AgentCallDetails>();

                string URL = "ClaimURL".AppSettings() + "/Calls/GetCallDetailsByMobileNo/" + MobileNo;
                string ClaimKey = "ClaimKey".AppSettings();
                string ClaimIV = "ClaimIV".AppSettings();

                string EncMobileNo = Crypto.encrypt_AES(MobileNo, ClaimKey, ClaimIV, 256, 128);

                if (!string.IsNullOrEmpty(EncMobileNo))
                {
                    Dictionary<object, object> _Dict = new()
                    {
                        { "SourceKey", "ClaimSourceKey".AppSettings() },
                        { "ValidationKey", "ClaimValidationKey".AppSettings() },
                        { "SecretKey", EncMobileNo }
                    };

                    var response = CommonAPICall.CallAPI(URL, "", "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", _Dict);
                    if (CoreCommonMethods.IsValidString(response))
                    {
                        ClaimCallDataRoot lstClaimCallDataRoot = JsonConvert.DeserializeObject<ClaimCallDataRoot>(response);
                        if (lstClaimCallDataRoot != null && lstClaimCallDataRoot.Data != null && lstClaimCallDataRoot.Data.Count > 0)
                        {
                            objAgentCallDetails.AddRange(from dr in lstClaimCallDataRoot.Data.AsEnumerable()
                                                         select new AgentCallDetails
                                                         {
                                                             EmployeeID = Convert.ToString(dr.EmployeeId),
                                                             Duration = Convert.ToInt32(dr.Duration),
                                                             CallDate = Convert.ToString(dr.CallDateTime),
                                                             CallingNo = dr.CallingNo != "" ? Convert.ToInt64(dr.CallingNo) : 0,
                                                             Name = Convert.ToString(dr.AgentName),
                                                             ProductName = "Claim Support"
                                                         });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(MobileNo), ex.ToString(), "AddClaimData", "LeadPrioritizationBLL", "Communication", "", string.Empty, DateTime.Now, DateTime.Now);
            }
            return objAgentCallDetails;
        }

        public string GetTermCustomerExitPoint(string exitPointURL, long LeadID)
        {
            string result = null;
            DateTime requesttime = DateTime.Now;
            string error = null, url = string.Empty, json = string.Empty;
            try
            {
                url = "TermCjOldAPI".AppSettings() + "/api/exit-point/url";
                json = "{\"exitPointUrl\":\"" + exitPointURL + "\"}";

                var header = new Dictionary<object, object>();
                header.Add("Authorization", "TermCJUrlAuth".AppSettings());

                var data = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                dynamic dynamicResult = JsonConvert.DeserializeObject<dynamic>(data);
                if (dynamicResult != null && dynamicResult.exitPointUrl != null)
                {
                    result = dynamicResult.exitPointUrl;
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadID.ToString(), LeadID, error, "GetTermExitPointUrl", "MatrixCore", "SalesViewBLL", "url - " + url + " , json - " + json, result, requesttime, DateTime.Now);
            }
            return result;
        }

        public string GetInvestmentCustomerExitPoint(string exitPointURL, long LeadID)
        {
            string result = null;
            DateTime requesttime = DateTime.Now;
            string error = null, url = string.Empty, json = string.Empty;
            try
            {
                url = "InvestmentCJAPI".AppSettings() + "/api/enqapi/prequote/GetCustomerJourneyURL";
                json = "{\"URL\":\"" + exitPointURL + "\"}";

                var header = new Dictionary<object, object>();
                header.Add("cjToken", "InvestmentCJUrlAuth".AppSettings());

                var data = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                dynamic dynamicResult = JsonConvert.DeserializeObject<dynamic>(data);
                if (dynamicResult != null && dynamicResult.ReturnValue != null)
                {
                    result = dynamicResult.ReturnValue;
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadID.ToString(), LeadID, error, "GetTermExitPointUrl", "MatrixCore", "SalesViewBLL", "url - " + url + " , json - " + json, result, requesttime, DateTime.Now);
            }
            return result;
        }

        public string GetHealthCustomerExitPoint(string exitPointURL, long leadId, string role)
        {
            string result = null;
            DateTime requesttime = DateTime.Now;
            string url = string.Empty, json = string.Empty;

            try
            {
                url = "HealthApiBaseUrl".AppSettings() + "applicationservices/HealthMasterServices.svc/GetNewExitPointUrl";
                json = "{\"ExitPointURL\":\"" + exitPointURL + "\",\"Role\":\"" + role + "\"}";

                var header = new Dictionary<object, object>();
                header.Add("apikey", "healthApiKey".AppSettings());

                var data = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                dynamic dynamicResult = JsonConvert.DeserializeObject<dynamic>(data);
                if (dynamicResult != null && dynamicResult.ExitPointURL != null)
                {
                    result = dynamicResult.ExitPointURL;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.Message, "HealthCustomerExitPoint", "MatrixCore", "SalesViewBLL", "url - " + url + " , json - " + json, result, requesttime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, "", "HealthCustomerExitPoint", "MatrixCore", "SalesViewBLL", "url - " + url + " , json - " + json, result, requesttime, DateTime.Now);
            }

            return result;
        }

        public LeadDetailsEntity GetLeadInfoByLeadId(long leadId)
        {
            LeadDetailsEntity response = null;
            try
            {
                var dt = LeadPrioritizationDLL.GetLeadInfoByLeadId(leadId);
                if (dt != null && dt.Rows.Count > 0)
                {
                    response = new LeadDetailsEntity
                    {
                        LeadID = dt.Rows[0]["LeadID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["LeadID"]) : default,
                        Name = dt.Rows[0]["Name"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["Name"]) : default,
                        DOB = dt.Rows[0]["DOB"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["DOB"]) : default,
                        CityId = dt.Rows[0]["CityId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["CityId"]) : default,
                        StateId = dt.Rows[0]["StateId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["StateId"]) : default,
                        ProductID = dt.Rows[0]["ProductId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["ProductId"]) : default,
                        PolicyTypeId = dt.Rows[0]["PolicyTypeId"] != DBNull.Value ? Convert.ToByte(dt.Rows[0]["PolicyTypeId"]) : default,
                        InvestmentTypeID = dt.Rows[0]["InvestmentTypeID"] != DBNull.Value ? Convert.ToByte(dt.Rows[0]["InvestmentTypeID"]) : default,
                        NoOfLives = dt.Rows[0]["TotalNoOfLives"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["TotalNoOfLives"]) : default,
                        NoOfEmployees = dt.Rows[0]["TotalNoOfEmployees"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["TotalNoOfEmployees"]) : default,
                        OccupancyId = dt.Rows[0]["OccupancyId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["OccupancyId"]) : default,
                        CoverTypeId = dt.Rows[0]["CoverTypeId"] != DBNull.Value ? Convert.ToByte(dt.Rows[0]["CoverTypeId"]) : default,
                        CompanyName = dt.Rows[0]["CompanyName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["CompanyName"]) : default,
                        InsuredName = dt.Rows[0]["InsuredName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["InsuredName"]) : default,
                        FamilyType = dt.Rows[0]["FamilyType"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["FamilyType"]) : default,
                        AssociationId = dt.Rows[0]["AssociationId"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["AssociationId"]) : default,
                        ShipmentType = dt.Rows[0]["ShipmentType"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["ShipmentType"]) : default,
                        TransitType = dt.Rows[0]["TransitType"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["TransitType"]) : default,
                        SumInsuredType = dt.Rows[0]["SumInsuredType"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["SumInsuredType"]) : default,
                        MedicalExtension = dt.Rows[0]["MedicalExtension"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["MedicalExtension"]) : default,
                        SA = dt.Rows[0]["SA"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["SA"]) : default,
                    };
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.Message, "GetLeadInfoByLeadId", "MatrixCore", "LeadDetailsBLL", leadId.ToString(), string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }


        public BasicLeadDetails GetLeadDetailsByLeadID(long LeadId)
        {
            BasicLeadDetails obj = null;
            string Exception = string.Empty;
            DateTime RequestDateTime = DateTime.Now;
            try
            {
                var oDataSet = PGDLL.GetLeadDetailsByLeadID(LeadId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    obj = new BasicLeadDetails();
                    DataTable dbResult = oDataSet.Tables[0];
                    obj = new BasicLeadDetails
                    {
                        LeadID = Convert.ToInt64(dbResult.Rows[0]["LeadID"]),
                        CreatedON = Convert.ToDateTime(dbResult.Rows[0]["CreatedON"].ToString()),
                        LeadSource = dbResult.Rows[0]["LeadSource"].ToString(),
                        MaritalStatus = dbResult.Rows[0]["MaritalStatus"].ToString(),
                        Name = dbResult.Rows[0]["Name"].ToString(),
                        ParentID = dbResult.Rows[0]["ParentID"].ToString().TryParseToInt64(),
                        IsParent = dbResult.Rows[0]["ParentID"].ToString().TryParseToInt64() == 0 ? true : false,
                        PostCode = dbResult.Rows[0]["PostCode"].ToString(),
                        ProductID = dbResult.Rows[0]["ProductID"].ToString().TryParseToInt32(),
                        Source = dbResult.Rows[0]["Source"].ToString(),
                        ReferralID = dbResult.Rows[0]["ReferralID"].ToString().TryParseToInt64(),
                        StateID = dbResult.Rows[0]["StateID"].ToString().TryParseToInt32(),
                        Utm_campaign = dbResult.Rows[0]["Utm_campaign"].ToString(),
                        UTM_Medium = dbResult.Rows[0]["UTM_Medium"].ToString(),
                        Utm_source = dbResult.Rows[0]["Utm_source"].ToString(),
                        Utm_term = dbResult.Rows[0]["Utm_term"].ToString(),
                        NoCostEMI = (dbResult.Rows[0]["NoCostEMI"]).ToString() == string.Empty ? null : (bool?)(dbResult.Rows[0]["NoCostEMI"]),
                        MonthlyMode = dbResult.Rows[0]["MonthlyMode"].ToString(),
                        ACH = dbResult.Rows[0]["ACH"].ToString(),
                        IsSmartCollect = Convert.ToInt16(dbResult.Rows[0]["IsSmartCollect"]),
                        AssignToGroupId = Convert.ToInt32(dbResult.Rows[0]["AssignToGroupId"])
                    };
                }
            }
            catch (Exception ex)
            {
                Exception = ex.StackTrace.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, Exception, "GetLeadDetailsByLeadID", "LeadDetailsBLL", "MatrixCore", string.Empty, string.Empty, RequestDateTime, DateTime.Now);
            }
            finally
            {

            }
            return obj;
        }
        public List<ScheduledCallbackData> GetCallBackDataByAgentId(long AgentId, string Source)
        {
            string Exception = string.Empty;
            DateTime RequestDateTime = DateTime.Now;
            var response = new List<ScheduledCallbackData>();
            try
            {
                if (AgentId > 0)
                {
                    var data = LeadPrioritizationDLL.GetCallBackDataByAgentId(Convert.ToInt64(AgentId), Source);
                    if (data != null && data.Tables != null && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0)
                    {
                        response = (from dr in data.Tables[0].AsEnumerable()
                                    select new ScheduledCallbackData()
                                    {
                                        LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : default,
                                        EventDate = dr["EventDate"] != DBNull.Value ? Convert.ToDateTime(dr["EventDate"]) : default,
                                        IsGoogleInvite = dr["IsGoogleInvite"] != DBNull.Value ? Convert.ToInt16(dr["IsGoogleInvite"]) : default,
                                        CustomerId = dr["CustomerID"] != DBNull.Value ? Convert.ToInt64(dr["CustomerID"]) : default,
                                    }).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                Exception = ex.StackTrace.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", AgentId, Exception, "GetCallBackDataByAgentId", "LeadDetailsBLL", "MatrixCore", string.Empty, string.Empty, RequestDateTime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", AgentId, Exception, "GetCallBackDataByAgentId", "LeadDetailsBLL", "MatrixCore", string.Empty, string.Empty, RequestDateTime, DateTime.Now);
            }
            return response;
        }
        public AssignedLeadData AssignLeadToAgent(long leadId)
        {
            AssignedLeadData result = new();
            DateTime requesttime = DateTime.Now;
            string url = string.Empty;

            try
            {
                url = "AllocationApi".AppSettings() + "SME/AssignLeadToAgent?leadId=" + leadId;

                Dictionary<object, object> header = new()
                {
                    { "source", "matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey", "matrixAPIclientKey".AppSettings() }
                };

                var data = CommonAPICall.CallAPI(url, "", "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                AssignedLeadData response = JsonConvert.DeserializeObject<AssignedLeadData>(data);
                if (response != null)
                {
                    result = response;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.Message, "AssignLeadToAgent", "MatrixCore", "SalesViewBLL", "url - " + url + " , leadId - " + leadId, Convert.ToString(result), requesttime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, "", "AssignLeadToAgent", "MatrixCore", "SalesViewBLL", "url - " + url + " , leadId - " + leadId, Convert.ToString(result), requesttime, DateTime.Now);
            }

            return result;
        }
        public ResponseData<List<LeadDetailsForCustId>> GetActiveLeadsByCustId(long customerId)
        {
            ResponseData<List<LeadDetailsForCustId>> response = new ResponseData<List<LeadDetailsForCustId>>()
            {
                Status = false,
                Message = string.Empty,
                Data = new List<LeadDetailsForCustId>()
            };
            DateTime reqTime = DateTime.Now;
            string responseDB = string.Empty;

            try
            {
                if (customerId > 0)
                {
                    DataSet ds = LeadDetailsDLL.GetActiveLeadsByCustId(customerId);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[1].Rows.Count > 0)
                    {
                        responseDB = Convert.ToString(ds.Tables[1].Rows[0]["Message"] != null && ds.Tables[1].Rows[0]["Message"] != DBNull.Value ? ds.Tables[1].Rows[0]["Message"] : string.Empty);

                        if (!string.IsNullOrEmpty(responseDB) && responseDB.Equals("Success!") && ds.Tables[0].Rows.Count > 0)
                        {
                            foreach (DataRow row in ds.Tables[0].Rows)
                            {
                                LeadDetailsForCustId obj = new LeadDetailsForCustId()
                                {
                                    LeadId = Convert.ToInt64(row["LeadId"] != null && row["LeadId"] != DBNull.Value ? row["LeadId"] : 0),
                                    ProductId = Convert.ToInt32(row["ProductID"] != null && row["ProductID"] != DBNull.Value ? row["ProductID"] : 0),
                                    ProductName = Convert.ToString(row["ProductDisplayName"] != null && row["ProductDisplayName"] != DBNull.Value ? row["ProductDisplayName"] : string.Empty),
                                    CreatedON = Convert.ToDateTime(row["CreatedON"] != null && row["CreatedON"] != DBNull.Value ? row["CreatedON"] : string.Empty),
                                    StatusID = Convert.ToInt32(row["StatusID"] != null && row["StatusID"] != DBNull.Value ? row["StatusID"] : 0),
                                    StatusName = Convert.ToString(row["StatusName"] != null && row["StatusName"] != DBNull.Value ? row["StatusName"] : string.Empty),
                                    RegistrationNo = Convert.ToString(row["RegistrationNo"] != null && row["RegistrationNo"] != DBNull.Value ? row["RegistrationNo"] : string.Empty)
                                };

                                response.Data.Add(obj);
                            }
                            response.Status = true;
                            response.Message = "Success!";
                        }
                        else if (!string.IsNullOrEmpty(responseDB) && responseDB.Equals("Invalid CustomerIds exists!"))
                        {
                            response.Message = "Invalid customerId Exists!";
                        }
                        else
                        {
                            response.Message = "No Data Found!";
                        }
                    }
                }
                else
                {
                    response.Message = "Invalid CustomerId!";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", customerId, ex.ToString(), "GetActiveLeadsByCustId", "LeadDetailsBLL", "MatrixCore", string.Empty, JsonConvert.SerializeObject(response), reqTime, DateTime.Now);
            }
            return response;
        }


        public LeadDetailsResponse GetLeadDetailsWithAgentInfo(long leadId)
        {
            LeadDetailsResponse response = new();
            try
            {
                var ds = LeadDetailsDLL.GetRenewalLeadDetailsWA(leadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    var leadDetailsRow = ds.Tables[0].Rows[0];
                    response = new LeadDetailsResponse
                    {
                        AgentName = leadDetailsRow["AgentName"] != DBNull.Value ? Convert.ToString(leadDetailsRow["AgentName"]) : default,
                        SubProductName = leadDetailsRow["SubProductName"] != DBNull.Value ? Convert.ToString(leadDetailsRow["SubProductName"]) : "Insurance",
                        CustomerName = leadDetailsRow["Name"] != DBNull.Value ? Convert.ToString(leadDetailsRow["Name"]) : "Sir/Madam",
                        PolicyNumber = leadDetailsRow["PolicyNumber"] != DBNull.Value ? Convert.ToString(leadDetailsRow["PolicyNumber"]) : "Policy",
                        ProductAbbreviation = leadDetailsRow["Abbreviation"] != DBNull.Value ? Convert.ToString(leadDetailsRow["Abbreviation"]) : "Insurance",
                        AgentEcode = leadDetailsRow["AgentEcode"] != DBNull.Value ? Convert.ToString(leadDetailsRow["AgentEcode"]) : default,
                        SubproductId = leadDetailsRow["InvestmentTypeID"] != DBNull.Value ? Convert.ToInt16(leadDetailsRow["InvestmentTypeID"]) : default,
                    };
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(leadId), leadId, ex.ToString(), "GetLeadDetailsWithAgentInfo", "LeadDetailsBLL", "MatrixCore", string.Empty, JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }


            return response;
        }

        public long FetchParentLead(long LeadID, int ProductID, long CustomerID)
        {
            DateTime reqTime = DateTime.Now;
            string strexception = string.Empty;
            long parentLeadId = 0;

            try
            {
                parentLeadId = LeadDetailsDLL.FetchParentLead(LeadID, ProductID, CustomerID);
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), LeadID, strexception, "FetchParentLead", "MatrixCore", "LeadDetailsBLL", string.Empty, string.Empty, reqTime, DateTime.Now);
            }

            return parentLeadId;
        }
    }

}