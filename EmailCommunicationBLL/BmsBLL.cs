﻿using DataAccessLibrary;
using Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Primitives;
using MongoConfigProject;
using Newtonsoft.Json;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace EmailCommunicationBLL
{
    public class BmsBLL : IBmsBLL
    {
        public string GetRMDetails(Int64 CustomerId)
        {
            {
                string url = string.Empty;
                string Response = string.Empty;
                string api = "bmsApiServiceurl".AppSettings() + "Partner/GetRMDetailsByCustomerID";
                string strexception = string.Empty;

                try
                {
                    Dictionary<object, object> header = new Dictionary<object, object>();

                    header.Add("AppId", "Matrix");
                    header.Add("AppKey", "bmsAppKey".AppSettings());
                    //header.Add("Content-Type", "text/plain");
                    string content = "{\"CustomerId\":" + CustomerId + "}";
                    var result = CommonAPICall.CallAPI(api, content, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);

                    if (!string.IsNullOrEmpty(result))
                    {
                        if (result != null)
                        {
                            Response = result;
                        }
                    }
                }
                catch (Exception ex)
                {
                    strexception = ex.ToString();
                    LoggingHelper.LoggingHelper.Log(CustomerId.ToString(), 0, strexception, "GetRMDetails", "BmsBLL", "MatrixCoreAPI", CustomerId.ToString(), "", DateTime.Now, DateTime.Now);
                }
                return Response;
            }
        }

        public bookingModel GetAgentTypeByBooking(Int64 AgentId, Int64 BookingId, long bookingDate, string EncKey, string EncIV)
        {
            var obookingModel = new bookingModel
            {
                statusCode = 404
            };
            string exception = string.Empty;
            StringBuilder sb = new StringBuilder();
            DateTime dt = DateTime.Now;
            try
            {

                if (AgentId > 0)
                {
                    obookingModel.SalesAgentId = AgentId;
                }

                if (bookingDate > 0 && (AgentId == 0))
                {
                    DateTime date = CoreCommonMethods.UnixTimeToDateTime(Convert.ToInt64(bookingDate));
                    //date = date.AddMinutes(-330);//adjust 05:30

                    obookingModel.SalesAgentId = SalesViewDLL.GetSalesAgent(BookingId, date);


                }

                if (obookingModel.SalesAgentId == 0)
                {
                    return obookingModel;
                }

                DataSet ds = SalesViewDLL.GetAgentTypeByBooking(obookingModel.SalesAgentId, BookingId);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                   
                    DataRow row = ds.Tables[0].Rows[0];
                    obookingModel.AssigmentId = row["AssigmentId"] == DBNull.Value ? 0 : Convert.ToInt32(row["AssigmentId"]);
                    obookingModel.BookingType = row["AssignmentType"] == DBNull.Value ? "" : Convert.ToString(row["AssignmentType"]);
                    obookingModel.SalesAgentGroupId = row["SalesAgentGroupId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(row["SalesAgentGroupId"]);
                    //obookingModel.SalesAgentGroupName = row["SalesAgentGroupName"] == DBNull.Value ? Convert.ToString("") : Convert.ToString(row["SalesAgentGroupName"]);
                    if (ds.Tables[0].Columns.Contains("LanguageID"))
                    {
                        obookingModel.LanguageId = row["LanguageID"] == DBNull.Value ? 0 : Convert.ToInt32(row["LanguageID"]);
                    }
                    if (ds.Tables[0].Columns.Contains("IsCrossBUSale"))
                    {
                        obookingModel.IsCrossBUSale = row["IsCrossBUSale"] == DBNull.Value ? false : Convert.ToBoolean(row["IsCrossBUSale"]);
                    }
                    if (ds.Tables[0].Columns.Contains("SalesAgentLocation"))
                    {
                        obookingModel.SalesAgentLocation = row["SalesAgentLocation"] == DBNull.Value ? "" : Convert.ToString(row["SalesAgentLocation"]);
                    }
                    if (ds.Tables[0].Columns.Contains("AppointmentUserProcessId"))
                    {
                        obookingModel.AppointmentUserProcessId = row["AppointmentUserProcessId"] == DBNull.Value ?0 : Convert.ToInt32(row["AppointmentUserProcessId"]);
                    }
                    if (bookingDate > 0)
                    {
                        var _Data = AppendLeadProductDetails(BookingId, EncKey, EncIV);
                        if (_Data != null)
                        {
                            obookingModel.LeadProductData = new List<LeadProduct> { _Data };
                            if (_Data.ParentID > 0)
                            {
                                var _ParentData = AppendLeadProductDetails(_Data.ParentID, EncKey, EncIV);
                                if (_ParentData != null)
                                    obookingModel.LeadProductData.Add(_ParentData);
                            }

                        }


                    }
                    obookingModel.statusCode = 200;
                    long ParentId = ds.Tables[0].Columns.Contains("ParentId") ? row["ParentId"] == DBNull.Value ? 0 : Convert.ToInt64(row["ParentId"]) : 0;
                    if (ParentId > 0)
                        SalesViewDLL.UpdateBookedFlag(ParentId);
                }

            }
            catch (Exception ex)
            {
                exception = ex.ToString();

                // string dataToPost = "{\"AgentId\":\"" + AgentId + "\",\"BookingId\":\"" + BookingId + "\"}";
                //LoggingHelper.LoggingHelper.AddloginQueue("", BookingId, ex.ToString(), "GetAgentTypeByBooking", "SalesViewBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(dataToPost), JsonConvert.SerializeObject(obookingModel), dt, DateTime.Now);
                var newobookingModel = new bookingModel
                {
                    statusCode = 404
                };
                sb.Append("enter in Exception" + JsonConvert.SerializeObject(newobookingModel) + "\r\n");
                return newobookingModel;
            }
            finally
            {
                sb.Append("enter in Finally" + JsonConvert.SerializeObject(obookingModel) + "\r\n");
                string dataToPost = "{\"AgentId\":\"" + AgentId + "\",\"BookingId\":\"" + BookingId + "\",\"bookingDate\":\"" + bookingDate + "\"}";
                LoggingHelper.LoggingHelper.AddloginQueue("", BookingId, exception, "GetAgentTypeByBooking", "BMSBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(dataToPost), sb.ToString(), dt, DateTime.Now);
            }
            return obookingModel;
        }

        public SaveInfo UpdateLeadFlags(LeadFlags oLeadFlags)
        {
            SaveInfo response = new SaveInfo();
            try
            {
                bool res = BmsDLL.UpdateLeadAttribute(oLeadFlags);
                response.StatusCode = res ? 200 : 404;
                response.IsSaved = res ? true : false;
                response.Message = res ? "Data Saved successfully" : "";
            }
            catch (Exception ex)
            {
                response.Message = "failed";
                response.StatusCode = 404;
                LoggingHelper.LoggingHelper.AddloginQueue("", oLeadFlags.LeadId, ex.ToString(), "UpdateLeadAttribute", "MatrixCore", "SalesViewBLL", oLeadFlags.LeadId.ToString(), JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public NewSVURLModel GetNewSVURL(Int64 LeadId, Int64 UserId, string Source)
        {
            NewSVURLModel oNewSVURLModel = new NewSVURLModel();
            oNewSVURLModel.StatusCode = 404;
            long CustomerId = 0;
            int ProductId = 0;
            long RenewalLeadId = 0;
            String NewSVInitials = "NewSVURL".AppSettings().ToString();
            string EncLeadDetails = string.Empty;
            DateTime RequestTime = DateTime.Now;

            try
            {
                DataSet ds = BmsDLL.GetNewSVURL(LeadId, Source);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    DataRow row = ds.Tables[0].Rows[0];
                    CustomerId = row["CustomerId"] == DBNull.Value ? 0 : Convert.ToInt64(row["CustomerId"]);
                    ProductId = row["ProductId"] == DBNull.Value ? 0 : Convert.ToInt32(row["ProductId"]);
                    RenewalLeadId = row["RenewalLead"] == DBNull.Value ? 0 : Convert.ToInt64(row["RenewalLead"]);

                    if (Source == "SalesView")
                    {
                        RenewalLeadId = LeadId;
                    }

                    if (CustomerId != 0 && ProductId != 0)
                    {
                        EncLeadDetails = Convert.ToBase64String(Encoding.UTF8.GetBytes(CustomerId.ToString() + "/" + ProductId.ToString() + "/" + RenewalLeadId.ToString() + "//" + UserId.ToString()));
                        oNewSVURLModel.URL = NewSVInitials + EncLeadDetails;
                        oNewSVURLModel.StatusCode = 200;
                        oNewSVURLModel.StatusMessage = "Success";
                    }
                    else
                    {
                        oNewSVURLModel.URL = "";
                        oNewSVURLModel.StatusCode = 200;
                        oNewSVURLModel.StatusMessage = "Invalid LeadId";
                    }
                }
                else
                {
                    oNewSVURLModel.URL = "";
                    oNewSVURLModel.StatusCode = 200;
                    oNewSVURLModel.StatusMessage = "Invalid LeadId";
                }
                //LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), LeadId, "", "GetNewSVURL", "BMSBLL", "MatrixCoreAPI", NewSVInitials.ToString() + Source, oNewSVURLModel.URL, RequestTime, DateTime.Now);
            }
            catch (Exception ex)
            {
                oNewSVURLModel.URL = "";
                oNewSVURLModel.StatusCode = 500;
                oNewSVURLModel.StatusMessage = ex.StackTrace.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), LeadId, ex.ToString(), "GetNewSVURL", "BMSBLL", "MatrixCoreAPI", NewSVInitials.ToString() + Source, oNewSVURLModel.URL, RequestTime, DateTime.Now);
            }

            return oNewSVURLModel;

        }

        public SaveInfo UpdateNoCostEMI(Int64 LeadId, Int64 UserId, bool NoCostEMI)
        {
            SaveInfo saveInfo = new SaveInfo();
            saveInfo.StatusCode = 404;
            try
            {
                DataSet ds = BmsDLL.UpdateNoCostEMI(LeadId, UserId, NoCostEMI);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    DataRow row = ds.Tables[0].Rows[0];
                    saveInfo.StatusCode = 200;
                    saveInfo.IsSaved = Convert.ToInt32(row["Rcount"]) == 0 ? false : true;
                    saveInfo.Message = saveInfo.IsSaved ? "Updated NoCostEMI successfully" : "Update NoCostEMI was not successful";
                    LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), LeadId, "", "UpdateNoCostEMI", "BMSBLL", "MatrixCoreAPI", "", saveInfo.Message, DateTime.Now, DateTime.Now);
                }
            }
            catch (Exception ex)
            {
                saveInfo.StatusCode = 500;
                saveInfo.IsSaved = false;
                saveInfo.Message = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), LeadId, ex.ToString(), "UpdateNoCostEMI", "BMSBLL", "MatrixCoreAPI", "", saveInfo.Message, DateTime.Now, DateTime.Now);
            }
            return saveInfo;
        }

        public AgentSupervisor GetAgentSupervisor(int UserId)
        {
            AgentSupervisor agentSupervisor = new AgentSupervisor();
            string response = string.Empty;
            try
            {
                DataSet ds = BmsDLL.GetAgentSupervisor(UserId);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    DataRow row = ds.Tables[0].Rows[0];
                    agentSupervisor = new AgentSupervisor()
                    {
                        EmployeeId = row["EmployeeId"] == DBNull.Value ? string.Empty : row["EmployeeId"].ToString(),
                        EmailID = row["Email"] == DBNull.Value ? string.Empty : row["Email"].ToString(),
                        UserName = row["UserName"] == DBNull.Value ? string.Empty : row["UserName"].ToString(),
                        Supervisors = new List<Supervisor>()
                    };
                    if (row["l1EmployeeId"] != DBNull.Value)
                    {
                        Supervisor supervisor = new Supervisor()
                        {
                            Level = 1,
                            EmployeeId = row["l1EmployeeId"] == DBNull.Value ? string.Empty : row["l1EmployeeId"].ToString(),
                            EmailID = row["l1Email"] == DBNull.Value ? string.Empty : row["l1Email"].ToString(),
                            UserName = row["l1UserName"] == DBNull.Value ? string.Empty : row["l1UserName"].ToString(),
                        };
                        agentSupervisor.Supervisors.Add(supervisor);
                    }
                    if (row["l2EmployeeId"] != DBNull.Value)
                    {
                        Supervisor supervisor = new Supervisor()
                        {
                            Level = 2,
                            EmployeeId = row["l2EmployeeId"] == DBNull.Value ? string.Empty : row["l2EmployeeId"].ToString(),
                            EmailID = row["l2Email"] == DBNull.Value ? string.Empty : row["l2Email"].ToString(),
                            UserName = row["l2UserName"] == DBNull.Value ? string.Empty : row["l2UserName"].ToString(),
                        };
                        agentSupervisor.Supervisors.Add(supervisor);
                    }
                }
            }
            catch (Exception ex)
            {
                response = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), 0, response, "GetAgentSupervisor", "BMSBLL", "MatrixCoreAPI", UserId.ToString(), agentSupervisor.ToString(), DateTime.Now, DateTime.Now);
            }

            return agentSupervisor;
        }

        public string GetInternalEmailURL(GenerateUrlModel ogenerateUrlModel)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;

            string url = "pbserviceDomain".AppSettings() + "maildashboard/generateurl";
            string token = "PbServiceAPIToken".AppSettings();
            try
            {
                Dictionary<object, object> header = new Dictionary<object, object>();
                header.Add("requestingsystem", "Matrix");
                header.Add("token", token);

                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                string dataToPost = "{\"LeadId\":\"" + ogenerateUrlModel.LeadId + "\",\"UserId\":\"" + ogenerateUrlModel.UserId + "\",\"ProductId\":\"" + ogenerateUrlModel.TypeId + "\"}";

                var data = CommonAPICall.CallAPI(url, dataToPost, "POST", timeout, "application/json", header);
                if (CoreCommonMethods.IsValidString(data))
                    result = data;

            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(ogenerateUrlModel.LeadId), Error, "GetInternalEmailURL", "BMSBLL", "GetInternalEmailURL", JsonConvert.SerializeObject(ogenerateUrlModel), result, dt, DateTime.Now);
            }
            return result;
        }

        public bool GetNoCostEMI(long ParentId)
        {
            bool result = false;
            string response = string.Empty;
            try
            {
                DataSet ds = BmsDLL.GetNoCostEMI(ParentId);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    result = ds.Tables[0].Rows[0]["NoCostEMI"] == DBNull.Value ? false : Convert.ToBoolean(ds.Tables[0].Rows[0]["NoCostEMI"]);

                }
            }
            catch (Exception ex)
            {
                response = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(null, ParentId, response, "GetNoCostEMI", "BMSBLL", "MatrixCoreAPI", ParentId.ToString(), result.ToString(), DateTime.Now, DateTime.Now);
            }

            return result;
        }
        public List<RMCommentsDetailsResponse> GetRMComments(string LeadIds, string EmployeeID)
        {
            List<RMCommentsDetailsResponse> comments = new List<RMCommentsDetailsResponse>();
            string OldBookingIds = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(LeadIds))
                {
                    DataSet ds = BmsDLL.GetOldBookingIds(LeadIds);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        OldBookingIds = ds.Tables[0].Rows[0]["LeadIds"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["LeadIds"]);
                    }
                    if (!string.IsNullOrEmpty(OldBookingIds))
                    {
                        string json = "{\"LeadIds\":[" + OldBookingIds + "]}";
                        string url = ("PIVC".AppSettings()).ToString() + "GetRMComments";
                        Dictionary<object, object> objHeaders = new Dictionary<object, object>() { { "Token", "PIVCToken".AppSettings() } };
                        string response = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", objHeaders);
                        if (!string.IsNullOrEmpty(response))
                        {
                            var Response = JsonConvert.DeserializeObject<RMCommentsAPIresponse>(response);
                            if (Response.RMCommentsDetailsResponse.Count > 0)
                            {
                                comments = Response.RMCommentsDetailsResponse.Where(c => (c.EmployeeId == EmployeeID)).ToList();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(EmployeeID, 0, ex.ToString(), "GetRMComments", "MatrixCore", null, LeadIds.ToString(), comments.ToString(), DateTime.Now, DateTime.Now);
            }
            return comments;
        }
        public CallTransferLeadInfo GetleadDetailsForCalltranfer(long leadId, string TransferType)
        {
            CallTransferLeadInfo obj = new CallTransferLeadInfo();
            DateTime reqDt = DateTime.Now;
            string reqbody = string.Empty;
            string url = "bmsApiServiceurl".AppSettings() + "booking/GetLeadDetailsForCallTransfer";
            try
            {
                Dictionary<object, object> header = new Dictionary<object, object>();
                header.Add("AppId", "Matrix1.0");
                header.Add("AppKey", "bmsServiceToken".AppSettings());
                //reqbody = !string.IsNullOrEmpty(TransferType)
                //    ? "{\"LeadId\":" + leadId + ",\"TransferType\":\"" + TransferType + "\"}"
                //    : "{\"LeadId\":" + leadId + "}";
                reqbody = "{\"LeadId\":" + leadId + ",\"TransferType\":\"" + TransferType + "\"}";
                var result = CommonAPICall.CallAPI(url, reqbody, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                if (!string.IsNullOrEmpty(result))
                {
                    var d = JsonConvert.DeserializeObject<dynamic>(result);
                    obj.LeadID = Convert.ToInt64(d.LeadID);
                    obj.ApplicationNo = Convert.ToString(d.ApplicationNo);
                    obj.InsurerName = Convert.ToString(d.InsurerName);
                    obj.InsurerId = Convert.ToInt32(d.InsurerId);
                    obj.Campaign = Convert.ToString(d.Contex);
                    obj.TransferType = "sales_to_service";
                    obj.ServiceAgentGrade = Convert.ToByte(d.AgentGrade);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(leadId.ToString(), leadId, ex.ToString(), "GetleadDetailsForCalltranfer", "BmsBLL", "MatrixCoreAPI", leadId.ToString(), obj.ToString(), reqDt, DateTime.Now);
            }
            return obj;
        }
        public LeadProduct AppendLeadProductDetails(Int64 BookingId, string EncKey, string EncIV)
        {
            LeadProduct obookingModel = new LeadProduct();
            DataSet ds = SalesViewDLL.GetLeadProductDetails(BookingId);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                DataRow row = ds.Tables[0].Rows[0];
                obookingModel.LeadID = row["LeadID"] != null && row["LeadID"] != DBNull.Value ? Convert.ToInt64(row["LeadID"]) : Convert.ToInt64(0);
                obookingModel.Name = row["Name"] != null && row["Name"] != DBNull.Value ? Convert.ToString(row["Name"]) : string.Empty;
                obookingModel.Gender = row["Gender"] != null && row["Gender"] != DBNull.Value ? Convert.ToString(row["Gender"]) : string.Empty;
                obookingModel.DOB = row["DOB"] != null && row["DOB"] != DBNull.Value ? Convert.ToString(row["DOB"]) : string.Empty;

                obookingModel.MobileNo = row["MobileNo"] != null && row["MobileNo"] != DBNull.Value && (CoreCommonMethods.IsValidInteger(Convert.ToString(row["MobileNo"])) > 0) ?
                    Crypto.Encrytion_Payment_AES(Convert.ToString(row["MobileNo"]), "Core", 256, 128, EncKey, EncIV, false)
                     : string.Empty;
                obookingModel.AltPhoneNo = row["AltPhoneNo"] != null && row["AltPhoneNo"] != DBNull.Value && (CoreCommonMethods.IsValidInteger(Convert.ToString(row["AltPhoneNo"])) > 0) ?
                    Crypto.Encrytion_Payment_AES(Convert.ToString(Convert.ToString(row["AltPhoneNo"])), "Core", 256, 128, EncKey, EncIV, false)
                    : string.Empty;
                obookingModel.EmailID = row["EmailID"] != null && row["EmailID"] != DBNull.Value && (CoreCommonMethods.IsValidString(Convert.ToString(row["EmailID"]))) ?
                    Crypto.Encrytion_Payment_AES(Convert.ToString(Convert.ToString(row["EmailID"])), "Core", 256, 128, EncKey, EncIV, false) : string.Empty;

                obookingModel.Address = row["Address"] != null && row["Address"] != DBNull.Value ? Convert.ToString(row["Address"]) : string.Empty;
                obookingModel.CityID = row["CityID"] != null && row["CityID"] != DBNull.Value ? Convert.ToString(row["CityID"]) : string.Empty;
                obookingModel.StateID = row["StateID"] != null && row["StateID"] != DBNull.Value ? Convert.ToInt16(row["StateID"]) : Convert.ToInt16(0);
                obookingModel.PostCode = row["PostCode"] != null && row["PostCode"] != DBNull.Value ? Convert.ToString(row["PostCode"]) : string.Empty;
                obookingModel.Country = row["Country"] != null && row["Country"] != DBNull.Value ? Convert.ToString(row["Country"]) : string.Empty;
                obookingModel.MaritalStatus = row["MaritalStatus"] != null && row["MaritalStatus"] != DBNull.Value ? Convert.ToString(row["MaritalStatus"]) : string.Empty;
                obookingModel.AnnualIncome = row["AnnualIncome"] != null && row["AnnualIncome"] != DBNull.Value ? Convert.ToString(row["AnnualIncome"]) : string.Empty;
                obookingModel.LeadSource = row["LeadSource"] != null && row["LeadSource"] != DBNull.Value ? Convert.ToString(row["LeadSource"]) : string.Empty;
                obookingModel.AnnualIncome = row["AnnualIncome"] != null && row["AnnualIncome"] != DBNull.Value ? Convert.ToString(row["AnnualIncome"]) : string.Empty;
                obookingModel.ExitPointURL = row["ExitPointURL"] != null && row["ExitPointURL"] != DBNull.Value ? Convert.ToString(row["ExitPointURL"]) : string.Empty;
                obookingModel.Utm_source = row["Utm_source"] != null && row["Utm_source"] != DBNull.Value ? Convert.ToString(row["Utm_source"]) : string.Empty;
                obookingModel.UTM_Medium = row["UTM_Medium"] != null && row["UTM_Medium"] != DBNull.Value ? Convert.ToString(row["UTM_Medium"]) : string.Empty;
                obookingModel.Utm_term = row["Utm_term"] != null && row["Utm_term"] != DBNull.Value ? Convert.ToString(row["Utm_term"]) : string.Empty;
                obookingModel.Utm_campaign = row["Utm_campaign"] != null && row["Utm_campaign"] != DBNull.Value ? Convert.ToString(row["Utm_campaign"]) : string.Empty;
                obookingModel.ProductID = row["ProductID"] != null && row["ProductID"] != DBNull.Value ? Convert.ToInt16(row["ProductID"]) : Convert.ToInt16(0);
                obookingModel.CustomerID = row["CustomerID"] != null && row["CustomerID"] != DBNull.Value ? Convert.ToInt64(row["CustomerID"]) : 0;
                obookingModel.CreatedON = row["CreatedON"] != null && row["CreatedON"] != DBNull.Value ? Convert.ToDateTime(row["CreatedON"]) : DateTime.MinValue;
                obookingModel.UpdatedON = row["UpdatedON"] != null && row["UpdatedON"] != DBNull.Value ? Convert.ToDateTime(row["UpdatedON"]) : DateTime.MinValue;
                obookingModel.ReferralID = row["ReferralID"] != null && row["ReferralID"] != DBNull.Value ? Convert.ToInt64(row["ReferralID"]) : 0;
                obookingModel.AddOnParentID = row["AddOnParentID"] != null && row["AddOnParentID"] != DBNull.Value ? Convert.ToInt64(row["AddOnParentID"]) : 0;
                obookingModel.ProductType = row["ProductType"] != null && row["ProductType"] != DBNull.Value ? Convert.ToInt16(row["ProductType"]) : Convert.ToInt16(0);
                obookingModel.DateOfBirth = row["DateOfBirth"] != null && row["DateOfBirth"] != DBNull.Value ? Convert.ToDateTime(row["DateOfBirth"]) : DateTime.MinValue;
                obookingModel.Source = row["Source"] != null && row["Source"] != DBNull.Value ? Convert.ToString(row["Source"]) : string.Empty;
                obookingModel.LeadCreationSource = row["LeadCreationSource"] != null && row["LeadCreationSource"] != DBNull.Value ? Convert.ToString(row["LeadCreationSource"]) : string.Empty;
                obookingModel.utm_content = row["utm_content"] != null && row["utm_content"] != DBNull.Value ? Convert.ToString(row["utm_content"]) : string.Empty;
                obookingModel.EnquiryId = row["EnquiryId"] != null && row["EnquiryId"] != DBNull.Value ? Convert.ToInt64(row["EnquiryId"]) : 0;
                obookingModel.EncryptionText = row["EncryptionText"] != null && row["EncryptionText"] != DBNull.Value ? Convert.ToString(row["EncryptionText"]) : string.Empty;
                obookingModel.EncryptedLeadId = row["EncryptedLeadId"] != null && row["EncryptedLeadId"] != DBNull.Value ? Convert.ToString(row["EncryptedLeadId"]) : string.Empty;
                obookingModel.AddOnParentID = row["AddOnParentID"] != null && row["AddOnParentID"] != DBNull.Value ? Convert.ToInt64(row["AddOnParentID"]) : 0;
                obookingModel.TenantId = row["TenantId"] != null && row["TenantId"] != DBNull.Value ? Convert.ToInt16(row["TenantId"]) : Convert.ToInt16(0);
                obookingModel.BusinessPartnerId = row["BusinessPartnerId"] != null && row["BusinessPartnerId"] != DBNull.Value ? Convert.ToInt32(row["BusinessPartnerId"]) : 0;
                obookingModel.ProfessionId = row["ProfessionId"] != null && row["ProfessionId"] != DBNull.Value ? Convert.ToInt16(row["ProfessionId"]) : Convert.ToInt16(0);
                obookingModel.ParentID = row["ParentID"] != null && row["ParentID"] != DBNull.Value ? Convert.ToInt64(row["ParentID"]) : 0;
                obookingModel.Term = row["Term"] != null && row["Term"] != DBNull.Value ? Convert.ToInt32(row["Term"]) : 0;
                obookingModel.TypeOfPolicy = row["TypeOfPolicy"] != null && row["TypeOfPolicy"] != DBNull.Value ? Convert.ToString(row["TypeOfPolicy"]) : string.Empty;
                obookingModel.InvestmentTypeID = row["InvestmentTypeID"] != null && row["InvestmentTypeID"] != DBNull.Value ? Convert.ToInt16(row["InvestmentTypeID"]) : Convert.ToInt16(0);
                obookingModel.Noofmembers = row["Noofmembers"] != null && row["Noofmembers"] != DBNull.Value ? Convert.ToInt16(row["Noofmembers"]) : Convert.ToInt16(0);
                obookingModel.PayTerm = row["PayTerm"] != null && row["PayTerm"] != DBNull.Value ? Convert.ToInt16(row["PayTerm"]) : Convert.ToInt16(0);
                obookingModel.PlanFeatureType = row["PlanFeatureType"] != null && row["PlanFeatureType"] != DBNull.Value ? Convert.ToString(row["PlanFeatureType"]) : string.Empty;
                obookingModel.CompanyName = row["CompanyName"] != null && row["CompanyName"] != DBNull.Value ? Convert.ToString(row["CompanyName"]) : string.Empty;
                obookingModel.TransitType = row["TransitType"] != null && row["TransitType"] != DBNull.Value ? Convert.ToString(row["TransitType"]) : string.Empty;
                obookingModel.Nationality = row["Nationality"] != null && row["Nationality"] != DBNull.Value ? Convert.ToString(row["Nationality"]) : string.Empty;
                obookingModel.SlotSource = row["SlotSource"] != null && row["SlotSource"] != DBNull.Value ? Convert.ToString(row["SlotSource"]) : string.Empty;

            }

            else
            {
                obookingModel.InternalResponse = "No Data from DB";
            }
            return obookingModel;
        }

        #region createBooking

        public int UpdateLeadDetails(LeadDetailsEntity reqData)
        {
            var result = 0;
            try
            {
                result = BmsDLL.UpdateLeadDetails(reqData);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(reqData.LeadID.ToString(), reqData.LeadID, ex.Message, "UpdateLeadDetails", "MatrixCore", "BmsBLL", JsonConvert.SerializeObject(reqData), string.Empty, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public LeadDetailsEntity GetLeadDetailsByLeadId(long leadId, string EncKey, string EncIV)
        {
            LeadDetailsEntity response = null;
            try
            {
                var dt = BmsDLL.GetLeadDetailsByLeadId(leadId);
                if (dt != null && dt.Rows.Count > 0)
                {
                    response = new LeadDetailsEntity
                    {
                        LeadID = dt.Rows[0]["LeadID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["LeadID"]) : default,
                        Name = dt.Rows[0]["Name"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["Name"]) : default,
                        //CustomerID = dt.Rows[0]["CustomerID"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(Convert.ToString(dt.Rows[0]["CustomerID"]), "Core", 256, 128, EncKey, EncIV, false) : default,
                        DOB = dt.Rows[0]["DOB"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["DOB"]) : default,
                        MobileNo = dt.Rows[0]["MobileNo"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(Convert.ToString(dt.Rows[0]["MobileNo"]), "Core", 256, 128, EncKey, EncIV, false) : default,
                        EmailID = dt.Rows[0]["EmailID"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(Convert.ToString(dt.Rows[0]["EmailID"]), "Core", 256, 128, EncKey, EncIV, false) : default,
                        //Address = dt.Rows[0]["Address"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["Address"]) : default,
                        CityId = dt.Rows[0]["CityId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["CityId"]) : default,
                        StateId = dt.Rows[0]["StateId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["StateId"]) : default,
                        //PostCode = dt.Rows[0]["PostCode"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["PostCode"]) : default,
                        //Country = dt.Rows[0]["Country"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["Country"]) : default,
                        //LeadSource = dt.Rows[0]["LeadSource"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["LeadSource"]) : default,
                        //ParentID = dt.Rows[0]["ParentID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["ParentID"]) : default,
                        ProductID = dt.Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["ProductID"]) : default,
                        //StatusID = dt.Rows[0]["StatusID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["StatusID"]) : default,
                        //SubStatusID = dt.Rows[0]["SubStatusID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["SubStatusID"]) : default,
                        //StatusName = dt.Rows[0]["StatusName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["StatusName"]) : default,
                        //SubStatusName = dt.Rows[0]["SubStatusName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["SubStatusName"]) : default,
                        //PreviousPolicyNo = dt.Rows[0]["PreviousPolicyNo"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["PreviousPolicyNo"]) : default,
                        //PlanID = dt.Rows[0]["PlanID"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["PlanID"]) : default,
                        //SupplierID = dt.Rows[0]["SupplierID"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["SupplierID"]) : default,
                        //Amount = dt.Rows[0]["Amount"] != DBNull.Value ? Convert.ToDouble(dt.Rows[0]["Amount"]) : default,
                        //EnquiryID = dt.Rows[0]["EnquiryID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["EnquiryID"]) : default,
                        //PolicyExpiryDate = dt.Rows[0]["PolicyExpiryDate"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["PolicyExpiryDate"]) : default,
                        //RenewalSupplierID = dt.Rows[0]["RenewalSupplierID"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["RenewalSupplierID"]) : default ,
                        //RenewalPlanID = dt.Rows[0]["RenewalPlanID"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["RenewalPlanID"]) : default,
                        PolicyTypeId = dt.Rows[0]["PolicyTypeId"] != DBNull.Value ? Convert.ToByte(dt.Rows[0]["PolicyTypeId"]) : default,
                        InvestmentTypeID = dt.Rows[0]["InvestmentTypeID"] != DBNull.Value ? Convert.ToByte(dt.Rows[0]["InvestmentTypeID"]) : default,
                        NoOfLives = dt.Rows[0]["TotalNoOfLives"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["TotalNoOfLives"]) : default,
                        NoOfEmployees = dt.Rows[0]["TotalNoOfEmployees"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["TotalNoOfEmployees"]) : default,
                        OccupancyId = dt.Rows[0]["OccupancyId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["OccupancyId"]) : default,
                        CoverTypeId = dt.Rows[0]["CoverTypeId"] != DBNull.Value ? Convert.ToByte(dt.Rows[0]["CoverTypeId"]) : default,
                        CompanyName = dt.Rows[0]["CompanyName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["CompanyName"]) : default,
                        InsuredName = dt.Rows[0]["InsuredName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["InsuredName"]) : default,
                    };
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.Message, "GetLeadDetailsByLeadId", "MatrixCore", "BmsBLL", leadId.ToString(), string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        private static bool CanUpdateAdditionalBookingDetails(BookingDetailsModel bookingDetails, string source)
        {
            List<int> productIds = "7,2".Split(',').Select(int.Parse).ToList();

            bool canUpdate = productIds.Contains(bookingDetails.Supplier.ProductId) ||
                            (bookingDetails?.Supplier?.ProductId == 117 && bookingDetails?.AgentId > 0) ||
                            (
                                (string.IsNullOrEmpty(source) || source.ToLower() != "pbcroma") &&
                                (
                                    bookingDetails?.Supplier?.ProductId == 117 ||
                                    (bookingDetails?.Supplier?.ProductId == 131 && bookingDetails.BookingSource == "BulkBooking")
                                )
                            );
            canUpdate = canUpdate &&
                        !bookingDetails.IsRetargeting &&
                        bookingDetails.BulkBookingProcess != "MatrixJob";

            return canUpdate;
        }

        public SaveInfo CreateBooking(BookingDetailsModel bookingDetails, string userId, string source)
        {
            var saveInfo = new SaveInfo();
            var requestTime = DateTime.Now;
            var error = string.Empty;
            var logs = new StringBuilder();
            try
            {
                bookingDetails.UserId = CoreCommonMethods.IsValidInteger(userId);

                if (bookingDetails != null && !string.IsNullOrEmpty(userId))
                {
                    if (bookingDetails != null &&
                           bookingDetails.Supplier != null &&
                           bookingDetails.Supplier?.ProductId == 131 &&
                           bookingDetails.InstallmentsData != null &&
                           bookingDetails.InstallmentsData.Any() &&
                           bookingDetails.InstallmentsData.Count() > 1)
                    {
                        bookingDetails.PaymentPeriodicityId = 14;
                    }
                    bookingDetails.PolicyTypeName = Enum.GetName(typeof(PolicyType), bookingDetails.PolicyTypeId);
                    if (bookingDetails.PaymentPeriodicityId != null)
                    {
                        bookingDetails.PaymentPeriodicity = PaymentPeriodicity.GetPaymentPeriodicity(Convert.ToInt16(bookingDetails.PaymentPeriodicityId)); 
                    }
                        
                    if (bookingDetails.Supplier.ProductId == 7)
                        bookingDetails.LiveFlag = 1;
                    if (bookingDetails.BookingTypeId != null)
                        bookingDetails.BookingType = Enum.GetName(typeof(BooKingType), bookingDetails.BookingTypeId);
                    if (bookingDetails.PaymentStatus != null)
                        bookingDetails.UpdatePaymentdetails = true;

                   
                    if (CanUpdateAdditionalBookingDetails(bookingDetails, source))
                    {
                        BmsDLL.UpdateBookingDetails(bookingDetails);
                    }

                    if (bookingDetails.IsConfirmPayment)
                    {
                        var isSmePos = bookingDetails?.Supplier?.ProductId == 131 &&
                                       bookingDetails.PaymentStatus == 4001 &&
                                       bookingDetails.PaymentSubStatus == 50 &&
                                       (!string.IsNullOrEmpty(bookingDetails.PAN) || !string.IsNullOrEmpty(bookingDetails.GST));
                        if (isSmePos)
                        {
                            string authToken = GetSmeAuthToken(saveInfo, logs);
                            if (!string.IsNullOrEmpty(authToken))
                            {
                                GetSmeOrganisationRegistration(authToken, bookingDetails, saveInfo, logs);
                                if (bookingDetails.OrgId > 0)
                                {
                                    SaveSmeOrgContactDetails(authToken, bookingDetails, saveInfo, logs);
                                    if (!string.IsNullOrEmpty(saveInfo.Message) && saveInfo.Message.Equals("This contact already exists in this booking"))
                                    {
                                        saveInfo.IsSaved = true;
                                        saveInfo.Message = string.Empty;
                                    }
                                }
                            }
                        }

                        //if ((isSmePos && saveInfo.IsSaved) || !isSmePos)
                        ConfirmLeadPayment(saveInfo, logs, bookingDetails);
                        if(saveInfo.IsSaved && bookingDetails?.Supplier?.ProductId == 131)
                        {
                            SendUploadedDocs(bookingDetails);
                        }
                    }
                    else
                    {
                        saveInfo.StatusCode = BmsDLL.InsertUpdateBookingAndPaymentDetails(bookingDetails, source, out string message);
                        if (saveInfo.StatusCode == 1)
                            saveInfo.IsSaved = true;
                        else
                            saveInfo.Message = message;
                    }
                }
                else
                    saveInfo.Message = "Invalid input or UserId";
            }
            catch (Exception ex)
            {
                saveInfo.IsSaved = false;
                saveInfo.Message = string.IsNullOrEmpty(saveInfo.Message) ? "Something went wrong. Payment confirmation failed." : saveInfo.Message;
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", bookingDetails.MatrixLeadId, error, "CreateBooking", "MatrixCore", "BmsBLL", JsonConvert.SerializeObject(bookingDetails), logs.ToString(), requestTime, DateTime.Now);
            }
            return saveInfo;
        }

        private void GetDocumentCategory(List<string> fields, List<RequiredDocumentCategories> requiredDocumentCategories, LeadDocument dbDocument, List<CustomerDocument> CustDocumentReqList)
        {
            foreach (var requiredDocumentCategory in requiredDocumentCategories)
            {
                foreach (var requiredDocument in requiredDocumentCategory.RequiredDocuments)
                {
                    if (fields.Contains(requiredDocument.DocumentName))
                    {
                        var customerDocument = new CustomerDocument
                        {
                            DocCategoryID = Convert.ToString(requiredDocumentCategory.DocCategoryId),
                            DocCategoryName = Convert.ToString(requiredDocumentCategory.DocCategoryName),
                            DocUploadId = Convert.ToString(dbDocument.DocId),
                            DocumentID = Convert.ToInt16(requiredDocument.DocumentId),
                            FileName = Convert.ToString(dbDocument.FileName),
                            StatusID = 3,
                            DocumentName = Convert.ToString(requiredDocument.DocumentName)
                        };
                        CustDocumentReqList.Add(customerDocument);
                    }
                }
            }
        }

        private async Task SendUploadedDocs(BookingDetailsModel bookingDetails)
        {
            var bmsDocsPointResponse = String.Empty;
            var requestTime = DateTime.Now;
            var dbDocuments = new List<LeadDocument>();
            var bmsDocuments = new GetDocuments();
            var docs = new DocsUpload();
            try
            {
                docs.LeadId = Convert.ToInt64(bookingDetails.MatrixLeadId);
                var agentId = Convert.ToString(bookingDetails.UserId);
                await Task.Run(() =>
                {
                    var data = SalesViewDLL.AddUpdateUploadedDocs(docs, agentId);
                    if (data != null && data.Tables != null && data.Tables[0].Rows.Count > 0)
                    {
                        dbDocuments = data.Tables[0].AsEnumerable().Select(dr => new LeadDocument()
                        {
                            DocTypeId = Convert.ToInt16(dr["DocTypeId"]),
                            DocId = Convert.ToString(dr["DocId"]),
                            FileName = Convert.ToString(dr["DocName"])
                        }).ToList();
                    }
                    else
                    {
                        return;
                    }

                    var endpoint = "bmsDocsServiceUrl".AppSettings() + "coreapi/api/GetMasterDocumentList" + "?supplierId=" + bookingDetails?.Supplier?.SupplierId + "&productId=" + bookingDetails?.Supplier?.ProductId + "&KYCType=" + 0 + "&SubProductId=" + bookingDetails?.InvestmentTypeID;

                    var headers = new Dictionary<object, object>()
                    {
                        { "Content-Type", "application/json"},
                        { "Authorization", "bmsDocsAuthKey".AppSettings()}
                    };
                    var bmsDocListRes = CommonAPICall.CallAPI(endpoint, string.Empty, "GET", Convert.ToInt32("BmsTimeout".AppSettings()), headers);
                    if (string.IsNullOrEmpty(bmsDocListRes))
                    {
                        return;
                    }
                    else
                    {
                        bmsDocuments = JsonConvert.DeserializeObject<GetDocuments>(bmsDocListRes);
                    }

                    var bmsDocsPointRequestPayload = new UploadedDocumentPayload
                    {
                        InsurerID = bookingDetails.Supplier.SupplierId,
                        UserId = Convert.ToInt32(bookingDetails.UserId),
                        ProductId = 131,
                        PlanId = 0,
                        BookingId = Convert.ToInt64(bookingDetails.MatrixLeadId),
                        Type = 9,
                        CustDocumentReqList = new List<CustomerDocument>(),
                        Source = "matrix"
                    };
                    foreach (var dbDocument in dbDocuments)
                    {
                        if (dbDocument.DocTypeId == 67) //Invoice
                        {
                            var fields = new List<string>
                            {
                                "Transit Invoice Copy"
                            };
                            GetDocumentCategory(fields, bmsDocuments.RequiredDocumentCategories, dbDocument, bmsDocsPointRequestPayload.CustDocumentReqList);
                        }
                        else if (dbDocument.DocTypeId == 774) //GST
                        {
                            var fields = new List<string>
                            {
                                "GST"
                            };
                            GetDocumentCategory(fields, bmsDocuments.RequiredDocumentCategories, dbDocument, bmsDocsPointRequestPayload.CustDocumentReqList);
                        }
                        else if (dbDocument.DocTypeId == 3)//PAN
                        {
                            var fields = new List<string>
                            {
                                "Pancard",
                                "Pan Card",
                                "PAN"
                            };
                            GetDocumentCategory(fields, bmsDocuments.RequiredDocumentCategories, dbDocument, bmsDocsPointRequestPayload.CustDocumentReqList);
                        }
                        else if (dbDocument.DocTypeId == 853)//Payment
                        {
                            var fields = new List<string>
                            {
                                "Payment Acknowledgement"
                            };
                            GetDocumentCategory(fields, bmsDocuments.RequiredDocumentCategories, dbDocument, bmsDocsPointRequestPayload.CustDocumentReqList);
                        }
                        else
                        {
                            continue;
                        }
                    }
                    string url2 = "bmsDocsServiceUrl".AppSettings() + "service/DocRepositoryService.svc/UploadDocumentStatusForCustomer";
                    string request = JsonConvert.SerializeObject(bmsDocsPointRequestPayload);
                    bmsDocsPointResponse = CommonAPICall.CallAPI(url2, request,
                                                                "POST",
                                                                Convert.ToInt32("BmsTimeout".AppSettings()),
                                                                headers);
                });
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", bookingDetails.MatrixLeadId, ex.ToString(), "sendUploadedDocs", "MatrixCore", "BmsBLL", JsonConvert.SerializeObject(bmsDocsPointResponse), "", requestTime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", bookingDetails.MatrixLeadId, "", "sendUploadedDocs", "MatrixCore", "BmsBLL", JsonConvert.SerializeObject(bmsDocsPointResponse), "", requestTime, DateTime.Now);
            }
        }

        private static void SaveSmeOrgContactDetails(string authToken, BookingDetailsModel bookingDetails, SaveInfo saveInfo, StringBuilder logs)
        {
            var data = BmsDLL.GetAgentData(bookingDetails.MatrixLeadId > 0 ? bookingDetails.MatrixLeadId : bookingDetails.LeadId);
            var headers = new Dictionary<object, object>()
            {
                { "Content-Type", "application/json"},
                { "Authorization", "Bearer " + authToken}
            };
            var dynamic = new
            {
                orgId = bookingDetails.OrgId,
                bookingId = bookingDetails.MatrixLeadId > 0 ? bookingDetails.MatrixLeadId : bookingDetails.LeadId,
                orgContacts = new List<object>()
                {
                    new
                    {
                        Id = 0,
                        ContactType = 1,
                        Name = data.Tables[0].Rows[0]["Name"] != DBNull.Value ? data.Tables[0].Rows[0]["Name"].ToString() : "",
                        Email = data.Tables[0].Rows[0]["EmailID"] != DBNull.Value ? data.Tables[0].Rows[0]["EmailID"].ToString() : "",
                        MobileNo = data.Tables[0].Rows[0]["MobileNo"] != DBNull.Value ? data.Tables[0].Rows[0]["MobileNo"].ToString() : "",
                        Designation = string.Empty,
                        Address1 = string.Empty,
                        Address2 = string.Empty,
                        PinCode = 0,
                        CityId = 0,
                        CreatedBy = bookingDetails.UserId,
                        ModifiedBy = 0,
                        ActionPerformedBy = BmsDLL.GetAgentNameAndEmpId(bookingDetails.UserId)
                    }
                }
            };
            string request = JsonConvert.SerializeObject(dynamic);
            string apiResponse = CommonAPICall.CallAPI("smeBaseApiEndpoint".AppSettings() + "organisation/saveorgcontactdetails",
                                                        request,
                                                        "POST",
                                                        Convert.ToInt32("CJTimeout".AppSettings()),
                                                        headers);
            var result = JsonConvert.DeserializeObject<SmeResponse>(apiResponse);
            if (result != null && result.IsSuccess)
            {
                saveInfo.IsSaved = result.IsSuccess;
            }
            else
            {
                if (result.Errors != null && result.Errors.Count > 0)
                {
                    foreach (var error in result.Errors)
                    {
                        if (error.Count > 0)
                        {
                            foreach (var err in error)
                            {
                                if (err.Key == "ErrorMessage")
                                    saveInfo.Message += $"{err.Key} : {err.Value} {Environment.NewLine} ";
                            }
                        }
                        else
                        {
                            if (error.FirstOrDefault().Key == "ErrorMessage")
                                saveInfo.Message += $"{error.FirstOrDefault().Key} : {error.FirstOrDefault().Value} {Environment.NewLine} ";
                        }
                    }
                    saveInfo.Message = saveInfo.Message.Trim();
                }
                else
                {
                    saveInfo.Message = result.Message;
                }
                logs.Append(" SaveSmeOrgContactDetails : OrgContactDetails are not saved. ApiResponse : " + apiResponse + " ");
            }
        }

        private static string GetSmeAuthToken(SaveInfo saveInfo, StringBuilder logs)
        {
            var response = string.Empty;
            var endpoint = "smeBaseApiEndpoint".AppSettings() + "auth/getsenderauthtoken" + "?senderId=" + "senderId".AppSettings() + "&senderPassword=" + "senderPassword".AppSettings();
            var apiResponse = CommonAPICall.CallAPI(endpoint, string.Empty, "POST", Convert.ToInt32("CJTimeout".AppSettings()), string.Empty);

            if (!string.IsNullOrEmpty(apiResponse) && !string.IsNullOrEmpty(JsonConvert.DeserializeObject<dynamic>(apiResponse).AccessToken.ToString()))
            {
                response = JsonConvert.DeserializeObject<dynamic>(apiResponse).AccessToken;
            }
            else
            {
                saveInfo.Message = "Unable to fetch Sme Token";
                logs.Append(" GetSmeAuthToken : AuthToken is not recieved. ApiResponse : " + apiResponse + " ");
            }
            return response;
        }

        private static void GetSmeOrganisationRegistration(string authToken, BookingDetailsModel bookingDetails, SaveInfo saveInfo, StringBuilder logs)
        {
            var headers = new Dictionary<object, object>()
            {
                { "Content-Type", "application/json"},
                { "Authorization", "Bearer " + authToken}
            };
            var dynamic = new
            {
                orgId = 0,
                gSTNO = bookingDetails.GST,
                pAN = bookingDetails.PAN,
                cIN = bookingDetails.CIN,
                companyName = bookingDetails.CompanyName,
                bookingId = bookingDetails.MatrixLeadId > 0 ? bookingDetails.MatrixLeadId : bookingDetails.LeadId,
                createdBy = bookingDetails.UserId,
                actionPerformedBy = BmsDLL.GetAgentNameAndEmpId(bookingDetails.UserId)
            };
            string apiResponse = CommonAPICall.CallAPI("smeBaseApiEndpoint".AppSettings() + "organisation/organisationregistration",
                                                       JsonConvert.SerializeObject(dynamic),
                                                       "POST",
                                                       Convert.ToInt32("CJTimeout".AppSettings()),
                                                       headers);
            var result = JsonConvert.DeserializeObject<SmeResponse>(apiResponse);
            if (result != null && result.IsSuccess)
            {
                saveInfo.IsSaved = result.IsSuccess;
            }
            else
            {
                if (result.Errors != null && result.Errors.Count > 0)
                {
                    foreach (var error in result.Errors)
                    {
                        if (error.Count > 0)
                        {
                            foreach (var err in error)
                            {
                                if (err.Key == "ErrorMessage")
                                    saveInfo.Message += $"{err.Key} : {err.Value} {Environment.NewLine} ";
                            }
                        }
                        else
                        {
                            if (error.FirstOrDefault().Key == "ErrorMessage")
                                saveInfo.Message += $"{error.FirstOrDefault().Key} : {error.FirstOrDefault().Value} {Environment.NewLine} ";
                        }
                    }
                    saveInfo.Message = saveInfo.Message.Trim();
                }
                else
                {
                    saveInfo.Message = result.Message;
                }
                logs.Append(" SaveSmeOrgContactDetails : OrgContactDetails are not saved. ApiResponse : " + apiResponse + " ");
            }

            if (result != null && result.IsSuccess && result.Data > 0)
            {
                bookingDetails.OrgId = result.Data;
            }
            else
            {
                saveInfo.IsSaved = false;
                if (!string.IsNullOrEmpty(result.Message))
                {
                    saveInfo.Message = result.Message;
                    logs.Append(" GetSmeOrganisationRegistration : OrganisationId is not recieved. ApiResponse : " + apiResponse + " ");
                }
            }
        }

        private static string GetBookingPayload(BookingDetailsModel bookingDetailsModel)
        {
            int[] premiumfields = new int[] { 5, 7, 8, 112, 113 };
            List<PremiumListM> premium = new();

            if (Array.IndexOf(premiumfields, bookingDetailsModel.InvestmentTypeID) > -1)
            {
                if (bookingDetailsModel.TerrorismPremium > 0)
                {
                    premium.Add(new PremiumListM
                    {
                        Premium = bookingDetailsModel.TerrorismPremium,
                        PremiumTypeId = (int)PremiumTypeMasters.Terrorism
                    });
                }

                if (bookingDetailsModel.BurglaryPremium > 0)
                {
                    premium.Add(new PremiumListM
                    {
                        Premium = bookingDetailsModel.BurglaryPremium,
                        PremiumTypeId = (int)PremiumTypeMasters.Burglary
                    });
                }

                if (bookingDetailsModel.FirePremium > 0)
                {
                    premium.Add(new PremiumListM
                    {
                        Premium = bookingDetailsModel.FirePremium,
                        PremiumTypeId = (int)PremiumTypeMasters.Fire
                    });
                }
            }

            var bookingPaymentModel = new BookingPaymentModel()
            {
                LeadID = bookingDetailsModel.LeadId > 0 ? bookingDetailsModel.LeadId : bookingDetailsModel.MatrixLeadId,
                ProductID = bookingDetailsModel.Supplier.ProductId,
                BookingDetails = new BookingModel()
                {
                    CustomerID = bookingDetailsModel.CustomerId,
                    InsuredName = bookingDetailsModel.InsuredName,
                    PolicyType = bookingDetailsModel.PolicyTypeId,
                    PolicyTerm = bookingDetailsModel.PolicyTerm,
                    PlanId = bookingDetailsModel.Plan.OldPlanId,
                    SupplierId = bookingDetailsModel.Supplier.OldSupplierId,
                    SumInsured = bookingDetailsModel.SumInsured,
                    BasicPremium = bookingDetailsModel.Premium,
                    TotalPremium = bookingDetailsModel.TotalPremium,
                    ServiceTax = bookingDetailsModel.ServiceTax,
                    IsSTP = bookingDetailsModel.IsSTP != null ? (bool)bookingDetailsModel.IsSTP : bookingDetailsModel.IsSTP,
                    OfferCreatedON = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    InstallmentsPaid = bookingDetailsModel.InstallmentPaid,
                    PaymentPeriodicity = bookingDetailsModel.PaymentPeriodicityId != null && bookingDetailsModel.PaymentPeriodicityId > 0 ? bookingDetailsModel.PaymentPeriodicityId : 12,
                    PayTerm = bookingDetailsModel.PayTerm,
                    CreatedBy = bookingDetailsModel.UserId,
                    BookingType = bookingDetailsModel.BookingType,
                    IsEMI = bookingDetailsModel.IsEMI,
                    TransRefNo = bookingDetailsModel.TransRefNo

                },
                BookingInfo = new BookingInfo()
                {
                    ApplicationNo = bookingDetailsModel.ApplicationNo,
                    ProposalNo = bookingDetailsModel.ProposalNo,
                    PolicyNo = bookingDetailsModel.PolicyNo,
                    PreviousBookingNo = bookingDetailsModel.PreviousBookingNo,
                    PreviousPolicyNo = bookingDetailsModel.PrevPolicyNo,
                    PreviousInsurerID = bookingDetailsModel.ExpiringInsurer,
                    PolicyDocUploadId = bookingDetailsModel.DocumentId,
                    Rider = bookingDetailsModel.Rider,
                    RiderSI = bookingDetailsModel.RiderSI,

                    IsBookingValidated = bookingDetailsModel.IsBookingValidated,
                    Portability = (short)(bookingDetailsModel.Portability != null ? bookingDetailsModel.Portability : 0)

                },
                MotorBookingDetails = new MotorBookingDetails()
                {
                    ODPremium = bookingDetailsModel.ODPremium,
                    TPPremium = bookingDetailsModel.TPPremium,
                    RegistrationNo = bookingDetailsModel.RegistrationNo
                },
                PaymentDetails = new PaymentDetails()
                {
                    PaymentMode = bookingDetailsModel.PaymentMode,
                    PaymentStatus = bookingDetailsModel.PaymentStatus,
                    PaymentSource = bookingDetailsModel.PaymentSource,
                    PaymentDate = DateTime.TryParse(Convert.ToInt64(bookingDetailsModel.PaymentDate).ToDateTime().ToString(), out DateTime dt) ? dt : default,
                    PaymentSubStatus = bookingDetailsModel.PaymentSubStatus
                },
                SmeBookingDetails = new SmeBookingDetails()
                {
                    Brokerage = bookingDetailsModel.Brokerage != null ? (decimal)bookingDetailsModel.Brokerage : default,
                    TotalNoOfLives = bookingDetailsModel.NoOfLives,
                    TotalNoOfEmployees = bookingDetailsModel.NoOfEmployees,
                    OccupancyId = Convert.ToInt32(bookingDetailsModel.OccupancyId),
                    CorpOrgId = bookingDetailsModel.OrgId,
                    MarineCoverType = bookingDetailsModel.MarineCoverType,
                    InstallmentsData = bookingDetailsModel.InstallmentsData,
                    QuoteId = bookingDetailsModel.QuoteId,
                    PremiumList = premium
                },
                HealthBookingDetails = new HealthBookingDetails()
                {
                    Portability = (short)(bookingDetailsModel.Portability != null ? bookingDetailsModel.Portability : 0),
                    TermTenure = bookingDetailsModel.TermTenure,
                    TermSI = bookingDetailsModel.TermSI,
                    InsurerPolicyNo = bookingDetailsModel.InsurerPolicyNo
                },
                TravelBookingDetails = new TravelBookingDetails()
                {
                    DestinationCountry = bookingDetailsModel.DestinationCountry,
                }
            };

            if (bookingDetailsModel.Supplier.ProductId == 101)
            {
                bookingPaymentModel.HomeBookingDetails = new HomeBookingDetails()
                {
                    CoverageType = bookingDetailsModel.CoverageTypeId,
                    BuildingValue = (decimal)bookingDetailsModel.BuildingSI,
                    ContentValue = (decimal)bookingDetailsModel.ContentSI,
                    PropertyType = bookingDetailsModel.PropertyTypeId,
                    Purpose = bookingDetailsModel.PurposeId
                };
            }

            if (bookingDetailsModel.Supplier.ProductId == 114)
            {
                bookingPaymentModel.TwBookingDetails = new TwBookingDetails()
                {
                    RegistrationNo = bookingDetailsModel.RegistrationNo,
                    ODPremium = bookingDetailsModel.ODPremium
                };
            }

            int[] policydatePrd = { 3, 7, 115, 101 };
            if (Array.IndexOf((policydatePrd), bookingDetailsModel.Supplier.ProductId) >= 0)
            {
                if (bookingDetailsModel.PolicyStartDate != null && bookingDetailsModel.PolicyStartDate > 0)
                {
                    bookingPaymentModel.BookingInfo.PolicyStartDate = DateTime.TryParse(Convert.ToInt64(bookingDetailsModel.PolicyStartDate).ToDateTime().ToString("yyyy-MM-dd"), out dt) ? dt : default;
                    bookingPaymentModel.BookingInfo.PolicyEndDate = DateTime.TryParse(Convert.ToInt64(bookingDetailsModel.PolicyEndDate).ToDateTime().ToString("yyyy-MM-dd"), out dt) ? dt : default;
                }
            }
            else
            {
                bookingPaymentModel.BookingInfo.PolicyStartDate = DateTime.TryParse(Convert.ToInt64(bookingDetailsModel.PolicyStartDate).ToDateTime().ToString("yyyy-MM-dd"), out dt) && bookingDetailsModel.PolicyStartDate > 0 ? dt : null;
                bookingPaymentModel.BookingInfo.PolicyEndDate = DateTime.TryParse(Convert.ToInt64(bookingDetailsModel.PolicyEndDate).ToDateTime().ToString("yyyy-MM-dd"), out dt) && bookingDetailsModel.PolicyEndDate > 0 ? dt : null;
            }

            return JsonConvert.SerializeObject(bookingPaymentModel);
        }

        private static string GetMatrixJobData(BookingDetailsModel bookingDetailsModel)
        {
            var bookingPaymentModel = new BookingPaymentModel()
            {
                LeadID = bookingDetailsModel.LeadId > 0 ? bookingDetailsModel.LeadId : bookingDetailsModel.MatrixLeadId,
                ProductID = bookingDetailsModel.Supplier.ProductId,
                BookingDetails = new BookingModel()
                {
                    CustomerID = bookingDetailsModel.CustomerId,
                    InsuredName = bookingDetailsModel.InsuredName,
                    PolicyType = bookingDetailsModel.PolicyTypeId,
                    PolicyTerm = bookingDetailsModel.PolicyTerm,
                    PlanId = bookingDetailsModel.Plan.OldPlanId,
                    SupplierId = bookingDetailsModel.Supplier.OldSupplierId,
                    SumInsured = bookingDetailsModel.SumInsured,
                    BasicPremium = bookingDetailsModel.Premium,
                    TotalPremium = bookingDetailsModel.TotalPremium,
                    OfferCreatedON = Convert.ToDateTime(bookingDetailsModel.OfferCreatedON).ToString("yyyy-MM-dd HH:mm:ss"),                  
                    PaymentPeriodicity = bookingDetailsModel.PaymentPeriodicityId != null && bookingDetailsModel.PaymentPeriodicityId > 0 ? bookingDetailsModel.PaymentPeriodicityId : 12,
                    PayTerm = bookingDetailsModel.PayTerm,
                    CreatedBy = bookingDetailsModel.UserId,
                    BookingType = bookingDetailsModel.BookingType
                },
                BookingInfo = new BookingInfo()
                {
                    ApplicationNo = bookingDetailsModel.ApplicationNo
                },
                PaymentDetails = new PaymentDetails()
                {
                    PaymentMode = bookingDetailsModel.PaymentMode,
                    PaymentStatus = bookingDetailsModel.PaymentStatus,
                    PaymentDate = (DateTime)bookingDetailsModel.OfferCreatedON,
                    PaymentSubStatus = bookingDetailsModel.PaymentSubStatus
                }
            };
            return JsonConvert.SerializeObject(bookingPaymentModel);
        }

        private static string GetRetargetingData(BookingDetailsModel bookingDetailsModel)
        {
            var bookingPaymentModel = new BookingPaymentModel()
            {
                LeadID = bookingDetailsModel.LeadId > 0 ? bookingDetailsModel.LeadId : bookingDetailsModel.MatrixLeadId,
                ProductID = bookingDetailsModel.Supplier.ProductId,
                BookingDetails = new BookingModel()
                {
                    CustomerID = bookingDetailsModel.CustomerId,
                    InsuredName = bookingDetailsModel.InsuredName,
                    PlanId = bookingDetailsModel.Plan.OldPlanId,
                    SupplierId = bookingDetailsModel.Supplier.OldSupplierId,
                    SumInsured = bookingDetailsModel.SumInsured,
                    TotalPremium = bookingDetailsModel.TotalPremium,
                    IsSTP = bookingDetailsModel.IsSTP != null ? (bool)bookingDetailsModel.IsSTP : bookingDetailsModel.IsSTP,
                    OfferCreatedON = Convert.ToDateTime(bookingDetailsModel.OfferCreatedON).ToString("yyyy-MM-dd HH:mm:ss"),
                    InstallmentsPaid = bookingDetailsModel.InstallmentPaid,
                    PaymentPeriodicity = bookingDetailsModel.PaymentPeriodicityId != null && bookingDetailsModel.PaymentPeriodicityId > 0 ? bookingDetailsModel.PaymentPeriodicityId : 12,
                    CreatedBy = bookingDetailsModel.UserId,
                    BookingType = bookingDetailsModel.BookingType,
                    IsEMI = bookingDetailsModel.IsEMI,
                    TransRefNo = bookingDetailsModel.TransRefNo,
                    PartnerId = bookingDetailsModel.PartnerId,
                    PolicyTerm = bookingDetailsModel.PolicyTerm,
                    PayTerm=bookingDetailsModel.PayTerm
                },
                BookingInfo = new BookingInfo()
                {
                    ApplicationNo = bookingDetailsModel.ApplicationNo,
                    Portability = (short)(bookingDetailsModel.Portability != null ? bookingDetailsModel.Portability : 0),
                    InCreditPendingPool = bookingDetailsModel.InCreditPendingPool,
                    CreditReceived = (byte)bookingDetailsModel.CreditReceived
                },
                PaymentDetails = new PaymentDetails()
                {
                    PaymentMode = bookingDetailsModel.PaymentMode,
                    PaymentStatus = bookingDetailsModel.PaymentStatus,
                    PaymentSource = bookingDetailsModel.PaymentSource,
                    PaymentSubStatus = bookingDetailsModel.PaymentSubStatus,
                    PaymentDate = (DateTime)bookingDetailsModel.OfferCreatedON
                },
                DocumentDetails = new DocumentDetails()
                {
                    DocumentRequired = bookingDetailsModel.DocumentsRequired,
                    Medical_or_InspectionRequired = bookingDetailsModel.Medical_or_InspectionRequired
                },
                CommunicationDetails = new CommunicationDetails()
                {
                    BlockAutoCommunication = bookingDetailsModel.BlockAutoCommunication
                }
            };
            return JsonConvert.SerializeObject(bookingPaymentModel);
        }

        private static Dictionary<object, object> GetHeaders(string bookingSource, bool isRetargeting, string bulkBookingProcess, string source)
        {
            if (isRetargeting)
                return new Dictionary<object, object>
                {
                    { "source", "RetargetBookingSource".AppSettings() },
                    { "clientkey", "RetargetClientKey".AppSettings() },
                    { "authkey", "RetargetAuthKey".AppSettings() },
                };
            else if (bulkBookingProcess == "MatrixJob")
                return new Dictionary<object, object>
                {
                    { "source", "MatrixJobSource".AppSettings() },
                    { "clientkey", "MatrixJobClientKey".AppSettings() },
                    { "authkey", "MatrixJobAuthKey".AppSettings() },
                };
            else if (!string.IsNullOrEmpty(source) && source == "MatrixGoApp")
            {
                return new Dictionary<object, object>
                {
                    { "source", "MatrixGoAppSource".AppSettings() },
                    { "clientkey", "MatrixGoAppClientKey".AppSettings() },
                    { "authkey", "MatrixGoAppAuthKey".AppSettings() }
                };
            }
            else
                return new Dictionary<object, object>
                {
                    { "source", string.IsNullOrEmpty(bookingSource) ? "PBCromaSource".AppSettings() : bookingSource },
                    { "clientkey", !string.IsNullOrEmpty(bookingSource) && bookingSource.ToLower() == "bulkbooking" ? "PBBulkBookingClientKey".AppSettings() : "PBCromaclientkey".AppSettings() },
                    { "authkey", !string.IsNullOrEmpty(bookingSource) && bookingSource.ToLower() == "bulkbooking" ? "PBBulkBookingAuthKey".AppSettings() : "PBCromaAuthkey".AppSettings() }
                };
        }

        private static void ConfirmLeadPayment(BookingDetailsModel bookingDetails, SaveInfo saveInfo, StringBuilder logData)
        {
            var header = GetHeaders(bookingDetails.BookingSource, bookingDetails.IsRetargeting, bookingDetails.BulkBookingProcess, bookingDetails.Source);
            string dataToPost;
            if (bookingDetails.IsRetargeting)
                dataToPost = GetRetargetingData(bookingDetails);
            else if(bookingDetails.BulkBookingProcess == "MatrixJob")
                dataToPost = GetMatrixJobData(bookingDetails);
            else
                dataToPost = GetBookingPayload(bookingDetails);

            logData.Append(" BmsPayload: " + dataToPost + " ");
            logData.Append(" BMSHeader: " + JsonConvert.SerializeObject(header) + " ");
            string data = CommonAPICall.CallAPI("BmsCromaURL".AppSettings() + "api/CromaServices/CreateUpdateBooking",
                                                dataToPost, "POST",
                                                Convert.ToInt32("BmsTimeout".AppSettings()),
                                                "application/json", header);
            logData.Append(" BmsResponse: " + data + " ");
            if (!string.IsNullOrEmpty(data))
            {
                BmsResponse response = JsonConvert.DeserializeObject<BmsResponse>(data);
                if (response.Status)
                {
                    BmsDLL.UpdatePayment(bookingDetails);
                    saveInfo.IsSaved = true;
                }
                else
                {
                    if (response.ErrorMessage != null && response.ErrorMessage.Count > 0)
                    {
                        foreach (var error in response.ErrorMessage)
                        {
                            saveInfo.Message += $"{error.FirstOrDefault().Key} : {error.FirstOrDefault().Value} {Environment.NewLine} ";
                        }
                    }

                    saveInfo.Message += "Payment confirmation failed";

                    if (!string.IsNullOrEmpty(saveInfo.Message))
                        saveInfo.Message = saveInfo.Message.Trim();
                }

                if (response.WarningMessage != null && response.WarningMessage.Count > 0)
                {
                    foreach (var error in response.WarningMessage)
                    {
                        saveInfo.Warnings += $"{error.FirstOrDefault().Key} : {error.FirstOrDefault().Value} {Environment.NewLine} ";
                    }
                }

                if (!string.IsNullOrEmpty(saveInfo.Warnings))
                    saveInfo.Warnings = saveInfo.Warnings.Trim();
            }
            else
            {
                saveInfo.Message = "Invalid response";
            }
        }

        private static void ConfirmLeadPayment(SaveInfo saveInfo, StringBuilder logs, BookingDetailsModel bookingDetails)
        {
            saveInfo.IsSaved = false;
            bookingDetails.Comments = "Payment details confirmed";
            if ("3001,4001,5001,6001,7001,8001".Split(',').Contains(bookingDetails.PaymentStatus.ToString()))
                bookingDetails.PaymentStatus++;
            if (bookingDetails.PaymentStatus == 1 || bookingDetails.PaymentStatus == 1001)
                bookingDetails.PaymentStatus = 300;
            if ("29,31,39,41,43,45".Split(',').Contains(bookingDetails.PaymentSubStatus.ToString()))
                bookingDetails.PaymentSubStatus++;
            if (bookingDetails.Supplier.ProductId == 7)
                bookingDetails.LiveFlag = 1;

            saveInfo.StatusCode = BmsDLL.ValidateBookingDetails(bookingDetails.MatrixLeadId,
                                                                bookingDetails.Supplier.ProductId,
                                                                bookingDetails.InvestmentTypeID,
                                                                bookingDetails.OccupancyId,
                                                                bookingDetails.UserId);
            if (saveInfo.StatusCode == 1)
            {
                ConfirmLeadPayment(bookingDetails, saveInfo, logs);
            }
            else
            {
                if (saveInfo.StatusCode == 2)
                    saveInfo.Message = "Lead already booked";
                else if (saveInfo.StatusCode == 3)
                    saveInfo.Message = "Lead cannot be booked without Notice premium";
                else if (saveInfo.StatusCode == 4)
                    saveInfo.Message = "PolicyNo already exist.";
                else if (saveInfo.StatusCode == 5)
                    saveInfo.Message = "Occupancy/Risk Category is invalid.";
                else if (saveInfo.StatusCode == 6)
                    saveInfo.Message = "Lead is assigned to other Agent.";
                else
                    saveInfo.Message = "Unable to save";
            }
        }

        #endregion

        public string SalesLogin(dynamic details, string Token,out string BMSAuthToken)
        {
            {
                string url = string.Empty;
                string api = "bmsApiServiceurl2".AppSettings() + "api/User/SalesLoginV2";
                string strexception = string.Empty;
                BMSAuthToken = "";
                try
                {
                    Dictionary<object, object> header = new Dictionary<object, object>
                    {
                        { "MatrixToken", Token },
                        { "AppId", "Matrix"},
                        { "UserId", details.UserId },
                        { "AppKey", "bmsServiceToken".AppSettings() }
                    };
                    string content = "{\"UserId\":" + details.UserId + ",\"LeadId\":" + details.LeadId + ",\"Source\":\"" + details.Source + "\"}";
                    var result = CommonAPICall.CallAPI(api, content, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);

                    if (!string.IsNullOrEmpty(result))
                    {
                        dynamic Response = JsonConvert.DeserializeObject<dynamic>(result);

                        if (result != null)
                        {
                            //BMSAuthToken = Response.BMSAuthToken;
                            return Response.Dashboard;
                        }
                    }
                }
                catch (Exception ex)
                {
                    strexception = ex.ToString();
                    LoggingHelper.LoggingHelper.Log(details.LeadId.ToString(), details.LeadId.ToString(), strexception, "SalesLogin", "BmsBLL", "MatrixCoreAPI", details.ToString(), "", DateTime.Now, DateTime.Now);
                }
                return null;
            }
        }

        public AdditionalLeadDetails GetAdditionalLeadDetails(long LeadId, string Source)
        {
            AdditionalLeadDetails response = new AdditionalLeadDetails();
            try
            {
                response.LeadID = LeadId;
                DataTable dt = BmsDLL.GetAdditionalLeadDetailsByLeadID(LeadId, Source);
                if (dt != null && dt.Rows.Count > 0)
                {
                    response.SalesOffers = (from DataRow dr in dt.Rows
                                            select new SalesOffer()
                                            {
                                                OfferId = dr["OfferID"] == null || dr["OfferID"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(dr["OfferID"]),
                                                AvailedVoucher = dr["VoucherAvailed"] == null || dr["VoucherAvailed"] == DBNull.Value ? "" : Convert.ToString(dr["VoucherAvailed"])
                                            }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), LeadId, ex.Message, "GetAdditionalLeadDetails", "MatrixCore", "BmsBLL", LeadId.ToString(), string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public BMSRenewDetails GetRenewalDetails(string LeadID,string Source)
        {
            BMSRenewDetails renewDetails = new BMSRenewDetails();
            string strexception = string.Empty;
            DateTime dateTime = DateTime.Now;
            if (LeadID == null)
            {
               
                renewDetails.Message = "Invalid Lead Id";
                return null;
            }
            try
            {
                DataSet ds = BmsDLL.GetRenewalDetails(Convert.ToInt64(LeadID));
                if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
                {
                    var table1 = ds.Tables[0];
                   

                    if(table1 != null && table1.Rows != null && table1.Rows.Count > 0)
                    {
                        renewDetails.OldPolicyNo = table1.Rows[0]["OldPolicyNo"] != DBNull.Value ? table1.Rows[0]["OldPolicyNo"].ToString() : null;
                        renewDetails.PolicyExpiryDate = table1.Rows[0]["PolicyExpiryDate"] != DBNull.Value ? table1.Rows[0]["PolicyExpiryDate"].ToString() : null;
                        renewDetails.OldBookingId = table1.Rows[0]["OldBookingId"] != DBNull.Value ? table1.Rows[0]["OldBookingId"].ToString() : null;
                        renewDetails.ComboProduct = table1.Rows[0]["ComboProduct"] != DBNull.Value ? table1.Rows[0]["ComboProduct"].ToString() : null;
                        renewDetails.SlotSource = table1.Rows[0]["SlotSource"] != DBNull.Value ? table1.Rows[0]["SlotSource"].ToString() : null;
                        renewDetails.NoticePremium = table1.Rows[0]["NoticePremium"] != DBNull.Value ? table1.Rows[0]["NoticePremium"].ToString() : null;
                        renewDetails.planterm = table1.Rows[0]["planterm"] != DBNull.Value ? table1.Rows[0]["planterm"].ToString() : null;
                        renewDetails.MonthlyMode = table1.Rows[0]["MonthlyMode"] != DBNull.Value ? table1.Rows[0]["MonthlyMode"].ToString() : null;
                        renewDetails.SumInsured = table1.Rows[0]["SumInsured"] != DBNull.Value ? table1.Rows[0]["SumInsured"].ToString() : null;
                        renewDetails.PlanName = table1.Rows[0]["PlanName"] != DBNull.Value ? table1.Rows[0]["PlanName"].ToString() : null;
                        renewDetails.IsPreviousClaimsTaken = table1.Rows[0]["IsPreviousClaimsTaken"] != DBNull.Value ? table1.Rows[0]["IsPreviousClaimsTaken"].ToString() : null;
                        renewDetails.RenewalYear = table1.Rows[0]["RenewalYear"] != DBNull.Value ? table1.Rows[0]["RenewalYear"].ToString() : null;
                        renewDetails.PlanId = table1.Rows[0]["PlanId"] != DBNull.Value ? table1.Rows[0]["PlanId"].ToString() : null;
                        renewDetails.SupplierId = table1.Rows[0]["SupplierId"] != DBNull.Value ? table1.Rows[0]["SupplierId"].ToString() : null;
                        renewDetails.SupplierName = table1.Rows[0]["SupplierName"] != DBNull.Value ? table1.Rows[0]["SupplierName"].ToString() : null;
                        renewDetails.ProposerName = table1.Rows[0]["ProposerName"] != DBNull.Value ? table1.Rows[0]["ProposerName"].ToString() : null;
                        renewDetails.CustomerID = table1.Rows[0]["CustomerId"] == DBNull.Value ? 0 : Convert.ToInt64(table1.Rows[0]["CustomerId"]);
                        
                    }

                    if (Source.Contains("bms"))
                    {
                        var table2 = ds.Tables[1];
                        if (table2 != null && table2.Rows != null && table2.Rows.Count > 0)
                        {
                            renewDetails.UpsellReasons = (from DataRow dr in table2.Rows
                                                          select new UpsellReason()
                                                          {
                                                              UpsellReasonId = dr["UpsellReasonId"] == null || dr["UpsellReasonId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(dr["UpsellReasonId"])
                                                          }).ToList();
                        }
                    }

                }
                else
                {
                    return null;
                }
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), Convert.ToInt64(LeadID), strexception, "GetRenewalDetails", "MatrixCore", "BMSBLL", null, JsonConvert.SerializeObject(renewDetails), dateTime, DateTime.Now);
            }
            return renewDetails;
        }

        public Envelope<bool> InsertUpdateLeadStatus(StampingRequestModel stampingRequestModel)
        {
            DateTime requestTime = DateTime.Now;
            var error = string.Empty;
            string json = string.Empty;
            Envelope<bool> response = new Envelope<bool>() { Data = false };
            try
            {
                json=JsonConvert.SerializeObject(stampingRequestModel);
                if (stampingRequestModel != null && stampingRequestModel.LeadId > 0 && stampingRequestModel.StatusId > 0)
                {
                    BmsDLL.InsertUpdateLeadStatus(stampingRequestModel);
                    response.Data = true;
                }
            }
            catch (Exception ex)
            {
                error = ex.StackTrace;
                //LoggingHelper.LoggingHelper.AddloginQueue("", stampingRequestModel.LeadId, error.ToString(), "InsertUpdateLeadStatus", "Matrixcore", "BMSBLL", JsonConvert.SerializeObject(stampingRequestModel), "", requestTime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", stampingRequestModel.LeadId, error.ToString(), "InsertUpdateLeadStatus", "Matrixcore", "BMSBLL", json, "", requestTime, DateTime.Now);
            }
            return response;

        }

        public ResponseData<List<LeadHistoryResponse>> GetLeadHistory(long LeadID)
        {
            ResponseData<List<LeadHistoryResponse>> LHrespose = new ResponseData<List<LeadHistoryResponse>>();
            DateTime dateTime = DateTime.Now;
            try
            {
                DataTable dt = BmsDLL.GetLeadHistory(Convert.ToInt64(LeadID));
                if (dt != null && dt.Rows != null && dt.Rows.Count > 0)
                {
                    LHrespose.Data = (from dr in dt.AsEnumerable()
                                 select new LeadHistoryResponse
                                 {
                                     LeadID = Convert.ToInt64(dr["LeadID"]),
                                     UserId = Convert.ToInt64(dr["UserId"]),
                                     Comments = dr["Comments"].ToString(),
                                     EventType = Convert.ToInt16(dr["EventType"]),
                                     EventTypeName = dr["EventTypeName"].ToString(),
                                     EventDate = dr["EventDate"].ToString(),
                                     StatusId = Convert.ToInt16(dr["StatusId"]),
                                     StatusName = dr["StatusName"].ToString(),
                                     SubStatusId = Convert.ToInt16(dr["SubStatusId"]),
                                     SubStatusName  = dr["SubStatusName"].ToString(),
                                     EmployeeID = dr["EmployeeID"].ToString(),
                                     EmployeeName = dr["EmployeeName"].ToString(),
                                     ProductID = Convert.ToInt16(dr["ProductID"]),
                                     IsParent = Convert.ToInt16(dr["IsParent"])
                                 }).ToList();
                    LHrespose.Status = true;
                }
                else
                {
                    LHrespose.Status = true;
                    LHrespose.Message = "No Data Found";
                    LHrespose.Data = null;
                }
            }
            catch (Exception ex)
            {
                LHrespose.Status = false;
                LHrespose.Message = ex.ToString();
                LHrespose.Data = null;
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), Convert.ToInt64(LeadID), ex.ToString(), "GetLeadHistory", "MatrixCore", "BMSBLL", null, JsonConvert.SerializeObject(LeadID), dateTime, DateTime.Now);
            }
            return LHrespose;
        }

        public LeadStatusResponseModel GetLeadLogs(long leadId)
        {
            var leadStatusResponse = new LeadStatusResponseModel();
            try
            {
                if (leadId > 0)
                {
                    DataSet data = BmsDLL.GetLeadLogs(leadId);
                    if (data != null && data.Tables != null && data.Tables.Count > 0)
                    {
                        var commentsTable = data.Tables[0];
                        if (commentsTable != null && commentsTable.Rows != null && commentsTable.Rows.Count > 0)
                        {
                            leadStatusResponse.Comments = (from dr in commentsTable.AsEnumerable()
                                                           select new CommentsInfo
                                                           {
                                                               LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : default,
                                                               Comment = dr["Comment"] != DBNull.Value ? dr["Comment"].ToString() : default,
                                                               UserId = dr["UserId"] != DBNull.Value ? Convert.ToInt64(dr["UserId"]) : default,
                                                               UserName = dr["UserName"] != DBNull.Value ? dr["UserName"].ToString() : default,
                                                               CommentedOn = dr["CommentedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CommentedOn"]) : default
                                                           }).ToList();
                        }

                        var leadHistoryTable = data.Tables[1];
                        if (leadHistoryTable != null && leadHistoryTable.Rows != null && leadHistoryTable.Rows.Count > 0)
                        {
                            var row = leadHistoryTable.Rows[0];
                            leadStatusResponse.LeadId = row["LeadId"] != DBNull.Value ? Convert.ToInt64(row["LeadId"]) : default;
                            leadStatusResponse.UserName = row["UserName"] != DBNull.Value ? row["UserName"].ToString() : null;
                            leadStatusResponse.UserId = row["UserId"] != DBNull.Value ? Convert.ToInt64(row["UserId"]) : default;
                            leadStatusResponse.Premium = row["Premium"] != DBNull.Value ? Convert.ToDecimal(row["Premium"]) : default;
                            leadStatusResponse.SumInsurred = row["SumInsurred"] != DBNull.Value ? Convert.ToDecimal(row["SumInsurred"]) : default;
                            leadStatusResponse.StatusId = row["StatusId"] != DBNull.Value ? Convert.ToInt16(row["StatusId"]) : default;
                            leadStatusResponse.LastStatusDate = row["LastStatusDate"] != DBNull.Value ? Convert.ToDateTime(row["LastStatusDate"]) : default;
                            leadStatusResponse.StatusName = row["StatusName"] != DBNull.Value ? row["StatusName"].ToString() : default;
                            leadStatusResponse.SubStatusId = row["SubStatusId"] != DBNull.Value ? Convert.ToInt16(row["SubStatusId"]) : default;
                            leadStatusResponse.SubStatusName = row["SubStatusName"] != DBNull.Value ? row["SubStatusName"].ToString() : default;
                            leadStatusResponse.LeadSource = row["LeadSource"] != DBNull.Value ? row["LeadSource"].ToString() : default;
                            leadStatusResponse.UTMSource = row["Utm_source"] != DBNull.Value ? row["Utm_source"].ToString() : default;
                        }

                        var callHistoryTable = data.Tables[2];
                        if (callHistoryTable != null && callHistoryTable.Rows != null && callHistoryTable.Rows.Count > 0)
                        {
                            leadStatusResponse.CallLogs = (from dr in callHistoryTable.AsEnumerable()
                                                           select new CallLogs
                                                           {
                                                               UserID = dr["UserId"] != DBNull.Value ? Convert.ToInt64(dr["UserId"]) : default,
                                                               CallDate = dr["CallDate"] != DBNull.Value ? Convert.ToDateTime(dr["CallDate"]) : default,
                                                               Duration = dr["Duration"] != DBNull.Value ? Convert.ToInt16(dr["Duration"]) : default,
                                                               Talktime = dr["Talktime"] != DBNull.Value ? Convert.ToInt16(dr["Talktime"]) : default,
                                                               CallType = dr["CallType"] != DBNull.Value ? (dr["CallType"]).ToString() : default,
                                                               UserName = dr["UserName"] != DBNull.Value ? (dr["UserName"]).ToString() : default,
                                                           }).ToList();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(leadId.ToString(), leadId, ex.ToString(), "GetLeadLogs", "MatrixCore", "BMSBLL", null, string.Empty, DateTime.Now, DateTime.Now);
            }
            return leadStatusResponse;
        }

        public List<LeadDetailsByAgent> GetBookingDetailsByAgentId(string userId, long FromDate, long ToDate)
        {
            var leadDetails = new List<LeadDetailsByAgent>();
            DateTime FromDatereq;
            DateTime ToDatereq;
            DateTime dateTime = DateTime.Now;
            try
            {
                if (FromDate > 0 && ToDate > 0)
                {
                    FromDatereq = CoreCommonMethods.UnixTimeToDateTime(Convert.ToInt64(FromDate));
                    ToDatereq = CoreCommonMethods.UnixTimeToDateTime(Convert.ToInt64(ToDate));
                    DataTable dt = BmsDLL.GetBookingDetailsByAgentId(Convert.ToInt64(userId), FromDatereq, ToDatereq);

                    if (dt != null && dt.Rows != null && dt.Rows.Count > 0)
                    {
                        leadDetails = (from row in dt.AsEnumerable()
                                       select new LeadDetailsByAgent
                                       {
                                           LeadId = row["LeadId"] != DBNull.Value ? Convert.ToInt64(row["LeadId"]) : default,
                                           CompanyName = row["CompanyName"] != DBNull.Value ? Convert.ToString(row["CompanyName"]) : default,
                                           LeadSource = row["LeadSource"] != DBNull.Value ? Convert.ToString(row["LeadSource"]) : default,
                                           SubProduct = row["SubProduct"] != DBNull.Value ? Convert.ToString(row["SubProduct"]) : default,
                                           SubProductID = row["SubProductID"] != DBNull.Value ? Convert.ToInt16(row["SubProductID"]) : default,
                                           UtmSource = row["UtmSource"] != DBNull.Value ? Convert.ToString(row["UtmSource"]) : default,
                                           LeadStatus = row["LeadStatus"] != DBNull.Value ? Convert.ToString(row["LeadStatus"]) : default,
                                           LeadStatusId = row["LeadStatusId"] != DBNull.Value ? Convert.ToInt16(row["LeadStatusId"]) : default,
                                           CreatedOn = row["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(row["CreatedOn"]) : default,
                                           Premium = row["Premium"] != DBNull.Value ? Convert.ToDecimal(row["Premium"]) : default,
                                       }).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(userId.ToString(), Convert.ToInt64(userId), ex.ToString(), "GetBookingDetailsByAgentId", "MatrixCore", "BMSBLL", null, string.Empty, DateTime.Now, DateTime.Now);
            }
            return leadDetails;
        }
        public ResponseData<BookedAndRenewalLeadData> GetBookedAndRenewalLeadData(long LeadID)
        {
            ResponseData<BookedAndRenewalLeadData> respose = new ResponseData<BookedAndRenewalLeadData>();
            respose.Data = new BookedAndRenewalLeadData();
            DateTime dateTime = DateTime.Now;
            respose.Data.InputLeadID = LeadID;
            respose.Status = true;
            respose.Message = "No Data Found";
            try
            {
                DataSet ds = BmsDLL.GetBookedAndRenewalLeadData(Convert.ToInt64(LeadID));
                if (ds != null && ds.Tables.Count > 0 &&  ds.Tables[0].Rows != null && ds.Tables[0].Rows.Count > 0)
                {
                    respose.Data.BookedLeadIDs = (from dr in ds.Tables[0].AsEnumerable()
                                      select new LeadIDs
                                      {
                                          LeadID = Convert.ToInt64(dr["LeadId"])
                                      }).ToList();
                    respose.Message = "Success";
                }
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[1].Rows != null && ds.Tables[1].Rows.Count > 0)
                {
                    respose.Data.RenewalLeadIDs = (from dr in ds.Tables[1].AsEnumerable()
                                                  select new LeadIDs
                                                  {
                                                      LeadID = Convert.ToInt64(dr["LeadId"])
                                                  }).ToList();
                    respose.Message = "Success";
                }
                respose.Code = 200;
            }
            catch (Exception ex)
            {
                respose.Status = false;
                respose.Message = ex.ToString();
                respose.Data = null;
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), Convert.ToInt64(LeadID), ex.ToString(), "GetBookedAndRenewalLeadData", "MatrixCore", "BMSBLL", null, JsonConvert.SerializeObject(respose), dateTime, DateTime.Now);
            }

            return respose;
        }

        public string GetRMDetailsForTermSavings(long customerId, long LeadId)
        {
            string Response = string.Empty;
            DateTime reqTime = DateTime.Now;
            RMDetailsTerm RMDetails = null;

            try
            {
                string encCustId = Crypto.encrypt_AES(Convert.ToString(customerId), 128, 128, "BMSCromaENCKeyForMatrix".AppSettings(), "BMSCromaENCIVForMatrix".AppSettings());
                string url = "BmsCromaURL".AppSettings() + "api/MobileApp/GetAssignedDetailsByCustId?CustId=" + encCustId;

                Dictionary<object, object> header = new Dictionary<object, object>();
                header.Add("accept", "text/plain");
                header.Add("source", "matrix");
                header.Add("clientKey", "PBCromaclientkey".AppSettings());
                header.Add("authKey", "PBCromaAuthkey".AppSettings());

                var result = CommonAPICall.CallAPI(url, "", "GET", 3000, "application/json", header);

                if (!string.IsNullOrEmpty(result))
                {
                    if (result != null)
                    {
                        RMDetails = JsonConvert.DeserializeObject<RMDetailsTerm>(result);
                        if(RMDetails.Status == true)
                        {
                            AssignedAgent assignedAgent = RMDetails.AssignedAgents.Find(x => x.LeadId == LeadId);
                            if(assignedAgent != null)
                            {
                                string MobileNo = Crypto.decrypt_AES(assignedAgent.AgentMobileNo, 128, 128, "BMSCromaENCKeyForMatrix".AppSettings(), "BMSCromaENCIVForMatrix".AppSettings());
                                Response = "{\"RMEmployeeID\":\"" + assignedAgent.AgentEmpID + "\",\"RMName\":\"" + assignedAgent.AgentName + "\",\"RMMobileNo\":\"" + MobileNo + "\"}";
                            }
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(customerId.ToString(), Convert.ToInt64(customerId), ex.ToString(), "GetRMDetailsForTermSavings", "MatrixCore", "BMSBLL", null, Response, reqTime, DateTime.Now);
            }
            return Response;
        }

        public MailboxResponse GetMailURL(StringValues? agentId, HttpContext httpContext)
        {
            long userId = 0;
            string error = string.Empty;
            DateTime dt = DateTime.Now;
            var result = new MailboxResponse();
            try
            {
                if (!string.IsNullOrEmpty(agentId))
                {
                    userId = Convert.ToInt64(agentId);
                    string url = "PBServiceMailBoxUrl".AppSettings() + "api/mailbox/GetMailURL";
                    string token = "PbServiceMailBoxToken".AppSettings();
                    var header = new Dictionary<object, object>
                    {
                        { "app-name", "matrix" },
                        { "token", token }
                    };
                    DataSet data = BmsDLL.GetAgentDetails(userId);
                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        var jsonData = new
                        {
                            LDAPLoginId = data.Tables[0].Rows[0]["DomainUserName"] != DBNull.Value ? data.Tables[0].Rows[0]["DomainUserName"].ToString() : string.Empty,
                            Name = data.Tables[0].Rows[0]["UserName"] != DBNull.Value ? data.Tables[0].Rows[0]["UserName"].ToString() : string.Empty,
                            EmailId = data.Tables[0].Rows[0]["Email"] != DBNull.Value ? data.Tables[0].Rows[0]["Email"].ToString() : string.Empty,
                            EmployeeId = data.Tables[0].Rows[0]["EmployeeId"] != DBNull.Value ? data.Tables[0].Rows[0]["EmployeeId"].ToString() : string.Empty,
                            UserId = userId
                        };
                        var res = CommonAPICall.CallApiAddCookie(url, JsonConvert.SerializeObject(jsonData), "POST", Convert.ToInt32("BmsTimeout".AppSettings()), "application/json", "token", httpContext, header);
                        if (!string.IsNullOrEmpty(res))
                            result = JsonConvert.DeserializeObject<MailboxResponse>(res);
                    }
                    else
                    {
                        result.Message = "AgentId details not found";
                    }
                }
                else
                {
                    result.Message = "AgentId is not provided";
                }
                return result;
            }
            catch (Exception ex)
            {
                result.Message = "No Data found";
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(agentId, userId, error, "GetMailURL", "MatrixCore", "BMSBLL", string.Empty, JsonConvert.SerializeObject(result), dt, DateTime.Now);
            }
            return result;
        }

    }
}