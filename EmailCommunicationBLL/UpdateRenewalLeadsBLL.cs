﻿using DataAccessLibrary;
using DataHelper;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using ExcelDataReader;
using MongoConfigProject;
using System.Text;
using Newtonsoft.Json;
using DataAccessLayer;
using File = System.IO.File;
using Helper;
using Amazon.S3;
using Amazon;
using Amazon.S3.Model;
using System.Threading.Tasks;

namespace EmailCommunicationBLL
{
    public class UpdateRenewalLeadsBLL : IUpdateRenewalLeadsBLL
    {
        #region Variable Declaration Block

        long userId;

        private const string constName = "Name";
        private const string constValidMessage = " is not valid <br>";
        private const string constEmptyMessage = " can not be empty <br>";
        private const string constProductID = "ProductID";

        private const string constMobileNo = "MobileNo";
        private const string constEmail = "Email";
        private const string constInsurer = "Insurer";
        private const string constNewPremium = "NewPremium";
        private const string constSI = "Sum Insured";
        private const string constED = "Expiry Date";
        private const string constGracePeriod = "Grace Period";
        private const string constLeadID = "LeadID";

        private const string constRenewalYear = "Renewal Year";

        private const string constOfferDetails = "OfferDetails";
        private const string constDOB = "DOB";
        private const string constAltContatNumber = "AltContatNumber";
        private const string constAddress = "Address";
        private const string constCity = "CityName";
        private const string constSubProd = "SubProduct";
        private const string constProductCode = "ProductCode";
        private const string SmeBulkUploadType = "3";

        #endregion
        List<Plans> PlansList = new List<Plans>();
        public string ProcessRenewalLeads(UploadRenewalLeadsDetails objUploadRD)
        {
            string result = string.Empty;
            string Exception = string.Empty;
            try
            {
                var filepath = UpdateRenewalLeadsDLL.FetchRenewalBulkUploadFile(objUploadRD.UniqueId, objUploadRD.ProductId);
                if (objUploadRD.ProcessType == 1)
                {
                    result = UploadUpdatedData((filepath).Trim(), (objUploadRD.ProductId).Trim(), (objUploadRD.UniqueId).Trim(), (objUploadRD.UploadedBy).Trim(), objUploadRD.ProcessType).ToString();
                }
                else
                {
                    result = UploadCreateData((filepath).Trim(), (objUploadRD.ProductId).Trim(), (objUploadRD.UniqueId).Trim(), (objUploadRD.UploadedBy).Trim(), objUploadRD.ProcessType, (objUploadRD.AssignedGroupId).Trim(), (objUploadRD.AssignedUserId).Trim(), (objUploadRD.ProcessId).Trim()).ToString();
                }

            }
            catch (Exception ex)
            {
                int resq = UpdateRenewalFileStatus((objUploadRD.UniqueId).Trim(), "ErrorOccured", (objUploadRD.UploadedBy).Trim());
                Exception = ex.ToString();
                InsertFileExceptionInMongo((objUploadRD.UniqueId).Trim(), (objUploadRD.UploadedBy).Trim(), Exception);
                result = "File Not Staged";
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objUploadRD.UniqueId, Convert.ToInt64(objUploadRD.UploadedBy), Exception, "ProcessRenewalLeads", "UpdateRenewalLeadsBLL", "ProcessRenewalLeads", objUploadRD.FilePath, result, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        protected string UploadUpdatedData(string FilePath, string ProductId, string TrackingId, string UploadedBy, int ProcessType)
        {
            DataSet ds;
            int index = 0;
            long leadID = 0;
            string ret = string.Empty;
            string Error = string.Empty;
            string sError = string.Empty;
            string extention = string.Empty;
            string strConn = string.Empty;
            try
            {
                int resq = UpdateRenewalFileStatus(TrackingId, "DataUnderProcessing", UploadedBy);
                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
                extention = Path.GetExtension(FilePath);
                var TempPath = Path.GetTempFileName();
                var client = new WebClient();

                if (FilePath.Contains("matrixinternal"))
                {

                    Uri uri = new(FilePath);
                    string path = uri.AbsolutePath;
                    var trustedStorageName = path.Replace("/matrixinternal/", "").TrimStart('/');

                    string bucketName = "matrixdocuments/matrixinternal";

                    var s3Client = new AmazonS3Client(RegionEndpoint.APSouth1);
                    FilePath = s3Client.GetPreSignedURL(new GetPreSignedUrlRequest
                    {
                        BucketName = bucketName,
                        Key = trustedStorageName,
                        Expires = DateTime.UtcNow.AddMinutes(15)
                    });
                }

                client.DownloadFile(FilePath, TempPath);

                using var stream = new FileStream(TempPath, FileMode.Open, FileAccess.Read);
                using IExcelDataReader reader = ExcelReaderFactory.CreateReader(stream);
                ds = reader.AsDataSet(new ExcelDataSetConfiguration()
                {
                    UseColumnDataType = false,
                    ConfigureDataTable = (tableReader) => new ExcelDataTableConfiguration()
                    {
                        UseHeaderRow = true
                    }
                });
                DataSet myDataset = ds;

                if (myDataset.Tables["Sheet1"] != null && myDataset.Tables["Sheet1"].Rows.Count > 0)
                {
                    DataTable dtSheet = myDataset.Tables["Sheet1"];

                    DataTable dt = CreateLeadUpdateTable();

                    for (index = dtSheet.Rows.Count - 1; index >= 0; index--)
                    {
                        DataRow dr = dtSheet.Rows[index];
                        StringBuilder validateError = new StringBuilder();
                        StringBuilder emptyError = new StringBuilder();
                        StringBuilder numberError = new StringBuilder();
                        StringBuilder sbError = new StringBuilder();
                        DataRow rowtoAdd = dt.NewRow();
                        if (!string.IsNullOrEmpty(Convert.ToString(dr[constLeadID])))
                        {
                            long result = 0; decimal decimalOutput = 0;
                            leadID = 0;
                            if (long.TryParse(Convert.ToString(dr[constLeadID]).Trim(), out leadID))
                            {


                                rowtoAdd["leadId"] = Convert.ToString(dr[constLeadID]).Trim();
                                rowtoAdd["CreatedBy"] = userId;

                                var basicValidations = dr.Validate("RenewalUpdate", "LeadId", Convert.ToInt32(ProductId));
                                foreach (var b in basicValidations)
                                {
                                    sbError.AppendFormat("{0} for {1}", b.Value, b.Key);
                                }
                                if (!string.IsNullOrEmpty(sbError.ToString()))
                                {
                                    sError = sbError.ToString();
                                    emptyError.Append(sbError.ToString());
                                    //return;
                                }
                                if (dr.Table.Columns.Contains("Name"))
                                {
                                    if (string.IsNullOrEmpty(Convert.ToString(dr["Name"]).Trim()))
                                    {
                                        emptyError.Append("Name");
                                    }
                                    else
                                    {
                                        rowtoAdd["Name"] = Convert.ToString(dr["Name"]).Trim();
                                    }
                                }
                                if (dr.Table.Columns.Contains("PrimaryEmail"))
                                {

                                    if (string.IsNullOrEmpty(Convert.ToString(dr["PrimaryEmail"]).Trim()))
                                    {
                                        emptyError.Append(" Email");
                                    }
                                    else
                                    {
                                        if (IsValidEmailId(Convert.ToString(dr["PrimaryEmail"]).Trim()))
                                        {
                                            rowtoAdd["EmailID"] = Convert.ToString(dr["PrimaryEmail"]).Trim();
                                        }
                                        else
                                        {
                                            validateError.Append(" Email not valid. ");
                                        }
                                    }
                                }
                                if (dr.Table.Columns.Contains("Address"))
                                {
                                    rowtoAdd["Address"] = Convert.ToString(dr["Address"]).Trim();
                                }
                                if (dr.Table.Columns.Contains("SupplierID"))
                                {
                                    string SupplierID = Convert.ToString(dr["SupplierID"]).Trim();
                                    if (!long.TryParse(SupplierID, out result))
                                    {
                                        numberError.Append("SupplierID ");
                                    }
                                    else
                                    {
                                        if (!dr.Table.Columns.Contains("PlanID"))
                                        {
                                            validateError.Append("for SupplierID " + SupplierID + "-> Provide PlanID.");
                                        }
                                        rowtoAdd["InsurerId"] = SupplierID;
                                    }
                                }
                                if (dr.Table.Columns.Contains("PlanID"))
                                {
                                    string planId = Convert.ToString(dr["PlanID"]).Trim();
                                    if (!long.TryParse(planId, out result))
                                    {
                                        numberError.Append("PlanID ");
                                    }
                                    else
                                    {
                                        if (!dr.Table.Columns.Contains("SupplierID"))
                                        {
                                            validateError.Append("for planId " + planId + "-> Provide SupplierID.");
                                        }
                                        rowtoAdd["PlanId"] = planId;
                                    }
                                }
                                if (dr.Table.Columns.Contains("Insurer"))
                                {
                                    rowtoAdd["InsurerName"] = Convert.ToString(dr["Insurer"]).Trim();
                                }
                                if (dr.Table.Columns.Contains("Plan name"))
                                {
                                    rowtoAdd["PlanName"] = Convert.ToString(dr["Plan name"]).Trim();
                                }
                                if (dr.Table.Columns.Contains("Sum Insured"))
                                {
                                    decimalOutput = 0;
                                    if (!Decimal.TryParse(Convert.ToString(dr["Sum Insured"]).Trim(), out decimalOutput))
                                    {
                                        numberError.Append("Sum Insured ");
                                    }

                                    rowtoAdd["SumInsured"] = Convert.ToString(decimalOutput);
                                }
                                if (dr.Table.Columns.Contains("NewPremium"))
                                {
                                    decimalOutput = 0;
                                    if (!Decimal.TryParse(Convert.ToString(dr["NewPremium"]).Trim(), out decimalOutput))
                                    {
                                        numberError.Append("FirstYearPremium ");
                                    }
                                    rowtoAdd["FirstYearPremium"] = decimalOutput.ToString().Trim();
                                }
                                if (dr.Table.Columns.Contains("Is Claims Taken"))
                                {
                                    string IsClaimsTaken = Convert.ToString(dr["Is Claims Taken"]).Trim();
                                    if (!string.IsNullOrEmpty(IsClaimsTaken))
                                    {

                                        if (IsClaimsTaken.ToUpper().Equals("YES"))
                                        {
                                            dr["Is Claims Taken"] = "True";
                                            rowtoAdd["IsPrevClaimsTaken"] = "True";
                                        }
                                        else if (IsClaimsTaken.ToUpper().Equals("NO"))
                                        {
                                            rowtoAdd["IsPrevClaimsTaken"] = "False";
                                        }
                                    }
                                }

                                if (dr.Table.Columns.Contains("PolicyStartDate"))
                                {
                                    if (!string.IsNullOrEmpty((Convert.ToString(dr["PolicyStartDate"])).TrimStart().TrimEnd()))
                                    {
                                        DateTime dtstartout;
                                        if (!DateTime.TryParse(Convert.ToString(dr["PolicyStartDate"]), out dtstartout))
                                        {
                                            validateError.Append(" Policy Start Date not valid. ");
                                        }
                                        else
                                        {
                                            rowtoAdd["PolicyStartDate"] = Convert.ToString(dr["PolicyStartDate"]).Trim();
                                        }
                                    }
                                    else
                                    {
                                        emptyError.Append("Policy Start Date");
                                    }
                                }
                                if (dr.Table.Columns.Contains(constED))
                                {
                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constED])).TrimStart().TrimEnd()))
                                    {
                                        DateTime dtout;
                                        if (!DateTime.TryParse(Convert.ToString(dr[constED]), out dtout))
                                        {
                                            validateError.Append(" Expiry Date not valid. ");
                                        }
                                        else
                                        {
                                            rowtoAdd["PED"] = Convert.ToString(dr[constED]).Trim();
                                        }
                                    }
                                    else
                                    {
                                        emptyError.Append(" Expiry Date ");
                                    }
                                }

                                if (dr.Table.Columns.Contains("Grace Period"))
                                {
                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constGracePeriod])).TrimStart().TrimEnd()))
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constGracePeriod]), false, true, false, false, 0, 2))
                                        {
                                            validateError.Append("Grace Period Not valid. ");
                                        }
                                        else
                                        {
                                            rowtoAdd["GracePeriod"] = Convert.ToString(dr["Grace Period"]).Trim();
                                        }
                                    }
                                    else
                                    {
                                        emptyError.Append(" Grace Period ");
                                    }



                                }
                                if (dr.Table.Columns.Contains("Renewal Year"))
                                {
                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constRenewalYear])).TrimStart().TrimEnd()))
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constRenewalYear]), false, true, false, false, 0, 2))
                                        {
                                            validateError.Append("Sorry Renewal Year not valid. ");
                                        }
                                        else
                                        {

                                            rowtoAdd["RenewalYear"] = Convert.ToString(dr["Renewal Year"]).Trim();
                                        }
                                    }
                                }
                                if (dr.Table.Columns.Contains("PolicyNumber"))
                                {
                                    rowtoAdd["OldPolicyNo"] = Convert.ToString(dr["PolicyNumber"]).Trim().Replace("'", string.Empty);
                                }

                                if (dr.Table.Columns.Contains("UpsellOffer"))
                                {
                                    rowtoAdd["UpsellOffer"] = Convert.ToString(dr["UpsellOffer"]).Trim();
                                }

                                if (dr.Table.Columns.Contains("OfferDetails"))
                                {
                                    rowtoAdd["OfferDetails"] = Convert.ToString(dr["OfferDetails"]).Trim();
                                }

                                /////New Fields Added
                                if (dr.Table.Columns.Contains("CustomerId"))
                                {
                                    var CustomerId = 0;
                                    if (int.TryParse(dr["CustomerId"].ToString(), out CustomerId))
                                    {
                                        rowtoAdd["CustomerId"] = CustomerId;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid CustomerId, enter valid numeric value");
                                    }
                                }

                                if (dr.Table.Columns.Contains("PlanTerm"))
                                {
                                    var PlanTerm = 0;
                                    if (int.TryParse(dr["PlanTerm"].ToString(), out PlanTerm))
                                    {
                                        rowtoAdd["PlanTerm"] = PlanTerm;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid PlanTerm, enter valid numeric value");
                                    }
                                }
                                if (dr.Table.Columns.Contains("UPSellSI"))
                                {
                                    var UPSellSI = 0;
                                    if (int.TryParse(dr["UPSellSI"].ToString(), out UPSellSI))
                                    {
                                        rowtoAdd["UPSellSI"] = UPSellSI;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid UPSellSI, enter valid numeric value");
                                    }
                                }

                                if (dr.Table.Columns.Contains("UPSellSIPremium"))
                                {
                                    var UPSellSIPremium = 0;
                                    if (int.TryParse(dr["UPSellSIPremium"].ToString(), out UPSellSIPremium))
                                    {
                                        rowtoAdd["UPSellSIPremium"] = UPSellSIPremium;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid UPSellSIPremium, enter valid numeric value");
                                    }
                                }

                                if (dr.Table.Columns.Contains("OneYearPremium"))
                                {
                                    var OneYearPremium = 0;
                                    if (int.TryParse(dr["OneYearPremium"].ToString(), out OneYearPremium))
                                    {
                                        rowtoAdd["OneYearPremium"] = OneYearPremium;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid OneYearPremium, enter valid numeric value");
                                    }
                                }

                                if (dr.Table.Columns.Contains("TwoYearPremium"))
                                {
                                    var TwoYearPremium = 0;
                                    if (int.TryParse(dr["TwoYearPremium"].ToString(), out TwoYearPremium))
                                    {
                                        rowtoAdd["TwoYearPremium"] = TwoYearPremium;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid TwoYearPremium, enter valid numeric value");
                                    }
                                }

                                if (dr.Table.Columns.Contains("ThreeYearPremium"))
                                {
                                    var ThreeYearPremium = 0;
                                    if (int.TryParse(dr["ThreeYearPremium"].ToString(), out ThreeYearPremium))
                                    {
                                        rowtoAdd["ThreeYearPremium"] = ThreeYearPremium;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid ThreeYearPremium, enter valid numeric value");
                                    }
                                }

                                if (dr.Table.Columns.Contains("AddOnProduct"))
                                {
                                    rowtoAdd["AddOnProduct"] = dr["AddOnProduct"].ToString();
                                }

                                if (dr.Table.Columns.Contains("FamilyType"))
                                {
                                    rowtoAdd["FamilyType"] = dr["FamilyType"].ToString();
                                }

                                if (dr.Table.Columns.Contains("OneYearQuote"))
                                {
                                    rowtoAdd["OneYearQuote"] = dr["OneYearQuote"].ToString();
                                }

                                if (dr.Table.Columns.Contains("TwoYearQuote"))
                                {
                                    rowtoAdd["TwoYearQuote"] = dr["TwoYearQuote"].ToString();
                                }

                                if (dr.Table.Columns.Contains("ThreeYearQuote"))
                                {
                                    rowtoAdd["ThreeYearQuote"] = dr["ThreeYearQuote"].ToString();
                                }
                                if (dr.Table.Columns.Contains("DOB"))
                                {
                                    rowtoAdd["DOB"] = dr["DOB"].ToString();
                                }
                                if (dr.Table.Columns.Contains("ProductCode"))
                                {
                                    rowtoAdd["ProductCode"] = dr["ProductCode"].ToString();
                                }
                                if (dr.Table.Columns.Contains("MasterPolicyNo"))
                                {
                                    rowtoAdd["MasterPolicyNo"] = dr["MasterPolicyNo"].ToString();
                                }
                                if (dr.Table.Columns.Contains("FreeHealthCheckup"))
                                {
                                    rowtoAdd["FreeHealthCheckup"] = dr["FreeHealthCheckup"].ToString();
                                }
                                if (dr.Table.Columns.Contains("NCB"))
                                {

                                    decimal NCB = 0;
                                    if (dr["NCB"].ToString() != "")
                                    {
                                        if (decimal.TryParse(dr["NCB"].ToString(), out NCB))
                                        {
                                            rowtoAdd["NCB"] = Convert.ToDecimal(dr["NCB"].ToString());
                                        }
                                        else
                                        {
                                            validateError.Append("Invalid NCB, enter valid numeric value");
                                        }
                                    }

                                }
                                if (dr.Table.Columns.Contains("SpecificDiseaseWaitingPeriod"))
                                {
                                    int SpecificDiseaseWaitingPeriod = 0;
                                    if (dr["SpecificDiseaseWaitingPeriod"].ToString() != "")
                                    {
                                        if (int.TryParse(dr["SpecificDiseaseWaitingPeriod"].ToString(), out SpecificDiseaseWaitingPeriod))
                                        {
                                            rowtoAdd["SpecificDiseaseWaitingPeriod"] = Convert.ToInt16(dr["SpecificDiseaseWaitingPeriod"].ToString());
                                        }
                                        else
                                        {
                                            validateError.Append("Invalid SpecificDiseaseWaitingPeriod, enter valid numeric value");
                                        }
                                    }


                                }
                                if (dr.Table.Columns.Contains("PreExistingDiseaseWaitingPeriod"))
                                {
                                    int PreExistingDiseaseWaitingPeriod = 0;
                                    if (dr["PreExistingDiseaseWaitingPeriod"].ToString() != "")
                                    {
                                        if (int.TryParse(dr["PreExistingDiseaseWaitingPeriod"].ToString(), out PreExistingDiseaseWaitingPeriod))
                                        {
                                            rowtoAdd["PreExistingDiseaseWaitingPeriod"] = Convert.ToInt16(dr["PreExistingDiseaseWaitingPeriod"].ToString());
                                        }
                                        else
                                        {
                                            validateError.Append("Invalid PreExistingDiseaseWaitingPeriod, enter valid numeric value");
                                        }
                                    }

                                    //rowtoAdd["PreExistingDiseaseWaitingPeriod"] = Convert.ToInt16(dr["PreExistingDiseaseWaitingPeriod"].ToString());
                                }
                                if (dr.Table.Columns.Contains("AdditionalBenefits"))
                                {
                                    rowtoAdd["AdditionalBenefits"] = dr["AdditionalBenefits"].ToString();
                                }

                                if (dr.Table.Columns.Contains("AltContatNumber"))
                                {
                                    long AltNo = 0;
                                    if (long.TryParse(dr["AltContatNumber"].ToString(), out AltNo))
                                    {
                                        rowtoAdd["AltNo"] = AltNo;
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid AltNumber, enter valid numeric value");
                                    }
                                }

                                /////New Fields Added
                                if (dr.Table.Columns.Contains("PrimaryNo"))
                                {
                                    long PrimaryNo = 0;


                                    if (!isTextValid(dr["PrimaryNo"].ToString(), false, true, false, false, 6, 12) && Convert.ToString(dr["PrimaryNo"].ToString()) != "0")
                                    {
                                        validateError.Append("Invalid PrimaryNo, enter valid numeric value.Mobile No should be between 6 to 12 digits");
                                    }
                                    else
                                    {

                                        if (long.TryParse(dr["PrimaryNo"].ToString(), out PrimaryNo))
                                        {
                                            rowtoAdd["PrimaryNo"] = PrimaryNo;
                                        }
                                        else
                                        {
                                            validateError.Append("Invalid PrimaryNo, enter valid numeric value");
                                        }
                                    }
                                }

                                if (dr.Table.Columns.Contains("AltEmailID"))
                                {

                                    if (string.IsNullOrEmpty(Convert.ToString(dr["AltEmailID"]).Trim()))
                                    {
                                        emptyError.Append(" AltEmailID");
                                    }
                                    else
                                    {
                                        if (IsValidEmailId(Convert.ToString(dr["AltEmailID"]).Trim()))
                                        {
                                            rowtoAdd["AltEmailID"] = Convert.ToString(dr["AltEmailID"]).Trim();
                                        }
                                        else
                                        {
                                            validateError.Append(" AltEmailID not valid. ");
                                        }
                                    }
                                }

                                if (dr.Table.Columns.Contains("UpdateAllSet"))
                                {
                                    int UpdateAllSet = 0;
                                    if (int.TryParse(dr["UpdateAllSet"].ToString(), out UpdateAllSet))
                                    {
                                        if (UpdateAllSet.Equals(1))
                                        {
                                            rowtoAdd["UpdateAllSet"] = 1;
                                        }
                                        else
                                        {
                                            rowtoAdd["UpdateAllSet"] = 0;
                                        }
                                    }
                                    else
                                    {
                                        validateError.Append("Invalid UpdateAllSet, enter valid numeric value");
                                    }
                                }

                                if (dr.Table.Columns.Contains("CountryID"))
                                {
                                    int CountryID = 0;
                                    if (dr["CountryID"].ToString() != "" && dr["CountryID"].ToString() != "0")
                                    {
                                        if (int.TryParse(dr["CountryID"].ToString(), out CountryID))
                                        {
                                            rowtoAdd["CountryID"] = CountryID;
                                        }
                                        else
                                        {
                                            validateError.Append("Invalid CountryID, enter valid numeric value");
                                        }
                                    }
                                }

                                if (dr.Table.Columns.Contains("WellnessPoint"))
                                {
                                    string WellnessPoint = Convert.ToString(dr["WellnessPoint"]).Trim();
                                    if (!string.IsNullOrEmpty(WellnessPoint) && !isTextValid(WellnessPoint, false, true, false, false, 0, 50))
                                    {
                                        sbError.AppendLine();
                                        sbError.Append("<br />Invalid WellnessPoint(Max 50 digits allowed)");
                                        dtSheet.Rows.Remove(dr);
                                        continue;
                                    }

                                    if (!string.IsNullOrEmpty(WellnessPoint))
                                    {
                                        rowtoAdd["WellnessPoint"] = WellnessPoint;
                                    }

                                }

                                if (dr.Table.Columns.Contains("OldBookingId"))
                                {
                                    if (!string.IsNullOrEmpty(Convert.ToString(dr["OldBookingId"]).Trim()))
                                    {
                                        long OldBookingId = 0;
                                        if (!long.TryParse(dr["OldBookingId"].ToString(), out OldBookingId))
                                        {
                                            validateError.Append("Invalid OldBookingId,Enter valid numeric value");
                                        }
                                        else if (OldBookingId > 0)
                                        {
                                            rowtoAdd["OldBookingId"] = Convert.ToString(dr["OldBookingId"]).Trim();
                                        }
                                    }
                                }
                                if (dr.Table.Columns.Contains("Loading"))
                                {
                                    string Loading = Convert.ToString(dr["Loading"]).Trim();
                                    if (string.IsNullOrEmpty(Loading))
                                    {
                                        dr["Loading"] = string.Empty;
                                    }
                                    if (!string.IsNullOrEmpty(Convert.ToString(dr["Loading"]).Trim()))
                                    {
                                        if (Loading.ToUpper().Equals("YES"))
                                        {
                                            dr["Loading"] = "YES";
                                            rowtoAdd["Loading"] = Convert.ToString(dr["Loading"]).Trim();
                                        }
                                        else if (Loading.ToUpper().Equals("NO"))
                                        {
                                            dr["Loading"] = "NO";
                                            rowtoAdd["Loading"] = Convert.ToString(dr["Loading"]).Trim();
                                        }
                                        else
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("<br />Loading Taken can be either YES or NO");
                                            dtSheet.Rows.Remove(dr);
                                            continue;
                                        }
                                    }

                                }
                                if (dr.Table.Columns.Contains("ComboProduct"))
                                {
                                    rowtoAdd["ComboProduct"] = Convert.ToString(dr["ComboProduct"]).Trim();
                                }

                                //Added Azhar
                                if (dr.Table.Columns.Contains("MonthlyMode"))
                                {
                                    rowtoAdd["MonthlyMode"] = Convert.ToString(dr["MonthlyMode"]).Trim();
                                }
                                if (dr.Table.Columns.Contains("ACH"))
                                {
                                    rowtoAdd["ACH"] = Convert.ToString(dr["ACH"]).Trim();
                                }

                                //if (dr.Table.Columns.Contains("PolicyInceptionDate") &&
                                //                !string.IsNullOrEmpty(Convert.ToString(dr["PolicyInceptionDate"])))
                                //{
                                //    DateTime d;
                                //    string inceptiondate = Convert.ToString(dr["PolicyInceptionDate"]).Trim();
                                //    if (DateTime.TryParseExact(inceptiondate, "M/d/yyyy hh:mm:ss tt", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out d))
                                //    {
                                //        inceptiondate = d.ToString("dd-MM-yyyy");
                                //    }
                                //    string[] array = Convert.ToString(inceptiondate).Split(' ');
                                //    inceptiondate = array[0];
                                //    if (!isValidDate(inceptiondate))
                                //    {
                                //        validateError.Append(" PolicyInceptionDate not valid. ");
                                //    }
                                //    else
                                //    {
                                //        rowtoAdd["PolicyInceptionDate"] = DateTime.ParseExact(inceptiondate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                                //    }
                                //}

                                if (dr.Table.Columns.Contains("PolicyInceptionDate") && !string.IsNullOrEmpty(Convert.ToString(dr["PolicyInceptionDate"]).TrimStart().TrimEnd()))
                                {
                                    DateTime dtstartout;
                                    if (!DateTime.TryParse(Convert.ToString(dr["PolicyInceptionDate"]), out dtstartout))
                                    {
                                        validateError.Append(" PolicyInceptionDate not valid. ");
                                    }
                                    else
                                    {
                                        rowtoAdd["PolicyInceptionDate"] = dtstartout;
                                    }
                                }


                                //Added End

                                //Added on 27/12/2021
                                if (dr.Table.Columns.Contains("BatchID"))
                                {
                                    rowtoAdd["BatchID"] = dr["BatchID"].ToString();
                                }
                                //End 27/12/2021
                                if (dr.Table.Columns.Contains("Remarks"))
                                {
                                    rowtoAdd["Remarks"] = Convert.ToString(dr["Remarks"]).Trim();
                                }

                                if (dr.Table.Columns.Contains("UpsellFreshLead"))
                                {
                                    string UpsellFreshLead = Convert.ToString(dr["UpsellFreshLead"]).Trim();
                                    if (string.IsNullOrEmpty(UpsellFreshLead))
                                    {
                                        rowtoAdd["UpsellFreshLead"] = 0;
                                    }
                                    else
                                    {
                                        rowtoAdd["UpsellFreshLead"] = Convert.ToInt64(UpsellFreshLead);
                                    }
                                }

                                if (dr.Table.Columns.Contains("EnrollmentDate"))
                                {
                                    if (!string.IsNullOrEmpty((Convert.ToString(dr["EnrollmentDate"])).TrimStart().TrimEnd()))
                                    {
                                        DateTime dtstartout;
                                        if (!DateTime.TryParse(Convert.ToString(dr["EnrollmentDate"]), out dtstartout))
                                        {
                                            validateError.Append(" EnrollmentDate not valid. ");
                                        }
                                        else
                                        {
                                            rowtoAdd["EnrollmentDate"] = Convert.ToString(dr["EnrollmentDate"]).Trim();
                                        }
                                    }
                                    else
                                    {
                                        emptyError.Append("EnrollmentDate");
                                    }
                                }

                                if (dr.Table.Columns.Contains("OverrideRecords"))
                                {
                                    string OverrideRecords = Convert.ToString(dr["OverrideRecords"]).Trim();
                                    string OverrideAccess = "OverrideAccess".AppSettings().ToString();

                                    if (string.IsNullOrEmpty(OverrideRecords) || !OverrideAccess.Contains(UploadedBy))
                                    //(UploadedBy != "8835" && UploadedBy != "90596" && UploadedBy != "90549" && UploadedBy != "8930" && UploadedBy != "36256" && UploadedBy != "40935")
                                    {
                                        rowtoAdd["OverrideRecords"] = 0;
                                    }
                                    else
                                    {
                                        rowtoAdd["OverrideRecords"] = Convert.ToInt16(OverrideRecords);
                                    }
                                }
                                if (dr.Table.Columns.Contains("PremiumInflationReason"))
                                {
                                    rowtoAdd["PremiumInflationReason"] = Convert.ToString(dr["PremiumInflationReason"]).Trim();
                                }
                                if (dr.Table.Columns.Contains("IsRegional"))
                                {
                                    string IsRegional = Convert.ToString(dr["IsRegional"]).Trim();
                                    if (!string.IsNullOrEmpty(IsRegional))
                                    {
                                        rowtoAdd["IsRegional"] = Convert.ToInt16(IsRegional);
                                    }
                                }
                                if (dr.Table.Columns.Contains("WasRegional"))
                                {
                                    string WasRegional = Convert.ToString(dr["WasRegional"]).Trim();
                                    if (!string.IsNullOrEmpty(WasRegional))
                                    {
                                        rowtoAdd["WasRegional"] = Convert.ToInt16(WasRegional);
                                    }
                                }
                                if (dr.Table.Columns.Contains("WasPort"))
                                {
                                    string WasPort = Convert.ToString(dr["WasPort"]).Trim();
                                    if (!string.IsNullOrEmpty(WasPort))
                                    {
                                        rowtoAdd["WasPort"] = Convert.ToInt16(WasPort);
                                    }
                                }
                                if (dr.Table.Columns.Contains("Isdeductible"))
                                {
                                    string Isdeductible = Convert.ToString(dr["Isdeductible"]).Trim();
                                    if (!string.IsNullOrEmpty(Isdeductible))
                                    {
                                        rowtoAdd["Isdeductible"] = Convert.ToInt16(Isdeductible);
                                    }
                                }
                                if (dr.Table.Columns.Contains("highpriority"))
                                {
                                    string highpriority = Convert.ToString(dr["highpriority"]).Trim();
                                    if (!string.IsNullOrEmpty(highpriority))
                                    {
                                        rowtoAdd["highpriority"] = Convert.ToInt16(highpriority);
                                    }
                                }
                                if (dr.Table.Columns.Contains("GSTRefundEligible"))
                                {
                                    string GSTRefundEligible = Convert.ToString(dr["GSTRefundEligible"]).Trim();
                                    if (!string.IsNullOrEmpty(GSTRefundEligible))
                                    {
                                        rowtoAdd["GSTRefundEligible"] = Convert.ToInt16(GSTRefundEligible);
                                    }
                                }
                                if (dr.Table.Columns.Contains("CardlessEmi"))
                                {
                                    string CardlessEmi = Convert.ToString(dr["CardlessEmi"]).Trim();
                                    if (!string.IsNullOrEmpty(CardlessEmi))
                                    {
                                        rowtoAdd["CardlessEmi"] = Convert.ToInt16(CardlessEmi);
                                    }
                                }
                                if (dr.Table.Columns.Contains("RolloverTagging"))
                                {
                                    string RolloverTagging = Convert.ToString(dr["RolloverTagging"]).Trim();
                                    if (!string.IsNullOrEmpty(RolloverTagging))
                                    {
                                        rowtoAdd["RolloverTagging"] = Convert.ToInt16(RolloverTagging);
                                    }
                                }
                                if (dr.Table.Columns.Contains("PEDInfo"))
                                {
                                    rowtoAdd["PEDInfo"] = Convert.ToString(dr["PEDInfo"]).Trim();
                                }
                                if (dr.Table.Columns.Contains("IsABHIDiscount"))
                                {
                                    string IsABHIDiscount = Convert.ToString(dr["IsABHIDiscount"]).Trim();
                                    if (!string.IsNullOrEmpty(IsABHIDiscount))
                                    {
                                        rowtoAdd["IsABHIDiscount"] = Convert.ToInt16(IsABHIDiscount);
                                    }
                                }

                                if (dr.Table.Columns.Contains("DummyNP"))
                                {
                                    decimal DummyNP = 0;
                                    if (dr["DummyNP"].ToString() != "")
                                    {
                                        if (decimal.TryParse(dr["DummyNP"].ToString(), out DummyNP))
                                        {
                                            rowtoAdd["DummyNP"] = Convert.ToDecimal(dr["DummyNP"].ToString());
                                        }
                                    }
                                }

                                if (dr.Table.Columns.Contains("DirectLastYear"))
                                {
                                    string DirectLastYear = Convert.ToString(dr["DirectLastYear"]).Trim();
                                    if (!string.IsNullOrEmpty(DirectLastYear))
                                    {
                                        rowtoAdd["DirectLastYear"] = Convert.ToInt16(DirectLastYear);
                                    }
                                }

                                if (dr.Table.Columns.Contains("IsPlanMigration"))
                                {
                                    string IsPlanMigration = Convert.ToString(dr["IsPlanMigration"]).Trim();
                                    if (!string.IsNullOrEmpty(IsPlanMigration))
                                    {
                                        rowtoAdd["IsPlanMigration"] = Convert.ToInt16(IsPlanMigration);
                                    }
                                }
                                if (dr.Table.Columns.Contains("RiderPlan"))
                                {
                                    if (dr["RiderPlan"].ToString() != "")
                                    {
                                        rowtoAdd["RiderPlan"] = dr["RiderPlan"].ToString();

                                    }
                                }


                                rowtoAdd["TrackingId"] = Convert.ToString(TrackingId).Trim();
                                rowtoAdd["ProductID"] = Convert.ToInt32(ProductId);
                                if (emptyError.Length > 0)
                                {
                                    validateError.Append(emptyError + " can't be empty.");
                                }
                                if (numberError.Length > 0)
                                {
                                    validateError.Append(numberError + " is not valid Number.");
                                }

                            }
                            else
                            {
                                sbError.Append("LeadID " + Convert.ToString(dr[constLeadID]) + " is not valid");
                                //dtSheet.Rows.Remove(dr);
                                //continue;
                            }
                        }
                        else
                        {
                            sbError.Append("LeadId" + constEmptyMessage);
                            //dtSheet.Rows.Remove(dr);
                            //continue;
                        }
                        //Added for all records- Riya
                        if (validateError.Length.Equals(0))
                        {
                            rowtoAdd["StatusMessage"] = "";
                        }
                        else
                        {
                            sbError.AppendLine();
                            sbError.Append(dr[constLeadID]);
                            sbError.AppendLine();
                            sbError.Append(validateError);
                            rowtoAdd["StatusMessage"] = Convert.ToString(sbError).Trim();
                        }
                        dt.Rows.Add(rowtoAdd);
                        sbError.Clear();
                    }


                    if (dt.Rows.Count > 0)
                    {
                        try
                        {
                            DataTable resulttbl = BulkUpdateRenewalleads(dt, Convert.ToInt64(UploadedBy), TrackingId);
                            ret = "Processed";
                        }
                        catch (Exception exc)
                        {
                            int retsq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", (UploadedBy).Trim());
                            InsertFileExceptionInMongo((TrackingId).Trim(), (UploadedBy).Trim(), exc.ToString());
                            ret = exc.ToString();
                        }

                    }
                    else
                    {
                        Error = Error + "Some error occured please try again.";
                        int retsq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", (UploadedBy).Trim());
                        InsertFileExceptionInMongo((TrackingId).Trim(), (UploadedBy).Trim(), Error.ToString());
                    }
                    myDataset.Dispose();
                    if (ret == "Processed")
                    {
                        int res = UpdateRenewalFileStatus(TrackingId, "DataStaged", UploadedBy);
                        if (res != 0)
                        {
                            ret = "File Staged Successfully...";
                        }
                    }

                }
                else
                {
                    ret = "Sheet 1 Not Found...";
                }
                stream.Dispose();
                if (System.IO.File.Exists(TempPath))
                {
                    System.IO.File.Delete(TempPath);
                }
            }
            catch (Exception ex)
            {
                int resq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", (UploadedBy).Trim());
                InsertFileExceptionInMongo((TrackingId).Trim(), (UploadedBy).Trim(), ex.ToString());
                if (leadID > 0)
                {
                    ret = "File Not Staged" + leadID.ToString() + ex.Message.ToString();
                }
                else
                {
                    ret = "File Not Staged";
                }

                Error = Error + ex.StackTrace + ret;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, Convert.ToInt64(UploadedBy), Error, "ProcessRenewalLeads", "UpdateRenewalLeadsBLL", "ProcessRenewalLeads", Error, Error, DateTime.Now, DateTime.Now);
            }
            return ret;
        }

        private static bool IsValidMotorBulkUploadColums(DataTable dtSheet)
        {
            try
            {
                return dtSheet.Columns.Contains("PlanId") &&
                        dtSheet.Columns.Contains("InsurerId") &&
                        dtSheet.Columns.Contains("Premium") &&
                        dtSheet.Columns.Contains("SumInsured") &&
                        dtSheet.Columns.Contains("PolicyNo") &&
                        dtSheet.Columns.Contains("IsTP") &&
                        dtSheet.Columns.Contains("ODPremium") &&
                        dtSheet.Columns.Contains("TPPremium") &&
                        dtSheet.Columns.Contains("PolicyStartDate") &&
                        dtSheet.Columns.Contains("PolicyEndDate") &&
                        dtSheet.Columns.Contains("RegNo") &&
                        dtSheet.Columns.Contains("RegDate") &&
                        dtSheet.Columns.Contains("CC") &&
                        dtSheet.Columns.Contains("FuelType") &&
                        dtSheet.Columns.Contains("ProposalNo") &&
                        dtSheet.Columns.Contains("SAOD") &&
                        dtSheet.Columns.Contains("NCB") &&
                        dtSheet.Columns.Contains("AgentId");
            }
            catch
            {
                return false;
            }
        }

        protected string UploadCreateData(string FilePath, string ProductId, string TrackingId, string UploadedBy, int ProcessType, string AssignedGroupId, string AssignedUserId, string ProcessId)
        {
            DataSet ds;
            string ret = string.Empty;
            string Error = string.Empty;
            string sError = string.Empty;
            string extention = string.Empty;
            string strConn = string.Empty;
            StringBuilder sbError = new StringBuilder();
            DateTime ReqDT = DateTime.Now;
            try
            {
                int resq = UpdateRenewalFileStatus(TrackingId, "DataUnderProcessing", UploadedBy);
                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
                extention = Path.GetExtension(FilePath);
                var TempPath = Path.GetTempFileName();
                var client = new WebClient();

                if (FilePath.Contains("matrixinternal"))
                {

                    Uri uri = new(FilePath);
                    string path = uri.AbsolutePath;
                    var trustedStorageName = path.Replace("/matrixinternal/", "").TrimStart('/');

                    string bucketName = "matrixdocuments/matrixinternal";

                    var s3Client = new AmazonS3Client(RegionEndpoint.APSouth1);
                    FilePath = s3Client.GetPreSignedURL(new GetPreSignedUrlRequest
                    {
                        BucketName = bucketName,
                        Key = trustedStorageName,
                        Expires = DateTime.UtcNow.AddMinutes(15)
                    });
                }
                client.DownloadFile(FilePath, TempPath);

                using var stream = new FileStream(TempPath, FileMode.Open, FileAccess.Read);
                using IExcelDataReader reader = ExcelReaderFactory.CreateReader(stream);
                ds = reader.AsDataSet(new ExcelDataSetConfiguration()
                {
                    UseColumnDataType = false,
                    ConfigureDataTable = (tableReader) => new ExcelDataTableConfiguration()
                    {
                        UseHeaderRow = true
                    }
                });

                ProductId = ProductId.Trim();

                if (ds.Tables["Sheet1"] != null && ds.Tables["Sheet1"].Rows.Count > 0)
                {
                    DataTable dtSheet = ds.Tables["Sheet1"];
                    if (ProductId.Equals("117"))
                    {
                        if (IsValidMotorBulkUploadColums(dtSheet))
                        {
                            var stMotor = new StringBuilder();
                            for (int i = dtSheet.Rows.Count - 1; i >= 0; i--)
                            {
                                DataRow dr = dtSheet.Rows[i];
                                ValidateMotorBulkUploadData(dr, stMotor);

                                if (!string.IsNullOrEmpty(stMotor.ToString().Trim()))
                                {
                                    InsertFileExceptionInMongo(TrackingId.Trim(), UploadedBy.Trim(), stMotor.ToString().Trim(), Convert.ToString(dr["AgentId"]).Trim());
                                }
                            }
                        }
                        else
                        {
                            sError += "File does not contain all the required columns, please refer to the Sample file.";
                        }
                    }
                    else
                    {
                        for (int i = dtSheet.Rows.Count - 1; i >= 0; i--)
                        {
                            DataRow dr = dtSheet.Rows[i];

                            if (ProductId == "2" || ProductId == "130" || ProductId == "118" || ProductId == "106")
                            {
                                if (dr["Name"].ToString() == "")
                                    dr.Delete();
                            }
                            else if (ProductId == "101")
                            {
                                if (dr["BookedLeadId"].ToString() == "")
                                    dr.Delete();
                            }
                            else
                            {
                                if (dr["Name"].ToString() == "" || dr["MobileNo"].ToString() == "")
                                    dr.Delete();
                            }
                            dtSheet.AcceptChanges();
                        }
                        dtSheet.Columns.Add(new DataColumn(constProductID, typeof(System.Int32)));
                        dtSheet.Columns.Add(new DataColumn("refsendsms", typeof(System.Boolean)));
                        dtSheet.Columns.Add(new DataColumn("refsendEmail", typeof(System.Boolean)));
                        dtSheet.Columns.Add(new DataColumn("AssignToGroupID", typeof(System.Int32)));
                        dtSheet.Columns.Add(new DataColumn("AssignToUserID", typeof(System.Int32)));
                        dtSheet.Columns.Add(new DataColumn("ErrorMessage", typeof(System.String)));

                        if (dtSheet.Rows.Count == 0)
                        {
                            sError = sError + "No records found for processing";
                        }

                        //if (dtSheet.Rows.Count > 5000)
                        //{
                        //    sError = sError + "Can not upload  more then 5000 records!";
                        //}
                        if (sError.ToString() == "")
                        {
                            if (ProductId == "101")
                            {
                                sError = UploadHomeRenewal(dtSheet, ProductId, AssignedGroupId, AssignedUserId, ProcessId, TrackingId, UploadedBy, FilePath);
                                return sError;
                            }
                            string coreSUpPlan = "CoreSupplierPlanFlag".AppSettings().ToString();
                            getSupplierandPlans();
                            for (int i = dtSheet.Rows.Count - 1; i >= 0; i--)
                            {
                                sbError.Clear();


                                DataRow dr = dtSheet.Rows[i];

                                if (Convert.ToString(dr[constProductCode]) != ProductId)
                                {
                                    sbError.Append("Product Code value in excel does not match with the selected product for: " + Convert.ToString(dr[constName]));
                                }

                                if (!string.IsNullOrEmpty((Convert.ToString(dr[constName]).Trim())))
                                {
                                    if (!isTextValid(Convert.ToString(dr[constName]), true, false, false, false, 2, 50))
                                    {
                                        sbError.Append("Sorry Name of " + Convert.ToString(dr[constName]) + " contains special characters/Check for 50 character length");
                                    }
                                    else
                                    {
                                        dr[constName] = Convert.ToString(dr[constName]).TrimStart().TrimEnd();
                                    }
                                }
                                else
                                {
                                    sbError.Append("Sorry Name" + constEmptyMessage);
                                }
                                if (!string.IsNullOrEmpty(Convert.ToString(dr[constEmail]).Trim()))
                                {
                                    if (!IsValidEmailId((Convert.ToString(dr[constEmail])).TrimStart().TrimEnd()))
                                    {
                                        sbError.Append("Email is not valid for " + Convert.ToString(dr[constName]));
                                    }
                                    else
                                    {
                                        dr[constEmail] = Convert.ToString(dr[constEmail]).TrimStart().TrimEnd();
                                    }
                                }
                                else
                                {
                                    dr[constEmail] = string.Empty;
                                }
                                if (!string.IsNullOrEmpty(Convert.ToString(dr[constMobileNo]).Trim()))
                                {
                                    if (ProductId == "2" || ProductId == "130" || ProductId == "118" || ProductId == "106")
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constMobileNo]), false, true, false, false, 6, 12) && Convert.ToString(dr[constMobileNo]) != "0")
                                        {
                                            sbError.Append("Invalid MobileNo for: " + Convert.ToString(dr[constName]) + "Mobile No should be between 6 to 12 digits or Put 0 in Mobile No & CustomerId in CustomerID Field");
                                        }
                                    }
                                    else
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constMobileNo]), false, true, false, false, 2, 10))
                                        {
                                            sbError.Append("<br />Invalid MobileNo for: " + Convert.ToString(dr[constName]));
                                        }
                                    }
                                }
                                //else
                                //{
                                //    sbError.Append("<br />MobileNo is empty for: " + Convert.ToString(dr[constName]));
                                //   dtSheet.Rows.Remove(dr);
                                //    continue;
                                //}

                                dr[constProductID] = ProductId;//"2";// rblProducts.SelectedItem.Value.ToString();
                                if (ProductId == "2" || ProductId == "130" || ProductId == "118" || ProductId == "106")
                                {
                                    //Validation for Data Type & Length
                                    var basicValidations = dr.Validate("RenewalUpload", "Name", Convert.ToInt32(ProductId));
                                    foreach (var b in basicValidations)
                                    {
                                        sbError.AppendFormat("{0} for {1}", b.Value, b.Key);
                                    }
                                    if (!string.IsNullOrEmpty(sbError.ToString()))
                                    {
                                        sError = sbError.ToString();
                                    }

                                    if ((string.IsNullOrEmpty(dr["CustomerId"].ToString()) || dr["CustomerId"].ToString() == "0") && (string.IsNullOrEmpty(dr[constMobileNo].ToString()) || dr[constMobileNo].ToString() == "0"))
                                    {
                                        sbError.Append("Provide either MobileNo or CustomerId for : " + Convert.ToString(dr[constName]));
                                    }

                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constInsurer]).Trim())))
                                    {

                                        if (!isTextValid(Convert.ToString(dr[constInsurer]), true, false, false, false, 2, 100))
                                        {
                                            dr[constInsurer] = string.Empty;
                                        }
                                        else
                                        {
                                            dr[constInsurer] = Convert.ToString(dr[constInsurer]).TrimStart().TrimEnd();
                                        }
                                    }


                                    if (string.IsNullOrEmpty(Convert.ToString(dr["PolicyNumber"]).Trim()))
                                    {
                                        sbError.AppendLine();
                                        sbError.Append("PolicyNo is empty for: " + Convert.ToString(dr[constName]));
                                    }
                                    else
                                    {
                                        dr["PolicyNumber"] = Convert.ToString(dr["PolicyNumber"]).Trim().Replace("'", string.Empty);
                                    }

                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constNewPremium])).TrimStart().TrimEnd()))
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constNewPremium]).Trim(), false, true, false, false, 2, 30))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("New Premium not valid for: " + Convert.ToString(dr[constName]));
                                        }
                                    }
                                    else
                                    {
                                        sbError.AppendLine();
                                        sbError.Append("New Premium is empty for: " + Convert.ToString(dr[constName]));
                                    }

                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constSI])).TrimStart().TrimEnd()))
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constSI]), false, true, false, false, 2, 30))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Sum Insured  Not valid for :" + Convert.ToString(dr[constName]));
                                        }
                                    }
                                    else
                                    {
                                        sbError.AppendLine();
                                        sbError.Append(">Sum Insured is empty for :" + Convert.ToString(dr[constName]));
                                    }

                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constGracePeriod])).TrimStart().TrimEnd()))
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constGracePeriod]), false, true, false, false, 0, 2))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Sorry Grace Period of " + Convert.ToString(dr[constName]) + constValidMessage);
                                        }
                                    }
                                    else
                                    {
                                        sbError.AppendLine();
                                        sbError.Append("Grace Period is empty for :" + Convert.ToString(dr[constName]));
                                    }

                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constED])).TrimStart().TrimEnd()))
                                    {
                                        DateTime dtout;
                                        if (!DateTime.TryParse(Convert.ToString(dr[constED]), out dtout))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Expiry Date not valid for :" + Convert.ToString(dr[constName]));
                                        }
                                    }
                                    else
                                    {
                                        sbError.AppendLine();
                                        sbError.Append("Expiry Date is empty for :" + Convert.ToString(dr[constName]));
                                    }

                                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constRenewalYear])).TrimStart().TrimEnd()))
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constRenewalYear]), false, true, false, false, 0, 2))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Sorry Renewal Year of " + Convert.ToString(dr[constName]) + constValidMessage);
                                        }
                                    }
                                    if (dr.Table.Columns.Contains(constOfferDetails) && !string.IsNullOrEmpty((Convert.ToString(dr[constOfferDetails])).Trim()))
                                    {
                                        if (!isTextValid(Convert.ToString(dr[constOfferDetails]), false, false, true, false, 0, 360))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append(String.Format("Offer Details accepts max 360 alpha-numeric characters. Invalid details for {0}",
                                                    Convert.ToString(dr[constName])));
                                        }
                                    }
                                    var altContact = Convert.ToString(dr[constAltContatNumber]).Trim();
                                    if (!string.IsNullOrEmpty(altContact))
                                    {
                                        if (!isTextValid(altContact, false, true, false, false, 10, 10))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append(String.Format("Invalid Alternate contact number for {0}. Accepts 10 numeric chars only.",
                                                Convert.ToString(dr[constName])));
                                        }
                                    }
                                    var address = Convert.ToString(dr[constAddress]).Trim();
                                    if (!string.IsNullOrEmpty(address))
                                    {
                                        if (!isTextValid(address, false, false, true, false, 0, 360))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append(String.Format("Address accepts max 360 alpha-numeric characters. Invalid details for {0}",
                                                Convert.ToString(dr[constName])));
                                        }
                                    }

                                    //PlanId
                                    var planID = Convert.ToString(dr["PlanID"]).Trim();
                                    if (!isTextValid(planID, false, true, false, false, 1, 10))
                                    {
                                        sbError.AppendLine();
                                        sbError.Append(String.Format("Invalid PlanId {0} for {1} accepts numeric values.",
                                            Convert.ToString(dr["PlanID"]), Convert.ToString(dr[constName])));
                                    }

                                    var supplierID = Convert.ToString(dr["SupplierID"]).Trim();
                                    if (!isTextValid(supplierID, false, true, false, false, 1, 10))
                                    {
                                        sbError.AppendLine();
                                        sbError.Append(String.Format("Invalid SupplierID {0} for {1} accepts numeric values.",
                                             Convert.ToString(dr["SupplierID"]), Convert.ToString(dr[constName])));
                                    }


                                    bool planindex = true;
                                    if (coreSUpPlan == "True")
                                        planindex = checkExistingPlan(Convert.ToInt32(supplierID), Convert.ToInt32(planID), Convert.ToInt32(ProductId));
                                    else
                                    {
                                        //List<Plans> plans = SupplierPlans.Instance.GetPlan(Convert.ToInt16(2), Convert.ToInt32(supplierID), Convert.ToString(ProductId).ToLower());//check
                                        //planindex = plans.Any(item => item.PlanID == Convert.ToUInt32(planID));
                                    }

                                    if (!planindex)
                                    {
                                        sbError.AppendLine();
                                        sbError.Append(String.Format("Invalid SupplierID {0}, PlanID {1} for {2}. .",
                                             Convert.ToString(dr["SupplierID"]), Convert.ToString(dr["PlanID"]), Convert.ToString(dr[constName])));
                                    }


                                    string IsClaimsTaken = Convert.ToString(dr["Is Claims Taken"]).Trim();
                                    if (!string.IsNullOrEmpty(IsClaimsTaken))
                                    {

                                        if (IsClaimsTaken.ToUpper().Equals("YES"))
                                        {
                                            dr["Is Claims Taken"] = "True";
                                        }
                                        else if (IsClaimsTaken.ToUpper().Equals("NO"))
                                        {
                                            dr["Is Claims Taken"] = "False";
                                        }
                                        else
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Is Claims Taken can be either YES or NO");
                                        }
                                    }


                                    if (string.IsNullOrEmpty(dr["CountryId"].ToString()))
                                    {
                                        dr["CountryId"] = 0;
                                    }
                                    else
                                    {
                                        var CountryId = 0;
                                        if (!int.TryParse(dr["CountryId"].ToString(), out CountryId))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Invalid CountryId, enter valid numeric value");
                                            continue;
                                        }
                                        else
                                        {
                                            dr["CountryId"] = CountryId;
                                        }
                                    }

                                    if (ProductId == "2")
                                    {
                                        //New Health Fields
                                        /////New Fields Added
                                        if (dr.Table.Columns.Contains("CustomerId"))
                                        {
                                            if (string.IsNullOrEmpty(dr["CustomerId"].ToString()))
                                            {
                                                dr["CustomerId"] = 0;
                                            }
                                            else
                                            {
                                                var CustomerId = 0;
                                                if (!int.TryParse(dr["CustomerId"].ToString(), out CustomerId))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append("Invalid CustomerId, enter valid numeric value");
                                                }
                                                else
                                                {
                                                    dr["CustomerId"] = CustomerId;
                                                }
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("PlanTerm"))
                                        {
                                            if (string.IsNullOrEmpty(dr["PlanTerm"].ToString()))
                                            {
                                                dr["PlanTerm"] = 0;
                                            }
                                            else
                                            {
                                                var PlanTerm = 0;
                                                if (!int.TryParse(dr["PlanTerm"].ToString(), out PlanTerm))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append("Invalid PlanTerm, enter valid numeric value");
                                                }
                                                else
                                                {
                                                    dr["PlanTerm"] = PlanTerm;
                                                }
                                            }
                                        }



                                        if (dr.Table.Columns.Contains("UPSellSI"))
                                        {
                                            if (string.IsNullOrEmpty(dr["UPSellSI"].ToString()))
                                            {
                                                dr["UPSellSI"] = 0;
                                            }
                                            else
                                            {
                                                var UPSellSI = 0;
                                                if (!int.TryParse(dr["UPSellSI"].ToString(), out UPSellSI))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append("Invalid UPSellSI, enter valid numeric value");
                                                }
                                                else
                                                {
                                                    dr["UPSellSI"] = UPSellSI;
                                                }
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("UPSellSIPremium"))
                                        {
                                            if (string.IsNullOrEmpty(dr["UPSellSIPremium"].ToString()))
                                            {
                                                dr["UPSellSIPremium"] = 0;
                                            }
                                            else
                                            {
                                                var UPSellSIPremium = 0;
                                                if (!int.TryParse(dr["UPSellSIPremium"].ToString(), out UPSellSIPremium))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append("Invalid UPSellSIPremium, enter valid numeric value");
                                                }
                                                else
                                                {
                                                    dr["UPSellSIPremium"] = UPSellSIPremium;
                                                }
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("OneYearPremium"))
                                        {
                                            if (string.IsNullOrEmpty(dr["OneYearPremium"].ToString()))
                                            {
                                                dr["OneYearPremium"] = 0;
                                            }
                                            else
                                            {
                                                var OneYearPremium = 0;
                                                if (!int.TryParse(dr["OneYearPremium"].ToString(), out OneYearPremium))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append("Invalid OneYearPremium, enter valid numeric value");
                                                }
                                                else
                                                {
                                                    dr["OneYearPremium"] = OneYearPremium;
                                                }
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("TwoYearPremium"))
                                        {
                                            if (string.IsNullOrEmpty(dr["TwoYearPremium"].ToString()))
                                            {
                                                dr["TwoYearPremium"] = 0;
                                            }
                                            else
                                            {
                                                var TwoYearPremium = 0;
                                                if (!int.TryParse(dr["TwoYearPremium"].ToString(), out TwoYearPremium))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append("Invalid TwoYearPremium, enter valid numeric value");
                                                }
                                                else
                                                {
                                                    dr["TwoYearPremium"] = TwoYearPremium;
                                                }
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("ThreeYearPremium"))
                                        {
                                            if (string.IsNullOrEmpty(dr["ThreeYearPremium"].ToString()))
                                            {
                                                dr["ThreeYearPremium"] = 0;
                                            }
                                            else
                                            {
                                                var ThreeYearPremium = 0;
                                                if (!int.TryParse(dr["ThreeYearPremium"].ToString(), out ThreeYearPremium))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append("Invalid ThreeYearPremium, enter valid numeric value");
                                                }
                                                else
                                                {
                                                    dr["ThreeYearPremium"] = ThreeYearPremium;
                                                }
                                            }
                                        }


                                        if (dr.Table.Columns.Contains("BatchID"))
                                        {
                                            if (string.IsNullOrEmpty(dr["BatchID"].ToString()) || dr["BatchID"].ToString() == "0")
                                            {
                                                sbError.AppendLine();
                                                sbError.Append("BatchID is Mendatory Field value should not be 0 or blank");
                                            }
                                        }


                                    }

                                    if (dr.Table.Columns.Contains("PolicyStartDate"))
                                    {
                                        if (!string.IsNullOrEmpty((Convert.ToString(dr["PolicyStartDate"])).TrimStart().TrimEnd()))
                                        {
                                            DateTime dtstartout;
                                            if (!DateTime.TryParse(Convert.ToString(dr["PolicyStartDate"]), out dtstartout))
                                            {
                                                sbError.AppendLine();
                                                sbError.Append(String.Format("Invalid PolicyStartDate for {0}. Acceptable format is dd-MM-yyyy..",
                                                    Convert.ToString(dr[constName]) + Convert.ToString(dr["PolicyStartDate"]).Trim()));
                                            }
                                            else
                                            {
                                                dr["PolicyStartDate"] = Convert.ToString(dr["PolicyStartDate"]).Trim();
                                            }
                                        }
                                    }

                                    if (dr.Table.Columns.Contains("NoofLives"))
                                    {
                                        if (string.IsNullOrEmpty(dr["NoofLives"].ToString()))
                                        {
                                            dr["NoofLives"] = 0;
                                        }
                                        else
                                        {
                                            var NoofLives = 0;
                                            if (!int.TryParse(dr["NoofLives"].ToString(), out NoofLives))
                                            {
                                                sbError.AppendLine();
                                                sbError.Append("Invalid NoofLives,Enter valid numeric value");
                                            }
                                            else
                                            {
                                                dr["NoofLives"] = NoofLives;
                                            }
                                        }
                                    }

                                    if (dr.Table.Columns.Contains("Pincode"))
                                    {
                                        if (string.IsNullOrEmpty(dr["Pincode"].ToString()))
                                        {
                                            dr["Pincode"] = 0;
                                        }
                                        else
                                        {
                                            var Pincode = 0;
                                            if (!int.TryParse(dr["Pincode"].ToString(), out Pincode))
                                            {
                                                sbError.AppendLine();
                                                sbError.Append("Invalid Pincode,Enter valid numeric value");
                                            }
                                            else
                                            {
                                                dr["Pincode"] = Pincode;
                                            }
                                        }
                                    }

                                    if (dr.Table.Columns.Contains("OldBookingId"))
                                    {
                                        if (string.IsNullOrEmpty(dr["OldBookingId"].ToString()))
                                        {
                                            dr["OldBookingId"] = 0;
                                        }
                                        else
                                        {
                                            var OldBookingId = 0;
                                            if (!int.TryParse(dr["OldBookingId"].ToString(), out OldBookingId))
                                            {
                                                sbError.AppendLine();
                                                sbError.Append("Invalid OldBookingId,Enter valid numeric value");
                                            }
                                            else
                                            {
                                                dr["OldBookingId"] = OldBookingId;
                                            }
                                        }
                                    }


                                    if (ProductId == "2" || ProductId == "106" || ProductId == "118" || ProductId == "130")
                                    {
                                        if (dr.Table.Columns.Contains("ComboProduct"))
                                        {
                                            if (string.IsNullOrEmpty(Convert.ToString(dr["ComboProduct"])))
                                            {
                                                dr["ComboProduct"] = string.Empty;
                                            }
                                            else
                                            {
                                                dr["ComboProduct"] = Convert.ToString(dr["ComboProduct"]).Trim();
                                            }
                                        }
                                        if (dr.Table.Columns.Contains("MonthlyMode"))
                                        {
                                            if (string.IsNullOrEmpty(Convert.ToString(dr["MonthlyMode"])))
                                            {
                                                dr["MonthlyMode"] = string.Empty;
                                            }
                                            else
                                            {
                                                dr["MonthlyMode"] = Convert.ToString(dr["MonthlyMode"]).Trim();
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("ACH"))
                                        {
                                            if (string.IsNullOrEmpty(Convert.ToString(dr["ACH"])))
                                            {
                                                dr["ACH"] = string.Empty;
                                            }
                                            else
                                            {
                                                dr["ACH"] = Convert.ToString(dr["ACH"]).Trim();
                                            }
                                        }


                                        if (dr.Table.Columns.Contains("PolicyInceptionDate") &&
                                            !string.IsNullOrEmpty(Convert.ToString(dr["PolicyInceptionDate"])))
                                        {
                                            if (!string.IsNullOrEmpty((Convert.ToString(dr["PolicyInceptionDate"])).TrimStart().TrimEnd()))
                                            {
                                                DateTime dtstartout;
                                                if (!DateTime.TryParse(Convert.ToString(dr["PolicyInceptionDate"]), out dtstartout))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append(String.Format("Invalid PolicyInceptionDate for {0}. Acceptable format is dd-MM-yyyy..",
                                                        Convert.ToString(dr[constName]) + Convert.ToString(dr["PolicyInceptionDate"]).Trim()));
                                                }
                                                else
                                                {
                                                    dr["PolicyInceptionDate"] = Convert.ToString(dr["PolicyInceptionDate"]).Trim();
                                                }
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("EnrollmentDate") &&
                                            !string.IsNullOrEmpty(Convert.ToString(dr["EnrollmentDate"])))
                                        {
                                            if (!string.IsNullOrEmpty((Convert.ToString(dr["EnrollmentDate"])).TrimStart().TrimEnd()))
                                            {
                                                DateTime dtstartout;
                                                if (!DateTime.TryParse(Convert.ToString(dr["EnrollmentDate"]), out dtstartout))
                                                {
                                                    sbError.AppendLine();
                                                    sbError.Append(String.Format("Invalid EnrollmentDate for {0}. Acceptable format is dd-MM-yyyy..",
                                                        Convert.ToString(dr[constName]) + Convert.ToString(dr["EnrollmentDate"]).Trim()));
                                                }
                                                else
                                                {
                                                    dr["EnrollmentDate"] = Convert.ToString(dr["EnrollmentDate"]).Trim();
                                                }
                                            }
                                        }

                                        if (dr.Table.Columns.Contains("Remarks"))
                                        {
                                            if (string.IsNullOrEmpty(Convert.ToString(dr["Remarks"])))
                                            {
                                                dr["Remarks"] = string.Empty;
                                            }
                                            else
                                            {
                                                dr["Remarks"] = Convert.ToString(dr["Remarks"]).Trim();
                                            }
                                        }
                                    }

                                    if (Convert.ToString(dr["SendMail"]) == "0")
                                        dr["refsendEmail"] = 0;
                                    else
                                        dr["refsendEmail"] = 1;
                                    if (Convert.ToString(dr["SendSMS"]) == "0")
                                        dr["refsendsms"] = 0;
                                    else
                                        dr["refsendsms"] = 1;

                                    // UTM_Medium
                                    if (dr.Table.Columns.Contains("WellnessPoint"))
                                    {
                                        string WellnessPoint = Convert.ToString(dr["WellnessPoint"]).Trim();
                                        if (!string.IsNullOrEmpty(WellnessPoint) && !isTextValid(WellnessPoint, false, true, false, false, 0, 50))
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Invalid WellnessPoint(Max 50 digits allowed)");
                                        }

                                        if (!string.IsNullOrEmpty(WellnessPoint))
                                        {
                                            dr["WellnessPoint"] = WellnessPoint;
                                        }

                                    }

                                    if (dr.Table.Columns.Contains("Loading"))
                                    {
                                        string Loading = Convert.ToString(dr["Loading"]).Trim();
                                        if (string.IsNullOrEmpty(Loading))
                                        {
                                            dr["Loading"] = string.Empty;
                                        }
                                        else if (Loading.ToUpper().Equals("YES"))
                                        {
                                            dr["Loading"] = "YES";
                                        }
                                        else if (Loading.ToUpper().Equals("NO"))
                                        {
                                            dr["Loading"] = "NO";
                                        }
                                        else
                                        {
                                            sbError.AppendLine();
                                            sbError.Append("Loading Taken can be either YES or NO");
                                        }
                                    }
                                    string OverrideAccess = "OverrideAccess".AppSettings().ToString();

                                    if (dr.Table.Columns.Contains("OverrideRecords") && !OverrideAccess.Contains(UploadedBy))
                                    //UploadedBy != "8835" && UploadedBy != "90596" && UploadedBy != "90549")
                                    {
                                        dr["OverrideRecords"] = 0;
                                    }

                                    if (dr.Table.Columns.Contains("IsABHIDiscount"))
                                    {
                                        dr["IsABHIDiscount"] = Convert.ToInt16(dr["IsABHIDiscount"]);
                                    }

                                    if (dr.Table.Columns.Contains("DirectLastYear"))
                                    {
                                        dr["DirectLastYear"] = Convert.ToInt16(dr["DirectLastYear"]);
                                    }

                                    if (dr.Table.Columns.Contains("DummyNP"))
                                    {
                                        decimal DummyNP = 0;
                                        if (dr["DummyNP"].ToString() != "")
                                        {
                                            if (decimal.TryParse(dr["DummyNP"].ToString(), out DummyNP))
                                            {
                                                dr["DummyNP"] = Convert.ToDecimal(dr["DummyNP"].ToString());
                                            }
                                        }
                                    }

                                    if (dr.Table.Columns.Contains("PremiumInflationReason"))
                                    {
                                        if (string.IsNullOrEmpty(Convert.ToString(dr["PremiumInflationReason"])))
                                        {
                                            dr["PremiumInflationReason"] = string.Empty;
                                        }
                                        else
                                        {
                                            dr["PremiumInflationReason"] = Convert.ToString(dr["PremiumInflationReason"]).Trim();
                                        }
                                    }
                                }


                                dr["ErrorMessage"] = sbError.ToString();

                            }
                        }
                    }
                    if (string.IsNullOrEmpty(sError))
                    {
                        if (dtSheet.Rows.Count > 0)
                        {
                            DataColumn newColumn = new DataColumn("UploadedBy", typeof(System.Int64));
                            newColumn.DefaultValue = UploadedBy;
                            dtSheet.Columns.Add(newColumn);
                            DataColumn TrackingCol = new DataColumn("TrackingId", typeof(System.String));
                            TrackingCol.DefaultValue = TrackingId;
                            dtSheet.Columns.Add(TrackingCol);
                            DataColumn SourceCol = new DataColumn("Source", typeof(System.String));
                            SourceCol.DefaultValue = "DumpUpload";
                            dtSheet.Columns.Add(SourceCol);

                            if (ProductId.Equals("117"))
                            {
                                var dataColumnProdId = new DataColumn("ProductID", typeof(int));
                                dataColumnProdId.DefaultValue = 117;
                                dtSheet.Columns.Add(dataColumnProdId);

                                var ToUpdateCol = new DataColumn("ToUpdate", typeof(System.Int16));
                                ToUpdateCol.DefaultValue = 2;
                                dtSheet.Columns.Add(ToUpdateCol);
                            }
                            else
                            {
                                DataColumn ToUpdateCol = new DataColumn("ToUpdate", typeof(System.Int16));
                                ToUpdateCol.DefaultValue = 1;
                                dtSheet.Columns.Add(ToUpdateCol);
                            }

                            int result = UploadExcelFileToDatabase(dtSheet, Convert.ToInt64(UploadedBy), ProcessId, TrackingId);
                            if (result >= 0)
                            {
                                sError = "LeadsUploadedSuccessfully";
                                int res = UpdateRenewalFileStatus(TrackingId, "DataStaged", UploadedBy);
                                if (res != 0)
                                {
                                    sError = "File Staged Successfully";
                                }
                            }
                            else
                            {
                                sError = "Some error occured please try again.";
                            }
                        }
                        else
                        {
                            sError = "Fail to stage the File";
                        }
                        ds.Dispose();
                    }
                    else
                    {
                        int resXq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", (UploadedBy).Trim());
                        if (ProductId != "131" || !ProcessId.Equals(SmeBulkUploadType))
                        {
                            InsertFileExceptionInMongo((TrackingId).Trim(), (UploadedBy).Trim(), sError.ToString());
                        }
                        sError = "File Not Staged";
                    }
                }
                else
                {
                    throw new Exception("Uploaded file have no records");
                }
                sError = sError.Trim();
                //}
                stream.Dispose();
                if (System.IO.File.Exists(TempPath))
                {
                    System.IO.File.Delete(TempPath);
                }
            }
            catch (Exception er)
            {
                int resq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", (UploadedBy).Trim());
                InsertFileExceptionInMongo((TrackingId).Trim(), (UploadedBy).Trim(), er.ToString());
                sError = "File Not Staged";
            }
            finally
            {
                if (sError.ToLower().Contains("success"))
                {
                    LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, "", "UploadCreateData", "UpdateRenewalLeadsBLL", UploadedBy.ToString(), FilePath.ToString(), sError, ReqDT, DateTime.Now);
                }
                else
                {
                    LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, sError, "UploadCreateData", "UpdateRenewalLeadsBLL", UploadedBy.ToString(), FilePath.ToString(), sError, ReqDT, DateTime.Now);
                }
            }

            return sError;
        }

        private static void ValidateMotorBulkUploadData(DataRow row, StringBuilder error)
        {
            //ReferralID
            if (IsValidId(Convert.ToString(row["ReferralID"])))
            {
                row["ReferralID"] = Convert.ToInt64(Convert.ToString(row["ReferralID"]).Trim());
            }
            else
            {
                error.Append("Invalid ReferralID/");
            }
            //PlanId
            if (IsValidId(Convert.ToString(row["PlanId"])))
            {
                row["PlanId"] = Convert.ToInt32(Convert.ToString(row["PlanId"]).Trim());
            }
            else
            {
                error.Append("Invalid PlanId/");
            }
            //InsurerId
            if (IsValidId(Convert.ToString(row["InsurerId"])))
            {
                row["InsurerId"] = Convert.ToInt32(Convert.ToString(row["InsurerId"]).Trim());
            }
            else
            {
                error.Append("Invalid InsurerId/");
            }
            //Premium
            if (IsValidDecimal(Convert.ToString(row["Premium"])))
            {
                row["Premium"] = Convert.ToDecimal(Convert.ToString(row["Premium"]).Trim());
            }
            else
            {
                error.Append("Invalid Premium/");
            }
            //SumInsured
            if (IsValidDecimal(Convert.ToString(row["SumInsured"])))
            {
                row["SumInsured"] = Convert.ToDecimal(Convert.ToString(row["SumInsured"]).Trim());
            }
            else
            {
                error.Append("Invalid SumInsured/");
            }
            //PolicyNo
            if (!string.IsNullOrEmpty(Convert.ToString(row["PolicyNo"]).Trim()))
            {
                row["PolicyNo"] = Convert.ToString(row["PolicyNo"]).Trim();
            }
            else
            {
                row["PolicyNo"] = DBNull.Value;
            }
            //IsTP
            if (!string.IsNullOrEmpty(Convert.ToString(row["IsTP"]).Trim()))
            {
                row["IsTP"] = Convert.ToInt16(Convert.ToString(row["IsTP"]).Trim());
            }
            else
            {
                row["IsTP"] = DBNull.Value;
            }
            //ODPremium
            if (IsValidDecimal(Convert.ToString(row["ODPremium"])))
            {
                row["ODPremium"] = Convert.ToDecimal(Convert.ToString(row["ODPremium"]).Trim());
            }
            else
            {
                row["ODPremium"] = DBNull.Value;
            }
            //TPPremium
            if (IsValidDecimal(Convert.ToString(row["TPPremium"])))
            {
                row["TPPremium"] = Convert.ToDecimal(Convert.ToString(row["TPPremium"]).Trim());
            }
            else
            {
                row["TPPremium"] = DBNull.Value;
            }
            //PolicyStartDate
            if (DateTime.TryParse(Convert.ToString(row["PolicyStartDate"]).Trim(), out DateTime dt))
            {
                row["PolicyStartDate"] = dt;
            }
            else
            {
                error.Append("Invalid PolicyStartDate/");
            }
            //PolicyEndDate
            if (DateTime.TryParse(Convert.ToString(row["PolicyEndDate"]).Trim(), out dt))
            {
                row["PolicyEndDate"] = dt;
            }
            else
            {
                error.Append("Invalid PolicyEndDate/");
            }
            //RegNo
            if (!string.IsNullOrEmpty(Convert.ToString(row["RegNo"]).Trim()))
            {
                row["RegNo"] = Convert.ToString(row["RegNo"]).Trim();
            }
            else
            {
                error.Append("Invalid RegNo/");
            }
            //RegDate
            if (DateTime.TryParse(Convert.ToString(row["RegDate"]).Trim(), out dt))
            {
                row["RegDate"] = dt;
            }
            else
            {
                row["RegDate"] = DBNull.Value;
            }
            //CC
            if (!string.IsNullOrEmpty(Convert.ToString(row["CC"]).Trim()))
            {
                row["CC"] = Convert.ToDecimal(Convert.ToString(row["CC"]).Trim());
            }
            else
            {
                row["CC"] = DBNull.Value;
            }
            //FuelType
            if (!string.IsNullOrEmpty(Convert.ToString(row["FuelType"]).Trim()))
            {
                row["FuelType"] = Convert.ToString(row["FuelType"]).Trim();
            }
            else
            {
                row["FuelType"] = DBNull.Value;
            }
            //ProposalNo
            if (!string.IsNullOrEmpty(Convert.ToString(row["ProposalNo"]).Trim()))
            {
                row["ProposalNo"] = Convert.ToString(row["ProposalNo"]).Trim();
            }
            else
            {
                row["ProposalNo"] = DBNull.Value;
            }
            //SAOD
            if (!string.IsNullOrEmpty(Convert.ToString(row["SAOD"]).Trim()))
            {
                row["SAOD"] = Convert.ToInt16(Convert.ToString(row["SAOD"]).Trim());
            }
            else
            {
                row["SAOD"] = DBNull.Value;
            }
            //NCB
            if (!string.IsNullOrEmpty(Convert.ToString(row["NCB"]).Trim()))
            {
                row["NCB"] = Convert.ToDecimal(Convert.ToString(row["NCB"]).Trim());
            }
            else
            {
                row["NCB"] = DBNull.Value;
            }
            //AgentId
            if (!string.IsNullOrEmpty(Convert.ToString(row["AgentId"]).Trim()))
            {
                row["AgentId"] = Convert.ToString(row["AgentId"]).Trim();
            }
            else
            {
                row["AgentId"] = DBNull.Value;
            }
        }

        private static void ValidateSmeBulkUploadData(DataRow row, StringBuilder error, string assignedGroupId = null, string assignedUserId = null)
        {
            var errorRow = new StringBuilder();
            string primaryRow = "ContactPersonName: " + Convert.ToString(row["ContactPersonName"]).Trim()
                                + ", Email: " + Convert.ToString(row["Email"]).Trim()
                                + ", Error: ";

            //ContactPersonName
            if (!string.IsNullOrEmpty((Convert.ToString(row["ContactPersonName"]).Trim())) &&
                isTextValid(Convert.ToString(row["ContactPersonName"]), true, false, false, false, 2, 100))
            {
                row["ContactPersonName"] = Convert.ToString(row["ContactPersonName"]).Trim();
            }
            else
            {
                errorRow.Append("Invalid ContactPersonName/");
            }

            //CompanyName
            if (!string.IsNullOrEmpty((Convert.ToString(row["CompanyName"]).Trim())))
            {
                row["CompanyName"] = Convert.ToString(row["CompanyName"]).Trim();
            }
            else
            {
                error.Append("Invalid CompanyName/");
            }

            //Email
            if (!string.IsNullOrEmpty(Convert.ToString(row["Email"]).Trim()) &&
                IsValidEmailId(Convert.ToString(row["Email"]).Trim()))
            {
                row["Email"] = Convert.ToString(row["Email"]).Trim();
            }
            else
            {
                errorRow.Append("Invalid Email/");
            }

            //MobileNo
            if (!string.IsNullOrEmpty(Convert.ToString(row["MobileNo"]).Trim()) &&
                isTextValid(Convert.ToString(row["MobileNo"]), false, true, false, false, 6, 12))
            {
                row["MobileNo"] = Convert.ToString(row["MobileNo"]).Trim();
            }
            else
            {
                errorRow.Append("Invalid MobileNo/");
            }

            //City
            if (!string.IsNullOrEmpty(Convert.ToString(row["City"]).Trim()))
            {
                row["City"] = Convert.ToString(row["City"]).Trim();
            }

            //SubProductId
            if (!string.IsNullOrEmpty((Convert.ToString(row["SubProductId"]).Trim())) && int.TryParse(Convert.ToString(row["SubProductId"]).Trim(), out int subProductId))
            {
                row["SubProductId"] = subProductId;
            }
            else
            {
                errorRow.Append("Invalid SubProductId/");
            }

            //LeadSource
            if (!string.IsNullOrEmpty((Convert.ToString(row["LeadSource"]).Trim())))
            {
                row["LeadSource"] = Convert.ToString(row["LeadSource"]).Trim();
            }
            else
            {
                errorRow.Append("Invalid LeadSource/");
            }

            //Utm_source
            if (!string.IsNullOrEmpty((Convert.ToString(row["Utm_source"]).Trim())))
            {
                row["Utm_source"] = Convert.ToString(row["Utm_source"]).Trim();
            }
            else
            {
                errorRow.Append("Invalid Utm_source/");
            }

            //EmployeeCode
            if (!string.IsNullOrEmpty((Convert.ToString(row["EmployeeCode"]).Trim())))
            {
                if (IsValidId(assignedUserId) || IsValidId(assignedGroupId))
                {
                    errorRow.Append("EmployeeCode in the excel OR Group/User fields, only one of them can be used/");
                }
                else
                {
                    long userId = LeadDetailsDLL.GetUserId(Convert.ToString(row["EmployeeCode"]).Trim());
                    if (userId > 0)
                    {
                        row["AssignToUserID"] = userId;
                    }
                    else
                    {
                        errorRow.Append("Invalid EmployeeCode/");
                    }
                }
            }
            else
            {
                if (IsValidId(assignedUserId))
                {
                    row["AssignToUserID"] = assignedUserId;
                }

                if (IsValidId(assignedGroupId))
                {
                    row["AssignToGroupID"] = assignedGroupId;
                }
            }

            if (!string.IsNullOrEmpty(errorRow.ToString().Trim()))
            {
                error.Append(primaryRow + errorRow.ToString());
            }
        }

        private static bool IsValidId(string Id)
        {
            return !string.IsNullOrEmpty(Id) && long.TryParse(Id, out long id) && id > 0;
        }

        private static bool IsValidDecimal(string Id)
        {
            return !string.IsNullOrEmpty(Id) && float.TryParse(Id, out float id) && id > 0;
        }

        public int UploadExcelFileToDatabase(DataTable dtSource, long userId, string processId, string TrackingId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            //var result = SqlHelper.ExecuteNonQuery(Connectionstring, CommandType.Text, "Update CRM.RenewalUploads set IsActive = 0 where Uploadedby=" + userId + " and ProductID=" + dtSource.Rows[0]["ProductID"]);
            int returnValue = 0;
            string sException = "";
            try
            {
                //DataTable dtSource = new DataTable();
                // = ds.Tables[0];
                var ProductId = Convert.ToString(dtSource.Rows[0]["ProductID"]);
                int ProdID = Convert.ToInt32(dtSource.Rows[0]["ProductID"]);
                using (SqlBulkCopy sqlBulk = new SqlBulkCopy(Connectionstring))
                {

                    sqlBulk.ColumnMappings.Add("ProductID", "ProductID");
                    sqlBulk.ColumnMappings.Add("UploadedBy", "UploadedBy");
                    sqlBulk.ColumnMappings.Add("TrackingId", "TrackingId");
                    sqlBulk.ColumnMappings.Add("ToUpdate", "ToUpdate");
                    sqlBulk.ColumnMappings.Add("Source", "Source");

                    if (ProdID != 117)
                        sqlBulk.ColumnMappings.Add("ErrorMessage", "ErrorMessage");

                    if (ProductId == "2" || ProductId == "130" || ProductId == "106" || ProductId == "118")
                    {
                        if (dtSource.Columns.Contains("Name"))
                            sqlBulk.ColumnMappings.Add("Name", "Name");
                        if (dtSource.Columns.Contains("Email"))
                            sqlBulk.ColumnMappings.Add("Email", "Email");
                        if (dtSource.Columns.Contains("MobileNo"))
                            sqlBulk.ColumnMappings.Add("MobileNo", "MobileNo");
                        if (dtSource.Columns.Contains("Insurer"))
                            sqlBulk.ColumnMappings.Add("Insurer", "Insurer");
                        if (dtSource.Columns.Contains("Sum Insured"))
                            sqlBulk.ColumnMappings.Add("Sum Insured", "SumInsured");
                        if (dtSource.Columns.Contains("PlanID"))
                            sqlBulk.ColumnMappings.Add("PlanID", "PlanID");
                        if (dtSource.Columns.Contains("SupplierID"))
                            sqlBulk.ColumnMappings.Add("SupplierID", "SupplierID");
                        if (dtSource.Columns.Contains("Plan name"))
                            sqlBulk.ColumnMappings.Add("Plan name", "PlanName");
                        if (dtSource.Columns.Contains("PolicyNumber"))
                            sqlBulk.ColumnMappings.Add("PolicyNumber", "OldPolicyNo");
                        if (dtSource.Columns.Contains("NewPremium"))
                            sqlBulk.ColumnMappings.Add("NewPremium", "FirstYrPremium");
                        if (dtSource.Columns.Contains("Is Claims Taken"))
                            sqlBulk.ColumnMappings.Add("Is Claims Taken", "IsPreviousClaimsTaken");
                        if (dtSource.Columns.Contains("Expiry Date"))
                            sqlBulk.ColumnMappings.Add("Expiry Date", "PolicyExpiryDate");
                        if (dtSource.Columns.Contains("Grace Period"))
                            sqlBulk.ColumnMappings.Add("Grace Period", "GracePeriod");
                        if (dtSource.Columns.Contains("Renewal Year"))
                            sqlBulk.ColumnMappings.Add("Renewal Year", "RenewalYear");
                        if (dtSource.Columns.Contains("refsendsms"))
                            sqlBulk.ColumnMappings.Add("refsendsms", "SendSMS");
                        if (dtSource.Columns.Contains("refsendEmail"))
                            sqlBulk.ColumnMappings.Add("refsendEmail", "SendMail");
                        // sqlBulk.ColumnMappings.Add("AltContatNumber", "AltContatNumber");
                        if (dtSource.Columns.Contains("DOB"))
                            sqlBulk.ColumnMappings.Add("DOB", "DOB");
                        if (dtSource.Columns.Contains("FamilyType"))
                            sqlBulk.ColumnMappings.Add("FamilyType", "FamilyType");
                        if (dtSource.Columns.Contains("BatchID"))
                            sqlBulk.ColumnMappings.Add("BatchID", "BatchID");
                        if (dtSource.Columns.Contains("PlanTerm"))
                            sqlBulk.ColumnMappings.Add("PlanTerm", "PlanTerm");
                        if (dtSource.Columns.Contains("CustomerId"))
                            sqlBulk.ColumnMappings.Add("CustomerId", "CustomerID");
                        if (dtSource.Columns.Contains("CountryId"))
                            sqlBulk.ColumnMappings.Add("CountryId", "CountryId");
                        if (dtSource.Columns.Contains("MasterPolicyNo"))
                            sqlBulk.ColumnMappings.Add("MasterPolicyNo", "MasterPolicyNo");
                        //if (ProductId == "2")
                        //{
                        if (dtSource.Columns.Contains("AddOnUPSellPremium"))
                            sqlBulk.ColumnMappings.Add("AddOnUPSellPremium", "AddOnUPSellPremium");
                        if (dtSource.Columns.Contains("OneYearQuote"))
                            sqlBulk.ColumnMappings.Add("OneYearQuote", "OneYearQuote");
                        if (dtSource.Columns.Contains("OneYearPremium"))
                            sqlBulk.ColumnMappings.Add("OneYearPremium", "OneYearPremium");
                        if (dtSource.Columns.Contains("TwoYearQuote"))
                            sqlBulk.ColumnMappings.Add("TwoYearQuote", "TwoYearQuote");
                        if (dtSource.Columns.Contains("TwoYearPremium"))
                            sqlBulk.ColumnMappings.Add("TwoYearPremium", "TwoYearPremium");
                        if (dtSource.Columns.Contains("ThreeYearQuote"))
                            sqlBulk.ColumnMappings.Add("ThreeYearQuote", "ThreeYearQuote");
                        if (dtSource.Columns.Contains("ThreeYearPremium"))
                            sqlBulk.ColumnMappings.Add("ThreeYearPremium", "ThreeYearPremium");
                        if (dtSource.Columns.Contains("AddOnProduct"))
                            sqlBulk.ColumnMappings.Add("AddOnProduct", "AddOnProduct");
                        if (dtSource.Columns.Contains("UPSellSI"))
                            sqlBulk.ColumnMappings.Add("UPSellSI", "UPSellSI");
                        if (dtSource.Columns.Contains("UPSellSIPremium"))
                            sqlBulk.ColumnMappings.Add("UPSellSIPremium", "UpSellSIPremium");
                        if (dtSource.Columns.Contains("ProductCode"))
                            sqlBulk.ColumnMappings.Add("ProductCode", "ProductCode");
                        if (dtSource.Columns.Contains("FreeHealthCheckup"))
                            sqlBulk.ColumnMappings.Add("FreeHealthCheckup", "FreeHealthCheckup");
                        if (dtSource.Columns.Contains("NCB"))
                            sqlBulk.ColumnMappings.Add("NCB", "NCB");
                        if (dtSource.Columns.Contains("SpecificDiseaseWaitingPeriod"))
                            sqlBulk.ColumnMappings.Add("SpecificDiseaseWaitingPeriod", "SpecificDiseaseWaitingPeriod");
                        if (dtSource.Columns.Contains("PreExistingDiseaseWaitingPeriod"))
                            sqlBulk.ColumnMappings.Add("PreExistingDiseaseWaitingPeriod", "PreExistingDiseaseWaitingPeriod");
                        if (dtSource.Columns.Contains("AdditionalBenefits"))
                            sqlBulk.ColumnMappings.Add("AdditionalBenefits", "AdditionalBenefits");
                        //if (ProductId != "2")
                        //{
                        if (dtSource.Columns.Contains("NoofLives"))
                            sqlBulk.ColumnMappings.Add("NoofLives", "NoofLives");
                        if (dtSource.Columns.Contains("CoverType"))
                            sqlBulk.ColumnMappings.Add("CoverType", "CoverType");
                        if (dtSource.Columns.Contains("Pincode"))
                            sqlBulk.ColumnMappings.Add("Pincode", "Pincode");
                        if (dtSource.Columns.Contains("PolicyStartDate"))
                            sqlBulk.ColumnMappings.Add("PolicyStartDate", "PolicyStartDate");
                        if (dtSource.Columns.Contains("OldBookingId"))
                            sqlBulk.ColumnMappings.Add("OldBookingId", "OldBookingId");

                        //}

                        if (dtSource.Columns.Contains("WellnessPoint"))
                            sqlBulk.ColumnMappings.Add("WellnessPoint", "WellnessPoint");

                        if (dtSource.Columns.Contains("ComboProduct"))
                            sqlBulk.ColumnMappings.Add("ComboProduct", "ComboProduct");

                        if (dtSource.Columns.Contains("Loading"))
                            sqlBulk.ColumnMappings.Add("Loading", "Loading");


                        if (ProductId == "2" || ProductId == "106" || ProductId == "118" || ProductId == "130")
                        {
                            if (dtSource.Columns.Contains("MonthlyMode"))
                                sqlBulk.ColumnMappings.Add("MonthlyMode", "MonthlyMode");
                            if (dtSource.Columns.Contains("ACH"))
                                sqlBulk.ColumnMappings.Add("ACH", "ACH");
                            if (dtSource.Columns.Contains("PolicyInceptionDate"))
                                sqlBulk.ColumnMappings.Add("PolicyInceptionDate", "PolicyInceptionDate");
                            if (dtSource.Columns.Contains("Remarks"))
                                sqlBulk.ColumnMappings.Add("Remarks", "Remarks");
                            if (dtSource.Columns.Contains("PremiumInflationReason"))
                                sqlBulk.ColumnMappings.Add("PremiumInflationReason", "PremiumInflationReason");
                        }
                        if (dtSource.Columns.Contains("UpsellOffer"))
                            sqlBulk.ColumnMappings.Add("UpsellOffer", "UpsellOffer");
                        if (dtSource.Columns.Contains("OfferDetails"))
                            sqlBulk.ColumnMappings.Add("OfferDetails", "OfferDetails");
                        if (dtSource.Columns.Contains("EnrollmentDate"))
                            sqlBulk.ColumnMappings.Add("EnrollmentDate", "EnrollmentDate");
                        if (dtSource.Columns.Contains("OverrideRecords"))
                            sqlBulk.ColumnMappings.Add("OverrideRecords", "OverrideRecords");
                        if (dtSource.Columns.Contains("IsABHIDiscount"))
                            sqlBulk.ColumnMappings.Add("IsABHIDiscount", "IsABHIDiscount");
                        if (dtSource.Columns.Contains("DirectLastYear"))
                            sqlBulk.ColumnMappings.Add("DirectLastYear", "DirectLastYear");
                        if (dtSource.Columns.Contains("DummyNP"))
                            sqlBulk.ColumnMappings.Add("DummyNP", "DummyNP");
                        string[] columns = { "AltContatNumber", "Address" };
                        foreach (var column in columns)
                        {
                            sqlBulk.ColumnMappings.Add(column, column);
                        }
                    }
                    else if (ProductId == "117")
                    {
                        sqlBulk.ColumnMappings.Add("PlanId", "PlanID");
                        sqlBulk.ColumnMappings.Add("InsurerId", "SupplierID");
                        sqlBulk.ColumnMappings.Add("Premium", "NoticePremium");
                        sqlBulk.ColumnMappings.Add("SumInsured", "SumInsured");
                        sqlBulk.ColumnMappings.Add("PolicyNo", "MasterPolicyNo");
                        sqlBulk.ColumnMappings.Add("IsTP", "IsTP");
                        sqlBulk.ColumnMappings.Add("ODPremium", "ODPremium");
                        sqlBulk.ColumnMappings.Add("TPPremium", "TPPremium");
                        sqlBulk.ColumnMappings.Add("PolicyStartDate", "PolicyStartDate");
                        sqlBulk.ColumnMappings.Add("PolicyEndDate", "PolicyExpiryDate");
                        sqlBulk.ColumnMappings.Add("RegNo", "RegistrationNo");
                        sqlBulk.ColumnMappings.Add("RegDate", "RegDate");
                        sqlBulk.ColumnMappings.Add("CC", "CC");
                        sqlBulk.ColumnMappings.Add("FuelType", "FuelType");
                        sqlBulk.ColumnMappings.Add("ProposalNo", "ProposalNo");
                        sqlBulk.ColumnMappings.Add("SAOD", "SAOD");
                        sqlBulk.ColumnMappings.Add("NCB", "NCB");
                        sqlBulk.ColumnMappings.Add("AgentId", "EmployeeID");
                        sqlBulk.ColumnMappings.Add("ReferralID", "ReferralID");
                    }
                    else if (ProductId == "101")
                    {
                        sqlBulk.ColumnMappings.Add("BookedLeadId", "UploadedLeadId");
                        sqlBulk.ColumnMappings.Add("LeadSource", "LeadSource");
                        sqlBulk.ColumnMappings.Add("AssignToGroupID", "AssignToGroupID");
                        sqlBulk.ColumnMappings.Add("AssignToUserID", "AssignToUserID");
                        sqlBulk.ColumnMappings.Add("PolicyStartDate", "PolicyStartDate");
                        sqlBulk.ColumnMappings.Add("PolicyEndDate", "PolicyExpiryDate");
                    }
                    sqlBulk.DestinationTableName = "CRM.RenewalUploads";
                    sqlBulk.WriteToServer(dtSource);
                }
            }
            catch (Exception ex)
            {
                int resq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", userId.ToString());
                sException = ex.ToString();
                InsertFileExceptionInMongo((TrackingId).Trim(), (userId.ToString()).Trim(), sException);
                returnValue = -1;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, sException, "UploadExcelFileToDatabase", "UpdateRenewalLeadsBLL", userId.ToString(), TrackingId.ToString(), returnValue.ToString(), DateTime.Now, DateTime.Now);
            }
            return returnValue;
        }
        public bool checkExistingPlan(int SupplierId, int planid, int ProductId)
        {
            bool temp = PlansList.Any(item => item.OldPlanId == Convert.ToUInt32(planid) && item.OldSupplierId == SupplierId && item.ProductId == ProductId);
            if (temp != null)
                return true;
            return false;
        }
        public void getSupplierandPlans()
        {
            String sAgentDetailsAPI = "OldSupplierPlan".AppSettings().ToString();

            String responseString = "";
            HttpWebRequest PlansDataRequest = (HttpWebRequest)WebRequest.Create(sAgentDetailsAPI);
            HttpWebResponse PlansDataResponse = null;
            try
            {
                PlansDataRequest.Method = "GET";
                PlansDataRequest.KeepAlive = false;
                PlansDataRequest.Headers.Add("Authorization", "HeaderAuthorizationKey".AppSettings().ToString());
                PlansDataRequest.ContentType = "application/json";
                PlansDataResponse = (HttpWebResponse)PlansDataRequest.GetResponse();
                using (Stream stream = PlansDataResponse.GetResponseStream())
                {
                    StreamReader reader = new StreamReader(stream, Encoding.UTF8);
                    responseString = reader.ReadToEnd();
                    if (reader != null) reader.Close();
                }
                responseString = responseString.Replace("{\"PlansResult\":", "");
                responseString = responseString.Replace("}}]}", "}}]");
                if (responseString != "[]}")
                {
                    int pFrom = responseString.IndexOf("[");
                    int pTo = responseString.LastIndexOf("]") + 1;

                    String result = responseString.Substring(pFrom, pTo - pFrom);

                    PlansList = (List<Plans>)Newtonsoft.Json.JsonConvert.DeserializeObject(result, typeof(List<Plans>));
                }
                PlansDataResponse.Close();
                return;
            }
            catch (Exception ex)
            {
                return;
            }
        }
        public DataTable BulkUpdateRenewalleads(DataTable dt, Int64 UserId, string TrackingId)
        {
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@Leads", dt);
                SqlParam[1] = new SqlParameter("@userId", UserId);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[DumpBulkUpdateRenewalleads]", 20, SqlParam);
                if (ds != null)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            catch (Exception ex)
            {
                int resq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", UserId.ToString());
                InsertFileExceptionInMongo((TrackingId).Trim(), (UserId.ToString()).Trim(), ex.ToString());
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, ex.ToString(), "BulkUpdateRenewalleads", "UpdateRenewalLeadsBLL", UserId.ToString(), TrackingId.ToString(), "1", DateTime.Now, DateTime.Now);
                return new DataTable();
            }

        }
        public int UpdateRenewalFileStatus(string UniqueId, string Status, string Uploadedby)
        {
            int result = 0;
            string Exception = string.Empty;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@UniqueId", UniqueId);
                sqlParam[1] = new SqlParameter("@Status", Status);
                sqlParam[2] = new SqlParameter("@Uploadedby", Uploadedby);
                DataSet oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateStatusRenewalBulkUploadFile]", 3000, sqlParam);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    result = Convert.ToInt32(oDataSet.Tables[0].Rows[0]["Result"]);
                }
            }
            catch (Exception ex)
            {
                Exception = ex.ToString();
                result = 0;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UniqueId, 0, Exception, "UpdateRenewalFileStatus", "UpdateRenewalLeadsBLL", Uploadedby.ToString(), Status.ToString(), result.ToString(), DateTime.Now, DateTime.Now);
            }
            return result;
        }
        private DataTable CreateLeadUpdateTable()
        {
            var details = new DataTable();

            DataColumn column = new DataColumn();
            column.DataType = System.Type.GetType("System.Int32");
            column.ColumnName = "ID";
            column.AutoIncrement = true;
            column.AutoIncrementSeed = 1;
            column.AutoIncrementStep = 1;
            details.Columns.Add(column);

            details.Columns.Add("leadId", typeof(long));
            details.Columns.Add("Name", typeof(string));
            details.Columns.Add("Address", typeof(string));
            details.Columns.Add("InsurerId", typeof(int));
            details.Columns.Add("PlanId", typeof(int));
            details.Columns.Add("InsurerName", typeof(string));
            details.Columns.Add("PlanName", typeof(string));
            details.Columns.Add("SumInsured", typeof(decimal));
            details.Columns.Add("FirstYearPremium", typeof(decimal));
            details.Columns.Add("TwoYrPremium", typeof(decimal));
            details.Columns.Add("IsPrevClaimsTaken", typeof(bool));
            details.Columns.Add("BONUS", typeof(decimal));
            details.Columns.Add("PED", typeof(DateTime));
            details.Columns.Add("PolicyStartDate", typeof(DateTime));
            details.Columns.Add("GracePeriod", typeof(Int16));
            details.Columns.Add("RenewalYear", typeof(Int16));
            details.Columns.Add("OldPolicyNo", typeof(string));
            details.Columns.Add("UpsellOffer", typeof(string));
            details.Columns.Add("Offerdetails", typeof(string));
            details.Columns.Add("UpsellPremium", typeof(decimal));
            details.Columns.Add("DOBofInsured01", typeof(DateTime));
            details.Columns.Add("DOBofInsured02", typeof(DateTime));
            details.Columns.Add("DOBofInsured03", typeof(DateTime));
            details.Columns.Add("DOBofInsured04", typeof(DateTime));
            details.Columns.Add("CreatedBy", typeof(long));
            details.Columns.Add("CustomerId", typeof(long));
            details.Columns.Add("PlanTerm", typeof(Int16));
            details.Columns.Add("UPSellSI", typeof(int));
            details.Columns.Add("UPSellSIPremium", typeof(int));
            details.Columns.Add("AddOnProduct", typeof(string));
            details.Columns.Add("AddOnBasePremium", typeof(int));
            details.Columns.Add("AddOnUPSellPremium", typeof(int));
            details.Columns.Add("FamilyType", typeof(string));
            details.Columns.Add("OneYearQuote", typeof(string));
            details.Columns.Add("OneYearPremium", typeof(int));
            details.Columns.Add("TwoYearQuote", typeof(string));
            details.Columns.Add("TwoYearPremium", typeof(int));
            details.Columns.Add("ThreeYearQuote", typeof(string));
            details.Columns.Add("ThreeYearPremium", typeof(int));
            details.Columns.Add("DOB", typeof(DateTime));
            details.Columns.Add("UpsellOneYearQuote", typeof(string));
            details.Columns.Add("UpsellOneYearPremium", typeof(int));
            details.Columns.Add("UpsellTwoYearQuote", typeof(string));
            details.Columns.Add("UpsellTwoYearPremium", typeof(int));
            details.Columns.Add("UpsellThreeYearQuote", typeof(string));
            details.Columns.Add("UpsellThreeYearPremium", typeof(int));
            details.Columns.Add("ProductCode", typeof(string));
            details.Columns.Add("MasterPolicyNo", typeof(string));
            //details.Columns.Add("AltContatNumber", typeof(long));
            details.Columns.Add("FreeHealthCheckup", typeof(string));
            details.Columns.Add("NCB", typeof(decimal));
            details.Columns.Add("SpecificDiseaseWaitingPeriod", typeof(Int16));
            details.Columns.Add("PreExistingDiseaseWaitingPeriod", typeof(Int16));
            details.Columns.Add("AdditionalBenefits", typeof(string));
            details.Columns.Add("PrimaryNo", typeof(long));
            details.Columns.Add("AltNo", typeof(long));
            details.Columns.Add("EmailID", typeof(string));
            details.Columns.Add("AltEmailID", typeof(string));
            details.Columns.Add("UpdateAllSet", typeof(bool));
            details.Columns.Add("CountryID", typeof(int));
            details.Columns.Add("WellnessPoint", typeof(string));
            details.Columns.Add("OldBookingId", typeof(long));

            //Added Azhar
            details.Columns.Add("MonthlyMode", typeof(string));
            details.Columns.Add("ACH", typeof(string));
            details.Columns.Add("PolicyInceptionDate", typeof(DateTime));
            //Added End
            details.Columns.Add("BatchID", typeof(string));
            details.Columns.Add("Loading", typeof(string));
            details.Columns.Add("ComboProduct", typeof(string));
            details.Columns.Add("Remarks", typeof(string));
            details.Columns.Add("StatusMessage", typeof(string));
            details.Columns.Add("TrackingId", typeof(string));
            details.Columns.Add("ErrorMessage", typeof(string));
            details.Columns.Add("ProductID", typeof(int));
            details.Columns.Add("ToUpdate", typeof(int));
            details.Columns.Add("WasPort", typeof(bool));
            details.Columns.Add("OverrideRecords", typeof(byte));
            details.Columns.Add("PremiumInflationReason", typeof(string));
            details.Columns.Add("EnrollmentDate", typeof(DateTime));
            details.Columns.Add("UpsellFreshLead", typeof(long));
            details.Columns.Add("IsRegional", typeof(bool));
            details.Columns.Add("Isdeductible", typeof(bool));
            details.Columns.Add("WasRegional", typeof(bool));
            details.Columns.Add("highpriority", typeof(bool));
            details.Columns.Add("GSTRefundEligible", typeof(bool));
            details.Columns.Add("CardlessEmi", typeof(bool));
            details.Columns.Add("RolloverTagging", typeof(bool));
            details.Columns.Add("IsABHIDiscount", typeof(bool));
            details.Columns.Add("PEDInfo", typeof(string));
            details.Columns.Add("DummyNP", typeof(decimal));
            details.Columns.Add("DirectLastYear", typeof(bool));
            details.Columns.Add("IsPlanMigration", typeof(bool));
            details.Columns.Add("RiderPlan", typeof(string));
            return details;
        }
        public static Boolean IsValidEmailId(string EmailIdToCheck)
        {
            Regex EmailRegExp = new Regex(@"^([0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*@([0-9a-zA-Z][-\w]*[0-9a-zA-Z]\.)+[a-zA-Z]{2,9})$");
            return EmailRegExp.IsMatch(EmailIdToCheck);
        }
        public static bool isTextValid(string sText, bool IsallowAlpa, bool IsallowNumeric, bool IsAlphaNumeric, bool IsDecimal, int minNoOfChars, int maxNoOfChars)
        {
            bool bReturn = true;
            string strToCheck = sText.ToString().Trim();
            if (strToCheck.ToString().Trim() == "")
                return false;
            if (strToCheck.Length < minNoOfChars || strToCheck.Length > maxNoOfChars)
                return false;
            if (IsallowAlpa == true)
            {
                Regex objAlphaPattern = new Regex("[^a-zA-Z\\ '.]");
                bReturn = !objAlphaPattern.IsMatch(strToCheck);
            }
            if (IsallowNumeric == true)
            {
                Regex objNotPositivePattern = new Regex("[^0-9.]");
                Regex objPositivePattern = new Regex("^[.][0-9]+$|[0-9]*[.]*[0-9]+$");
                Regex objTwoDotPattern = new Regex("[0-9]*[.][0-9]*[.][0-9]*");
                bReturn = !objNotPositivePattern.IsMatch(strToCheck) && objPositivePattern.IsMatch(strToCheck) && !objTwoDotPattern.IsMatch(strToCheck);
            }
            if (IsAlphaNumeric == true)
            {
                Regex objAlphaNumericPattern = new Regex("[^a-zA-Z0-9\\ .,-/'?:*()<>{}+@$%^&#!`~]");
                bReturn = !objAlphaNumericPattern.IsMatch(strToCheck);
            }
            if (IsAlphaNumeric == true)
            {
                Regex objHTMLPatern = new Regex("<[^>]*>");
                bReturn = !objHTMLPatern.IsMatch(strToCheck);
            }
            if (IsDecimal == true)
            {
                Regex objDecimalPattern = new Regex("[0-9]+\\.[0-9]{2}$");
                bReturn = !objDecimalPattern.IsMatch(strToCheck);
            }

            return (bReturn);
        }
        public static bool isValidDate(string date, string format = "dd-MM-yyyy")
        {
            try
            {
                DateTime dt = DateTime.ParseExact(date, format, System.Globalization.CultureInfo.InvariantCulture);
                return true;
            }
            catch (Exception er)
            {
                return false;
            }
        }
        public string ProcessSMELastyearLeads(DataTable myDataTable, string ProductId, string AssignedGroupId, string AssignedUserId, string ProcessId, string TrackingId, string UploadedBy, string FilePath)
        {
            System.Text.StringBuilder sbError = new System.Text.StringBuilder();
            String resultMessage = string.Empty;
            try
            {
                for (int i = myDataTable.Rows.Count - 1; i >= 0; i--)
                {
                    DataRow dr = myDataTable.Rows[i];
                    dr[constProductID] = ProductId;
                    if (!string.IsNullOrEmpty((Convert.ToString(dr[constName]).Trim())))
                    {
                        if (!isTextValid(Convert.ToString(dr[constName]), true, false, false, false, 2, 40))
                        {
                            sbError.Append("<br />Sorry Name of " + Convert.ToString(dr[constName]) + " contains special characters <br>");
                            myDataTable.Rows.Remove(dr);
                            continue;
                        }
                        else
                        {
                            dr[constName] = Convert.ToString(dr[constName]).TrimStart().TrimEnd();
                        }
                    }
                    else
                    {
                        sbError.Append("<br />Sorry Name" + constEmptyMessage);
                        myDataTable.Rows.Remove(dr);
                        continue;
                    }
                    if (ProductId == "131")
                    {

                        if ((string.IsNullOrEmpty(dr["UploadedLeadID"].ToString()) || dr["UploadedLeadID"].ToString() == "0"))
                        {
                            sbError.Append("<br />Provide UploadedLeadID for : " + Convert.ToString(dr[constName]));
                        }

                        //CompanyName
                        if (!string.IsNullOrEmpty((Convert.ToString(dr["CompanyName"]).Trim())))
                        {

                            if (!isTextValid(Convert.ToString(dr["CompanyName"]), true, false, false, false, 2, 100))
                            {
                                sbError.Append("Check for company Name.Special characters not allowed and Max length allowed 100");
                                dr["CompanyName"] = string.Empty;

                                myDataTable.Rows.Remove(dr);
                            }
                            else
                            {
                                dr["CompanyName"] = Convert.ToString(dr["CompanyName"]).TrimStart().TrimEnd();
                            }
                        }
                        else
                        {
                            dr["CompanyName"] = string.Empty;
                        }

                        //NumberOfEmployees
                        var empCount = Convert.ToString(dr["NumberOfEmployees"]).Trim();
                        if (empCount == "")
                        {
                            dr["NumberOfEmployees"] = "0";
                        }
                        else if (!isTextValid(empCount, false, true, false, false, 1, 10))
                        {
                            sbError.Append(String.Format("Invalid Number Of Employees {0} for {1} accepts numeric values.",
                                Convert.ToString(dr["NumberOfEmployees"]), Convert.ToString(dr[constName])));
                            myDataTable.Rows.Remove(dr);
                            continue;
                        }


                        //Subproduct
                        if (!string.IsNullOrEmpty((Convert.ToString(dr[constSubProd]).Trim())))
                        {

                            if (!isTextValid(Convert.ToString(dr[constSubProd]), true, false, true, false, 2, 100))
                            {
                                dr[constSubProd] = string.Empty;
                            }
                            else
                            {
                                dr[constSubProd] = Convert.ToString(dr[constSubProd]).TrimStart().TrimEnd();
                            }
                        }
                        else
                        {
                            dr[constSubProd] = string.Empty;
                        }

                        //UploadedLeadId	Name	CompanyName	QuoteSharedDate	SubProduct	 SumInsured	NoofLives	NumberOfEmployees	
                        //Occupancy	LastYearQuotedPremium	LeadSource	UTM_Medium

                        string QuoteSharedDate = Convert.ToString(dr["QuoteSharedDate"]).Trim();
                        QuoteSharedDate = QuoteSharedDate.Split()[0];
                        if (!string.IsNullOrEmpty(QuoteSharedDate))
                        {
                            DateTime dt;
                            if (DateTime.TryParse(Convert.ToString(dr["QuoteSharedDate"]).Trim(), out dt))
                            {
                                if (dt.Year < 2010)
                                {
                                    sbError.Append(String.Format("Invalid QuoteSharedDate less than 2010.for {0}. Acceptable format is dd-MM-yyyy.",
                                       Convert.ToString(dr["PolicyExpiryDate"])));
                                    myDataTable.Rows.Remove(dr);
                                    continue;
                                }
                                else
                                {
                                    dr["QuoteSharedDate"] = dt;
                                }

                            }
                            else if (!isValidDate(QuoteSharedDate))
                            {
                                sbError.Append(String.Format("Invalid QuoteSharedDate for {0}. Acceptable format is dd-MM-yyyy.",
                                        Convert.ToString(dr["QuoteSharedDate"])));
                                myDataTable.Rows.Remove(dr);
                                continue;
                            }
                            else
                            {
                                dr["QuoteSharedDate"] = DateTime.ParseExact(QuoteSharedDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                            }
                        }

                        // LeadSource
                        if (!string.IsNullOrEmpty((Convert.ToString(dr["LeadSource"]).Trim())))
                        {

                            if (!isTextValid(Convert.ToString(dr["LeadSource"]), true, false, true, false, 0, 100))
                            {
                                dr["LeadSource"] = string.Empty;
                            }
                            else
                            {
                                dr["LeadSource"] = Convert.ToString(dr["LeadSource"]).TrimStart().TrimEnd();
                            }
                        }
                        else
                        {
                            dr["LeadSource"] = string.Empty;
                        }
                        // UTM_Medium
                        if (!string.IsNullOrEmpty((Convert.ToString(dr["UTM_Medium"]).Trim())))
                        {

                            if (!isTextValid(Convert.ToString(dr["UTM_Medium"]), true, false, true, false, 0, 100))
                            {
                                dr["UTM_Medium"] = string.Empty;
                            }
                            else
                            {
                                dr["UTM_Medium"] = Convert.ToString(dr["UTM_Medium"]).TrimStart().TrimEnd();
                            }
                        }
                        else
                        {
                            dr["UTM_Medium"] = string.Empty;
                        }

                        //UploadedLeadID

                        var Leadid = Convert.ToString(dr["UploadedLeadId"]).Trim();
                        if (Leadid == "")
                        {
                            dr["UploadedLeadId"] = "0";
                        }
                        else
                        {
                            dr["UploadedLeadId"] = Convert.ToString(dr["UploadedLeadId"]).TrimStart().TrimEnd();
                        }


                        if (!string.IsNullOrEmpty(AssignedGroupId))
                        {
                            dr["AssignToGroupID"] = Convert.ToInt32(AssignedGroupId);
                        }

                        if (!string.IsNullOrEmpty(AssignedUserId))
                        {
                            dr["AssignToUserID"] = Convert.ToInt32(AssignedUserId);

                        }



                        empCount = Convert.ToString(dr["NoofLives"]).Trim();
                        if (empCount == "")
                        {
                            dr["NoofLives"] = "0";
                        }
                        else if (!isTextValid(empCount, false, true, false, false, 1, 10))
                        {
                            sbError.Append(String.Format("Invalid  NoofLives {0} for {1} accepts numeric values.",
                                Convert.ToString(dr["NoofLives"]), Convert.ToString(dr[constName])));
                            myDataTable.Rows.Remove(dr);
                            continue;
                        }


                        if (dr.Table.Columns.Contains("SumInsured"))
                        {
                            decimal decimalOutput = 0;
                            if (!string.IsNullOrEmpty(Convert.ToString(dr["SumInsured"]).Trim()))
                            {
                                if (!Decimal.TryParse(Convert.ToString(dr["SumInsured"]).Trim(), out decimalOutput))
                                {
                                    sbError.Append("Invalid SumInsured");
                                    myDataTable.Rows.Remove(dr);
                                    continue;
                                }
                                else
                                {
                                    dr["SumInsured"] = Convert.ToDecimal(dr["SumInsured"]);
                                }
                            }
                        }



                        dr["Insurer"] = Convert.ToString(dr["Insurer"]).Trim();
                        dr["Occupancy"] = Convert.ToString(dr["Occupancy"]);

                        if (dr.Table.Columns.Contains("LastYearQuotedPremium"))
                        {
                            if (string.IsNullOrEmpty(dr["LastYearQuotedPremium"].ToString()))
                            {
                                dr["LastYearQuotedPremium"] = 0;
                            }
                            else
                            {
                                var UpsellTwoYearPremium = 0;
                                if (!int.TryParse(dr["LastYearQuotedPremium"].ToString(), out UpsellTwoYearPremium))
                                {
                                    sbError.AppendLine();
                                    sbError.Append("<br />Invalid LastYearQuotedPremium, enter valid numeric value");
                                    continue;
                                }
                                else
                                {
                                    dr["LastYearQuotedPremium"] = UpsellTwoYearPremium;
                                }
                            }
                        }


                    }
                    dr["ErrorMessage"] = sbError.ToString();
                }

                if (myDataTable.Rows.Count > 0)
                {
                    DataColumn newColumn = new DataColumn("UploadedBy", typeof(System.Int32));
                    newColumn.DefaultValue = userId;
                    myDataTable.Columns.Add(newColumn);
                    DataColumn TrackingCol = new DataColumn("TrackingId", typeof(System.String));
                    TrackingCol.DefaultValue = TrackingId;
                    myDataTable.Columns.Add(TrackingCol);
                    DataColumn ToUpdateCol = new DataColumn("ToUpdate", typeof(System.Int16));
                    ToUpdateCol.DefaultValue = 1;
                    myDataTable.Columns.Add(ToUpdateCol);
                    DataColumn SourceCol = new DataColumn("Source", typeof(System.String));
                    SourceCol.DefaultValue = "DumpUpload";
                    myDataTable.Columns.Add(SourceCol);


                    int result = UploadExcelFileToDatabase(myDataTable, userId, ProcessId, TrackingId);
                    if (result >= 0)
                    {
                        resultMessage = "LeadsUploadedSuccessfully";
                        int res = UpdateRenewalFileStatus(TrackingId, "DataStaged", userId.ToString());
                        if (res != 0)
                        {
                            resultMessage = "File Staged Successfully";
                        }
                    }
                    else
                    {
                        resultMessage = "Some error occured please try again.";
                    }
                }
                else
                {
                    resultMessage = "Fail to stage the File";
                }
            }
            catch (Exception er)
            {
                int resq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", UploadedBy.ToString());
                InsertFileExceptionInMongo((TrackingId).Trim(), (UploadedBy.ToString()).Trim(), er.ToString());
                resultMessage = "File Not Staged";
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, resultMessage, "ProcessSMELastyearLeads", "UpdateRenewalLeadsBLL", UploadedBy.ToString(), FilePath.ToString(), resultMessage, DateTime.Now, DateTime.Now);
            }
            return resultMessage;
        }
        public string UploadHomeRenewal(DataTable myDataTable, string ProductId, string GroupId, string AssignedUserId, string ProcessId, string TrackingId, string UploadedBy, string FilePath)
        {
            System.Text.StringBuilder sbError = new System.Text.StringBuilder();
            String resultMessage = string.Empty;
            try
            {
                for (int i = myDataTable.Rows.Count - 1; i >= 0; i--)
                {
                    DataRow dr = myDataTable.Rows[i];
                    dr[constProductID] = ProductId;


                    if ((string.IsNullOrEmpty(dr["BookedLeadId"].ToString()) || dr["BookedLeadId"].ToString() == "0"))
                    {
                        sbError.Append("Provide BookedLeadId for : " + Convert.ToString(dr[constName]));
                    }

                    dr["LeadSource"] = "Renewal";

                    //UploadedLeadID
                    var Leadid = Convert.ToString(dr["BookedLeadId"]).Trim();
                    if (Leadid == "")
                    {
                        dr["BookedLeadId"] = "0";
                    }
                    else
                    {
                        dr["BookedLeadId"] = Convert.ToString(dr["BookedLeadId"]).TrimStart().TrimEnd();
                    }


                    if (!string.IsNullOrEmpty(GroupId))
                    {
                        dr["AssignToGroupID"] = Convert.ToInt32(GroupId);
                    }

                    if (!string.IsNullOrEmpty(AssignedUserId))
                    {
                        dr["AssignToUserID"] = Convert.ToInt32(AssignedUserId);

                    }


                    string PolicyEndDate = Convert.ToString(dr["PolicyEndDate"]).Trim();
                    PolicyEndDate = PolicyEndDate.Split()[0];
                    if (!string.IsNullOrEmpty(PolicyEndDate))
                    {
                        DateTime dt;
                        if (DateTime.TryParse(Convert.ToString(dr["PolicyEndDate"]).Trim(), out dt))
                        {

                            dr["PolicyEndDate"] = dt;
                        }
                        else if (!isValidDate(PolicyEndDate))
                        {
                            sbError.Append(String.Format("Invalid PolicyEndDate for {0}. Acceptable format is dd-MM-yyyy.",
                                    Convert.ToString(dr["PolicyEndDate"])));
                            myDataTable.Rows.Remove(dr);
                            continue;
                        }
                        else
                        {
                            dr["PolicyEndDate"] = DateTime.ParseExact(PolicyEndDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                        }
                    }

                    string PolicyStartDate = Convert.ToString(dr["PolicyStartDate"]).Trim();
                    PolicyStartDate = PolicyStartDate.Split()[0];
                    if (!string.IsNullOrEmpty(PolicyStartDate))
                    {
                        DateTime dt;
                        if (DateTime.TryParse(Convert.ToString(dr["PolicyStartDate"]).Trim(), out dt))
                        {

                            dr["PolicyStartDate"] = dt;
                        }
                        else if (!isValidDate(PolicyStartDate))
                        {
                            sbError.Append(String.Format("Invalid PolicyStartDate for {0}. Acceptable format is dd-MM-yyyy.",
                                    Convert.ToString(dr["PolicyExpiryDate"])));
                            myDataTable.Rows.Remove(dr);
                            continue;
                        }
                        else
                        {
                            dr["PolicyStartDate"] = DateTime.ParseExact(PolicyStartDate, "dd-MM-yyyy", CultureInfo.InvariantCulture);
                        }
                    }

                    //sqlBulk.ColumnMappings.Add("UploadedLeadId", "BookedLeadId");
                    //sqlBulk.ColumnMappings.Add("LeadSource", "LeadSource");
                    //sqlBulk.ColumnMappings.Add("UploadedAssignToGroupID", "AssignToGroupID");
                    //sqlBulk.ColumnMappings.Add("UploadedAssignToUserID", "AssignToUserID");
                    //sqlBulk.ColumnMappings.Add("PolicyStartDate", "PolicyStartDate");
                    //sqlBulk.ColumnMappings.Add("PolicyExpiryDate", "PolicyEndDate");
                }

                if (myDataTable.Rows.Count > 0)
                {
                    DataColumn newColumn = new DataColumn("UploadedBy", typeof(System.Int32));
                    newColumn.DefaultValue = userId;
                    myDataTable.Columns.Add(newColumn);
                    DataColumn TrackingCol = new DataColumn("TrackingId", typeof(System.String));
                    TrackingCol.DefaultValue = TrackingId;
                    myDataTable.Columns.Add(TrackingCol);
                    DataColumn ToUpdateCol = new DataColumn("ToUpdate", typeof(System.Int16));
                    ToUpdateCol.DefaultValue = 1;
                    myDataTable.Columns.Add(ToUpdateCol);
                    DataColumn SourceCol = new DataColumn("Source", typeof(System.String));
                    SourceCol.DefaultValue = "DumpUpload";
                    myDataTable.Columns.Add(SourceCol);

                    int result = UploadExcelFileToDatabase(myDataTable, userId, ProcessId, TrackingId);
                    if (result >= 0)
                    {
                        resultMessage = "LeadsUploadedSuccessfully";
                        int res = UpdateRenewalFileStatus(TrackingId, "DataStaged", userId.ToString());
                        if (res != 0)
                        {
                            resultMessage = "File Staged Successfully";
                        }
                    }
                    else
                    {
                        resultMessage = "Some error occured please try again.";
                    }
                }
                else
                {
                    resultMessage = "Fail to stage the File";
                }
            }
            catch (Exception er)
            {
                int resq = UpdateRenewalFileStatus((TrackingId).Trim(), "ErrorOccured", UploadedBy.ToString());
                InsertFileExceptionInMongo((TrackingId).Trim(), (UploadedBy.ToString()).Trim(), er.ToString());
                resultMessage = "File Not Staged";
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, resultMessage, "UploadHomeRenewal", "UpdateRenewalLeadsBLL", UploadedBy.ToString(), FilePath.ToString(), resultMessage, DateTime.Now, DateTime.Now);
            }
            return resultMessage;
        }
        public void InsertFileExceptionInMongo(string TrackingId, string UploadedBy, string Exception, string primaryColumn = null)
        {
            FileExceptionLog objFileExceptionLog = new FileExceptionLog();
            try
            {
                objFileExceptionLog = new FileExceptionLog
                {
                    TrackingId = TrackingId,
                    Exception = Exception,
                    UploadedBy = UploadedBy,
                    Createon = DateTime.Now,
                    PrimaryColumn = primaryColumn
                };
                LoggingHelper.LoggingHelper.LogFileExceptiontoMongo(objFileExceptionLog);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, ex.ToString(), "InsertFileExceptionInMongo", "UpdateRenewalLeadsBLL", UploadedBy, JsonConvert.SerializeObject(objFileExceptionLog), "", DateTime.Now, DateTime.Now);
            }
        }

        public string GetCJUrl(string LeadID, int ProductId, int SupplierId, string LeadSource, string AgentID)
        {
            string result = null, json = null;
            try
            {
                byte[] encode = Encoding.UTF8.GetBytes(LeadID);
                string encLeadId = Convert.ToBase64String(encode);
                if (!string.IsNullOrEmpty(LeadID))
                {
                    //if (CoreCommonMethods.IsValidString(LeadSource) && LeadSource.ToLower() == "renewal" && (ProductId == 2 || ProductId == 106 || ProductId == 118 || ProductId == 130))
                    if (ProductId == 2 || ProductId == 106 || ProductId == 118 || ProductId == 130)
                    {
                        json = "{\"continueId\":\"" + encLeadId + "\",\"productID\":" + ProductId + ",\"supplierID\":" + SupplierId + "}";
                        Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"authkey", "CJAuthToken".AppSettings()}};
                        if (CoreCommonMethods.IsValidString(AgentID))
                        {
                            header.Add("source", "matrixagent");
                        }
                        string response = CommonAPICall.CallAPI("HealthCJUrl".AppSettings(), json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json-patch+json", header);
                        if (!string.IsNullOrEmpty(response))
                        {
                            var Response = JsonConvert.DeserializeObject<CJUrlModel>(response);
                            if (Response != null)
                            {
                                return Response.redirectionUrl;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result = ex.ToString();
                LoggingHelper.LoggingHelper.Log(null, Convert.ToInt64(LeadID), ex.ToString(), "GetCJUrl", "UpdateRenewalLeadsBLL", "MatrixCore", json, null, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public string ProcessSmeUploads(UploadRenewalLeadsDetails requestData)
        {
            DataSet dataSet;
            var maxRecords = 500;
            var response = string.Empty;
            var errors = new StringBuilder();
            var triggerName = "SME_MTX_Em_RenewalMatrix";
            var upsellLeadSource = "Upsell";
            try
            {
                requestData.FilePath = UpdateRenewalLeadsDLL.FetchRenewalBulkUploadFile(requestData.UniqueId, requestData.ProductId);
                UpdateRenewalFileStatus(requestData.UniqueId, "DataUnderProcessing", requestData.UploadedBy);
                Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                Path.GetExtension(requestData.FilePath);
                var tempPath = Path.GetTempFileName();
                var client = new WebClient();

                if (requestData.FilePath.Contains("matrixinternal"))
                {

                    Uri uri = new(requestData.FilePath);
                    string path = uri.AbsolutePath;
                    var trustedStorageName = path.Replace("/matrixinternal/", "").TrimStart('/');

                    string bucketName = "matrixdocuments/matrixinternal";

                    var s3Client = new AmazonS3Client(RegionEndpoint.APSouth1);
                    requestData.FilePath = s3Client.GetPreSignedURL(new GetPreSignedUrlRequest
                    {
                        BucketName = bucketName,
                        Key = trustedStorageName,
                        Expires = DateTime.UtcNow.AddMinutes(15)
                    });
                }

                client.DownloadFile(requestData.FilePath, tempPath);

                using var stream = new FileStream(tempPath, FileMode.Open, FileAccess.Read);
                using IExcelDataReader reader = ExcelReaderFactory.CreateReader(stream);
                dataSet = reader.AsDataSet(new ExcelDataSetConfiguration()
                {
                    UseColumnDataType = false,
                    ConfigureDataTable = (tableReader) => new ExcelDataTableConfiguration()
                    {
                        UseHeaderRow = true
                    }
                });

                if (dataSet.Tables["Sheet1"] != null && dataSet.Tables["Sheet1"].Rows.Count > 0)
                {
                    DataTable dtSheet = dataSet.Tables["Sheet1"];

                    if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.PaymentLinkUploads).ToString()))
                    {
                        if (dtSheet.Columns.Contains("PolicyNo") && dtSheet.Columns.Contains("RenewalLink"))
                        {
                            if (dtSheet.Rows.Count <= maxRecords)
                            {
                                foreach (DataRow row in dtSheet.Rows)
                                {
                                    if (string.IsNullOrEmpty(row["PolicyNo"].ToString().Trim()))
                                    {
                                        errors.Append("Blank PolicyNo exists");
                                        errors.Append(Environment.NewLine);
                                    }
                                    if (string.IsNullOrEmpty(row["RenewalLink"].ToString().Trim()))
                                    {
                                        errors.Append("Blank RenewalLink exists");
                                        errors.Append(Environment.NewLine);
                                    }
                                    else if (!Uri.IsWellFormedUriString(row["RenewalLink"].ToString().Trim(), UriKind.Absolute))
                                    {
                                        errors.Append("Invalid RenewalLink - " + row["RenewalLink"].ToString().Trim());
                                        errors.Append(Environment.NewLine);
                                    }
                                }

                                if (string.IsNullOrEmpty(errors.ToString().Trim()))
                                {
                                    foreach (DataRow row in dtSheet.Rows)
                                    {
                                        var status = UpdateRenewalLeadsDLL.ProcessPolicyNoForSmeUploads(row["PolicyNo"].ToString().Trim(),
                                                                                                 row["RenewalLink"].ToString().Trim(),
                                                                                                 requestData.UploadedBy,
                                                                                                 requestData.UniqueId,
                                                                                                 3, ProcessTypes.PaymentLinkUploads.ToString(),
                                                                                                 triggerName);
                                    }
                                    response = "File Staged Successfully";
                                    UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                                }
                            }
                            else
                            {
                                errors.Append("File should contain maximum " + maxRecords + " records");
                            }
                        }
                        else
                        {
                            errors.Append("File does not contain PolicyNo/RenewalLink columns");
                        }
                    }
                    else if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.UpsellLeadCreate).ToString()))
                    {
                        if (dtSheet.Columns.Contains("ExistingLeadId") && dtSheet.Columns.Contains("LeadSource") &&
                            dtSheet.Columns.Contains("UtmSource") && dtSheet.Columns.Contains("EmployeeCode"))
                        {
                            if (dtSheet.Rows.Count <= maxRecords)
                            {
                                foreach (DataRow row in dtSheet.Rows)
                                {
                                    if (string.IsNullOrEmpty(row["LeadSource"].ToString().Trim()))
                                    {
                                        errors.Append("Blank LeadSource exists, ");
                                        errors.Append(Environment.NewLine);
                                    }
                                    else if (!row["LeadSource"].ToString().Trim().ToUpper().Equals(upsellLeadSource.ToUpper()))
                                    {
                                        errors.Append("Only Upsell LeadSource is allowed, ");
                                        errors.Append(Environment.NewLine);
                                    }

                                    if (string.IsNullOrEmpty(row["ExistingLeadId"].ToString().Trim()))
                                    {
                                        errors.Append("Blank ExistingLeadId exists, ");
                                        errors.Append(Environment.NewLine);
                                    }
                                    if (string.IsNullOrEmpty(row["UtmSource"].ToString().Trim()))
                                    {
                                        errors.Append("Blank UtmSource exists, ");
                                        errors.Append(Environment.NewLine);
                                    }
                                }

                                if (string.IsNullOrEmpty(errors.ToString().Trim()))
                                {
                                    foreach (DataRow row in dtSheet.Rows)
                                    {
                                        var status = UpdateRenewalLeadsDLL.ProcessUpsellSmeUploads(row["ExistingLeadId"].ToString().Trim(),
                                                                                            row["LeadSource"].ToString().Trim(),
                                                                                            row["UtmSource"].ToString().Trim().Trim(),
                                                                                            row["EmployeeCode"].ToString().Trim(),
                                                                                            requestData.UploadedBy,
                                                                                            requestData.UniqueId);
                                    }
                                    response = "File Staged Successfully";
                                    UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                                }
                            }
                            else
                            {
                                errors.Append("File should contain maximum " + maxRecords + " records");
                            }
                        }
                        else
                        {
                            errors.Append("File does not contain any of ExistingLeadId/LeadSource/UtmSource/EmployeeCode columns");
                        }
                    }
                    else if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.SmeBulkBooking).ToString()))
                    {
                        bool isFileProccessed = ValidateAndUploadDataSmeBulkBooking(requestData, dtSheet, errors);
                        if (isFileProccessed)
                        {
                            response = "File Staged Successfully";
                            UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                        }
                    }
                    else if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.LastYearLostCases).ToString()))
                    {
                        bool isFileProccessed = ValidateAndUploadDataLastYearLostCases(requestData, dtSheet, errors);
                        if (isFileProccessed)
                        {
                            response = "File Staged Successfully";
                            UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                        }
                    }
                    else if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.SmeBulkLeadUpload).ToString()))
                    {
                        bool isFileProccessed = ValidateAndUploadDataBulkLeadUpload(requestData, dtSheet, errors);
                        if (isFileProccessed)
                        {
                            response = "File Staged Successfully";
                            UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                        }
                    }
                    else if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.RenewalMissingLeads).ToString()))
                    {
                        bool isFileProccessed = ValidateAndUploadRenewalMissingLeads(requestData, dtSheet, errors);
                        if (isFileProccessed)
                        {
                            response = "File Staged Successfully";
                            UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                        }
                    }
                    else if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.CrossSmeUpload).ToString()))
                    {
                        bool isFileProccessed = ValidateAndUploadDataCrossSme(requestData, dtSheet, errors);
                        if (isFileProccessed)
                        {
                            response = "File Staged Successfully";
                            UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                        }
                    }
                    else if (requestData.ProcessId.Equals(Convert.ToInt16(ProcessTypes.SmeFosBulkUpload).ToString()))
                    {
                        bool isFileProccessed = ValidateAndUploadDataSmeFosBulkUpload(requestData, dtSheet, errors);
                        if (isFileProccessed)
                        {
                            response = "File Staged Successfully";
                            UpdateRenewalFileStatus(requestData.UniqueId, "DataStaged", requestData.UploadedBy);
                        }
                    }
                }
                else
                {
                    errors.Append("No records to process");
                }

                stream.Dispose();
                if (File.Exists(tempPath))
                {
                    File.Delete(tempPath);
                }

                if (!string.IsNullOrEmpty(errors.ToString().Trim()))
                {
                    UpdateRenewalFileStatus(requestData.UniqueId, "ErrorOccured", requestData.UploadedBy);
                    InsertFileExceptionInMongo(requestData.UniqueId, requestData.UploadedBy, errors.ToString().Trim());
                    response = "File Not Staged";
                }
            }
            catch (Exception er)
            {
                UpdateRenewalFileStatus(requestData.UniqueId, "ErrorOccured", requestData.UploadedBy);
                InsertFileExceptionInMongo(requestData.UniqueId, requestData.UploadedBy, er.ToString());
                response = "File Not Staged";
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(requestData.UniqueId, 0, errors.ToString().Trim(), "ProcessSmeUploads", "UpdateRenewalLeadsBLL", requestData.UploadedBy.ToString(), requestData.FilePath.ToString(), errors.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        private static bool ValidateAndUploadDataCrossSme(UploadRenewalLeadsDetails requestData, DataTable dtSheet, StringBuilder errors)
        {
            var result = false;
            short maxRecords = 2000;
            if (IsValidCrossSmeSheet(dtSheet))
            {
                if (dtSheet.Rows.Count <= maxRecords)
                {
                    foreach (DataRow row in dtSheet.Rows)
                    {
                        UpdateRenewalLeadsDLL.ProcessBulkCrossSmeLeads(row, requestData);
                    }
                    result = true;
                }
                else
                {
                    errors.Append("File should contain maximum " + maxRecords + " records.");
                }
            }
            else
            {
                errors.Append("File does not contain all the required columns, please refer to the Sample file.");
            }
            return result;
        }
        private static bool ValidateAndUploadRenewalMissingLeads(UploadRenewalLeadsDetails requestData, DataTable dtSheet, StringBuilder errors)
        {
            var result = false;
            short maxRecords = 5000;
            if (IsValidRenewalMissingLeadsSheet(dtSheet))
            {
                if (dtSheet.Rows.Count <= maxRecords)
                {
                    foreach (DataRow row in dtSheet.Rows)
                    {
                        UpdateRenewalLeadsDLL.ProcessRenewalMissingLeads(row, requestData);
                    }
                    result = true;
                }
                else
                {
                    errors.Append("File should contain maximum " + maxRecords + " records.");
                }
            }
            else
            {
                errors.Append("File does not contain all the required columns, please refer to the Sample file.");
            }
            return result;
        }

        private static bool ValidateAndUploadDataBulkLeadUpload(UploadRenewalLeadsDetails requestData, DataTable dtSheet, StringBuilder errors)
        {
            var result = false;
            short maxRecords = 2000;
            if (IsValidBulkLeadUploadSheet(dtSheet))
            {
                if (dtSheet.Rows.Count <= maxRecords)
                {
                    foreach (DataRow row in dtSheet.Rows)
                    {
                        var isRowInserted = UpdateRenewalLeadsDLL.ProcessSmeBulkLeadUpload(row, requestData) > 0;
                    }
                    result = true;
                }
                else
                {
                    errors.Append("File should contain maximum " + maxRecords + " records.");
                }
            }
            else
            {
                errors.Append("File does not contain all the required columns, please refer to the Sample file.");
            }
            return result;
        }

        private static bool ValidateAndUploadDataSmeFosBulkUpload(UploadRenewalLeadsDetails requestData, DataTable dtSheet, StringBuilder errors)
        {
            var result = false;
            short maxRecords = 2000;
            if (IsValidSmeFosBulkUploadSheet(dtSheet))
            {
                if (dtSheet.Rows.Count <= maxRecords)
                {
                    foreach (DataRow row in dtSheet.Rows)
                    {
                        try
                        {
                            var isRowInserted = UpdateRenewalLeadsDLL.ProcessSmeFosBulkUpload(row, requestData) > 0;
                        }
                        catch (Exception ex)
                        {
                            string mobileNo = Convert.ToString(row["MobileNo"]);
                            string companyName = Convert.ToString(row["CompanyName"]);
                            string uploadedBy = requestData?.UploadedBy ?? "Unknown";
                            string filePath = requestData?.FilePath ?? "Unknown";

                            LoggingHelper.LoggingHelper.AddloginQueue(
                                $"{mobileNo} {companyName}",
                                0,
                                ex.ToString(),
                                "ValidateAndUploadDataSmeFosBulkUpload",
                                "UpdateRenewalLeadsBLL",
                                uploadedBy,
                                filePath,
                                ex.Message,
                                DateTime.Now,
                                DateTime.Now
                            );
                        }
                    }
                    result = true;
                }
                else
                {
                    errors.Append("File should contain maximum " + maxRecords + " records.");
                }
            }
            else
            {
                errors.Append("File does not contain all the required columns, please refer to the Sample file.");
            }
            return result;
        }

        private static bool ValidateAndUploadDataLastYearLostCases(UploadRenewalLeadsDetails requestData, DataTable dtSheet, StringBuilder errors)
        {
            var result = false;
            short maxRecords = 2000;
            if (IsValidLastYearLostCasesColums(dtSheet))
            {
                if (dtSheet.Rows.Count <= maxRecords)
                {
                    foreach (DataRow row in dtSheet.Rows)
                    {
                        var isRowInserted = UpdateRenewalLeadsDLL.ProcessLastYearLostCases(row, requestData) > 0;
                    }
                    result = true;
                }
                else
                {
                    errors.Append("File should contain maximum " + maxRecords + " records.");
                }
            }
            else
            {
                errors.Append("File does not contain all the required columns, please refer to the Sample file.");
            }
            return result;
        }

        private static bool ValidateAndUploadDataSmeBulkBooking(UploadRenewalLeadsDetails requestData, DataTable dtSheet, StringBuilder errors)
        {
            var result = false;
            short maxRecords = 2000;
            if (IsValidSmeBulkUploadColums(dtSheet))
            {
                if (dtSheet.Rows.Count <= maxRecords)
                {
                    foreach (DataRow row in dtSheet.Rows)
                    {
                        UpdateRenewalLeadsDLL.ProcessSmeBulkBookingUploads(row, requestData);
                    }
                    result = true;
                }
                else
                {
                    errors.Append("File should contain maximum " + maxRecords + " records.");
                }
            }
            else
            {
                errors.Append("File does not contain all the required columns, please refer to the Sample file.");
            }
            return result;
        }

        private static bool IsValidRenewalMissingLeadsSheet(DataTable dtSheet)
        {
            try
            {
                return dtSheet.Columns.Contains("BookingId") &&
                       dtSheet.Columns.Contains("MobileNo");
            }
            catch
            {
                return false;
            }
        }

        private static bool IsValidBulkLeadUploadSheet(DataTable dtSheet)
        {
            try
            {
                return dtSheet.Columns.Contains("ContactPersonName") &&
                       dtSheet.Columns.Contains("CompanyName") &&
                       dtSheet.Columns.Contains("Email") &&
                       dtSheet.Columns.Contains("MobileNo") &&
                       dtSheet.Columns.Contains("City") &&
                       dtSheet.Columns.Contains("SubProductId") &&
                       dtSheet.Columns.Contains("LeadSource") &&
                       dtSheet.Columns.Contains("Utm_source") &&
                       dtSheet.Columns.Contains("EmployeeCode");
            }
            catch
            {
                return false;
            }
        }

        private static bool IsValidLastYearLostCasesColums(DataTable dtSheet)
        {
            try
            {
                return dtSheet.Columns.Contains("LastYearLeadId") &&
                       dtSheet.Columns.Contains("CompanyName") &&
                       dtSheet.Columns.Contains("Name") &&
                       dtSheet.Columns.Contains("NumberOfEmployees") &&
                       dtSheet.Columns.Contains("Email") &&
                       dtSheet.Columns.Contains("SubProduct") &&
                       dtSheet.Columns.Contains("CityName") &&
                       dtSheet.Columns.Contains("LeadSource") &&
                       dtSheet.Columns.Contains("UTM_Medium") &&
                       dtSheet.Columns.Contains("PolicyExpiryDate") &&
                       dtSheet.Columns.Contains("Utm_Campaign") &&
                       dtSheet.Columns.Contains("Utm_Source") &&
                       dtSheet.Columns.Contains("AgentId");
            }
            catch
            {
                return false;
            }
        }

        private static bool IsValidSmeBulkUploadColums(DataTable dtSheet)
        {
            try
            {
                return dtSheet.Columns.Contains("LeadId") &&
                        dtSheet.Columns.Contains("SubProductId") &&
                        dtSheet.Columns.Contains("InsurerId") &&
                        dtSheet.Columns.Contains("PlanId") &&
                        dtSheet.Columns.Contains("PolicyType") &&
                        dtSheet.Columns.Contains("InsuredName") &&
                        dtSheet.Columns.Contains("CompanyName") &&
                        dtSheet.Columns.Contains("City") &&
                        dtSheet.Columns.Contains("Occupancy") &&
                        dtSheet.Columns.Contains("PolicyTerm") &&
                        dtSheet.Columns.Contains("PayTerm") &&
                        dtSheet.Columns.Contains("PolicyNumber") &&
                        dtSheet.Columns.Contains("SumInsured") &&
                        dtSheet.Columns.Contains("Premium") &&
                        dtSheet.Columns.Contains("PolicyStartDate(MM/DD/YYYY)") &&
                        dtSheet.Columns.Contains("PolicyEndDate(MM/DD/YYYY)") &&
                        dtSheet.Columns.Contains("PaymentMode") &&
                        dtSheet.Columns.Contains("PaymentFrequency") &&
                        dtSheet.Columns.Contains("BankName") &&
                        dtSheet.Columns.Contains("SalesAgent");
            }
            catch
            {
                return false;
            }
        }
        private static bool IsValidCrossSmeSheet(DataTable dtSheet)
        {
            try
            {
                return dtSheet.Columns.Contains("ContactPersonName") &&
                       dtSheet.Columns.Contains("MobileNo") &&
                       dtSheet.Columns.Contains("Email") &&
                       dtSheet.Columns.Contains("LeadSource") &&
                       dtSheet.Columns.Contains("Utm_source") &&
                       dtSheet.Columns.Contains("Utm_Medium") &&
                       dtSheet.Columns.Contains("EmployeeCode") &&
                       dtSheet.Columns.Contains("ProductId");
            }
            catch
            {
                return false;
            }
        }

        private static bool IsValidSmeFosBulkUploadSheet(DataTable dtSheet)
        {
            try
            {
                return dtSheet.Columns.Contains("CIN") &&
                       dtSheet.Columns.Contains("SubProductId") &&
                       dtSheet.Columns.Contains("CompanyName") &&
                       dtSheet.Columns.Contains("ClientCityName") &&
                       dtSheet.Columns.Contains("ParentCompany") &&
                       dtSheet.Columns.Contains("Name") &&
                       dtSheet.Columns.Contains("MobileNo") &&
                       dtSheet.Columns.Contains("EmailId") &&
                       dtSheet.Columns.Contains("ExecutiveRole") &&
                       dtSheet.Columns.Contains("IndustryType") &&
                       dtSheet.Columns.Contains("Utm_source") &&
                       dtSheet.Columns.Contains("PolicyType") &&
                       dtSheet.Columns.Contains("DecisionMakerCityName") &&
                       dtSheet.Columns.Contains("LinkedinLink") &&
                       dtSheet.Columns.Contains("AltMobileNo") &&
                       dtSheet.Columns.Contains("AltEmailId") &&
                       dtSheet.Columns.Contains("Probability") &&
                       dtSheet.Columns.Contains("Utm_medium") &&
                       dtSheet.Columns.Contains("CrossSellSubProductIds") &&
                       dtSheet.Columns.Contains("Discussion") &&
                       dtSheet.Columns.Contains("ClaimHistory") &&
                       dtSheet.Columns.Contains("ExistingBrokerName") &&
                       dtSheet.Columns.Contains("ExistingInsurerName") &&
                       dtSheet.Columns.Contains("ExistingTPAName") &&
                       dtSheet.Columns.Contains("SumInsured") &&
                       dtSheet.Columns.Contains("PremiumAtInception") &&
                       dtSheet.Columns.Contains("PolicyStartDate (MM/DD/YYYY)") &&
                       dtSheet.Columns.Contains("EmployeeCode");
            }
            catch
            {
                return false;
            }
        }

    }

    public class InputValidation
    {
        public string ValidationGroup { get; set; }
        public string DateTimeFormat { get; set; }
        public string FieldName { get; set; }
        public string FieldType { get; set; }
        public string Regex { get; set; }
        public bool Required { get; set; }
        public int FilterProduct { get; set; }
        public decimal Min { get; set; }
        public decimal Max { get; set; }
    }
    public static class ValidateData
    {
        static List<InputValidation> _validations;
        public static List<InputValidation> Validations
        {
            get
            {
                if (_validations == null)
                {
                    //string xmlPath = AppDomain.CurrentDomain.BaseDirectory + "\\ValidationConfig.xml";
                    //Console.Write(xmlPath);
                    var validationsDb = new DataSet();
                    //validationsDb.ReadXml(xmlPath);
                    validationsDb.ReadXml(Environment.CurrentDirectory + "/ValidationConfig.xml");
                    _validations = new List<InputValidation>();
                    for (int i = 0; i < validationsDb.Tables[0].Rows.Count; i++)
                    {
                        if (string.IsNullOrEmpty(validationsDb.Tables[0].Rows[i]["FieldName"].ToString()))
                        {
                            continue;
                        }

                        var validation = new InputValidation
                        {
                            ValidationGroup = validationsDb.Tables[0].Rows[i].ReadColumn("ValidationGroup"),
                            FieldName = validationsDb.Tables[0].Rows[i].ReadColumn("FieldName"),
                            Required = Convert.ToBoolean(validationsDb.Tables[0].Rows[i].ReadColumn("Required")),
                            FieldType = validationsDb.Tables[0].Rows[i].ReadColumn("FieldType"),
                            FilterProduct = Convert.ToInt32(validationsDb.Tables[0].Rows[i].ReadColumn("FilterProduct")),
                            Min = Convert.ToDecimal(validationsDb.Tables[0].Rows[i].ReadColumn("Min")),
                            Max = Convert.ToDecimal(validationsDb.Tables[0].Rows[i].ReadColumn("Max")),
                            DateTimeFormat = validationsDb.Tables[0].Rows[i].ReadColumn("DateTimeFormat"),
                        };
                        _validations.Add(validation);
                    }
                }
                return _validations;
            }
        }
        private static string ReadColumn(this DataRow row, string field)
        {
            try
            {
                return row[field].ToString();
            }
            catch
            {
                return string.Empty;
            }
        }
        public static void ClearCache()
        {
            _validations = null;
        }
        public static List<KVPair> Validate(this DataTable inputTable, string validationGroup, string identifier, int productId)
        {

            var result = new List<KVPair>();
            for (int i = 0; i < inputTable.Rows.Count; i++)
            {
                var rowValidate = Validate(inputTable.Rows[i], validationGroup, identifier, productId);
                result.AddRange(rowValidate);
            }
            return result;
        }
        public static List<KVPair> Validate(this DataRow row, string validationGroup, string identifier, int productId)
        {
            var selectedValidations = Validations;
            if (productId > 0)
            {
                selectedValidations = selectedValidations.Where(t => t.ValidationGroup == validationGroup && (t.FilterProduct == productId || t.FilterProduct == 0)).ToList();
            }
            var result = new List<KVPair>();
            foreach (var v in selectedValidations)
            {
                var value = row.ReadColumn(v.FieldName);
                if (v.FieldType == "VARCHAR")
                {
                    if (v.Required && string.IsNullOrEmpty(value))
                    {
                        result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Required", v.FieldName)));
                    }
                    if (!string.IsNullOrEmpty(value))
                    {
                        if (v.Min > 0 && value.Length < v.Min)
                        {
                            result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Less then Required", v.FieldName)));
                        }
                        if (v.Max > 0 && value.Length > v.Max)
                        {
                            result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is More then Limit ", v.FieldName)));
                        }
                    }
                }
                else if (v.FieldType == "INT")
                {
                    if (v.Required && string.IsNullOrEmpty(value))
                    {
                        result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Required", v.FieldName)));
                    }
                    long intVal = 0;
                    if (long.TryParse(value, out intVal))
                    {
                        if (v.Required && intVal == 0)
                        {
                            result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Required", v.FieldName)));
                        }
                        if (intVal > 0)
                        {
                            if (v.Min > 0 && value.Length < v.Min)
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Less then " + v.Min + " digits.", v.FieldName)));
                            }
                            if (v.Max > 0 && value.Length > v.Max)
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is More then Limit " + v.Max, v.FieldName)));
                            }
                            if (v.FieldName.Equals("Mobile") && value.Length >= v.Min)
                            {

                                if (productId.Equals(158) || productId.Equals(159))
                                    if (value.Substring(0, 3).Equals("971"))
                                    {
                                        result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Country code not allowed in starting", v.FieldName)));
                                    }

                            }
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(value))
                        {
                            result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is not valid number", v.FieldName)));
                        }
                    }
                }
                else if (v.FieldType == "DECIMAL")
                {
                    if (v.Required && string.IsNullOrEmpty(value))
                    {
                        result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Required", v.FieldName)));
                    }
                    if (!string.IsNullOrEmpty(value))
                    {
                        decimal decimalVal;
                        if (decimal.TryParse(value, out decimalVal))
                        {
                            if (v.Required && decimalVal == 0)
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Required", v.FieldName)));
                            }
                            if (v.Min > 0 && decimalVal < v.Min)
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Less then Required", v.FieldName)));
                            }
                            if (v.Max > 0 && decimalVal > v.Max)
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is More then Limit ", v.FieldName)));
                            }
                        }
                        else
                        {
                            if (!string.IsNullOrEmpty(value))
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is not valid decimal value", v.FieldName)));
                            }
                        }
                    }
                }
                else if (v.FieldType == "DATETIME")
                {
                    if (v.Required && string.IsNullOrEmpty(value))
                    {
                        result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Required", v.FieldName)));
                    }
                    if (!string.IsNullOrEmpty(value))
                    {
                        if (string.IsNullOrEmpty(v.DateTimeFormat))
                        {
                            DateTime dt;
                            if (!DateTime.TryParse(value, out dt))
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is not a valid date", v.FieldName)));
                            }
                            else
                            {
                                if (v.Required && dt <= DateTime.MinValue)
                                {
                                    result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is Required", v.FieldName)));
                                }
                            }
                        }
                        else
                        {
                            try
                            {
                                DateTime dt = DateTime.ParseExact(value, v.DateTimeFormat, System.Globalization.CultureInfo.InvariantCulture);
                            }
                            catch
                            {
                                result.Add(new KVPair(row.ReadColumn(identifier).ToString(), string.Format("Value of {0} is not a valid date", v.FieldName)));
                            }
                        }
                    }
                }
            }
            return result;

        }
    }

    public class KVPair
    {
        public KVPair(string k, string v)
        {
            this.Key = k;
            this.Value = v;
        }
        public string Key { get; set; }
        public string Value { get; set; }
    }
    //public class SupplierPlans
    //{
    //    private static SupplierPlans _instance;
    //    public static SupplierPlans Instance { get { return _instance; } }
    //    private SupplierPlans()
    //    {

    //    }
    //    static SupplierPlans()
    //    {
    //        _instance = new SupplierPlans();
    //        //Load suppliers and plans. Do not prevert error So in case supplier and plans not built that will not create object
    //    }
    //    public List<Supplier> GetSupplier(short prodID, string prodName)
    //    {
    //        return GetSuppliers(prodID, prodName).Values.ToList();
    //    }
    //    public List<Plans> GetPlan(short prodID, int supplierID, string prodName)
    //    {
    //        return GetSuppliers(prodID, prodName)[supplierID].Plans;
    //    }
    //    private Dictionary<int, Supplier> GetSuppliers(short prodID, string prodName)
    //    {
    //        if (prodID == 1000)
    //        {
    //            prodName = "Life";
    //            prodID = 7;
    //        }
    //        var suppliers = (Dictionary<short, Dictionary<int, Supplier>>)ServerCache.getFromCache("SupplierPlans");
    //        if (suppliers == null)
    //        {
    //            lock (this)
    //            {
    //                suppliers = (Dictionary<short, Dictionary<int, Supplier>>)ServerCache.getFromCache("SupplierPlans");
    //                if (suppliers == null)
    //                {
    //                    BuilSupplierPlanCache();
    //                    suppliers = (Dictionary<short, Dictionary<int, Supplier>>)ServerCache.getFromCache("SupplierPlans");
    //                }
    //            }

    //        }
    //        if (suppliers.ContainsKey(prodID))
    //        {
    //            return suppliers[prodID];
    //        }
    //        else
    //        {
    //            lock (this)
    //            {
    //                if (!suppliers.ContainsKey(prodID))
    //                {
    //                    var suppplan = BuildSupplierPlan(prodName, prodID);
    //                    if (suppplan != null)
    //                    {
    //                        suppliers[prodID] = suppplan;
    //                    }
    //                }


    //            }
    //            if (suppliers.ContainsKey(prodID))
    //            {
    //                return suppliers[prodID];
    //            }
    //            else
    //            {
    //                return new Dictionary<int, Supplier>();
    //            }
    //        }
    //    }

    //    private void BuilSupplierPlanCache()
    //    {
    //        Dictionary<short, Dictionary<int, Supplier>> suppliersCache = new Dictionary<short, Dictionary<int, Supplier>>();

    //        ServerCache.insertCacheObject("SupplierPlans", suppliersCache);

    //    }
    //    private Dictionary<int, Supplier> BuildSupplierPlan(string prodName, short prodid)
    //    {
    //        var dataset = GetInsurer(prodName, prodid);
    //        Dictionary<int, Supplier> supplier = null;
    //        if (dataset.Tables.Count > 0 && dataset.Tables[0].Rows.Count > 0)
    //        {
    //            supplier = new Dictionary<int, Supplier>();
    //            for (var i = 0; i < dataset.Tables[0].Rows.Count; i++)
    //            {
    //                var dr = dataset.Tables[0].Rows[i];
    //                var supp = new Supplier()
    //                {
    //                    OldSupplierId = Convert.ToInt32(dr["id"]),
    //                    SupplierId = Convert.ToInt32(dr["id"]),
    //                    SupplierDisplayName = dr["SupplierName"].ToString(),
    //                    SupplierName = dr["SupplierName"].ToString()
    //                };

    //                if (dataset.Tables.Count > 1)
    //                {
    //                    var Plans = dataset.Tables[1].Select("supplierid=" + dr["id"].ToString());
    //                    foreach (var curdr in Plans)
    //                    {
    //                        int planId = 0;
    //                        if (int.TryParse(curdr["PlanId"].ToString(), out planId))
    //                        {
    //                            supp.Plans.Add(new Plans()
    //                            {
    //                                PlanID = Convert.ToInt32(curdr["PlanId"]),
    //                                OldPlanId = Convert.ToInt32(curdr["PlanId"]),
    //                                PlanDisplayName = curdr["PlanName"].ToString(),
    //                                PlanName = curdr["PlanName"].ToString()
    //                            });
    //                        }
    //                    }
    //                    supplier[supp.SupplierId] = supp;
    //                }

    //            }
    //        }

    //        return supplier;
    //    }

    //    public static DataSet GetInsurer(string Productname, short? prodid = null)
    //    {
    //            SqlParameter[] sqlParam = new SqlParameter[3];
    //            sqlParam[0] = new SqlParameter("@ProductName", Productname);
    //            sqlParam[1] = new SqlParameter("@ProductId", prodid);
    //            DataSet oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[CRM].[Get_SuppliersAndPlans]", 3000, sqlParam);
    //            //if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
    //            //{
    //            //    result = Convert.ToInt32(oDataSet.Tables[0].Rows[0]["Result"]);
    //            //}
    //        return oDataSet;
    //    }

    //}
    //public class Supplier
    //{

    //    public Supplier()
    //    {
    //        Plans = new List<Plans>(); //Create default empty list to prever further errors;
    //    }

    //    public Supplier(Plans plan) //Cretae supplier using plan
    //    {
    //        this.ProductId = plan.ProductId;
    //        this.ProductName = plan.ProductName;
    //        this.OldSupplierId = plan.OldSupplierId;
    //        this.SupplierName = plan.SupplierName;
    //        this.SupplierDisplayName = plan.SupplierDisplayName;
    //        this.SupplierId = plan.SupplierId;
    //    }

    //    public short ProductId { get; set; }

    //    public string ProductName { get; set; }

    //    public int SupplierId { get; set; }

    //    public string SupplierName { get; set; }

    //    public int? OldSupplierId { get; set; }

    //    public string SupplierDisplayName { get; set; }

    //    public List<Plans> Plans { get; set; }

    //}
    //public class ServerCache
    //{
    //    #region InsertCache
    //    /// <summary>
    //    /// Method to insert an item into the Cache.
    //    /// </summary>
    //    /// <param name="strCacheID">input</param>
    //    /// <param name="objDataArr">input</param>
    //    public static void insertCacheObject(string strCacheID, object objDataArr)
    //    {
    //        try
    //        {
    //            object objDataFromCache = System.Web.HttpRuntime.Cache.Get(strCacheID);
    //            if (objDataFromCache == null)
    //            {
    //                System.Web.HttpRuntime.Cache.Insert(strCacheID, objDataArr, null, DateTime.Now.AddHours(4), Cache.NoSlidingExpiration);
    //            }
    //            else
    //            {
    //                System.Web.HttpRuntime.Cache[strCacheID] = objDataArr;
    //                System.Web.Caching.Cache obj = new System.Web.Caching.Cache();

    //            }
    //        }
    //        catch (Exception objException)
    //        {
    //            throw objException;
    //        }
    //    }

    //    public static void insertCacheObject(string strCacheID, object objDataArr, int expiryMinutes)
    //    {
    //        try
    //        {
    //            object objDataFromCache = System.Web.HttpRuntime.Cache.Get(strCacheID);
    //            if (objDataFromCache == null)
    //            {
    //                //System.Web.HttpRuntime.Cache.Insert(strCacheID, objDataArr, null, DateTime.Now.AddMinutes(expiryMinutes), Cache.NoSlidingExpiration);
    //                System.Web.HttpRuntime.Cache.Add(strCacheID, objDataArr, null, DateTime.Now.AddMinutes(expiryMinutes), Cache.NoSlidingExpiration, System.Web.Caching.CacheItemPriority.Default, null);
    //            }
    //            else
    //            {
    //                System.Web.HttpRuntime.Cache[strCacheID] = objDataArr;
    //                System.Web.Caching.Cache obj = new System.Web.Caching.Cache();

    //            }
    //        }
    //        catch (Exception objException)
    //        {
    //            throw objException;
    //        }
    //    }

    //    #endregion
    //    #region GetCache
    //    /// <summary>
    //    /// Method to get the item from Cache.
    //    /// </summary>
    //    /// <param name="strCacheID">Input</param>
    //    /// <returns></returns>
    //    public static object getFromCache(string strCacheID)
    //    {
    //        try
    //        {
    //            object objData;
    //            objData = System.Web.HttpRuntime.Cache.Get(strCacheID);
    //            return objData;
    //        }
    //        catch (Exception objException)
    //        {
    //            throw objException;
    //        }
    //    }
    //    #endregion
    //}

}
