﻿using DataAccessLibrary;
using Helper;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using System;
using System.Collections.Generic;
using System.Data;
using System.Dynamic;
using System.Linq;
using System.Text;

namespace EmailCommunicationBLL
{
    public class PGBLL : IPGBLL
    {
        public ResponseAPI SavePgData(PGViewModel oPGViewModel)
        {
            string error = string.Empty;
            ResponseAPI response = new() { message = "failed" };
            DateTime dt = DateTime.Now;

            try
            {
                response.status = PGDLL.SavePgData(oPGViewModel);
                if (response.status)
                    response.message = "Data Save Successfully";
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oPGViewModel.LeadId), error, "SavePgData", "MatrixCore", "PGViewBLL", JsonConvert.SerializeObject(oPGViewModel), JsonConvert.SerializeObject(response), dt, DateTime.Now);
            }
            return response;

        }

        /* public List<PGViewModel> GetPGAttemptsData(long LeadId, long UserId, Int16 NoOfDays, long CustId)
        {
            List<PGViewModel> lstPGViewModel = new();
            try
            {
                DataSet oDataSet = PGDLL.GetPGAttemptsData(LeadId, UserId, NoOfDays, CustId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    lstPGViewModel = (from dr in oDataSet.Tables[0].AsEnumerable()
                                      select new PGViewModel
                                      {
                                          LeadId = dr["LeadId"] != null && dr["LeadId"] != DBNull.Value ? Convert.ToString(dr["LeadId"]) : string.Empty,
                                          Paymentstatus = dr["Paymentstatus"] != null && dr["Paymentstatus"] != DBNull.Value ? Convert.ToInt16(dr["Paymentstatus"]) : Convert.ToInt16(0),
                                          OrderNo = dr["OrderNo"] != null && dr["OrderNo"] != DBNull.Value ? Convert.ToString(dr["OrderNo"]) : string.Empty,
                                          AttemptID = dr["AttemptID"] != null && dr["AttemptID"] != DBNull.Value ? Convert.ToString(dr["AttemptID"]) : string.Empty,
                                          PaymentMode = dr["PaymentMode"] != null && dr["PaymentMode"] != DBNull.Value ? Convert.ToString(dr["PaymentMode"]) : string.Empty,
                                          Product = dr["Product"] != null && dr["Product"] != DBNull.Value ? Convert.ToString(dr["Product"]) : string.Empty,
                                          Insurer = dr["Insurer"] != null && dr["Insurer"] != DBNull.Value ? Convert.ToString(dr["Insurer"]) : string.Empty,
                                          Amount = dr["Amount"] != null && dr["Amount"] != DBNull.Value ? Convert.ToDecimal(dr["Amount"]) : 0,
                                          FailureReason = dr["FailureReason"] != null && dr["FailureReason"] != DBNull.Value ? Convert.ToString(dr["FailureReason"]) : string.Empty,
                                          CreatedOn = dr["CreatedOn"] != null && dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : DateTime.MinValue,
                                          AttemptTime = dr["AttemptTime"] != null && dr["AttemptTime"] != DBNull.Value ? Convert.ToDateTime(dr["AttemptTime"]) : DateTime.MinValue,
                                          AttemptCount = dr["AttemptCount"] != null && dr["AttemptCount"] != DBNull.Value ? Convert.ToInt16(dr["AttemptCount"]) : Convert.ToInt16(0),
                                          CustomerId = dr["CustId"] != null && dr["CustId"] != DBNull.Value ? Convert.ToInt64(dr["CustId"]) : 0,

                                      }).ToList();
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "GetPGAttemptsData", "MatrixCore", "PGBLL", JsonConvert.SerializeObject(LeadId), JsonConvert.SerializeObject(lstPGViewModel), DateTime.Now, DateTime.Now);
            }
            return lstPGViewModel;
        }*/

        public List<PGViewModel> GetPGAttemptsData(long LeadId, long UserId, Int16 NoOfDays, long CustId, string TokenId)
        {
            List<PGViewModel> lstPGViewModel = new();
            try
            {

                if (!string.IsNullOrEmpty(TokenId))
                {
                    string decryptUserId = Crypto.Decrytion_Payment_AES(TokenId, "Core", 256, 128, "MatrixSecretKey".AppSettings(), "MatrixSecretIV".AppSettings(), true);
                    string[] parts = CoreCommonMethods.Split2(decryptUserId, "&");
                    if (parts is Array && parts.Length > 0)
                    {
                        if (Convert.ToInt64(parts[1]) == UserId)
                        {
                            UserId = Convert.ToInt64(parts[0]);
                        }
                        else
                        {
                            return lstPGViewModel;
                        }
                    }

                }
                DataSet oDataSet = PGDLL.GetPGAttemptsData(LeadId, UserId, NoOfDays, CustId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    lstPGViewModel = (from dr in oDataSet.Tables[0].AsEnumerable()
                                      select new PGViewModel
                                      {
                                          LeadId = dr["LeadId"] != null && dr["LeadId"] != DBNull.Value ? Convert.ToString(dr["LeadId"]) : string.Empty,
                                          Paymentstatus = dr["Paymentstatus"] != null && dr["Paymentstatus"] != DBNull.Value ? Convert.ToInt16(dr["Paymentstatus"]) : Convert.ToInt16(0),
                                          OrderNo = dr["OrderNo"] != null && dr["OrderNo"] != DBNull.Value ? Convert.ToString(dr["OrderNo"]) : string.Empty,
                                          AttemptID = dr["AttemptID"] != null && dr["AttemptID"] != DBNull.Value ? Convert.ToString(dr["AttemptID"]) : string.Empty,
                                          PaymentMode = dr["PaymentMode"] != null && dr["PaymentMode"] != DBNull.Value ? Convert.ToString(dr["PaymentMode"]) : string.Empty,
                                          Product = dr["Product"] != null && dr["Product"] != DBNull.Value ? Convert.ToString(dr["Product"]) : string.Empty,
                                          Insurer = dr["Insurer"] != null && dr["Insurer"] != DBNull.Value ? Convert.ToString(dr["Insurer"]) : string.Empty,
                                          Amount = dr["Amount"] != null && dr["Amount"] != DBNull.Value ? Convert.ToDecimal(dr["Amount"]) : 0,
                                          FailureReason = dr["FailureReason"] != null && dr["FailureReason"] != DBNull.Value ? Convert.ToString(dr["FailureReason"]) : string.Empty,
                                          CreatedOn = dr["CreatedOn"] != null && dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : DateTime.MinValue,
                                          AttemptTime = dr["AttemptTime"] != null && dr["AttemptTime"] != DBNull.Value ? Convert.ToDateTime(dr["AttemptTime"]) : DateTime.MinValue,
                                          AttemptCount = dr["AttemptCount"] != null && dr["AttemptCount"] != DBNull.Value ? Convert.ToInt16(dr["AttemptCount"]) : Convert.ToInt16(0),
                                          CustomerId = dr["CustId"] != null && dr["CustId"] != DBNull.Value ? Convert.ToInt64(dr["CustId"]) : 0,
                                          CustomerName = dr["CustName"] != null && dr["CustName"] != DBNull.Value ? Convert.ToString(dr["CustName"]) : string.Empty,

                                      }).ToList();
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "GetPGAttemptsData", "MatrixCore", "PGBLL", JsonConvert.SerializeObject(LeadId), JsonConvert.SerializeObject(lstPGViewModel), DateTime.Now, DateTime.Now);
            }
            return lstPGViewModel;
        }


        public string CreateTicket(TicketModel oTicketModel)
        {
            string result = "No Data found";
            dynamic obj = new ExpandoObject();
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            try
            {
                string url = "matrixticket".AppSettings() + "/service/SalesTicket.svc/CreateTicket";
                string TicketToken = "TicketToken".AppSettings();
                result = CommonAPICall.CallAPI(url, JsonConvert.SerializeObject(oTicketModel), "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oTicketModel.CreatedBy), Error, "CreateTicket", "PGBLL", "MatrixCore", JsonConvert.SerializeObject(oTicketModel), result, dt, DateTime.Now);
            }
            return result;
        }

        public BasicLeadDetails GetLeadDetailsByLeadID(long LeadId)
        {
            BasicLeadDetails obj = new BasicLeadDetails();
            string Exception = string.Empty;
            DateTime RequestDateTime = DateTime.Now;
            try
            {
                var oDataSet = PGDLL.GetLeadDetailsByLeadID(LeadId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    DataTable dbResult = oDataSet.Tables[0];
                    obj = new BasicLeadDetails
                    {
                        LeadID = Convert.ToInt64(dbResult.Rows[0]["LeadID"]),
                        CreatedON = Convert.ToDateTime(dbResult.Rows[0]["CreatedON"].ToString()),
                        LeadSource = dbResult.Rows[0]["LeadSource"].ToString(),
                        MaritalStatus = dbResult.Rows[0]["MaritalStatus"].ToString(),
                       // Name = dbResult.Rows[0]["Name"].ToString(),
                        ParentID = dbResult.Rows[0]["ParentID"].ToString().TryParseToInt64(),
                        IsParent = dbResult.Rows[0]["ParentID"].ToString().TryParseToInt64() == 0 ? true : false,
                        PostCode = dbResult.Rows[0]["PostCode"].ToString(),
                        ProductID = dbResult.Rows[0]["ProductID"].ToString().TryParseToInt32(),
                        Source = dbResult.Rows[0]["Source"].ToString(),
                        ReferralID = dbResult.Rows[0]["ReferralID"].ToString().TryParseToInt64(),
                        StateID = dbResult.Rows[0]["StateID"].ToString().TryParseToInt32(),
                        Utm_campaign = dbResult.Rows[0]["Utm_campaign"].ToString(),
                        UTM_Medium = dbResult.Rows[0]["UTM_Medium"].ToString(),
                        Utm_source = dbResult.Rows[0]["Utm_source"].ToString(),
                        Utm_term = dbResult.Rows[0]["Utm_term"].ToString(),
                        NoCostEMI = (dbResult.Rows[0]["NoCostEMI"]).ToString() == string.Empty ? null : (bool?)(dbResult.Rows[0]["NoCostEMI"]),
                        MonthlyMode = dbResult.Rows[0]["MonthlyMode"].ToString(),
                        ACH = dbResult.Rows[0]["ACH"].ToString(),
                        IsSmartCollect = Convert.ToInt16(dbResult.Rows[0]["IsSmartCollect"]),
                        AssignToGroupId = Convert.ToInt32(dbResult.Rows[0]["AssignToGroupId"]),
                        RollOver = Convert.ToString(dbResult.Rows[0]["RollOver"])
                    };
                }
            }
            catch (Exception ex)
            {
                Exception = ex.StackTrace.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, Exception, "GetLeadDetails", "PGBLL", "MatrixCore", string.Empty, string.Empty, RequestDateTime, DateTime.Now);
            }
            return obj;
        }
    }
}
