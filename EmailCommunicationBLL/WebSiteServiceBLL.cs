using DataAccessLayer;
using DataAccessLibrary;
using Helper;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using System;
using System.Collections.Generic;
using System.Data;
using System.DirectoryServices.Protocols;
using System.Dynamic;
using System.Globalization;
using System.Linq;
using System.Net.Mail;
using System.Runtime.Caching;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Redis;
using Microsoft.IdentityModel.Tokens;

namespace EmailCommunicationBLL
{
    public class WebSiteServiceBLL : IWebSiteServiceBLL
    {
        public SaveInfo SaveSelectedQuotes(SelectedQuote obj)
        {
            SaveInfo saveInfo = new SaveInfo()
            {
                StatusCode = 404,
                IsSaved = false
            };
            try
            {
                int? result = WebSiteServicDLL.SaveSelectedQuotes(obj);
                if (result != null && result == 1)
                {

                    if (obj.ProductID == 2)
                    {
                        SelectionLead _SelectionLead = new SelectionLead();
                        _SelectionLead.LeadID = obj.LeadID;
                        _SelectionLead.ProductID = obj.ProductID;
                        _SelectionLead.InsurerID = (short)obj.SupplierId;
                        _SelectionLead.PlanID = obj.PlanID;
                        _SelectionLead.SourcePage = obj.SourcePage;
                        LeadPrioritizationBLL _obj = new LeadPrioritizationBLL();
                        _obj.UpdateSelectionLeadsintoQueue(_SelectionLead);
                    }

                    saveInfo.StatusCode = 200;
                    saveInfo.IsSaved = true;
                    LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadID, "", "SaveSelectedQuotes", "WebSiteServiceBLL", "MatrixCoreAPI", obj.SourcePage, saveInfo.StatusCode.ToString(), DateTime.Now, DateTime.Now);

                }
            }
            catch (Exception ex)
            {
                saveInfo.StatusCode = 500;
                saveInfo.Message = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadID, "", "SaveSelectedQuotes", "WebSiteServiceBLL", "MatrixCoreAPI", obj.SourcePage, ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return saveInfo;
        }

        public Response SaveCustomerIntent(List<CustomerIntent> customerIntent)
        {
            Response response = new();
            try
            {
                if (customerIntent.Exists(x => x.KeyName == "RenewalFOS"))
                {
                    bool output = WebSiteServicDLL.SetHealthRenewalFOS(customerIntent.Find(x => x.KeyName == "RenewalFOS").LeadID);
                    response.status = true;
                    response.message = "Saved";
                    
                }
                else
                {
                    DataTable dt = CreateCustomerRemarksTable();
                    foreach (var item in customerIntent)
                    {
                        DataRow rowtoAdd = dt.NewRow();
                        rowtoAdd["LeadId"] = item.LeadID;
                        rowtoAdd["Remarks"] = item.Remarks;
                        dt.Rows.Add(rowtoAdd);
                    }
                    int? output = WebSiteServicDLL.SaveCustomerIntent(dt);
                    if (output == customerIntent.Count)
                    {
                        response.status = true;
                        response.message = "Saved";
                    }
                    else
                    {
                        response.status = false;
                        response.message = "Not all records were saved";
                    }
                }
            }
            catch (Exception ex)
            {
                response.status = false;
                response.message = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, response.message, "SaveCustomerIntent", "WebSiteServiceBLL", "MatrixCoreAPI", customerIntent.ToString(), response.ToString(), DateTime.Now, DateTime.Now);
            }

            return response;
        }

        private DataTable CreateCustomerRemarksTable()
        {
            var details = new DataTable();

            DataColumn column = new DataColumn();
            column.DataType = System.Type.GetType("System.Int16");
            column.ColumnName = "ID";
            column.AutoIncrement = true;
            column.AutoIncrementSeed = 1;
            column.AutoIncrementStep = 1;
            details.Columns.Add(column);

            details.Columns.Add("LeadId", typeof(long));
            details.Columns.Add("Remarks", typeof(string));
            return details;
        }

        public Response SaveLeadAction(LeadAction leadAction)
        {
            var request = DateTime.Now;
            Response response = new Response();
            response.LeadId = leadAction.LeadID.ToString();
            try
            {
                (response.status, response.message) = WebSiteServicDLL.SaveLeadAction(leadAction);
            }
            catch (Exception ex)
            {
                response.message = ex.ToString();
                response.status = false;
                LoggingHelper.LoggingHelper.AddloginQueue("", leadAction.LeadID, ex.ToString(), "SaveLeadAction", "WebSiteServiceBLL", "MatrixCoreAPI", leadAction.ToString(), response.ToString(), request, DateTime.Now);
            }
            return response;

        }

        public LeadStatusResponse GetLeadQuoteUploadStatus(long leadId)
        {
            LeadStatusResponse response;
            try
            {
                response = WebSiteServicDLL.GetLeadQuoteUploadStatus(leadId);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, leadId, ex.Message, "GetLeadQuoteUploadStatus",
                                                "MatrixCore", "", "",
                                                "", DateTime.Now, DateTime.Now);
                response = new LeadStatusResponse()
                {
                    IsAllowed = false,
                    Message = ex.Message
                };
            }
            return response;
        }

        public SaveInfo UploadSmeRequestForQuotes(List<SmeRequestForQuotesEntity> rfqEntities)
        {
            long leadId = 0;
            SaveInfo response;
            try
            {
                foreach (var entity in rfqEntities)
                {
                    leadId = entity.LeadId;
                    UploadSmeRequestForQuotes(entity);
                }
                response = new SaveInfo()
                {
                    IsSaved = true,
                    Message = "Success"
                };
            }
            catch (Exception ex)
            {
                response = new SaveInfo()
                {
                    IsSaved = false,
                    Message = Convert.ToString(ex.Message)
                };
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId,
                                                          ex.ToString(), "UploadSmeRequestForQuotes", "WebSiteServiceBLL",
                                                          "", JsonConvert.SerializeObject(rfqEntities),
                                                          JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        private static void UploadSmeRequestForQuotes(SmeRequestForQuotesEntity entity)
        {
            var smeQuotesModel = GetAddUpdateSmeQuotesModel(entity);
            smeQuotesModel.ControlId = SalesViewDLL.AddUpdateSMEQuotes(smeQuotesModel);

            if (smeQuotesModel.ControlId > 0 && entity.DocumentType == DocumentType.RFQ)
            {
                smeQuotesModel.SMEQuote = GetSmeQuoteXml(entity.SmeSupplierFamilyQuotes);
                SalesViewDLL.AddSMEQuotesList(smeQuotesModel);
            }

            if (smeQuotesModel.ControlId > 0 && entity.AdditionalFiles != null && entity.AdditionalFiles.Count > 0)
            {
                foreach (var additionalFile in entity.AdditionalFiles)
                {
                    var smeQuotesAdditionalFileUploadModel = GetSmeQuotesAdditionalFileUploadModel(entity, additionalFile, smeQuotesModel.ControlId);
                    SalesViewDLL.SMEQuotesAdditionalUploadV2(smeQuotesAdditionalFileUploadModel);
                }
            }

            new WebSiteServiceBLL().PushCJNotification(entity.LeadId);
        }

        public async void PushCJNotification(long leadId)
        {
            await Task.Run(() =>
            {
                try
                {
                    DataRow data = WebSiteServicDLL.GetEmployeeDetailsByLeadId(leadId);
                    if (data != null)
                    {
                        new SalesViewBLL().PostAgentNotification(leadId, 0, data, true);
                    }
                    else
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, "GetEmployeeDetailsByLeadId is null", "PushCJNotification", "WebSiteServiceBLL", "MatrixCoreAPI", "", string.Empty, DateTime.Now, DateTime.Now);
                    }
                }
                catch (Exception ex)
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.StackTrace.ToString(), "PushCJNotification", "WebSiteServiceBLL", "MatrixCoreAPI", "", string.Empty, DateTime.Now, DateTime.Now);
                }
            });
        }

        private static SMEQuoteModal GetAddUpdateSmeQuotesModel(SmeRequestForQuotesEntity entity)
        {
            return new SMEQuoteModal()
            {
                LeadID = entity.LeadId,
                DocumentId = entity.DocumentId,
                UserId = entity.UserId,
                typeid = (short)entity.DocumentType,
                fileName = entity.FileName,
                roleId = entity.UserId > 0 ? 13 : 0,
                ReasonId = string.Empty,
                comment = entity.Comment,
                QuoteSource = Constants.QuoteSourceCJ
            };
        }

        private static SMEAdditionalFileModal GetSmeQuotesAdditionalFileUploadModel(SmeRequestForQuotesEntity entity,
                                                                                    SmeRequestForQuotesAdditionalFile file,
                                                                                    int controlId)
        {
            return new SMEAdditionalFileModal()
            {
                LeadID = entity.LeadId,
                DocumentId = file.DocumentId,
                UserId = entity.UserId,
                FileName = file.FileName,
                roleId = entity.UserId > 0 ? 13 : 0,
                ControlId = controlId,
                IsDelete = false,
                RowId = 0,
                QuoteSource = Constants.QuoteSourceCJ
            };
        }

        private static string GetSmeQuoteXml(List<SmeRequestForQuotesSupplierFamilyXmlEntity> smeQuotes)
        {
            var smeQuoteBuilder = new StringBuilder("<root>");

            if (smeQuotes != null && smeQuotes.Count > 0)
            {
                foreach (var quote in smeQuotes)
                {
                    if (quote.FamilyIds != null && quote.FamilyIds.Count > 0)
                    {
                        foreach (var familyId in quote.FamilyIds)
                        {
                            smeQuoteBuilder.Append(GetNodeWithParentIdAndSupplierId(quote.SupplierId, familyId));
                        }
                    }
                    else
                    {
                        smeQuoteBuilder.Append(GetNodeWithParentIdAndSupplierId(quote.SupplierId, 0));
                    }
                }
            }

            return smeQuoteBuilder.Append("</root>").ToString();
        }

        private static string GetNodeWithParentIdAndSupplierId(int supplierId, int parentId)
        {
            return @"<q><supplierId>" +
                     supplierId +
                     "</supplierId><FamilyId>" +
                     parentId +
                     "</FamilyId><price>0</price><OTC>0</OTC></q>";
        }

        public string GetOfflineBookedLeads(string date, int productId)
        {
            var response = new BookedLeads();
            try
            {
                if (DateTime.Now.Hour >= Convert.ToInt16("OfficeEndHour".AppSettings()) || DateTime.Now.Hour <= Convert.ToInt16("OfficeStartHour".AppSettings()))
                {
                    var data = WebSiteServicDLL.GetOfflineBookedLeads(date, productId);

                    if (data != null && data.Tables != null && data.Tables.Count > 0)
                    {
                        response.Leads = data.Tables[0].AsEnumerable().Select(p => p.Field<long>("LeadId")).ToList();
                        response.Message = "Success";
                    }
                    else
                    {
                        response.Message = "Leads not found.";
                    }
                }
                else
                {
                    response.Message = "Working hours.";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, productId, ex.Message, "GetOfflineBookedLeads",
                                                "MatrixCore", "",
                                                "date: " + date + ", productId: " + productId,
                                                "", DateTime.Now, DateTime.Now);
                response.Message = ex.Message;
            }
            return JsonConvert.SerializeObject(response);
        }

        public string GetLeadDetailsPcd(long leadId)
        {
            var response = new PcdData();
            try
            {
                var bookingDetails = CommunicationDataAccess.GetBookingDetailsForPCD(leadId);

                if (bookingDetails != null && bookingDetails.Rows != null && bookingDetails.Rows.Count > 0)
                {
                    var dataRow = bookingDetails.Rows[0];
                    GetBookingDetailsForPcd(response, dataRow);
                }

                if (response.ProductId == 114 || response.ProductId == 117 || response.ProductId == 139)
                {
                    var bookingAddInfo = CommunicationDataAccess.GetBookingAddInfo(leadId, response.ProductId);

                    if (bookingAddInfo != null && bookingAddInfo.Rows != null && bookingAddInfo.Rows.Count > 0)
                    {
                        var dataRow = bookingAddInfo.Rows[0];
                        GetBookingAddInfo(response, dataRow);
                    }
                }
                response.Message = "Success";
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, leadId, ex.Message, "GetLeadDetailsPcd",
                                                "MatrixCore", "", leadId.ToString(),
                                                "", DateTime.Now, DateTime.Now);
                response.Message = ex.Message;
            }
            return JsonConvert.SerializeObject(response);
        }

        private static void GetBookingDetailsForPcd(PcdData pcdData, DataRow dataRow)
        {
            pcdData.SupplierId = dataRow["SupplierId"] != DBNull.Value ? Convert.ToInt32(dataRow["SupplierId"]) : default;
            pcdData.SupplierName = dataRow["SupplierName"] != DBNull.Value ? Convert.ToString(dataRow["SupplierName"]) : default;
            pcdData.PlanId = dataRow["PlanID"] != DBNull.Value ? Convert.ToInt32(dataRow["PlanID"]) : default;
            pcdData.PlanName = dataRow["PlanName"] != DBNull.Value ? Convert.ToString(dataRow["PlanName"]) : default;
            pcdData.SumInsured = dataRow["SumInsured"] != DBNull.Value ? Convert.ToDecimal(dataRow["SumInsured"]) : default;
            pcdData.Premium = dataRow["TotalPremium"] != DBNull.Value ? Convert.ToDecimal(dataRow["TotalPremium"]) : default;
            pcdData.PolicyNumber = dataRow["PolicyNo"] != DBNull.Value ? Convert.ToString(dataRow["PolicyNo"]) : default;
            pcdData.ProductId = dataRow["productId"] != DBNull.Value ? Convert.ToInt32(dataRow["productId"]) : default;
            pcdData.PaymentFrequency = dataRow["PaymentPeriodicity"] != DBNull.Value ? Convert.ToInt32(dataRow["PaymentPeriodicity"]) : default;
            pcdData.PolicyTerm = dataRow["PolicyTerm"] != DBNull.Value ? Convert.ToInt32(dataRow["PolicyTerm"]) : default;
            pcdData.PremiumPayingTerm = dataRow["PayTerm"] != DBNull.Value ? Convert.ToInt32(dataRow["PayTerm"]) : default;
            pcdData.CommCityId = dataRow["CityID"] != DBNull.Value ? Convert.ToInt32(dataRow["CityID"]) : default;
            pcdData.CommStateId = dataRow["StateID"] != DBNull.Value ? Convert.ToInt32(dataRow["StateID"]) : default;
            pcdData.PolicyStartDate = (dataRow["PolicyStartDate"] != DBNull.Value && DateTime.TryParse(dataRow["PolicyStartDate"].ToString(), out DateTime policyStartDate)) ? policyStartDate.ToString("yyyy-MM-dd") : default;
            pcdData.PolicyEndDate = (dataRow["PolicyEndDate"] != DBNull.Value && DateTime.TryParse(dataRow["PolicyEndDate"].ToString(), out DateTime policyEndDate)) ? policyEndDate.ToString("yyyy-MM-dd") : default;
            pcdData.CommPinCode = (dataRow["PostCode"] != DBNull.Value && int.TryParse(Convert.ToString(dataRow["PostCode"]), out int postcode)) ? postcode : default;
        }

        private static void GetBookingAddInfo(PcdData pcdData, DataRow dataRow)
        {
            pcdData.VehicleDetails = new VehicleDetail
            {
                ChassisNumber = dataRow["ChassisNumber"] != DBNull.Value ? Convert.ToString(dataRow["ChassisNumber"]) : default,
                EngineNumber = dataRow["EngineNumber"] != DBNull.Value ? Convert.ToString(dataRow["EngineNumber"]) : default,
                RegistrationNo = dataRow["RegistrationNo"] != DBNull.Value ? Convert.ToString(dataRow["RegistrationNo"]) : default,
                VariantId = dataRow["VehicleVariant"] != DBNull.Value ? Convert.ToInt32(dataRow["VehicleVariant"]) : default,
                VariantName = dataRow["VehicleVariantName"] != DBNull.Value ? Convert.ToString(dataRow["VehicleVariantName"]) : default,
                ModelId = dataRow["VehicleModel"] != DBNull.Value ? Convert.ToInt32(dataRow["VehicleModel"]) : default,
                ModelName = dataRow["VehicleModelName"] != DBNull.Value ? Convert.ToString(dataRow["VehicleModelName"]) : default,
                MakeId = dataRow["MakeId"] != DBNull.Value ? Convert.ToInt32(dataRow["MakeId"]) : default,
                MakeName = dataRow["MakeName"] != DBNull.Value ? Convert.ToString(dataRow["MakeName"]) : default,
                FuelTypeName = dataRow["FuleType"] != DBNull.Value ? Convert.ToString(dataRow["FuleType"]) : default,
                ManufacturingDate = (dataRow["ManufactureDate"] != DBNull.Value && (DateTime.TryParse(dataRow["ManufactureDate"].ToString(), out DateTime manufacturingDate))) ? manufacturingDate.ToString("yyyy-MM-dd") : default
            };
        }

        public LeadDetailsForCustIdResponse GetLeadDetailsForCustId(long customerId)
        {
            var response = new LeadDetailsForCustIdResponse();
            try
            {
                var data = WebSiteServicDLL.GetLeadDetailsForCustId(customerId);

                if (data != null && data.Tables != null && data.Tables.Count > 0)
                {
                    response.LeadDetails = data.Tables[0].AsEnumerable().ToList().Select(dr => new LeadDetailsForCustId()
                    {
                        LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : default,
                        RenewalLeadId = dr["RenewalLeadId"] != DBNull.Value ? Convert.ToInt64(dr["RenewalLeadId"]) : default,
                        ProductId = dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : default
                    }).ToList();
                    response.Status = true;
                    response.Message = "Success";
                }
                else
                {
                    response.Status = false;
                    response.Message = "Data does not exists!";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, customerId, ex.Message,
                                                          "GetLeadDetailsForCustId", "MatrixCore",
                                                          "GetLeadDetailsForCustId", "",
                                                          ex.ToString(), DateTime.Now, DateTime.Now);
                response.Status = false;
                response.Message = ex.Message;
            }
            return response;
        }

        public CallDataResponse GetCallData(long leadId, long creationDate, string callType)
        {
            var response = new CallDataResponse();
            try
            {
                var ticketCreationDateTime = CoreCommonMethods.UnixTimeToDateTime(creationDate);
                var data = WebSiteServicDLL.GetCallData(leadId, ticketCreationDateTime, callType);

                if (data != null && data.Tables != null && data.Tables.Count > 0)
                {
                    response.CallDetails = data.Tables[0].AsEnumerable().ToList().Select(dr => new CallDetail()
                    {
                        LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : default,
                        Duration = dr["Duration"] != DBNull.Value ? Convert.ToInt32(dr["Duration"]) : default,
                        Talktime = dr["Talktime"] != DBNull.Value ? Convert.ToInt32(dr["Talktime"]) : default,
                        UserId = dr["UserId"] != DBNull.Value ? Convert.ToInt64(dr["UserId"]) : default,
                        CallDate = dr["CallDate"] != DBNull.Value ? Convert.ToString(dr["CallDate"]) : default
                    }).ToList();
                    response.Status = true;
                    response.Message = "Success";
                }
                else
                {
                    response.Status = false;
                    response.Message = "Data does not exists!";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadId, ex.Message,
                                                          "GetCallData", "MatrixCore",
                                                          "GetCallData", "",
                                                          ex.ToString(), DateTime.Now, DateTime.Now);
                response.Status = false;
                response.Message = ex.Message;
            }
            return response;
        }

        public LeadDeatils GetAllBasicLeadDetails(string LeadId, string Source, string EncKey, string EncIV)
        {
            string strexception = string.Empty;
            LeadDeatils LeadDetails = new LeadDeatils();
            Int64 LeadID = 0;
            try
            {
                //var leadid = Crypto.Encrytion_Payment_AES(LeadId, "Core", 256, 128, EncKey, EncIV, true);
                if (CoreCommonMethods.IsValidString(Source) && (Source.ToLower() == "matrix" || Source.ToLower() == "customerwhatsapp"))
                    LeadID = Convert.ToInt64(LeadId);
                else
                    LeadID = Convert.ToInt64(Crypto.Decrytion_Payment_AES(LeadId, "Core", 256, 128, EncKey, EncIV, true));

                DataSet oDataSet = LeadDetailsDLL.GetLeadDetails(LeadID, false);



                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {

                    LeadDetails = new LeadDeatils()
                    {
                        IsReferral = Source == "sme" && oDataSet.Tables[0].Rows[0]["IsReferral"] != DBNull.Value && Convert.ToBoolean(oDataSet.Tables[0].Rows[0]["IsReferral"]),
                        LeadSource = oDataSet.Tables[0].Rows[0]["LeadSource"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["LeadSource"]),
                        Gender = oDataSet.Tables[0].Rows[0]["Gender"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["Gender"]),
                        StatusId = oDataSet.Tables[0].Rows[0]["LeadStatusId"] != DBNull.Value ? Convert.ToInt16(oDataSet.Tables[0].Rows[0]["LeadStatusId"]) : default,
                        StatusName = oDataSet.Tables[0].Rows[0]["LeadStatusName"] != DBNull.Value ? Convert.ToString(oDataSet.Tables[0].Rows[0]["LeadStatusName"]) : default,
                        IsFos = oDataSet.Tables[0].Rows[0]["Isfos"] != DBNull.Value && Convert.ToBoolean(oDataSet.Tables[0].Rows[0]["Isfos"]),
                        BookingFraud = (oDataSet.Tables[0].Rows[0]["ProductID"] != DBNull.Value && Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"]) == 2) ? CheckHealthFraud(oDataSet, EncKey, EncIV) : Convert.ToInt16(0),
                        ParentLeadId = oDataSet.Tables[0].Rows[0]["ParentId"] != DBNull.Value ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentId"]) : default,
                        IsAssigned = oDataSet.Tables[0].Rows[0]["IsAssigned"] != DBNull.Value && Convert.ToBoolean(oDataSet.Tables[0].Rows[0]["IsAssigned"]),
                        //PlanName = oDataSet.Tables[0].Rows[0]["SelectedPlanName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["SelectedPlanName"]),
                        //UTMCampaign = oDataSet.Tables[0].Rows[0]["Utm_campaign"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Utm_campaign"]),
                        //UTMSource = oDataSet.Tables[0].Rows[0]["Utm_source"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Utm_source"]),
                        //UTMTerm = oDataSet.Tables[0].Rows[0]["Utm_term"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Utm_term"]),
                        //UTMMedium = oDataSet.Tables[0].Rows[0]["UTM_Medium"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["UTM_Medium"]),
                        //Name = oDataSet.Tables[0].Rows[0]["Name"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["Name"]),
                        //DOB = oDataSet.Tables[0].Rows[0]["DOB"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["DOB"]),
                        //MobileNo = oDataSet.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? "" : Crypto.Encrytion_Payment_AES(Convert.ToString(oDataSet.Tables[0].Rows[0]["MobileNo"]), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false),
                        //EmailId = oDataSet.Tables[0].Rows[0]["EmailID"] == DBNull.Value ? "" : Crypto.Encrytion_Payment_AES(Convert.ToString(oDataSet.Tables[0].Rows[0]["EmailID"]), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings(), false),
                        CityID = (oDataSet.Tables[0].Rows[0]["CityID"] != null && oDataSet.Tables[0].Rows[0]["CityID"] != DBNull.Value) ? Convert.ToInt16(oDataSet.Tables[0].Rows[0]["CityID"]) : 0,
                        StateID = (oDataSet.Tables[0].Rows[0]["StateID"] != null && oDataSet.Tables[0].Rows[0]["StateID"] != DBNull.Value) ? Convert.ToInt16(oDataSet.Tables[0].Rows[0]["StateID"]) : 0,
                        ProductID = oDataSet.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? 0 : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"]),
                        CustomerID = oDataSet.Tables[0].Rows[0]["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["CustomerID"]),
                        //SupplierId = oDataSet.Tables[0].Rows[0]["SupplierId"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["SupplierId"]),
                        //PlanId = oDataSet.Tables[0].Rows[0]["PlanId"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["PlanId"]),
                        //SupplierName = oDataSet.Tables[0].Rows[0]["SupplierName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["SupplierName"]),
                        //PolicyNo = oDataSet.Tables[0].Rows[0]["PolicyNo"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["PolicyNo"]),
                        PolicyExpiryDate = (oDataSet.Tables[0].Rows[0]["PolicyExpiryDate"] != null && oDataSet.Tables[0].Rows[0]["PolicyExpiryDate"] != DBNull.Value) ? Convert.ToDateTime(oDataSet.Tables[0].Rows[0]["PolicyExpiryDate"]) : DateTime.MinValue
                    };
                }

            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId, Convert.ToInt64(LeadId), strexception, "GetAllBasicLeadDetails", "MatrixCore", "WebsiteServiceBLL", LeadId, strexception, DateTime.Now, DateTime.Now);
            }

            return LeadDetails;
        }

        public int getPreBookingTalkTime(long LeadId)
        {
            DateTime dt = DateTime.Now;
            int TotalTalkTime = -1;
            try
            {
                TotalTalkTime = WebSiteServicDLL.getPreBookingTalkTime(LeadId);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.Message, "getPreBookingTalkTime", "MatrixCore", "BLL", LeadId.ToString(), "", dt, DateTime.Now);
            }
            return TotalTalkTime;


        }

        public bool AssignLead(long LeadID, string EmpCode, string Source, Int16 LeadType, String CallID, string callDate, string TransferType)
        {
            bool result = false;
            string error = string.Empty;
            Int16 CallType = 0;
            DateTime dt = DateTime.Now;
            StringBuilder sb = new();
            sb.Append("EmpCode" + EmpCode + " ,Source:" + Source + " ,LeadType:" + LeadType + " CallID:" + CallID + " callDate" + callDate + "TransferType:" + TransferType);

            try
            {
                if (!string.IsNullOrEmpty(Source) && !(Source.ToUpper() == "RENEWAL CALLBACK"))
                {
                    switch (Source.ToUpper())
                    {
                        case "INBOUND":
                            CallType = 1;
                            break;
                        case "IB":
                            CallType = 1;
                            break;
                        case "CTC":
                            CallType = 2;
                            break;
                        case "C2C":
                            CallType = 2;
                            break;
                        case "PDOB":
                            CallType = 3;
                            break;
                        case "CTCSCHEDULAR":
                            CallType = 4;
                            break;
                        default:
                            CallType = 0;
                            break;
                    }
                    //CallType = (short)((!string.IsNullOrEmpty(Source) && Source.ToUpper() == "INBOUND" || Source.ToUpper() == "CTC" || Source.ToUpper() == "C2C") ? 1 : 0);

                    if (WebSiteServicDLL.AssignLead(LeadID, EmpCode, CallType, TransferType))
                    {
                        DataSet oDataSet = LeadDetailsDLL.GetLeadDetails(LeadID, false);

                        if ((CallType == 1 || CallType == 2) && oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                        {
                            long ParenId = oDataSet.Tables[0].Rows[0]["ParentId"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentId"]);
                            short ProductID = oDataSet.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"]);
                            DialerDispDetails _DispositionUpdate = new()
                            {
                                CallId = CallID,
                                ParentID = ParenId,
                                ProductID = ProductID,
                                AgentCode = EmpCode,
                                IsBMS = false,
                                Status = "16",
                                CallType = Source.ToUpper() == "INBOUND" ? "IB" : Source.ToUpper(),
                                callDate = Convert.ToDateTime(callDate)
                            };
                            WebSiteServicDLL.InsertCallData(_DispositionUpdate);
                            if (!string.IsNullOrEmpty(EmpCode))
                            {
                                Int64 UserId = LeadDetailsDLL.GetUserId(EmpCode);

                                UserNext5Leads oUserNext5Leads = LeadPrioritizationDLL.GetUserNxt5LeadsFromMongo(UserId);

                                if (LeadID > 0 && oUserNext5Leads != null && oUserNext5Leads.Leads != null && oUserNext5Leads.Leads.Count > 0)
                                {

                                    Next5WidgetLead _Next5WidgetLead = oUserNext5Leads.Leads.Where(x => x.LeadId == LeadID).FirstOrDefault();
                                    if(_Next5WidgetLead!=null)
                                    {
                                        _Next5WidgetLead.CustConnectTime = dt;
                                        LeadPrioritizationDLL.UpdateNext5Leads(UserId, oUserNext5Leads);
                                    }
                                  
                                }
                            }
                        }

                    }
                }
                result = true;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadID, error, "AssignLead", "MatrixCore", "BLL", sb.ToString(), result.ToString(), dt, DateTime.Now);
            }
            return result;
        }

        public bool SendCustCommtoUnansLeads()
        {
            DateTime requestTime = DateTime.Now;
            sendcommunicationResponse osendcommunicationResponse = null;
            string Error = string.Empty;
            string LeadId = null;
            bool result = false;
            StringBuilder sb = new StringBuilder();
            try
            {
                DataSet ds = WebSiteServicDLL.GetUnansweredCommLeads();



                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {

                    foreach (DataRow row in ds.Tables[0].Rows)
                    {

                        LeadId = row["LeadId"] != null && row["LeadId"] != DBNull.Value ? Convert.ToString(row["LeadId"]) : string.Empty;
                        string MobileNo = row["MobileNo"] != null && row["MobileNo"] != DBNull.Value ? Convert.ToString(row["MobileNo"]) : string.Empty;
                        string CountryCode = row["CountryCode"] != null && row["CountryCode"] != DBNull.Value ? Convert.ToString(row["CountryCode"]) : string.Empty;
                        string Template = row["Template"] != null && row["Template"] != DBNull.Value ? Convert.ToString(row["Template"]) : string.Empty;
                        string ProductId = row["ProductId"] != null && row["ProductId"] != DBNull.Value ? Convert.ToString(row["ProductId"]) : string.Empty;
                        string InputURL = row["ExitPointURL"] != null && row["ExitPointURL"] != DBNull.Value ? Convert.ToString(row["ExitPointURL"]) : "TermExitPointURL".AppSettings();
                        string UpdatedTemplate = string.IsNullOrEmpty(Template) ? "3" : (CountryCode == "91" ? "2" : "1");
                        string Trigger = UpdatedTemplate == "1" ? "TI_MTX_WA_Connected_WhypbforNRI" : (UpdatedTemplate == "2" ? "TI_MTX_WA_Not_Connected_ContinueJourney" : (UpdatedTemplate == "3" ? "TI_MTX_WA_NotConnected_CallbackorSchedule" : ""));

                        sb.Append(" LeadID - " + LeadId);
                        if (CoreCommonMethods.IsValidInteger(LeadId) > 0 && CoreCommonMethods.IsValidInteger(MobileNo) > 0 && CoreCommonMethods.IsValidInteger(CountryCode) > 0 && CoreCommonMethods.IsValidInteger(ProductId) > 0)
                        {
                            string UpdatedInputURL = UpdateWithLoadType(InputURL);
                            osendcommunicationResponse = new sendcommunicationResponse()
                            {
                                LeadId = Convert.ToInt64(LeadId),
                                MobileNo = Convert.ToInt64(MobileNo),
                                CountryCode = Convert.ToInt16(CountryCode),
                                ProductId = Convert.ToInt16(ProductId),
                                CommunicationType = 8,
                                TriggerName = Trigger,
                                InputData = new Inputdata()
                                { DynamicURL = UpdatedInputURL }

                            };
                            if (WebSiteServicDLL.SendCommunicationtoUnAnsLeads(osendcommunicationResponse))
                            {
                                result = WebSiteServicDLL.UpdateUnansweredCommLeads(Convert.ToInt64(LeadId), Convert.ToInt32(UpdatedTemplate));

                            }
                        }
                    }

                }
            }

            catch (Exception ex)
            {

                Error = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, Error, "SendCustCommtoUnansLeads", "MatrixCore", "WebSiteServiceBLL", sb.ToString(), string.Empty, requestTime, DateTime.Now);
            }


            return result;
        }

        public string UpdateWithLoadType(string Inputurl)
        {
            string AppendURL = "?loadType=whatsappterm";

            if (string.IsNullOrEmpty(Inputurl))
            {
                return AppendURL;
            }
            string url = Inputurl.IndexOf('?') > -1 ? Inputurl + "&loadType=whatsappterm" : Inputurl + AppendURL;
            if (url.Contains("https://termlife.policybazaar.com/quotes/"))
            {
                url = url.Replace("https://termlife.policybazaar.com/quotes/", "");
                return url;
            }
            else
            {
                return AppendURL;
            }


        }
        public UrlResponse GetExitPointURL(long LeadId, long CustomerId, short ProductId)
        {
            UrlResponse response = new() { StatusMessage = "Not Found" };
            try
            {
                DataSet oDataSet = WebSiteServicDLL.GetExitPointURL(LeadId, CustomerId, ProductId);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    string ExitPointURL = oDataSet.Tables[0].Rows[0]["ExitPointURL"] != null && oDataSet.Tables[0].Rows[0]["ExitPointURL"] != DBNull.Value ? Convert.ToString(oDataSet.Tables[0].Rows[0]["ExitPointURL"]) : String.Empty;

                    if (!string.IsNullOrEmpty(ExitPointURL))
                    {
                        response.ExitPointURL = ExitPointURL;
                        response.StatusMessage = "Success";
                    }
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.Message,
                                                          "GetExitPointURL", "MatrixCore",
                                                          "WebSiteServiceBLL", "",
                                                          "", DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public AgentProfileData GetAgentProfileData(long LeadID)
        {
            AgentProfileData response = new AgentProfileData();
            DateTime reqdate = DateTime.Now;
            string url = "GetRealTimeStatusURL".AppSettings();
            try
            {
                DataSet data = WebSiteServicDLL.GetAgentProfileDataDLL(LeadID);

                if (data != null && data.Tables != null && data.Tables.Count > 0)
                {
                    if (data.Tables[0].Rows.Count > 0)
                    {
                        response.AgentName = data.Tables[0].Rows[0]["AgentName"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["AgentName"]);
                        response.AgentEmployeeId = data.Tables[0].Rows[0]["AgentEmployeeId"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["AgentEmployeeId"]);
                        response.AgentProfileImage = data.Tables[0].Rows[0]["AgentProfileImage"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["AgentProfileImage"]);
                        response.PreferredLanguage = data.Tables[0].Rows[0]["PreferredLanguage"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["PreferredLanguage"]);
                        response.TotalPoliciesSold = data.Tables[0].Rows[0]["TotalPoliciesSold"] == DBNull.Value ? 0 : Convert.ToInt32(data.Tables[0].Rows[0]["TotalPoliciesSold"]);
                        response.Tenure = data.Tables[0].Rows[0]["Tenure"] == DBNull.Value ? 0 : Convert.ToInt32(data.Tables[0].Rows[0]["Tenure"]);
                        response.Rewards = data.Tables[0].Rows[0]["Rewards"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["Rewards"]);
                        response.IsAVCertified = data.Tables[0].Rows[0]["IsAVCertified"] == DBNull.Value ? 0 : Convert.ToInt16(data.Tables[0].Rows[0]["IsAVCertified"]);
                        response.IsFOS = data.Tables[0].Rows[0]["IsFOS"] == DBNull.Value ? 0 : Convert.ToInt16(data.Tables[0].Rows[0]["IsFOS"]);
                        response.TotalSumInsured = data.Tables[0].Rows[0]["TotalSumInsured"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(data.Tables[0].Rows[0]["TotalSumInsured"]);
                        if (!string.IsNullOrEmpty(response.AgentEmployeeId))
                        {
                            url = url + response.AgentEmployeeId;
                            var result = CommonAPICall.CallAPI(url, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
                            if (!string.IsNullOrEmpty(result))
                            {
                                var d = JsonConvert.DeserializeObject<dynamic>(result);
                                response.AgentStatus = d.Count > 0 ? Convert.ToString(d[0].Status) : "";
                            }
                        }
                        response.Gender = data.Tables[0].Rows[0]["Gender"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["Gender"]);
                    }
                    if (data.Tables[1].Rows.Count > 0)
                    {
                        response.AssignmentDetailsData = data.Tables[1].AsEnumerable().ToList().Select(dr => new AssignmentDetails()
                        {
                            LeadID = dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : default,
                            AgentUserId = dr["AgentUserId"] != DBNull.Value ? Convert.ToInt64(dr["AgentUserId"]) : default,
                            AgentName = dr["AgentName"] != DBNull.Value ? Convert.ToString(dr["AgentName"]) : default,
                            AgentEmpId = dr["AgentEmployeeId"] != DBNull.Value ? Convert.ToString(dr["AgentEmployeeId"]) : default,
                            IsLastAssigned = dr["IsLastAssigned"] != DBNull.Value ? Convert.ToBoolean(dr["IsLastAssigned"]) : default,
                            AssignmentTime = dr["AssignmentTime"] != DBNull.Value ? Convert.ToDateTime(dr["AssignmentTime"]) : default,
                            AgentProfileImage = dr["AgentProfilePhoto"] != DBNull.Value ? Convert.ToString(dr["AgentProfilePhoto"]) : default
                        }).ToList();
                    }
                    if (data.Tables[2].Rows.Count > 0)
                    {
                        response.CallDetailsData = data.Tables[2].AsEnumerable().ToList().Select(dr => new CallDetails()
                        {
                            LeadID = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : default,
                            CallDuration = dr["Duration"] != DBNull.Value ? Convert.ToInt32(dr["Duration"]) : default,
                            Talktime = dr["talktime"] != DBNull.Value ? Convert.ToInt32(dr["talktime"]) : default,
                            AgentUserId = dr["AgentUserId"] != DBNull.Value ? Convert.ToInt64(dr["AgentUserId"]) : default,
                            CallDateTime = dr["CallDate"] != DBNull.Value ? Convert.ToDateTime(dr["CallDate"]) : default
                        }).ToList();
                    }
                    response.Status = true;
                    response.Message = "Success";
                }
                else
                {
                    response.Status = false;
                    response.Message = "Data does not exist!";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadID.ToString(), LeadID, ex.Message, "GetAgentProfileData", "MatrixCore", "GetAgentProfileData", LeadID.ToString(), ex.ToString(), reqdate, DateTime.Now);
                response.Status = false;
                response.Message = ex.Message;
            }
            return response;
        }


        public ResponseAPI GetCityIdByPincode(int Pincode, long LeadId)
        {
            DateTime dt = DateTime.Now;
            ResponseAPI oResponseAPI = new() { status = false, message = "Invalid pincode" };
            try
            {
                DataSet oDataSet = WebSiteServicDLL.GetCityIdByPincode(Pincode, LeadId);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    string CityID = oDataSet.Tables[0].Rows[0]["CityID"] != null && oDataSet.Tables[0].Rows[0]["CityID"] != DBNull.Value ? Convert.ToString(oDataSet.Tables[0].Rows[0]["CityID"]) : String.Empty;

                    if (CoreCommonMethods.IsValidInteger(CityID) > 0)
                    {
                        oResponseAPI.status = true;
                        oResponseAPI.message = "";
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "GetCityIdByPincode", "MatrixCore", "FOSBLL", Pincode.ToString(), JsonConvert.SerializeObject(oResponseAPI), dt, DateTime.Now);
            }
            return oResponseAPI;
        }



        public EmployeeDetails GetAgentDetails(string userId, StringValues? encKey, StringValues? encIV)
        {
            var response = new EmployeeDetails();
            try
            {
                if (!string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(encKey) && !string.IsNullOrEmpty(encIV))
                {
                    userId = Crypto.Decrytion_Payment_AES(userId, "Core", 256, 128, encKey, encIV, true);
                    if (!string.IsNullOrEmpty(userId))
                    {
                        DataSet data = WebSiteServicDLL.GetAgentDetails(Convert.ToInt64(userId));
                        if (data != null && data.Tables != null && data.Tables.Count > 0)
                        {
                            foreach (DataRow row in data.Tables[0].Rows)
                            {
                                response.EmployeeId = row["EmployeeId"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(row["EmployeeId"].ToString(), "Core", 256, 128, encKey, encIV, true) : default;
                                response.EmailId = row["Email"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(row["Email"].ToString(), "Core", 256, 128, encKey, encIV, true) : default;
                                response.UserName = row["UserName"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(row["UserName"].ToString(), "Core", 256, 128, encKey, encIV, true) : default;
                                response.ContactNo = row["ContactNo"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(row["ContactNo"].ToString(), "Core", 256, 128, encKey, encIV, true) : default;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(userId, 0, ex.Message, "GetAgentDetails", "MatrixCore", "GetAgentDetails", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public LeadDeatils GetParentLeadSource(string leadId)
        {
            var leadDetails = new LeadDeatils();
            try
            {
                if (!string.IsNullOrEmpty(leadId))
                {
                    DataSet data = WebSiteServicDLL.GetLeadDetails(Convert.ToInt64(leadId), false);

                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        leadDetails.ParentLeadSource = data.Tables[0].Rows[0]["ParentLeadSource"] == DBNull.Value ? default : Convert.ToString(data.Tables[0].Rows[0]["ParentLeadSource"]);
                        leadDetails.ParentLeadId = data.Tables[0].Rows[0]["ParentId"] == DBNull.Value ? default : Convert.ToInt64(data.Tables[0].Rows[0]["ParentId"]);
                        leadDetails.UTMSource = data.Tables[0].Rows[0]["Utm_source"] == DBNull.Value ? default : Convert.ToString(data.Tables[0].Rows[0]["Utm_source"]);
                        leadDetails.UTMMedium = data.Tables[0].Rows[0]["UTM_Medium"] == DBNull.Value ? default : Convert.ToString(data.Tables[0].Rows[0]["UTM_Medium"]);
                        leadDetails.UTMCampaign = data.Tables[0].Rows[0]["Utm_campaign"] == DBNull.Value ? default : Convert.ToString(data.Tables[0].Rows[0]["Utm_campaign"]);
                        leadDetails.UTMTerm = data.Tables[0].Rows[0]["Utm_term"] == DBNull.Value ? default : Convert.ToString(data.Tables[0].Rows[0]["Utm_term"]);
                        leadDetails.LeadCreatedOn = data.Tables[0].Rows[0]["CreatedOn"] == DBNull.Value ? default : Convert.ToDateTime(data.Tables[0].Rows[0]["CreatedOn"]);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId, 0, ex.Message, "GetParentLeadSource", "WebSiteServiceBLL", "MatrixCore", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return leadDetails;
        }

        public List<StoreDetails> GetAvailableStore(string CityID)
        {
            DateTime ReqDT = DateTime.Now;
            List<StoreDetails> objList = new List<StoreDetails>();
            try
            {
                if (!string.IsNullOrEmpty(CityID))
                {
                    DataSet data = WebSiteServicDLL.GetStoreDetails(Convert.ToInt32(CityID));

                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        objList = (from dr in data.Tables[0].AsEnumerable()
                                   select new StoreDetails
                                   {
                                       CityID = dr["CityID"] != null && dr["CityID"] != DBNull.Value ? Convert.ToInt32(dr["CityID"]) : 0,
                                       StoreName = dr["StoreName"] != null && dr["StoreName"] != DBNull.Value ? Convert.ToString(dr["StoreName"]) : "",
                                       StoreAddress = dr["StoreAddress"] != null && dr["StoreAddress"] != DBNull.Value ? Convert.ToString(dr["StoreAddress"]) : "",
                                       Longitude = dr["Longitude"] != null && dr["Longitude"] != DBNull.Value ? Convert.ToDouble(dr["Longitude"]) : 0,
                                       Latitude = dr["Latitude"] != null && dr["Latitude"] != DBNull.Value ? Convert.ToDouble(dr["Latitude"]) : 0,
                                       InboundNumber = dr["InboundNumber"] != null && dr["InboundNumber"] != DBNull.Value ? Convert.ToInt64(dr["InboundNumber"]) : 0
                                   }).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CityID, 0, ex.Message, "GetAvailableStore", "WebSiteServiceBLL", "MatrixCore", CityID, string.Empty, ReqDT, DateTime.Now);
            }
            return objList;
        }
        public CreateLeadRequest UpdateSMEFields(CreateLeadRequest request)
        {
            try
            {
                if (request.RefLeadIdSme > 0 && request.IsSmeFosCreateLead)
                {
                    var datasme = SMEDLL.GetMyLeadsData(request.UserId, request.RefLeadIdSme);
                    if (datasme.Tables.Count > 0 && datasme.Tables[0].Rows.Count > 0)
                    {
                        var row = datasme.Tables[0].Rows[0];

                        request.MobileNo = row["MobileNo"] != DBNull.Value ? Convert.ToString(row["MobileNo"]) : request.MobileNo;
                        request.Email = row["EmailId"] != DBNull.Value ? Convert.ToString(row["EmailId"]) : request.Email;

                        if (datasme != null && datasme.Tables != null && datasme.Tables.Count > 1 && datasme.Tables[1] != null && datasme.Tables[1].Rows !=null && datasme.Tables[1].Rows.Count > 0)
                        {
                            request.AltContactInformation = datasme.Tables[1].AsEnumerable()
                                .Select(dr => new AltContactInformationDetails
                                {
                                    PrimaryId = 0,
                                    AltContactPersonName = dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : null,
                                    AltMobileNo = dr["MobileNo"] != DBNull.Value ? (Convert.ToString(dr["MobileNo"])) : null,
                                    AltEmailId = dr["EmailId"] != DBNull.Value ? (Convert.ToString(dr["EmailId"])) : null
                                }).ToList();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    request.MobileNo,
                    0,
                    ex.ToString(),
                    "UpdateSMEFields",
                    "Matrixcore",
                    "WebSiteServiceBLL",
                    JsonConvert.SerializeObject(request),
                    ex.StackTrace,
                    DateTime.Now,
                    DateTime.Now
                );
            }

            return request;
        }

        public CreateLeadResponse CreateLead(CreateLeadRequest request)
        {
            var reqDate = DateTime.Now;
            var response = new CreateLeadResponse();
            var logs = new StringBuilder();
            CreateLeadDetails leadDetails = null;
            string error = string.Empty;
            string TrackingId = string.Empty;
            bool IsFOSIbProcess = (request!=null && request.VirtualNo == "FOSIbProcessVirtualNo".AppSettings()) ? true: false;
            try
            {
                TrackingId = request.ReferralLead.ToString();
                if (request.RefLeadIdSme > 0 && request.IsSmeFosCreateLead)
                {
                    request = UpdateSMEFields(request);
                }
                if (request.IsDialer)
                {
                    long CustomerId = request.CustomerId;
                    if (CustomerId > 0)
                    {
                        var datalead = WebSiteServicDLL.GetLeadMobileNoByCustId(CustomerId);
                        if (datalead != null && datalead.Tables != null
                            && datalead.Tables.Count > 0 && datalead.Tables[0].Rows.Count > 0)
                        {
                            if (datalead.Tables[0].Rows[0]["InvalidMobile"].ToString() == "")
                            {
                                request.MobileNo = datalead.Tables[0].Rows[0]["MobileNo"].ToString();
                            }
                        }
                    }
                   

                        if (request.ProductId == 131 && !string.IsNullOrEmpty(request.VirtualNo))
                    {
                        DataTable dt = WebSiteServicDLL.GetSubProductByVN(request.VirtualNo);
                        if (dt != null && dt.Rows != null && dt.Rows.Count > 0)
                        {
                            request.SubProductIdList = string.Join(",",
                                                    (from dr in dt.AsEnumerable()
                                                     where dr["SubProductId"] != DBNull.Value
                                                     select dr["SubProductId"].ToString())
                                                    .ToArray());
                        }
                    }
                    request.MobileNo = request.MobileNo.Length > 10 ? request.MobileNo.Remove(0, request.MobileNo.Length - 10) : request.MobileNo;
                    request.MobileNo = request.MobileNo.Contains("+") ? request.MobileNo.Replace('+', ' ').Trim() : request.MobileNo;
                    if (request.LeadId > 0)
                    {
                        if (!string.IsNullOrEmpty(request.DialerUniqueId))
                        {
                            WebSiteServicDLL.InsertDialerDataLeadDataMapping(request.LeadId, request.DialerUniqueId, Convert.ToInt64(request.MobileNo), true);
                        }

                        response.LeadId = request.LeadId;
                        response.IsLeadCreated = false;
                        response.Message = "Input Lead Returned";
                        return response;
                    }
                    else
                    {
                        List<string> validLeadSource = "ValidLeadSource".AppSettings()?.ToLower().Split(',').ToList();
                        leadDetails = new CreateLeadDetails
                        {
                            Name = "Inbound",
                            LeadSource = validLeadSource.Contains(request.LeadSource.ToLower()) ? request.LeadSource : "Inbound",
                            CountryID = GetCountryID(request.CountryId),
                            StateID = 0,
                            CityID = 0,
                            AnnualIncomeID = 0,
                            GenderID = 1,
                            Filter = "L",
                            DateOfBirth = "01-01-1900",
                            EmailID = string.Empty,
                            UTM_Source = request.UtmSource,
                            Utm_Campaign = request.UtmCampaign,
                            Utm_Medium = request.UtmMedium,
                            Utm_Term = request.UtmTerm,
                            Source = string.IsNullOrEmpty(request.Source) ? "Inbound" : request.Source,
                            MobileNo = request.MobileNo,
                            ProductID = request.ProductId,
                            SubProductID = request.SubProductId,
                            CompanyName = request.CompanyName,
                            OccupationId = request.OccupationId,
                            AssignLead = request.AssignLead,
                            SubProductIdList= request.SubProductIdList,
                        };

                        request.CountryId = leadDetails.CountryID;
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(request.CallTransferType) && request.CallTransferType == "SmeInterTeamTransfer")
                    {
                        /*Inter team call transfer ,on the basis of ReferralLead basic details is fetch*/
                        var dr = WebSiteServicDLL.GetLeadBasicInfo(request.ReferralLead);
                        if (dr != null)
                        {
                            request.Name = dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : default;
                            request.MobileNo = dr["MobileNo"] != DBNull.Value ? Convert.ToString(dr["MobileNo"]) : default;
                            request.Email = dr["EmailID"] != DBNull.Value ? Convert.ToString(dr["EmailID"]) : default;
                            request.LeadSource = dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : default;
                            request.CountryId = dr["Country"] != DBNull.Value ? Convert.ToInt32(dr["Country"]) : default;
                            request.UtmSource = dr["Utm_source"] != DBNull.Value ? Convert.ToString(dr["Utm_source"]) : default;
                            request.UtmMedium = dr["UTM_Medium"] != DBNull.Value ? Convert.ToString(dr["UTM_Medium"]) : default;
                            request.UtmTerm = dr["Utm_term"] != DBNull.Value ? Convert.ToString(dr["Utm_term"]) : default;
                            request.UtmCampaign = dr["Utm_campaign"] != DBNull.Value ? Convert.ToString(dr["Utm_campaign"]) : default;
                        }
                    }
                    if (!string.IsNullOrEmpty(request.CallTransferType) && request.CallTransferType == "renewaltofreshreferrals")
                    {
                        /*Inter team call transfer ,on the basis of ReferralLead basic details is fetch*/
                        var dr = WebSiteServicDLL.GetLeadBasicInfo(request.ReferralLead);
                        if (dr != null)
                        {
                            request.Name = dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : default;
                            request.MobileNo = dr["MobileNo"] != DBNull.Value ? Convert.ToString(dr["MobileNo"]) : default;
                            request.Email = dr["EmailID"] != DBNull.Value ? Convert.ToString(dr["EmailID"]) : default;
                            request.LeadSource = "Referral";
                            request.CountryId = dr["Country"] != DBNull.Value ? Convert.ToInt32(dr["Country"]) : default;
                            request.UtmCampaign = "HotCallReferrals";
                        }
                    }

                    if (!string.IsNullOrEmpty(request.UtmSource) &&
                        request.UtmSource.ToLower() == "fos_referral"
                        && request.ProductId == 2)
                    {
                        /*Inter team call transfer ,on the basis of ReferralLead basic details is fetch*/
                        var dr = WebSiteServicDLL.GetLeadBasicInfo(request.ReferralLead);
                        if (dr != null)
                        {
                            if (!CoreCommonMethods.IsValidString(request.UtmMedium))
                            {
                                request.UtmMedium = dr["LeadSource"] != DBNull.Value && Convert.ToString(dr["LeadSource"]).ToLower() == "renewal" ? "Health_Renewal" : default;
                            }
                        }
                    }
                    if (request.ProductId == 7)
                    {
                        var dr = WebSiteServicDLL.GetLeadBasicInfo(request.ReferralLead);
                        if (dr != null)
                        {
                            if (dr["LeadSource"].ToString().Equals("Reopen")
                              && dr["Utm_source"].ToString().ToUpper().Contains("TERM_WIFE_UPSELL"))
                            {
                                request.UtmTerm = "Term_Wife_upsell";
                                request.UtmMedium = dr["UTM_Medium"] != DBNull.Value ? Convert.ToString(dr["UTM_Medium"]) : default;
                            }
                        }
                    }

                    leadDetails = new CreateLeadDetails
                    {
                        Name = request.Name,
                        ProductID = request.ProductId,
                        CountryID = request.CountryId > 0 ? request.CountryId : 392,
                        EmailID = request.Email,
                        UTM_Source = request.UtmSource,
                        Utm_Campaign = request.UtmCampaign,
                        Utm_Medium = request.UtmMedium,
                        Utm_Term = request.UtmTerm,
                        LeadSource = request.LeadSource,
                        MobileNo = request.MobileNo,
                        SubProductID = request.SubProductId,
                        ReferralLead = request.ReferralLead,
                        GenderID = request.Gender,
                        StateID = request.StateID,
                        DateOfBirth = request.DateofBirth,
                        AnnualIncome = request.AnnualIncome,
                        Source = string.IsNullOrEmpty(request.Source) ? "" : request.Source,
                        CityID = request.CityID,
                        CompanyName = request.CompanyName,
                        OccupationId = request.OccupationId,
                        AssignLead = request.AssignLead,
                        AssignToUserId = request.AssignedUser,
                        AssignTogroupID = request.AssignedGroupId,
                        SubProductIdList = request.SubProductIdList,
                        AssociationId = request.AssociationId,
                        MaxStatusID = request.MaxStatusID
                    };
                }
                request = UpdateRequestInput(request, logs);
                leadDetails.MobileNo = request.MobileNo;

                if (string.IsNullOrEmpty(TrackingId) || TrackingId.Equals("0"))
                {
                    TrackingId = leadDetails.MobileNo;
                }

                if (request.IsDialer)
                {
                    //For call transfer from sme set the utm source for health and newcar
                    if (request.QueueName == "smemoc")
                    {
                        leadDetails.UTM_Source = "MotorOnCorp";
                        leadDetails.LeadSource = "CrossSell";
                    }
                    if (request.QueueName == "smehoc")
                    {
                        leadDetails.UTM_Source = "HealthOnCorp";
                        leadDetails.LeadSource = "CrossSell";
                    }
                }

                CreateLead(logs, request, response, leadDetails);

                DataSet data = null;

                if (request.ProductId == 131)
                {
                    var isDialer = request.IsDialer && request.ProductId == 131 && !string.IsNullOrEmpty(request.VirtualNo) && response.LeadId > 0;
                    var isWhattsapp = request.ApiSource == "whatsapp" && request.ProductId == 131 && !string.IsNullOrEmpty(request.VirtualNo) && response.LeadId > 0;

                    if ((isDialer || isWhattsapp))
                    {
                        // * In SME virtual No is mapped  against  agent */
                        data = WebSiteServicDLL.GetAgentDetailsByVirtualNo(request.VirtualNo);
                    }

                    if (isWhattsapp)
                    {
                        if (data != null && data.Tables != null && data.Tables[0] != null && data.Tables[0].Rows.Count > 0)
                        {
                            DataRow row = data.Tables[0].Rows[0];
                            var userId = data.Tables[0].Rows[0]["UserId"] != DBNull.Value ? Convert.ToInt64(data.Tables[0].Rows[0]["UserId"]) : default;
                            WebSiteServicDLL.AssignLeadToAgent(response.LeadId, userId, 124, request.ProductId, 0, 76);
                        }
                    }
                    else if (isDialer)
                    {
                        if (data != null && data.Tables != null && data.Tables[0] != null && data.Tables[0].Rows.Count > 0)
                        {
                            DataRow row = data.Tables[0].Rows[0];
                            response.AgentDetails = new VirtualNoAgentDetails()
                            {
                                DIDNo = row["DIDNo"] != DBNull.Value ? row["DIDNo"].ToString() : default,
                                IsWFH = row["IsWFH"] != DBNull.Value && Convert.ToBoolean(row["IsWFH"]),
                                EmployeeId = row["EmployeeId"] != DBNull.Value ? row["EmployeeId"].ToString() : default,
                                IsLoggedIn = row["IsLoggedIn"] != DBNull.Value && Convert.ToBoolean(row["IsLoggedIn"]),
                                Queue = row["Queues"] != DBNull.Value ? row["Queues"].ToString() : default                                
                            };
                        }
                    }
                }
                else if (request.IsDialer)
                {
                    if (!string.IsNullOrEmpty(request.VirtualNo) && !IsFOSIbProcess)
                    {
                        DataSet data1 = WebSiteServicDLL.GetAgentDetailsByVirtualNo(request.VirtualNo);
                        if(data1 != null && data1.Tables.Count > 0 && data1.Tables[0].Rows.Count > 0)
                        {
                            DataRow row = data1.Tables[0].Rows[0];
                            response.AgentDetails = new VirtualNoAgentDetails()
                            {
                                DIDNo = row["DIDNo"] != DBNull.Value ? row["DIDNo"].ToString() : default,
                                IsWFH = row["IsWFH"] != DBNull.Value && Convert.ToBoolean(row["IsWFH"]),
                                EmployeeId = row["EmployeeId"] != DBNull.Value ? row["EmployeeId"].ToString() : default,
                                IsLoggedIn = row["IsLoggedIn"] != DBNull.Value && Convert.ToBoolean(row["IsLoggedIn"]),
                                Queue = row["Queues"] != DBNull.Value ? row["Queues"].ToString() : default
                            };
                        }
                    }
                    else
                    {
                        response.AgentDetails = GetQueueDetailsForDailer(response.LeadId, request.ProductId, request.SubProductId, request.LeadSource, IsFOSIbProcess);
                    }

                    /*experiment if dnc customer choose inbound then subscribe the customer for calling*/
                    if (request.ProductId == 7 && response.AgentDetails != null
                        && !string.IsNullOrEmpty(response.AgentDetails.Queue)
                        && response.AgentDetails.Queue.Equals("termdnc"))
                    {
                        TermSubscribeApi(response.LeadId, leadDetails.MobileNo, leadDetails.CountryID);
                    }
                }
                if(request.IsSmeFosCreateLead && response.LeadId > 0)
                {
                    WebSiteServicDLL.SetSmeLeadDetails(request, response.LeadId);
                }
                if (!string.IsNullOrEmpty("SmeQueueTest".AppSettings()) && "SmeQueueTest".AppSettings().Equals("true"))
                {
                    if (request != null && response != null &&
                        request.IsDialer && response.LeadId > 0 &&
                        request.ProductId == 131 &&
                        !string.IsNullOrEmpty(request.LeadSource) &&
                        request.LeadSource.Equals("Inbound", StringComparison.OrdinalIgnoreCase) &&
                        request.SubProductId == 0 && response.SubProductId == 0)
                    {
                        if (response.AgentDetails == null)
                            response.AgentDetails = new VirtualNoAgentDetails();

                        response.AgentDetails.Queue = "Ibnotype";
                    }
                }                
            }
            catch (Exception ex)
            {
                response.Message = "Something Went wrong";
                error = ex.ToString();

            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(TrackingId, 0, error, "CreateLead",
                                                                        "Matrixcore", "WebSiteServiceBLL", JsonConvert.SerializeObject(request),
                                                                        logs.ToString(), reqDate, DateTime.Now);
            }
            return response;
        }

        private static int GetCountryID(int countryCode)
        {
            int CountryId = 392;
            if (countryCode > 0)
            {
                CountryId = WebSiteServicDLL.GetCountryID(countryCode);
            }
            return CountryId;
        }

        private static void CreateLead(StringBuilder logs, CreateLeadRequest request, CreateLeadResponse response, CreateLeadDetails leadDetails)
        {
            bool canCreateLead = ValidateCreateLeadRequest(request, response, leadDetails);
            if (canCreateLead)
            {

                if (request.ProductId == 115 && request.LeadSource.ToLower() == "referral" && request.CountryId == 392) //------ Investment Call CJ Api--------//
                    CreateLeadByCjApi(logs, request, response, leadDetails);
                else
                {
                    logs.Append(" create customer - " + DateTime.Now);
                    WebSiteServiceBLL obj = new WebSiteServiceBLL();
                    leadDetails.CustomerID = request.CustomerId > 0 ? request.CustomerId : obj.CreateCustomer(Convert.ToInt64(request.MobileNo), request.Email, request.Name, request.CountryId > 0 ? request.CountryId.ToString() : "392");
                    logs.Append(" create customer Done - " + leadDetails.CustomerID + " " + DateTime.Now);

                    if (leadDetails.CustomerID > 0)
                    {
                        logs.Append(" Lead creation start - " + DateTime.Now);
                        var details = new UTMLeadDetails
                        {
                            MobileNo = leadDetails.MobileNo,
                            ProductId = Convert.ToByte(leadDetails.ProductID),
                            SubProductId = Convert.ToInt16(leadDetails.SubProductID),
                            Name = leadDetails.Name,
                            DOB = getDOB(request, leadDetails, logs),
                            LeadSource = leadDetails.LeadSource,
                            Country = Convert.ToString(leadDetails.CountryID),
                            CustomerID = leadDetails.CustomerID,
                            Utm_campaign = leadDetails.Utm_Campaign,
                            UTM_Medium = leadDetails.Utm_Medium,
                            Utm_term = leadDetails.Utm_Term,
                            Utm_source = leadDetails.UTM_Source,
                            Source = leadDetails.Source,
                            EmailId = leadDetails.EmailID,
                            ReferralLead = leadDetails.ReferralLead,
                            Gender = Convert.ToByte(leadDetails.GenderID),
                            MaxStatusID = leadDetails.MaxStatusID,
                            UserId = request.UserId,
                            CityID = request.CityID,
                            StateID = request.StateID,
                            AnnualIncome = request.AnnualIncome,
                            CompanyName = request.CompanyName,
                            OccupationId = request.OccupationId,
                            AssignLead = request.AssignLead,
                            CreatedByEmpId = request.CreatedByEmpId,
                            AssignToUserId = request.AssignedUser,
                            AssignTogroupID = request.AssignedGroupId,
                            AssociationId = leadDetails.AssociationId,
                            RegNo = string.IsNullOrEmpty(request.RegistrationNo) ? "" : request.RegistrationNo,
                            IsSmeFosCreateLead = request.IsSmeFosCreateLead,
                            ISHNICustomer = request.IsHNICustomer,
                            ApiSource=request.ApiSource,
                            CreatedByEmpName = request.CreatedByEmpName
                        };
                        //For future lead creation
                        if (!string.IsNullOrEmpty(request.LeadCreationDate))
                        {
                            DateTime LeadCreationDate = DateTime.Today;
                            try
                            {
                                LeadCreationDate = DateTime.ParseExact(request.LeadCreationDate.ToString(), "dd/MM/yyyy", CultureInfo.InvariantCulture);
                            }
                            catch (Exception ex)
                            {
                                response.IsLeadCreated = false;
                                response.Message = "Provide LeadCreationDate in dd/mm/yyyy format only";
                            }
                            if (LeadCreationDate > DateTime.Today)
                            {
                                details.LeadCreationDate = LeadCreationDate;
                                details.Comments = request.Comment;
                                var data = WebSiteServicDLL.CreateLeadForFutureDate(details);
                                if (data.IsSaved)
                                {
                                    response.IsLeadCreated = true;
                                    response.Message = "Lead will be created on " + request.LeadCreationDate;
                                    logs.Append(" Lead will be created on " + request.LeadCreationDate);
                                }
                                else
                                {
                                    response.IsLeadCreated = false;
                                    response.Message = "Lead Creation failed";
                                    logs.Append(" Lead creation failed - " + DateTime.Now);
                                }
                            }
                            else
                            {
                                logs.Append(" Lead creation details - " + details);
                                var data = WebSiteServicDLL.CreateLead(details);
                                if (data.IsSaved)
                                {
                                    //response.CustId = leadDetails.CustomerID > 0 ? leadDetails.CustomerID : 0;
                                    response.LeadId = Convert.ToInt64(data.Output);
                                    response.IsLeadCreated = true;
                                    response.Message = "Lead Created ";

                                    if (!string.IsNullOrEmpty(request.LeadSource) && request.LeadSource.ToUpper() == "INBOUND" && request.IsDialer)
                                    {
                                        WebSiteServicDLL.LogLeadHistory(124, response.LeadId, "Inbound Call Recieved at " + DateTime.Now);
                                    }
                                    else if (!string.IsNullOrEmpty(request.Comment))
                                    {
                                        WebSiteServicDLL.LogLeadHistory(124, response.LeadId, request.Comment);
                                    }
                                    logs.Append(" Lead creation done - " + DateTime.Now);
                                }
                                else
                                {
                                    response.IsLeadCreated = false;
                                    if (!string.IsNullOrEmpty(data.Message))
                                    {
                                        if (data.Message.Contains("Mobile No cannot be same as referral Lead"))
                                        {
                                            response.Message = "Mobile No cannot be same as referral Lead";
                                        }
                                        else
                                        {
                                            if(!string.IsNullOrEmpty(data.Output))
                                            {
                                                response.LeadId = Convert.ToInt64(data.Output);
                                            }
                                            response.Message = data.Message + "." + data.Output;
                                        }
                                        logs.Append(" Lead creation failed - " + DateTime.Now + ". " + response.Message);
                                    }
                                    else
                                    {
                                        response.Message = "Lead Creation failed";
                                        logs.Append(" Lead creation failed - " + DateTime.Now);
                                    }
                                }
                            }
                        }
                        else
                        {
                            List<string> SourceArr = "ReferralSourceArr".AppSettings().Split(',').ToList();
                            bool IsAssigned = CoreCommonMethods.IsValidString(request.ApiSource) && (request.ApiSource.ToLower() == "fosapp" || SourceArr.Contains(request.ApiSource.ToLower())) ? true : false;
                            IsAssigned = request.UtmMedium == "Health_Renewal" ? false : IsAssigned;
                            var data = WebSiteServicDLL.CreateLead(details, IsAssigned);
                            if (data.IsSaved)
                            {
                                response.CustId = leadDetails.CustomerID > 0 ? Convert.ToInt64(leadDetails.CustomerID) : 0;
                                response.LeadId = Convert.ToInt64(data.Output);
                                response.IsLeadCreated = true;
                                response.Message = !String.IsNullOrEmpty(data.Message) ? data.Message : "Lead Created";
                                if (response.AgentDetails == null)
                                {
                                    response.AgentDetails = new VirtualNoAgentDetails();
                                }
                                response.AgentDetails.EmployeeId = !String.IsNullOrEmpty(data.AssignedToEcode) ? data.AssignedToEcode : "";
                                response.AgentDetails.EmployeeName = !String.IsNullOrEmpty(data.AssignedToAgentName) ? data.AssignedToAgentName : "";
                                response.AgentDetails.GroupID = data.AssignedToGroupID;

                                if (request.ProductId == 117 && response.LeadId > 0)
                                {
                                    _ = GetMotorEnquiryIDFromCJ(response.LeadId, response.CustId, 0);
                                }

                                if (!string.IsNullOrEmpty(request.LeadSource) && request.LeadSource.ToUpper() == "INBOUND" && request.IsDialer)
                                {
                                    WebSiteServicDLL.LogLeadHistory(124, response.LeadId, "Inbound Call Recieved at " + DateTime.Now);
                                }
                                else if (!string.IsNullOrEmpty(request.Comment))
                                {
                                    WebSiteServicDLL.LogLeadHistory((request.UserId > 0 ? request.UserId : 124), response.LeadId, request.Comment + " " + DateTime.Now);
                                }
                                if (!string.IsNullOrEmpty(request.Remarks))
                                {
                                    WebSiteServicDLL.LogLeadHistory(request.UserId, response.LeadId, request.Remarks, 7);
                                }
                                logs.Append(" Lead creation done - " + DateTime.Now);
                            }
                            else
                            {
                                response.IsLeadCreated = false;
                                if (!string.IsNullOrEmpty(data.Message))
                                {
                                    if (!string.IsNullOrEmpty(data.Output) && data.Output.Contains("Mobile No cannot be same as referral Lead"))
                                    {
                                        response.Message = data.Output;
                                    }
                                    else
                                    {
                                        response.Message = data.Message + "." + data.Output;
                                    }
                                    logs.Append(" Lead creation failed - " + DateTime.Now + ". " + response.Message);
                                }
                                else
                                {
                                    response.Message = "Lead Creation failed";
                                    logs.Append(" Lead creation failed - " + DateTime.Now);
                                }
                            }
                        }
                    }
                    else
                    {
                        response.IsLeadCreated = false;
                        response.Message = "Customer Creation failed";
                    }

                    if (response.IsLeadCreated && request.ProductId == 131 && request.UtmSource == "agentleadcreation" &&
                        request.UtmMedium == "agentleadcreation" && !string.IsNullOrEmpty(request.UtmTerm))
                    {
                        long userId = LeadDetailsDLL.GetUserId(request.UtmTerm);
                        if (userId > 0)
                        {
                            WebSiteServicDLL.AssignLeadToAgent(response.LeadId, userId, 124, request.ProductId, 0, 76);
                        }
                    }
                }
            }
            else
            {
                // If inbound dialer request and Lead already exists
                if(!response.IsLeadCreated && response.LeadId > 0 && request.LeadSource == "Inbound" && request.IsDialer && request.ProductId == 131)
                {
                    WebSiteServicDLL.UnassignDumpLead(response.LeadId);
                }
            }
        }


        private static bool ValidateCreateLeadRequest(CreateLeadRequest request, CreateLeadResponse response, CreateLeadDetails leadDetails)
        {
            var result = true;
            if (string.IsNullOrEmpty(request.LeadSource) || request.ProductId <= 0 || string.IsNullOrEmpty(request.MobileNo))
            {
                response.Message = "LeadSource, ProductId and MobileNo are mandatory.";
                response.IsLeadCreated = false;
                result = false;
            }
            else
            {
                if (request.LeadId > 0)
                {
                    response.LeadId = request.LeadId;
                    response.IsLeadCreated = false;
                    response.Message = "Lead Already Exists.";
                    result = false;
                }
                else
                {
                    response.Message = ValidateRequest(request);
                    if (CoreCommonMethods.IsValidString(response.Message))
                        return result = false;


                    if (!IsValidEmail(request.Email))
                    {
                        response.IsLeadCreated = false;
                        response.Message = "Invalid Email.";
                        result = false;
                    }

                    if (!request.IsDialer)
                    {
                        if (!request.MobileNo.All(char.IsDigit))
                        {
                            response.IsLeadCreated = false;
                            response.Message = "Invalid MobileNo.";
                            result = false;
                        }
                        else if (request.CountryId == 392 && request.MobileNo.Length != 10)
                        {
                            response.IsLeadCreated = false;
                            response.Message = "MobileNo should be 10 digit numeric.";
                            result = false;
                        }
                        else if (request.CountryId != 392 && (request.MobileNo.Length < 5 || request.MobileNo.Length > 13))
                        {
                            response.IsLeadCreated = false;
                            response.Message = "MobileNo should be 5 to 13 digit numeric.";
                            result = false;
                        }
                    }
                    if (!string.IsNullOrEmpty(request.UtmCampaign) && request.UtmCampaign.ToString().Length > 250)
                    {
                        response.IsLeadCreated = false;
                        response.Message = response.Message + "UtmCampaign should not be more than 250 chartacters.";
                        result = false;
                    }
                    if (!string.IsNullOrEmpty(request.UtmMedium) && request.UtmMedium.ToString().Length > 250)
                    {
                        response.IsLeadCreated = false;
                        response.Message = response.Message + "UtmMedium should not be more than 250 chartacters.";
                        result = false;
                    }
                    if (!string.IsNullOrEmpty(request.UtmTerm) && request.UtmTerm.ToString().Length > 250)
                    {
                        response.IsLeadCreated = false;
                        response.Message = response.Message + "UtmTerm should not be more than 250 chartacters.";
                        result = false;
                    }

                    List<string> SourceArr = "ReferralSourceArr".AppSettings().Split(',').ToList();
                    if (!(request != null && CoreCommonMethods.IsValidString(request.ApiSource) && SourceArr.Contains(request.ApiSource.ToLower())) || request.LeadSource == "PRB")
                    {
                        DataSet dataSet = WebSiteServicDLL.GetLeadDetailsbyMobileNoProductId(request.MobileNo, request.ProductId, request.SubProductId, "", request.LeadSource, request.UtmSource, request.UtmMedium, request.SubProductIdList, request.RegistrationNo);
                        if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                        {
                            if (dataSet.Tables[0].Rows[0]["ParentID"] == null || dataSet.Tables[0].Rows[0]["ParentID"] == DBNull.Value || Convert.ToInt64(dataSet.Tables[0].Rows[0]["ParentID"]) == 0)
                            {
                                response.CustId = Convert.ToInt64(dataSet.Tables[0].Rows[0]["CustomerId"]);
                                response.LeadId = Convert.ToInt64(dataSet.Tables[0].Rows[0]["LeadID"]);
                                if (request.ProductId == 131 && request.IsDialer && request.SubProductId == 0)
                                {
                                    response.SubProductId = (dataSet.Tables[0].Rows[0]["SubProductId"] != null && dataSet.Tables[0].Rows[0]["SubProductId"] != DBNull.Value) ? Convert.ToInt32(dataSet.Tables[0].Rows[0]["SubProductId"]) : 0;
                                }
                                if(request.ProductId == 117 && response.LeadId > 0)
                                {
                                    long EnquiryID = (dataSet.Tables[0].Rows[0]["EnquiryID"] != null && dataSet.Tables[0].Rows[0]["EnquiryID"] != DBNull.Value) ? Convert.ToInt64(dataSet.Tables[0].Rows[0]["EnquiryID"]) : 0;
                                    _ = GetMotorEnquiryIDFromCJ(response.LeadId, response.CustId, EnquiryID);
                                }
                            }
                            else
                            {
                                response.LeadId = Convert.ToInt64(dataSet.Tables[0].Rows[0]["ParentID"]);
                            }
                            if (request.UtmMedium == "Home_Visit")
                            {
                                response.CustId = Convert.ToInt64(dataSet.Tables[0].Rows[0]["CustomerId"]);
                            }
                        }

                    }
                    if (response.LeadId > 0)
                    {
                        response.IsLeadCreated = false;
                        response.Message = "Lead Already Exists.";
                        result = false;
                    }
                }
            }
            return result;
        }

        private static bool IsValidEmail(string emailAddress)
        {
            try
            {
                if (!string.IsNullOrEmpty(emailAddress))
                {
                    var email = new MailAddress(emailAddress);
                }
                return true;
            }
            catch (FormatException)
            {
                return false;
            }
        }

        public long CreateCustomer(long mobileNo, string email, string name, string countryId = "392")
        {
            var requestTime = DateTime.Now;
            string data = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(email) && email.Equals("<EMAIL>"))
                {
                    email = string.Empty;
                }
                dynamic custData = new ExpandoObject();
                custData.MobileNo = mobileNo;
                custData.CountryID = countryId;
                custData.IsNRI = "0";
                custData.Email = email;
                custData.FirstName = string.IsNullOrEmpty(name) ? "Offline" : name;
                custData.DOB = "01/01/1900";

                var headers = new Dictionary<object, object>()
                {
                    {"authKey", "coreAPIauthKey".AppSettings()},
                    {"clientKey", "coreAPIclientKey".AppSettings()}
                };

                data = CommonAPICall.CallAPI("coreAPI".AppSettings() + "cs/customer/registerCustomer/v1",
                                             JsonConvert.SerializeObject(custData), "POST", 5000,
                                             "application/json", headers);
                if (!string.IsNullOrEmpty(data))
                {
                    var Response = JsonConvert.DeserializeObject<dynamic>(data);
                    if (Response != null && Response.customer != null)
                        return Convert.ToInt64(Response.customer.CustomerId.Value);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(mobileNo.ToString(), mobileNo,
                                                          ex.Message, "CreateCustomer", "WebSiteServiceBLL",
                                                          "MatrixCore", string.Empty, data, requestTime, DateTime.Now);
            }
            return 0;
        }

        public ResponseAPI UploadUTMCampaign(UTMCampaignDetails request)
        {
            ResponseAPI objresponse = new ResponseAPI();
            DateTime dtreq = DateTime.Now;
            try
            {
                if (((!string.IsNullOrEmpty(request.UtmCampaign) && !string.IsNullOrEmpty(request.UtmSource)) ||
                    (!string.IsNullOrEmpty(request.UtmMedium) && !string.IsNullOrEmpty(request.LeadSource))) &&
                    !string.IsNullOrEmpty(request.link) && request.link.ToLower().Contains("http"))
                {
                    (objresponse.status, objresponse.message) = WebSiteServicDLL.UploadUTMCampaign(request);
                }
                else
                {
                    objresponse.status = false;
                    objresponse.message = "Invalid request parameter";
                }
            }
            catch (Exception ex)
            {
                objresponse.message = ex.ToString();
                objresponse.status = false;
                LoggingHelper.LoggingHelper.AddloginQueue(request.UtmCampaign.ToString(), 0, ex.Message, "UploadUTMCampaign", "WebSiteServiceBLL", "MatrixCore", request.ToString(), objresponse.ToString(), dtreq, DateTime.Now);
            }
            return objresponse;
        }


        public StoreAndAdvisorInfoModel GetStoreAndAdvisorInfo(Int32 CityID)
        {
            DateTime ReqDT = DateTime.Now;
            StoreAndAdvisorInfoModel objStoreDetails = new();

            try
            {
                if (CityID > 0)
                {
                    List<StoreDetails> StoreMasterList = GetStoreMaster();
                    if (StoreMasterList.Count > 0)
                        objStoreDetails.StoreInfo = StoreMasterList.Where(x => x.CityID == CityID).ToList();

                    objStoreDetails.AdvisorInfo = GetCityGroupMapping(CityID);
                }
                else
                    objStoreDetails.StoreInfo = GetStoreMaster();

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.Message, "GetStoreAndAdvisorInfo", "WebSiteServiceBLL", "MatrixCore", CityID.ToString(), string.Empty, ReqDT, DateTime.Now);
            }
            return objStoreDetails;
        }

        public List<StoreDetails> GetStoreMaster()
        {
            List<StoreDetails> oStoreDetails = null;
            string Key = $"{RedisCollection.StoreMaster()}";

            if (MemoryCache.Default[Key] != null)
                oStoreDetails = (List<StoreDetails>)(MemoryCache.Default.Get(Key));
            else
            {
                DataSet data = WebSiteServicDLL.GetStoreMaster();

                if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                {
                    oStoreDetails = (from dr in data.Tables[0].AsEnumerable()
                                     select new StoreDetails
                                     {
                                         CityID = dr["CityID"] != null && dr["CityID"] != DBNull.Value ? Convert.ToInt32(dr["CityID"]) : 0,
                                         StoreName = dr["StoreName"] != null && dr["StoreName"] != DBNull.Value ? Convert.ToString(dr["StoreName"]) : "",
                                         StoreAddress = dr["StoreAddress"] != null && dr["StoreAddress"] != DBNull.Value ? Convert.ToString(dr["StoreAddress"]) : "",
                                         Longitude = dr["Long"] != null && dr["Long"] != DBNull.Value ? Convert.ToDouble(dr["Long"]) : 0,
                                         Latitude = dr["Lat"] != null && dr["Lat"] != DBNull.Value ? Convert.ToDouble(dr["Lat"]) : 0,
                                         StoreId = dr["StoreId"] != null && dr["StoreId"] != DBNull.Value ? Convert.ToInt32(dr["StoreId"]) : 0,
                                         CityName = dr["CityName"] != null && dr["CityName"] != DBNull.Value ? Convert.ToString(dr["CityName"]) : string.Empty,
                                         InboundNumber = dr["InboundNumber"] != null && dr["InboundNumber"] != DBNull.Value ? Convert.ToInt64(dr["InboundNumber"]) : 0,
                                     }).ToList();
                    if (oStoreDetails.Count > 0)
                        CommonCache.GetOrInsertIntoCache(oStoreDetails, Key, 8 * 60);
                }
            }

            return oStoreDetails;
        }
        public List<ProductModel> GetFOSProductMaster(string source)
        {
            List<ProductModel> lstProducts = null;
            string Key = $"{RedisCollection.FOSProductMaster()}";
            DateTime ReqDT = DateTime.Now;

            try
            {
                if (MemoryCache.Default[Key] != null)
                    lstProducts = (List<ProductModel>)(MemoryCache.Default.Get(Key));
                else
                {
                    DataSet data = WebSiteServicDLL.FOSProductMaster(source);

                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        lstProducts = (from dr in data.Tables[0].AsEnumerable()
                                       select new ProductModel
                                       {
                                           ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                                           ProductName = dr["ProductName"] != null && dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : "",
                                       }).ToList();
                        if (lstProducts.Count > 0)
                            CommonCache.GetOrInsertIntoCache(lstProducts, Key, 8 * 60);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.Message, "GetFOSProductMaster", "WebSiteServiceBLL", "MatrixCore", string.Empty, string.Empty, ReqDT, DateTime.Now);
            }
            return lstProducts;
        }

        public static List<CityGroupMapping> GetCityGroupMapping(Int32 CityId)
        {
            List<CityGroupMapping> oStoreDetails = null;
            DateTime ReqDT = DateTime.Now;
            string Key = $"{RedisCollection.CityGroupMapping()}:{CityId}";
            try
            {
                if (MemoryCache.Default[Key] != null)
                    oStoreDetails = (List<CityGroupMapping>)(MemoryCache.Default.Get(Key));
                else
                {
                    DataSet data = WebSiteServicDLL.GetCityGroupMapping(CityId);

                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        oStoreDetails = (from dr in data.Tables[0].AsEnumerable()
                                         select new CityGroupMapping
                                         {
                                             // ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                                             CityId = dr["CityID"] != null && dr["CityID"] != DBNull.Value ? Convert.ToInt32(dr["CityID"]) : 0,
                                             GroupId = dr["GroupId"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt32(dr["GroupId"]) : 0,
                                             Latitude = dr["Lat"] != null && dr["Lat"] != DBNull.Value ? Convert.ToDecimal(dr["Lat"]) : 0,
                                             Longitude = dr["Long"] != null && dr["Long"] != DBNull.Value ? Convert.ToDecimal(dr["Long"]) : 0,
                                             UserID = dr["UserID"] != null && dr["UserID"] != DBNull.Value ? Convert.ToInt64(dr["UserID"]) : 0,
                                             UserName = dr["UserName"] != null && dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                                             JoiningMonth = dr["JoiningMonth"] != null && dr["JoiningMonth"] != DBNull.Value ? Convert.ToString(dr["JoiningMonth"]) : string.Empty,
                                             JoiningYear = dr["JoiningYear"] != null && dr["JoiningYear"] != DBNull.Value ? Convert.ToInt32(dr["JoiningYear"]) : 0,
                                             language = dr["language"] != null && dr["language"] != DBNull.Value ? Convert.ToString(dr["language"]) : string.Empty,
                                             InboundNumber = dr["InboundNumber"] != null && dr["InboundNumber"] != DBNull.Value ? Convert.ToInt64(dr["InboundNumber"]) : 0,
                                             bkgsCount = dr["bkgsCount"] != null && dr["bkgsCount"] != DBNull.Value ? Convert.ToInt64(dr["bkgsCount"]) : 0,
                                             ProductMapping = getProductModel(dr["GroupId"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt32(dr["GroupId"]) : 0),
                                             LanguageMapping = getLanguageModel(dr["GroupId"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt32(dr["GroupId"]) : 0)

                                         }).ToList();
                        if (oStoreDetails.Count > 0)
                            CommonCache.GetOrInsertIntoCache(oStoreDetails, Key, 4 * 60);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.Message, "GetCityGroupMapping", "WebSiteServiceBLL", "MatrixCore", string.Empty, string.Empty, ReqDT, DateTime.Now);

            }
            return oStoreDetails;
        }

        public static List<CityGroupMapping> GetGroupLangMaster()
        {
            List<CityGroupMapping> cityGroupMapping = null;
            string Key = $"{RedisCollection.GroupLangMaster()}";

            if (MemoryCache.Default[Key] != null)
                cityGroupMapping = (List<CityGroupMapping>)(MemoryCache.Default.Get(Key));
            else
            {
                DataSet data = WebSiteServicDLL.GetGroupLangMaster();

                if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                {
                    cityGroupMapping = (from dr in data.Tables[0].AsEnumerable()
                                        select new CityGroupMapping
                                        {
                                            GroupId = dr["GroupId"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt32(dr["GroupId"]) : 0,
                                            language = dr["language"] != null && dr["language"] != DBNull.Value ? Convert.ToString(dr["language"]) : "",
                                            Product = dr["ProductName"] != null && dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : string.Empty
                                        }).ToList();
                    if (cityGroupMapping.Count > 0)
                        CommonCache.GetOrInsertIntoCache(cityGroupMapping, Key, 8 * 60);
                }
            }

            return cityGroupMapping;
        }

        public static List<string> getProductModel(Int32 GroupId)
        {
            List<string> oProductModel = new List<string>();
            try
            {
                if (GroupId > 0)
                {
                    List<CityGroupMapping> groupLangMaster = GetGroupLangMaster();
                    if (groupLangMaster.Count > 0)
                        oProductModel = groupLangMaster.Where(x => x.GroupId == GroupId).Select(p => p.Product).Distinct().ToList();

                }
            }
            catch (Exception ex)
            {

            }
            return oProductModel;

        }
        public static List<string> getLanguageModel(Int32 GroupId)
        {
            List<string> oLanguageModel = null;
            try
            {
                if (GroupId > 0)
                {
                    List<CityGroupMapping> groupLangMaster = GetGroupLangMaster();
                    if (groupLangMaster.Count > 0)
                        oLanguageModel = groupLangMaster.Where(x => x.GroupId == GroupId).Select(p => p.language).Distinct().ToList();
                }
            }
            catch (Exception ex)
            {

            }
            return oLanguageModel;

        }

        public List<FOSCityMaster> GetFOSCityMaster(Int32 ProductId)
        {
            List<FOSCityMaster> cityMaster = null;
            DateTime dt = DateTime.Now;
            try
            {
                string Key = $"{RedisCollection.FOSCityMaster()}:{ProductId}";

                if (MemoryCache.Default[Key] != null)
                    cityMaster = (List<FOSCityMaster>)(MemoryCache.Default.Get(Key));
                else
                {
                    DataSet data = WebSiteServicDLL.GetFosCityMaster(ProductId);

                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        cityMaster = (from dr in data.Tables[0].AsEnumerable()
                                      select new FOSCityMaster
                                      {
                                          CityId = dr["CityId"] != null && dr["CityId"] != DBNull.Value ? Convert.ToInt32(dr["CityId"]) : 0,
                                          City = dr["city"] != null && dr["city"] != DBNull.Value ? Convert.ToString(dr["city"]) : "",
                                          IsFosConfigured = dr["IsFosConfigured"] != null && dr["IsFosConfigured"] != DBNull.Value ? Convert.ToBoolean(dr["IsFosConfigured"]) : false,
                                          IsStoreAvl = dr["IsStoreAvl"] != null && dr["IsStoreAvl"] != DBNull.Value ? Convert.ToBoolean(dr["IsStoreAvl"]) : false,
                                          IsDedicatedAvl = dr["IsDedicatedAvl"] != null && dr["IsDedicatedAvl"] != DBNull.Value ? Convert.ToBoolean(dr["IsDedicatedAvl"]) : false
                                      }).ToList();
                        if (cityMaster.Count > 0)
                            CommonCache.GetOrInsertIntoCache(cityMaster, Key, 8 * 60);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetFOSCityPrdMaster", "webSiteServiceBll", "Matrixcore", "", "", dt, DateTime.Now);
            }
            return cityMaster;
        }

        public List<CustomerLeads> GetCustomerLeads(CustomerLeadsRequest request, string encKey, string encIV, out string message)
        {
            var response = new List<CustomerLeads>();
            var reqTime = DateTime.Now;
            message = string.Empty;
            try
            {
                if (request != null && !string.IsNullOrEmpty(request.SearchInput))
                {
                    if (request.SearchType == 1 || request.SearchType == 2)
                    {
                        request.SearchInput = Crypto.Decrytion_Payment_AES(request.SearchInput, "Core", 256, 128, encKey, encIV, false);
                    }
                    var data = WebSiteServicDLL.GetCustomerLeads(request);

                    if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        if (data.Tables[0].Rows[0]["InvalidMobile"].ToString() != "")
                        {
                            message = "Invalid No";
                        }
                        response = (from dr in data.Tables[0].AsEnumerable()
                                    select new CustomerLeads
                                    {
                                        LeadId = dr["LeadID"] != null && dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : default,
                                        Name = dr["Name"] != null && dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : default,
                                        // MobileNo = dr["MobileNo"] != null && dr["MobileNo"] != DBNull.Value ? Crypto.Decrytion_Payment_AES(Convert.ToString(dr["MobileNo"]), "Core", 256, 128, encKey, encIV, true) : default,
                                        CustomerId = dr["CustID"] != null && dr["CustID"] != DBNull.Value ? Convert.ToInt64(dr["CustID"]) : default,
                                        EmailId = dr["EmailId"] != null && dr["EmailId"] != DBNull.Value ? Convert.ToString(dr["EmailId"]) : default,
                                        City = dr["City"] != null && dr["City"] != DBNull.Value ? Convert.ToString(dr["City"]) : default,
                                        CityId = dr["CityId"] != null && dr["CityId"] != DBNull.Value ? Convert.ToInt16(dr["CityId"]) : default,
                                        OfferNumber = dr["OfferNumber"] != null && dr["OfferNumber"] != DBNull.Value ? Convert.ToString(dr["OfferNumber"]) : default,
                                        StatusSubStatus = dr["StatusSubStatus"] != null && dr["StatusSubStatus"] != DBNull.Value ? Convert.ToString(dr["StatusSubStatus"]) : default,
                                        AgentName = dr["AgentName"] != null && dr["AgentName"] != DBNull.Value ? Convert.ToString(dr["AgentName"]) : default,
                                        GroupName = dr["GroupName"] != null && dr["GroupName"] != DBNull.Value ? Convert.ToString(dr["GroupName"]) : default,
                                        GroupId = dr["GroupId"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt16(dr["GroupId"]) : default,
                                        StatusDate = dr["StatusDate"] != null && dr["StatusDate"] != DBNull.Value ? Convert.ToString(dr["StatusDate"]) : default,
                                        AssignedOn = dr["AssignedOn"] != null && dr["AssignedOn"] != DBNull.Value ? Convert.ToString(dr["AssignedOn"]) : default,
                                        ProductName = dr["Product"] != null && dr["Product"] != DBNull.Value ? Convert.ToString(dr["Product"]) : default,
                                        CreatedOn = dr["CreatedOn"] != null && dr["CreatedOn"] != DBNull.Value ? Convert.ToString(dr["CreatedOn"]) : default,
                                        IsReleased = dr["IsReleased"] != null && dr["IsReleased"] != DBNull.Value && Convert.ToBoolean(dr["IsReleased"]),
                                        IsNDNC = dr["IsNDNC"] != null && dr["IsNDNC"] != DBNull.Value && Convert.ToBoolean(dr["IsNDNC"]),
                                        LeadSource = dr["LeadSource"] != null && dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : default,
                                        UtmSource = dr["Utm_source"] != null && dr["Utm_source"] != DBNull.Value ? Convert.ToString(dr["Utm_source"]) : default,
                                        ParentId = dr["ParentID"] != null && dr["ParentID"] != DBNull.Value ? Convert.ToInt64(dr["ParentID"]) : default,
                                        IsPaymentCall = dr["IsPaymentCall"] != null && dr["IsPaymentCall"] != DBNull.Value && Convert.ToBoolean(dr["IsPaymentCall"]),
                                        InvalidMobile = dr["InvalidMobile"] != null && dr["InvalidMobile"] != DBNull.Value ? Convert.ToString(dr["InvalidMobile"]) : default,
                                        ReturnsType = dr["ReturnsType"] != null && dr["ReturnsType"] != DBNull.Value ? Convert.ToString(dr["ReturnsType"]) : default,
                                        PropFormFilled = dr["PropFormFilled"] != null && dr["PropFormFilled"] != DBNull.Value ? Convert.ToString(dr["PropFormFilled"]) : default,
                                        CustomerStatus = dr["CustomerStatus"] != null && dr["CustomerStatus"] != DBNull.Value ? Convert.ToString(dr["CustomerStatus"]) : default,
                                        SalesAgentName = dr["SalesAgentName"] != null && dr["SalesAgentName"] != DBNull.Value ? Convert.ToString(dr["SalesAgentName"]) : default,
                                        IsCertified = dr["IsCertified"] != null && dr["IsCertified"] != DBNull.Value ? Convert.ToString(dr["IsCertified"]) : default,
                                        DateofCertification = dr["DateofCertification"] != null && dr["DateofCertification"] != DBNull.Value ? Convert.ToString(dr["DateofCertification"]) : default,
                                        SubProductName = dr["SubProductName"] != null && dr["SubProductName"] != DBNull.Value ? Convert.ToString(dr["SubProductName"]) : default,
                                        StatusName = dr["StatusName"] != null && dr["StatusName"] != DBNull.Value ? Convert.ToString(dr["StatusName"]) : default,
                                        StatusId = dr["StatusID"] != null && dr["StatusID"] != DBNull.Value ? Convert.ToInt32(dr["StatusID"]) : default,
                                        SubStatusId = dr["SubStatusID"] != null && dr["SubStatusID"] != DBNull.Value ? Convert.ToInt16(dr["SubStatusID"]) : default,
                                        ProductID = dr["ProductID"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt16(dr["ProductID"]) : default,
                                        ExitPointURL = dr["ExitPointURL"] != null && dr["ExitPointURL"] != DBNull.Value ? Convert.ToString(dr["ExitPointURL"]) : default
                                    }).ToList();

                    }
                    else
                    {
                        throw new Exception("Data not found");
                    }
                }
                else
                {
                    throw new Exception("Invalid request parameters");
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(request.SearchInput, 0, ex.Message, "GetCustomerLeads", "WebSiteServiceBLL", "MatrixCore", JsonConvert.SerializeObject(request), JsonConvert.SerializeObject(response), reqTime, DateTime.Now);
            }
            finally
            {

            }
            return response;
        }

        public List<CustomerLeads> GetLeadsByCustId(CustomerLeadsRequest request, string encKey, string encIV, out string message)
        {
            var response = new List<CustomerLeads>();
            var reqTime = DateTime.Now;
            message = string.Empty;

            try
            {
                if (request != null && !string.IsNullOrEmpty(request.SearchInput))
                {
                    request.SearchInput = Crypto.Decrytion_Payment_AES(request.SearchInput, "Core", 256, 128, encKey, encIV, false).ToString();

                    if (request.SearchInput != null || request.SearchInput == string.Empty)
                    {
                        var data = WebSiteServicDLL.GetLeadsByCustId(request);

                        if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                        {
                            if (data.Tables[0].Rows[0]["InvalidMobile"].ToString() != "")
                            {
                                message = "Invalid No";
                            }
                            response = (from dr in data.Tables[0].AsEnumerable()
                                        select new CustomerLeads
                                        {
                                            ProductID = dr["ProductID"] != null && dr["ProductID"] != DBNull.Value ? Convert.ToInt16(dr["ProductID"]) : default,
                                            ProductName = dr["ProductName"] != null && dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : default,
                                            CreatedOn = dr["CreatedOn"] != null && dr["CreatedOn"] != DBNull.Value ? Convert.ToString(dr["CreatedOn"]) : default,
                                            LeadId = dr["LeadID"] != null && dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : default,
                                            LeadSource = dr["LeadSource"] != null && dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : default,
                                        }).ToList();

                        }
                        else
                        {
                            throw new Exception("Data not found");
                        }
                    }
                    else
                    {
                        message = "Decryption Failed!";
                    }


                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(request.SearchInput, 0, ex.Message, "GetLeadsByCustId", "WebSiteServiceBLL", "MatrixCore", JsonConvert.SerializeObject(request), JsonConvert.SerializeObject(response), reqTime, DateTime.Now);
            }

            return response;
        }

        public List<LeadInfo> GetActiveLeadAppSet(string CustId, out bool status, string EncKey, string EncIV)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            List<LeadInfo> LeadInfo = null;
            status = false;
            try
            {
                //string descrypCustId = Crypto.Encrytion_Payment_AES(CustId, "Core", 256, 128, EncKey, EncIV, false);
                CustId = Crypto.Decrytion_Payment_AES(CustId, "Core", 256, 128, EncKey, EncIV, true);
                if (CoreCommonMethods.IsValidInteger(CustId) > 0)
                {
                    DataSet oDataSet = WebSiteServicDLL.GetActiveLeadAppSet(Convert.ToInt64(CustId));
                    if (oDataSet != null && oDataSet.Tables.Count > 0)
                    {

                        LeadInfo = (from dr in oDataSet.Tables[0].AsEnumerable()
                                    select new LeadInfo
                                    {
                                        LeadID = dr["LeadID"] != null && dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : 0,
                                        StatusId = dr["StatusId"] != null && dr["StatusId"] != DBNull.Value ? Convert.ToInt32(dr["StatusId"]) : 0,
                                        EmployeeId = dr["EmployeeId"] != null && dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty,
                                        LastUpdatedOn = dr["LastUpdatedOn"] != null && dr["LastUpdatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["LastUpdatedOn"]) : null,
                                        LeadStatus = dr["LeadStatus"] != null && dr["LeadStatus"] != DBNull.Value ? Convert.ToString(dr["LeadStatus"]) : string.Empty,
                                        ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                                        AssignUser = dr["UserName"] != null && dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                                        appDataModel = dr["AppointmentId"] != null && dr["AppointmentId"] != DBNull.Value && Convert.ToInt64(dr["AppointmentId"]) > 0
                                        ?
                                        new AppointmentsDataModel()
                                        {
                                            AppointmentId = dr["AppointmentId"] != null && dr["AppointmentId"] != DBNull.Value ? Convert.ToInt64(dr["AppointmentId"]) : 0,
                                            Address = dr["Address"] != null && dr["Address"] != DBNull.Value ? Convert.ToString(dr["Address"]) : string.Empty,
                                            Address1 = dr["Address1"] != null && dr["Address1"] != DBNull.Value ? Convert.ToString(dr["Address1"]) : string.Empty,
                                            Landmark = dr["Landmark"] != null && dr["Landmark"] != DBNull.Value ? Convert.ToString(dr["Landmark"]) : string.Empty,
                                            Pincode = dr["Pincode"] != null && dr["Pincode"] != DBNull.Value ? Convert.ToInt32(dr["Pincode"]) : 0,
                                            AssignTo = dr["AssignTo"] != null && dr["AssignTo"] != DBNull.Value ? Convert.ToInt64(dr["AssignTo"]) : 0,
                                            AppointmentDateTime = dr["AppointmentDateTime"] != null && dr["AppointmentDateTime"] != DBNull.Value ? Convert.ToDateTime(dr["AppointmentDateTime"]) : DateTime.MinValue,
                                            AppCreatedSource = dr["AppCreatedSource"] != null && dr["AppCreatedSource"] != DBNull.Value ? Convert.ToString(dr["AppCreatedSource"]) : string.Empty,
                                            SubStatusID = dr["SubStatusId"] != null && dr["SubStatusId"] != DBNull.Value ? Convert.ToInt32(dr["SubStatusId"]) : 0,
                                            IsReScheduled = dr["IsReScheduled"] != null && dr["IsReScheduled"] != DBNull.Value ? Convert.ToBoolean(dr["IsReScheduled"]) : false,
                                            OfflineCityId = dr["OfflineCityId"] != null && dr["OfflineCityId"] != DBNull.Value ? Convert.ToInt32(dr["OfflineCityId"]) : 0,
                                            AppointmentType = dr["AppointmentType"] != null && dr["AppointmentType"] != DBNull.Value ? Convert.ToInt32(dr["AppointmentType"]) : 0,
                                            SlotId = dr["SlotId"] != null && dr["SlotId"] != DBNull.Value ? Convert.ToInt16(dr["SlotId"]) : Convert.ToInt16(0),
                                            location = new PlaceLatLongModel()
                                            {
                                                place_id = dr["PlaceId"] == DBNull.Value ? "" : Convert.ToString(dr["PlaceId"])
                                            },
                                            City = dr["City"] != null && dr["City"] != DBNull.Value ? Convert.ToString(dr["City"]) : string.Empty,
                                        } : null

                                    }).ToList();

                    }

                    //if (LeadInfo.Count>0)
                    status = true;
                }
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(CustId, 0, Error, "GetActiveLeadAppSet", "MatrixCore", "WebSiteServiceBll", string.Empty, JsonConvert.SerializeObject(LeadInfo), dt, DateTime.Now);
            }
            return LeadInfo;
        }


        public SaveInfo SetCsatAssignLead(CSATModel oCSATModel)
        {
            SaveInfo oSaveInfo = null;
            string err = string.Empty;
            DateTime reqTime = DateTime.Now;
            try
            {
                oSaveInfo = new SaveInfo()
                {
                    IsSaved = WebSiteServicDLL.SetCsatAssignLead(oCSATModel)
                };
            }
            catch (Exception ex)
            {
                err = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, oCSATModel.LeadId, err, "SetCsatAssignLead", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(oCSATModel), JsonConvert.SerializeObject(oSaveInfo), reqTime, DateTime.Now);
            }
            return oSaveInfo;
        }
        public ResponseData<string> AddUpdateCallAnalysis(CallAnalyserRequest request)
        {
            var response = new ResponseData<string>();
            try
            {
                if (request == null)
                {
                    response.Message = "Invalid request";
                }
                else if (request.LeadId <= 0)
                {
                    response.Message = "Invalid LeadId";
                }
                else if (request.CallDataId <= 0)
                {
                    response.Message = "Invalid CallDataId";
                }
                else if (request.CallStatusId <= 0)
                {
                    response.Message = "Invalid CallStatusId";
                }
                else
                {
                    var data = WebSiteServicDLL.AddUpdateCallAnalysis(request);
                    if (data != null && (data["ErrorMessage"] == null || data["ErrorMessage"].ToString() == string.Empty))
                    {
                        response.Status = true;
                        // Update Customer NANC count
                        CustomerNANC oCustomerNANC = new()
                        {
                            CustID = Convert.ToInt64(data["CustomerId"]),
                            CallDate = data["CallDate"] != null && data["CallDate"] != DBNull.Value ? Convert.ToDateTime(data["CallDate"]) : DateTime.MinValue,
                            UpdatedOn = DateTime.Now
                        };

                        WebSiteServicDLL.InsertUpdateCustNANCInMongo(oCustomerNANC);

                        // Send voicemail data to bms
                        bool IsBMS = data["IsBMS"] != null && data["IsBMS"] != DBNull.Value ? Convert.ToBoolean(data["IsBMS"]) : false;
                        string EmpId = data["EmpId"] != null && data["EmpId"] != DBNull.Value ? Convert.ToString(data["EmpId"]) : string.Empty;

                        if ((!string.IsNullOrEmpty(EmpId) && EmpId.StartsWith("B")) || IsBMS == true)
                        {
                            DialerDispDetails _DispositionUpdate = new()
                            {
                                LeadID = request.LeadId,
                                CallTrackingID = request.CallDataId.ToString(),
                                Status = request.CallStatusId.ToString(),
                                Disposition = "VOICEMAIL",
                                talktime = 0,
                                IsBMS = IsBMS
                            };
                            InsertUpdateCallData.PushVoiceMailDatatoBMSSQS(_DispositionUpdate);
                        }
        
                    }
                    else
                    {
                        response.Message = data["ErrorMessage"].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                response.Message = "Error occured";
                LoggingHelper.LoggingHelper.AddloginQueue(null, request.LeadId, ex.ToString(), "AddUpdateCallAnalysis", "MatrixCore", "WebSiteServiceBll", string.Empty, JsonConvert.SerializeObject(request), DateTime.Now, DateTime.Now);
            }
            return response;
        }


        public ResponseData<Dictionary<string, string>> GetCityByPinCodeProduct(Int32 Pincode, Int16 ProductId, string Source)
        {
            string Exception = string.Empty;
            Dictionary<string, string> d = new Dictionary<string, string>() { { "CityId", "0" }, { "CityName", "" } };
            ResponseData<Dictionary<string, string>> oResponseData = new ResponseData<Dictionary<string, string>>() { Status = false, Message = "", Data = d };

            try
            {
                DataSet oDataSet = FOSDLL.GetAsssignmentsByProductPincode(ProductId, Pincode);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {

                    string City = oDataSet.Tables[0].Rows[0]["City"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["City"]);
                    Int32 CityId = oDataSet.Tables[0].Rows[0]["CityId"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["CityId"]);

                    bool IsAssignmentValid = CheckAssignmentAvailability(oDataSet, ProductId, Source);
                    if (IsAssignmentValid)
                    {
                        oResponseData.Status = true;
                        oResponseData.Message = "Success";
                        oResponseData.Data = new Dictionary<string, string>() { { "CityId", Convert.ToString(CityId) }, { "CityName", City } };
                    }
                    else
                    {
                        oResponseData.Status = false;
                        oResponseData.Message = "This Pincode is not serviceable";
                        oResponseData.Data = d;
                    }
                }
                else
                {
                    oResponseData.Status = false;
                    oResponseData.Message = "This Pincode is not serviceable";
                    oResponseData.Data = d;
                }


            }
            catch (Exception ex)
            {
                Exception = ex.ToString();
                oResponseData.Status = false;
                oResponseData.Message = "SomeThing went wrong";
                oResponseData.Data = d;

            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(Pincode), 0, Exception, "GetCityByPinCodeProduct", "FOS", "FOSBLL", Convert.ToString(ProductId), JsonConvert.SerializeObject(oResponseData), DateTime.Now, DateTime.Now);
            }

            return oResponseData;
        }

        public bool CheckAssignmentAvailability(DataSet oDataSet, Int16 ProductId, string Source)
        {
            bool result = false;
            List<int> AssignmentAvailableList = FOSBLL.GetAssignmentAvailableList(oDataSet);

            if (!string.IsNullOrEmpty(Source) && Source.ToLower() == "pbapp")
            {
                switch ((EnumProducts)ProductId)
                {
                    case EnumProducts.Health:
                        result = true;
                        break;
                    case EnumProducts.Term:
                        result = AssignmentAvailableList.Count > 0 && (AssignmentAvailableList.Contains(Convert.ToInt32(EnumAssignmentType.DedicatedFOS)) || AssignmentAvailableList.Contains(Convert.ToInt32(EnumAssignmentType.Self))) ? true : false;
                        break;
                    case EnumProducts.Investment:
                        result = AssignmentAvailableList.Count > 0 && (AssignmentAvailableList.Contains(Convert.ToInt32(EnumAssignmentType.DedicatedFOS)) || AssignmentAvailableList.Contains(Convert.ToInt32(EnumAssignmentType.Self))) ? true : false;
                        break;
                    default:
                        break;
                }

            }
            return result;

        }

        public SaveInfo SavePlanRecommendation(PlanRecommendationModel planRecommModel)
        {
            SaveInfo oSaveInfo = null;
            DateTime reqTime = DateTime.Now;

            try
            {
                oSaveInfo = new SaveInfo()
                {
                    IsSaved = WebSiteServicDLL.SavePlanRecommendation(planRecommModel)
                };
                oSaveInfo.Message = oSaveInfo.IsSaved ? "Save Successfully" : "failed";
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, planRecommModel.BookingId, ex.ToString(), "SetCsatAssignLead", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(planRecommModel), JsonConvert.SerializeObject(oSaveInfo), reqTime, DateTime.Now);
            }
            return oSaveInfo;
        }

        public PlanRecommendationModel GetPlanRecommendation(long leadId, out bool Status)
        {
            PlanRecommendationModel planRecommendationModel = new() { BookingId = leadId };
            DateTime reqTime = DateTime.Now;
            Status = false;
            try
            {
                DataSet oDataSet = WebSiteServicDLL.GetPlanRecommendation(leadId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    planRecommendationModel = new PlanRecommendationModel()
                    {
                        BookingId = oDataSet.Tables[0].Rows[0]["LeadId"] != DBNull.Value ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["LeadId"]) : 0,
                        CreatedBy = oDataSet.Tables[0].Rows[0]["CreatedBy"] != DBNull.Value ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["CreatedBy"]) : 0,
                        AlternatePlanDetails = new AlternatePlanDetails() { NoPlanSelected = true },
                    };
                    planRecommendationModel.AlternatePlanDetails.SelectedPlans = new List<SelectedPlans>();
                    if (oDataSet.Tables[0].Rows[0]["PlanId"] != null && oDataSet.Tables[0].Rows[0]["PlanId"] != DBNull.Value && Convert.ToInt32(oDataSet.Tables[0].Rows[0]["PlanId"]) > 0)
                    {
                        planRecommendationModel.AlternatePlanDetails.NoPlanSelected = false;

                        List<SelectedPlans> lstSelectedPlans = (from dr in oDataSet.Tables[0].AsEnumerable()
                                                                select new SelectedPlans
                                                                {
                                                                    PlanId = dr["PlanId"] != null && dr["PlanId"] != DBNull.Value ? Convert.ToInt32(dr["PlanId"]) : 0,
                                                                    SupplierId = dr["SupplierId"] != null && dr["SupplierId"] != DBNull.Value ? Convert.ToInt32(dr["SupplierId"]) : 0,
                                                                    Remarks = dr["Remarks"] != null && dr["Remarks"] != DBNull.Value ? Convert.ToString(dr["Remarks"]) : string.Empty
                                                                }).ToList();


                        planRecommendationModel.AlternatePlanDetails.SelectedPlans = lstSelectedPlans;
                    }
                    Status = true;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadId, ex.ToString(), "GetPlanRecommendation", "MatrixCore", "WebSiteServiceBLL", string.Empty, JsonConvert.SerializeObject(planRecommendationModel), reqTime, DateTime.Now);
            }
            return planRecommendationModel;
        }


        public List<KnowYoutAdvisorModel> GetProductListByEmpId(string empId, out bool status)
        {
            List<KnowYoutAdvisorModel> lstKnowYoutAdvisorModel = null;
            DateTime dt = DateTime.Now;
            status = false;
            try
            {
                DataSet data = WebSiteServicDLL.GetProductListByEmpId(empId);

                if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                {
                    lstKnowYoutAdvisorModel = (from dr in data.Tables[0].AsEnumerable()
                                               select new KnowYoutAdvisorModel
                                               {
                                                   ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                                                   ProductName = dr["ProductName"] != null && dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : "",
                                                   EmployeeId = dr["EmployeeId"] != null && dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : "",
                                                   UserName = dr["UserName"] != null && dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : "",
                                                   UserId = dr["UserId"] != null && dr["UserId"] != DBNull.Value ? Convert.ToInt64(dr["UserId"]) : 0,
                                               }).ToList();
                    status = true;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetProductListByEmpId", "webSiteServiceBll", "Matrixcore", "", "", dt, DateTime.Now);
            }
            return lstKnowYoutAdvisorModel;
        }
        public ResponseData<string> PushNotification(NotificationRequest request)
        {
            var response = new ResponseData<string>();
            try
            {
                if (request != null)
                {
                    request.DateTime = DateTime.Now;
                    request.TimeStamp = DateTime.Now.ToString("yyyyMMddHHmmss");

                    if (!string.IsNullOrEmpty(request.ExpiryInSeconds.ToString()) && Convert.ToInt32(request.ExpiryInSeconds) > 0)
                    {
                        request.ExpiryTime = DateTime.Now.AddSeconds(Convert.ToInt64(request.ExpiryInSeconds));
                    }
                    else
                    {
                        request.ExpiryTime = null;
                    }
                    response.Status = WebSiteServicDLL.PushNotification(request, MongoCollection.NotificationsData());
                }
                else
                {
                    response.Message = "Invalid request";
                }
            }
            catch (Exception ex)
            {
                response.Message = "Error occured";
                LoggingHelper.LoggingHelper.AddloginQueue(request?.EmpId, 0, ex.ToString(), "PushNotification", "MatrixCore", "WebSiteServiceBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public ResponseData<string> PushReadNotification(ReadNotificationRequest request)
        {
            var response = new ResponseData<string>();
            try
            {
                if (request != null)
                {
                    request.DateTime = DateTime.Now;
                    response.Status = WebSiteServicDLL.PushNotification(request, MongoCollection.ReadNotificationsData());
                }
                else
                {
                    response.Message = "Invalid request";
                }
            }
            catch (Exception ex)
            {
                response.Message = "Error occured";
                LoggingHelper.LoggingHelper.AddloginQueue(request.UserId, 0, ex.ToString(), "PushReadNotification", "MatrixCore", "WebSiteServiceBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        private static string getDOB(CreateLeadRequest request, CreateLeadDetails leadDetails, StringBuilder logs)
        {
            string dob = leadDetails.DateOfBirth;
            try
            {
                int desiredYear = DateTime.Now.Year;
                DateTime birthDate = request.Age > 0 ? new DateTime(desiredYear - request.Age, 1, 1) : DateTime.MinValue;
                if (!string.IsNullOrEmpty(request.ApiSource) && request.ApiSource.ToLower() == "bms" && !string.IsNullOrEmpty(leadDetails.DateOfBirth))
                {
                    DateTime ReqDOB = DateTime.ParseExact(leadDetails.DateOfBirth, "dd-MM-yyyy", null);
                    string formattedDate = ReqDOB.ToString("MM-dd-yyyy");
                    dob = request.Age > 0 ? birthDate.ToString("dd-MMM-yyyy") : formattedDate;
                }
                else
                {
                    dob = request.Age > 0 ? birthDate.ToString("dd-MMM-yyyy") : leadDetails.DateOfBirth;
                }
            }
            catch (FormatException)
            {
                dob = string.Empty;
                logs.Append("Error in getDOB - " + DateTime.Now);
            }
            return dob;
        }

        private static CreateLeadRequest UpdateRequestInput(CreateLeadRequest request, StringBuilder logs)
        {
            try
            {
                List<string> SourceArr = "ReferralSourceArr".AppSettings().Split(',').ToList();

                if (CoreCommonMethods.IsValidInteger(request.MobileNo) == 0 && CoreCommonMethods.IsValidString(request.ApiSource) && SourceArr.Contains(request.ApiSource.ToLower())) //chk in case of ReferralLead from dashboard 
                {
                    DataTable dt = CommunicationDLL.GetBasicLeadDetails(request.ReferralLead);
                    if (dt != null && dt.Rows.Count > 0)
                    {
                        long CustomerID = dt.Rows[0]["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(dt.Rows[0]["CustomerID"]);
                        request.MobileNo = CustomerID == Convert.ToInt64(request.CustomerId) ? dt.Rows[0]["MobileNo"] == DBNull.Value ? string.Empty : Convert.ToString(dt.Rows[0]["MobileNo"]) : string.Empty;
                        request.CountryId = dt.Rows[0]["Country"] == DBNull.Value ? 392 : Convert.ToInt32(dt.Rows[0]["Country"]);
                    }
                }
            }
            catch (Exception ex)
            {
                logs.Append("Error in UpdateRequestInput - " + ex.Message.ToString() + DateTime.Now);
            }
            return request;
        }

        public NotificationsResponse GetNotificationsData(string userId, short? numberOfDays)
        {
            var response = new NotificationsResponse();
            try
            {
                response = WebSiteServicDLL.GetNotificationsData(userId, numberOfDays);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(userId, 0, ex.ToString(), "GetNotificationsData", "MatrixCore", "WebSiteServiceBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        private static string ValidateRequest(CreateLeadRequest request)
        {
            string msg = string.Empty;
            try
            {
                List<string> SourceArr = "ReferralSourceArr".AppSettings().Split(',').ToList();

                if (request != null && CoreCommonMethods.IsValidString(request.ApiSource) && SourceArr.Contains(request.ApiSource.ToLower()))
                    msg = WebSiteServicDLL.CreateLeadValidation(request);

            }
            catch (Exception ex)
            {
                Console.WriteLine("Error in ValidateRequest-" + ex.Message.ToString());
            }
            return msg;
        }

        public ResponseData<string> ValidateCustomerContact(CustomerContactDetails contactDetails, string encKey, string encIV)
        {
            var response = new ResponseData<string>();
            try
            {
                if (!string.IsNullOrEmpty(contactDetails.MobileNo))
                {
                    contactDetails.MobileNo = Crypto.Decrytion_Payment_AES(contactDetails.MobileNo, "Core", 256, 128, encKey, encIV, true);
                    if (!string.IsNullOrEmpty(contactDetails.MobileNo))
                    {
                        if (contactDetails.IsValidCustomer)
                        {
                            WebSiteServicDLL.MarkMobileNoAsValid(Convert.ToInt64(contactDetails.MobileNo));
                            response.Status = true;
                            response.Message = "MobileNo is updated as valid";
                        }
                        else
                        {
                            bool isInvalidNo = WebSiteServicDLL.ValidateCustomerContact(Convert.ToInt64(contactDetails.MobileNo));
                            if (isInvalidNo)
                            {
                                response.Status = false;
                                response.Message = "Invalid MobileNo";
                            }
                            else
                            {
                                response.Status = true;
                                response.Message = "Valid MobileNo";
                            }
                        }
                    }
                    else
                    {
                        response.Message = "MobileNo decryption failed";
                    }
                }
                else
                {
                    response.Message = "Invalid request data";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, 0, ex.ToString(), "ValidateCustomerContact", "MatrixCore", "WebSiteServiceBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }


        public ResponseData<string> SaveAIAudioData(AIAudioModel aiModel)
        {
            DateTime dt = DateTime.Now;
            ResponseData<string> response = new()
            {
                Status = true,
                Message = "failed"
            };
            try
            {
                if (aiModel != null && aiModel.AudioClipsPath.Count > 0)
                {
                    for (int i = 0; i < aiModel.AudioClipsPath.Count; i++)
                    {
                        aiModel.audioClip = aiModel.AudioClipsPath[i] != "" ? Convert.ToString(aiModel.AudioClipsPath[i]) : string.Empty;
                        if (CoreCommonMethods.IsValidString(aiModel.audioClip))
                            WebSiteServicDLL.SaveAIAudioData(aiModel);
                    }

                }

                //if (result)
                //{
                response.Status = true;
                response.Message = "Save Successfully";
                // }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, 0, ex.ToString(), "SaveAIAudioData", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(aiModel), string.Empty, dt, DateTime.Now);

            }
            return response;

        }


        public List<KnowYoutAdvisorModel> GetProductListByEmpId(KnowYoutAdvisorModel knowYoutAdvisorModel, out bool status, out Int16 code)
        {
            List<KnowYoutAdvisorModel> lstKnowYoutAdvisorModel = null;
            DateTime dt = DateTime.Now;
            string err = string.Empty;
            status = false;
            code = 0;


            try
            {
                string EncryptData = Crypto.decrypt_AES(knowYoutAdvisorModel.encryptCustId, 256, 128, "MyAccEncKey".AppSettings(), "MyAccIVKey".AppSettings());

                if (CoreCommonMethods.IsValidString(EncryptData) && EncryptData.Contains("CustId"))
                {
                    dynamic obj = JsonConvert.DeserializeObject(EncryptData);
                    List<KnowYoutAdvisorModel> lstKnowYoutAdvisor = WebSiteServicDLL.GetKnowYoutAdvisorData(Convert.ToInt64(obj.CustId));
                    knowYoutAdvisorModel.CustId = Convert.ToInt64(obj.CustId);

                    if (lstKnowYoutAdvisor.Count > 0)
                    {
                        KnowYoutAdvisorModel LstRecordKnowYoutAdvisor = lstKnowYoutAdvisor.Last();
                        LstRecordKnowYoutAdvisor.CreatedOn = LstRecordKnowYoutAdvisor.CreatedOn != DateTime.MinValue && LstRecordKnowYoutAdvisor.CreatedOn.Kind == DateTimeKind.Utc ? LstRecordKnowYoutAdvisor.CreatedOn.ToLocalTime() : LstRecordKnowYoutAdvisor.CreatedOn;
                        knowYoutAdvisorModel.Count = LstRecordKnowYoutAdvisor.Count;
                        knowYoutAdvisorModel._id = LstRecordKnowYoutAdvisor._id;
                        double span = (DateTime.Now - LstRecordKnowYoutAdvisor.CreatedOn).TotalHours;
                        if (span < 24)
                            code = LstRecordKnowYoutAdvisor.Count >= 3 ? (Int16)EnumVerifyAdvisor.Attempt_Exceeded : WebSiteServicDLL.InsertUpdateAdvisoreData(knowYoutAdvisorModel, 1);//update case
                        else
                            WebSiteServicDLL.InsertUpdateAdvisoreData(knowYoutAdvisorModel);
                    }
                    else
                        WebSiteServicDLL.InsertUpdateAdvisoreData(knowYoutAdvisorModel); //insert data


                    if (code == 0)
                    {
                        lstKnowYoutAdvisorModel = GetProductListByEmpId(knowYoutAdvisorModel.EmployeeId, out status);
                        if (lstKnowYoutAdvisorModel != null && lstKnowYoutAdvisorModel.Count > 0)
                        {
                            status = lstKnowYoutAdvisorModel != null && lstKnowYoutAdvisorModel.Count > 0 ? true : false;
                            code = (short)(lstKnowYoutAdvisorModel.Count == 0 ? EnumVerifyAdvisor.Agent_NotFound : 0);
                        }
                        else
                            code = (short)EnumVerifyAdvisor.Agent_NotFound;
                    }
                }
                else
                    code = (Int16)EnumVerifyAdvisor.Customer_NotFound;

            }
            catch (Exception ex)
            {
                code = (Int16)EnumVerifyAdvisor.SomeErrorOccuerd;
                err = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, err, "GetProductListByEmpId", "webSiteServiceBll", "Matrixcore", JsonConvert.SerializeObject(knowYoutAdvisorModel), "", dt, DateTime.Now);
            }
            return lstKnowYoutAdvisorModel;
        }

        public ResponseData<string> GetPGPaymentLink(string leadId, string Source)
        {
            SalesViewBLL objSalesViewBLL = new SalesViewBLL();
            DateTime dt = DateTime.Now;
            var response = new ResponseData<string>();
            try
            {
                DataSet ds = SalesViewDLL.getBookingDetailsforPG(Convert.ToInt64(leadId), Source);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    string ProductID = ds.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["ProductID"]);
                    string paymentLink = objSalesViewBLL.GetInstantPaymentLink(leadId, ds, ProductID);
                    if (string.IsNullOrEmpty(paymentLink) || !paymentLink.Contains("https"))
                    {
                        response.Status = true;
                        response.Message = "Invalid Payment Link from PG";
                        response.Data = "";
                    }
                    else
                    {
                        response.Status = true;
                        response.Message = "Success";
                        response.Data = paymentLink;
                    }
                }
                else
                {
                    response.Status = true;
                    response.Message = "Not found any payment due on this Bookings ID - " + leadId;
                    response.Data = "";
                }
            }
            catch (Exception ex)
            {
                response.Status = false;
                response.Message = ex.Message;
                response.Data = "";
                LoggingHelper.LoggingHelper.AddloginQueue(leadId, Convert.ToInt64(leadId), ex.ToString(), "GetPGPaymentLink", "MatrixCore", "WebSiteServiceBLL", leadId, JsonConvert.SerializeObject(response), dt, DateTime.Now);

            }
            return response;
        }
        public ResponseData<string> GetEMandateEnableLink(string leadId)
        {
            DateTime dt = DateTime.Now;
            var response = new ResponseData<string>();
            string EmandateLink = "";
            string jsonData = "";
            try
            {
                string EncLeadID = Crypto.encrypt_AES(leadId.ToString(), 256, 128, "pgHealthEncKey".AppSettings(), "pgHealthIVKey".AppSettings());
                jsonData = "{\"leadId\":\"" + EncLeadID + "\"}";
                string url = "pgAPI".AppSettings() + "pgi/si/latestAll?isCombo=false";
                Dictionary<object, object> header = new Dictionary<object, object>();
                header.Add("auth", "pgAuthKey".AppSettings());
                header.Add("mid", "pgMID".AppSettings());
                if (!string.IsNullOrEmpty(url) && !string.IsNullOrEmpty(jsonData))
                {
                    var data = CommonAPICall.CallAPI(url, jsonData, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                    dynamic obj = JsonConvert.DeserializeObject(data);
                    if (obj.ok == "1")
                        EmandateLink = Convert.ToString(obj.details.recurringRegUrl);
                }
                if (string.IsNullOrEmpty(EmandateLink) || !EmandateLink.Contains("https"))
                {
                    response.Status = true;
                    response.Message = "Invalid Emandate Link from PG";
                    response.Data = "";
                }
                else
                {
                    response.Status = true;
                    response.Message = "Success";
                    response.Data = EmandateLink;
                }
            }
            catch (Exception ex)
            {
                response.Status = false;
                response.Message = ex.Message;
                response.Data = "";
                LoggingHelper.LoggingHelper.AddloginQueue(leadId, Convert.ToInt64(leadId), ex.ToString(), "GetEMandateEnableLink", "MatrixCore", "WebSiteServiceBLL", jsonData, JsonConvert.SerializeObject(response), dt, DateTime.Now);

            }
            return response;
        }

        public static void CreateLeadByCjApi(StringBuilder logs, CreateLeadRequest request, CreateLeadResponse response, CreateLeadDetails leadDetails)
        {
            DateTime dt = DateTime.Now;
            string err = string.Empty;

            string result = string.Empty;
            dynamic obj = new ExpandoObject();
            try
            {
                string url = "InvestmentJorneyUrl".AppSettings() + "api/enqapi/PreQuoteV2/ConsolidatedLeadWrapper";
                obj.CustomerName = request.Name;
                obj.MobileNo = request.MobileNo;
                obj.Email = request.Email;
                obj.UtmSource = CoreCommonMethods.IsValidString(request.UtmSource) ? request.UtmSource : "";
                obj.UtmTerm = request.UtmTerm;
                obj.LeadSource = request.LeadSource;
                if (request.ReferralLead > 0)
                {
                    obj.ReferralID = request.ReferralLead;
                }

                //result = CommonAPICall.CallAPI(url, JsonConvert.SerializeObject(obj), "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", null);
                result = CommonAPICall.CallAPI(url, JsonConvert.SerializeObject(obj), "POST", Convert.ToInt32("45000"), "application/json", null);
                if (CoreCommonMethods.IsValidString(result))
                {
                    logs.Append(" create customer and Lead from Cj - " + DateTime.Now);

                    CjReferralResponseModel response1 = JsonConvert.DeserializeObject<CjReferralResponseModel>(result.ToString());
                    if (response1 != null && !response1.HasError)
                    {
                        response.LeadId = Convert.ToInt64(response1.ReturnValue);

                        DataSet oDataSet = FOSDLL.GetLeadBasicInfo(response.LeadId);

                        if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                        {
                            long ParentId = (oDataSet.Tables[0].Rows[0]["ParentID"] != null && oDataSet.Tables[0].Rows[0]["ParentID"] != DBNull.Value) ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentID"]) : 0;
                            long AssignTo = (oDataSet.Tables[0].Rows[0]["AssignTo"] != null && oDataSet.Tables[0].Rows[0]["AssignTo"] != DBNull.Value) ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["AssignTo"]) : 0;

                            response.IsLeadCreated = true;
                            response.Message = ParentId != response.LeadId ? response.LeadId + " Child lead is created successfully, " + ParentId + " - Parent lead is already active in system" : "Lead Created successfully " + Convert.ToString(response.LeadId);
                            WebSiteServicDLL.UpdateReferralId(request.ReferralLead, response.LeadId, request.UserId);

                            if (AssignTo == 0 && ParentId == response.LeadId) //if not assign then assign 
                            {
                                if (request.AssignedUser == 0 && request.AssignedGroupId == 0)
                                {
                                    WebSiteServicDLL.AssignLeadToAgent(response.LeadId, request.UserId, request.UserId, request.ProductId, 0, 79);
                                    logs.Append(" Assignment Done - " + DateTime.Now);
                                }
                                else if (request.AssignedUser > 0 && request.AssignedGroupId > 0)
                                {
                                    WebSiteServicDLL.AssignLeadToAgent(response.LeadId, request.AssignedUser, request.UserId, request.ProductId, request.AssignedGroupId, 79);
                                    logs.Append(" Assignment Done - " + DateTime.Now);
                                }
                            }
                        }
                        else
                        {
                            var objToPush = new { LeadId = response.LeadId , IsLeadCreated = true, ReferralLeadId = request.ReferralLead, AssignedByAgentId = request.UserId, ProductId = request.ProductId, AssignedToUSerId = request.AssignedUser, AssignedToGroupId = request.AssignedGroupId };
                            string json = JsonConvert.SerializeObject(objToPush);

                            WebSiteServicDLL.SetReferralLeadCreationData(response.LeadId, json, request.UserId, "ReferralLeadCreationINV");

                            response.IsLeadCreated = true;
                            //and will be assigned to you in a minute
                            response.Message = response.LeadId.ToString() + " - Lead Created successfully and will be assigned to you shortly";
                        }
                    }
                }
                else
                {
                    logs.Append(" Lead Creation failed at CreateLeadByCjApi - " + DateTime.Now);
                    response.Message = "Lead Creation failed";
                }
            }
            catch (Exception ex)
            {
                err = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(null, request.ReferralLead, err.ToString(), "CreateLeadByCjApiException", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), JsonConvert.SerializeObject(result), dt, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, request.ReferralLead, err.ToString(), "CreateLeadByCjApi", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), JsonConvert.SerializeObject(result), dt, DateTime.Now);

            }
        }

        public ResponseData<string> NotifyAgent(NotifyAgentRequest NotifyAgentReq)
        {
            DateTime dt = DateTime.Now;
            ResponseData<string> response = new ResponseData<string>();
            string Exception = string.Empty;
            Dictionary<string, string> requestdata = null;
            try
            {
                if (NotifyAgentReq != null && Convert.ToInt64(NotifyAgentReq.LeadID) > 0)
                {
                    DataSet ds = WebSiteServicDLL.GetLeadAssignDetails(NotifyAgentReq.LeadID);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        int ProductID = ds.Tables[0].Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["ProductID"]) : 0;
                        string EmployeeID = ds.Tables[0].Rows[0]["EmployeeId"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]) : "";
                        string id = NotifyAgentReq.LeadID.ToString() + "NotifyAgent";

                        if (!string.IsNullOrEmpty(EmployeeID))
                        {
                            var data = new Dictionary<string, string> {
                             { "id", id },
                             { "type", "notification" },
                             { "event", "Whatsapp Notification" },
                             { "text", NotifyAgentReq.AlertMessage.ToString()},
                             { "Reason", "Notify"},
                             { "empID", EmployeeID},
                             //{ "productID", ProductID.ToString()},
                             { "link", null}
                            };

                            requestdata = data;
                            var request = new List<Dictionary<string, string>>
                        {
                            data
                        };
                            var result = CommonAPICall.CallAPI("notificationurl".AppSettings(),
                                                               JsonConvert.SerializeObject(request),
                                                               "POST", 3000, "application/json", null);
                            bool res = Convert.ToBoolean(result);
                            if (res)
                            {
                                response.Status = true;
                                response.Message = "Notified!";
                            }
                            else
                            {
                                response.Status = false;
                                response.Message = "Please try again";
                            }
                        }
                    }
                    else
                    {
                        response.Status = false;
                        response.Message = "Invalid Lead Details, Please try again";
                    }
                }
            }
            catch (Exception ex)
            {
                Exception = ex.ToString();
                response.Status = false;
                response.Message = "Error Occured, Please try again";
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(NotifyAgentReq.LeadID.ToString(), NotifyAgentReq.LeadID, Exception, "NotifyAgent", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(requestdata), JsonConvert.SerializeObject(response), dt, DateTime.Now);
            }
            return response;
        }

        public Int16 CheckHealthFraud(DataSet oDataSet, string EncKey, string EncIV)
        {
            dynamic leaddetailobj = new ExpandoObject();
            Int16 res = 0;
            string apiResponse = string.Empty;
            try
            {
                leaddetailobj.leadid = oDataSet.Tables[0].Rows[0]["LeadId"] != null && oDataSet.Tables[0].Rows[0]["LeadId"] != DBNull.Value ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["LeadId"]) : 0;
                leaddetailobj.customerid = oDataSet.Tables[0].Rows[0]["CustomerID"] != null && oDataSet.Tables[0].Rows[0]["CustomerID"] != DBNull.Value ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["CustomerID"]) : 0;
                leaddetailobj.mobile_no = oDataSet.Tables[0].Rows[0]["MobileNo"] != null && oDataSet.Tables[0].Rows[0]["MobileNo"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(Convert.ToString(oDataSet.Tables[0].Rows[0]["MobileNo"]), "Core", 256, 128, EncKey, EncIV, false) : "";
                leaddetailobj.emailid = oDataSet.Tables[0].Rows[0]["EmailID"] != null && oDataSet.Tables[0].Rows[0]["EmailID"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(Convert.ToString(oDataSet.Tables[0].Rows[0]["EmailID"]), "Core", 256, 128, EncKey, EncIV, false) : "";

                Dictionary<object, object> objHeaders = new Dictionary<object, object>() { { "x-access-token", "HealthFraudApiAuthKey".AppSettings() } };

                string url = "HealthFraudURL".AppSettings();
                string json = JsonConvert.SerializeObject(leaddetailobj);
                apiResponse = CommonAPICall.CallAPI(url, json, "POST", Convert.ToInt32("AITimeOut".AppSettings()), "application/json", objHeaders);
                var Response = JsonConvert.DeserializeObject<dynamic>(apiResponse);

                if (Response != null && Response.status == 200 && Response.score != null)
                {
                    res = (Response.score.age_score == 1 || Response.score.multiple_payments == 1 || Response.score.agent_fraud == 1 || Response.score.multiple_policies_on_emailid == 1 || Response.score.multiple_policies_on_mobile_no == 1) ? Convert.ToInt16(1) : Convert.ToInt16(0);

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leaddetailobj.leadid, ex.Message,
                                                  "CheckHealthFraud", "MatrixCore",
                                                  "WebSiteServiceBLL", "",
                                                  apiResponse, DateTime.Now, DateTime.Now);

            }
            return res;
        }


        public ResponseData<string> InsertAgentAPEData(InsertAgentAPEDataRequest objreq)
        {
            DateTime dt = DateTime.Now;
            ResponseData<string> response = new ResponseData<string>();

            try
            {
                if (objreq.AgentId > 0)
                {
                    if (objreq.StartTime > 0 && objreq.EndTime > 0)
                    {
                        DateTime StartTime = CoreCommonMethods.UnixTimeToDateTime(Convert.ToInt64(objreq.StartTime));
                        DateTime EndTime = CoreCommonMethods.UnixTimeToDateTime(Convert.ToInt64(objreq.EndTime));
                        WebSiteServicDLL.InsertAgentAPEData(objreq, StartTime, EndTime);
                        response.Message = "Successfully Inserted";
                        response.Status = true;
                    }
                    else
                    {
                        response.Message = "Invalid StartTime or EndTime";
                        response.Status = false;
                    }
                }
                else
                {
                    response.Message = "Invalid Agent Details";
                    response.Status = false;
                }
            }
            catch (Exception ex)
            {
                response.Status = false;
                response.Message = "Error Occured, Please try again";
                LoggingHelper.LoggingHelper.AddloginQueue(objreq.AgentId.ToString(), objreq.AgentId, ex.ToString(), "InsertAgentAPEData", "WebSiteServiceBLL", "MatrixCoreAPI", objreq.ToString(), "", dt, DateTime.Now);
            }

            return response;
        }

        public ApplicationMonitorMaster GetApplicationMonitoringMaster()
        {
            ApplicationMonitorMaster objres = new ApplicationMonitorMaster();
            List<TeamMaster> objTeamList = new List<TeamMaster>();
            List<ApplicationMaster> objApplicationList = new List<ApplicationMaster>();
            List<IssueCategoryMaster> objIssueCategoryList = new List<IssueCategoryMaster>();
            DataSet oDataSet = WebSiteServicDLL.GetApplicationMonitoringMasterDLL();
            if (oDataSet != null && oDataSet.Tables.Count > 0)
            {
                if (oDataSet.Tables[0].Rows.Count > 0)
                {
                    objTeamList = (from dr in oDataSet.Tables[0].AsEnumerable()
                                   select new TeamMaster
                                   {
                                       TeamID = Convert.ToInt32(dr["TeamId"]),
                                       TeamName = Convert.ToString(dr["TeamName"])
                                   }).ToList();
                }
                if (oDataSet.Tables[1].Rows.Count > 0)
                {
                    objApplicationList = (from dr in oDataSet.Tables[1].AsEnumerable()
                                          select new ApplicationMaster
                                          {
                                              TeamID = Convert.ToInt32(dr["TeamId"]),
                                              ApplicationID = Convert.ToInt32(dr["AppID"]),
                                              ApplicationName = Convert.ToString(dr["AppName"])
                                          }).ToList();
                }
                if (oDataSet.Tables[2].Rows.Count > 0)
                {
                    objIssueCategoryList = (from dr in oDataSet.Tables[2].AsEnumerable()
                                            select new IssueCategoryMaster
                                            {
                                                IssueCategoryID = Convert.ToInt32(dr["IssueCategoryID"]),
                                                IssueCategoryName = Convert.ToString(dr["IssueCategoryName"])
                                            }).ToList();
                }
                objres.TeamMasterList = objTeamList;
                objres.ApplicationMasterList = objApplicationList;
                objres.IssueCategoryMasterList = objIssueCategoryList;
            }
            return objres;
        }
        public SaveInfo PushApplicationMonitoringDetails(AppMonitorReqModel objAppMonitorReqModel)
        {
            SaveInfo oSaveInfo = new SaveInfo();
            DateTime reqTime = DateTime.Now;
            string URL = String.Empty;
            string PlugInURL = string.Empty;

            try
            {
                DataSet ds = WebSiteServicDLL.PushApplicationMonitoringDetails(objAppMonitorReqModel);
                if (ds != null & ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    oSaveInfo.IsSaved = true;
                    string UniqueID = ds.Tables[0].Rows[0]["UniqueID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["UniqueID"]);
                    string MessageDesc = ds.Tables[0].Rows[0]["MessageDescription"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["MessageDescription"]);
                    int IsMsgChange = objAppMonitorReqModel.AlertMessage == MessageDesc ? 0 : 1;
                    MessageDesc = !string.IsNullOrEmpty(objAppMonitorReqModel.AlertMessage) ? objAppMonitorReqModel.AlertMessage : MessageDesc;
                    int IsApplicationAlreadyExists = ds.Tables[0].Rows[0]["IsApplicationAlreadyExists"] == DBNull.Value ? 0 : Convert.ToInt16(ds.Tables[0].Rows[0]["IsApplicationAlreadyExists"]);
                    if (IsApplicationAlreadyExists == 0 || IsMsgChange == 1)
                    {
                        URL = "http://**********/api/v2/broadcast/setTtsWtihApplication?uid=" + UniqueID + "&message=" + MessageDesc;
                        string result = CommonAPICall.CallAPI(URL, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
                        dynamic res = JObject.Parse(result);
                        if (res != null && res.message == "Success")
                        { PlugInURL = Convert.ToString(res.data.api); }
                        bool isURLInserted = WebSiteServicDLL.SaveURLForApplnMonitor(UniqueID, PlugInURL);
                    }
                }
                oSaveInfo.Message = oSaveInfo.IsSaved ? "Save Successfully" : "failed";
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objAppMonitorReqModel.TeamID.ToString(), objAppMonitorReqModel.ApplicationID, ex.ToString(), "PushApplicationMonitoringDetails", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(objAppMonitorReqModel), JsonConvert.SerializeObject(oSaveInfo), reqTime, DateTime.Now);
            }
            return oSaveInfo;
        }
        public List<AppMonitorData> GetApplicationMonitoringData()
        {
            List<AppMonitorData> oAppMonitorData = new List<AppMonitorData>();
            DateTime dt = DateTime.Now;
            try
            {
                DataSet ds = WebSiteServicDLL.GetApplicationMonitoringDataDLL();
                if (ds != null & ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    oAppMonitorData = (from dr in ds.Tables[0].AsEnumerable()
                                       select new AppMonitorData
                                       {
                                           ID = dr["ID"] != null && dr["ID"] != DBNull.Value ? Convert.ToInt16(dr["ID"]) : 0,
                                           UniqueID = dr["UNIQUEID"] != null && dr["UNIQUEID"] != DBNull.Value ? Convert.ToString(dr["UNIQUEID"]) : string.Empty,
                                           TeamID = dr["TeamID"] != null && dr["TeamID"] != DBNull.Value ? Convert.ToInt16(dr["TeamID"]) : default,
                                           TeamName = dr["TeamName"] != null && dr["TeamName"] != DBNull.Value ? Convert.ToString(dr["TeamName"]) : string.Empty,
                                           ApplicationID = dr["AppID"] != null && dr["AppID"] != DBNull.Value ? Convert.ToInt16(dr["AppID"]) : default,
                                           ApplicationName = dr["AppName"] != null && dr["AppName"] != DBNull.Value ? Convert.ToString(dr["AppName"]) : string.Empty,
                                           MemberName = dr["MemberName"] != null && dr["MemberName"] != DBNull.Value ? Convert.ToString(dr["MemberName"]) : string.Empty,
                                           MobileNo = dr["MobileNo"] != null && dr["MobileNo"] != DBNull.Value ? Convert.ToString(dr["MobileNo"]) : string.Empty,
                                           Level = dr["Level"] != null && dr["Level"] != DBNull.Value ? Convert.ToString(dr["Level"]) : string.Empty,
                                           IssueCategoryID = dr["IssueCategoryID"] != null && dr["IssueCategoryID"] != DBNull.Value ? Convert.ToString(dr["IssueCategoryID"]) : string.Empty,
                                           IssueCategory = dr["IssueCategoryName"] != null && dr["IssueCategoryName"] != DBNull.Value ? Convert.ToString(dr["IssueCategoryName"]) : string.Empty,
                                           PlugInURL = dr["URL"] != null && dr["URL"] != DBNull.Value ? Convert.ToString(dr["URL"]) : string.Empty,
                                           MessageDesc = dr["MessageDesc"] != null && dr["MessageDesc"] != DBNull.Value ? Convert.ToString(dr["MessageDesc"]) : string.Empty,
                                       }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("0", 0, ex.ToString(), "GetApplicationMonitoringData", "MatrixCore", "WebSiteServiceBLL", "", "", dt, DateTime.Now);
            }
            return oAppMonitorData;
        }

        public bool DeleteApplicationData(int ID)
        {
            return WebSiteServicDLL.DeleteApplicationData(ID);
        }

        public string GetRenewalLeadData(long leadId)
        {
            var res = string.Empty;
            try
            {
                if (leadId > 0)
                {
                    DataSet ds = WebSiteServicDLL.GetRenewalLeadData(leadId);
                    if (ds != null && ds.Tables.Count > 0)
                    {
                        var dataRow = ds.Tables[0].Rows[0];
                        var data = new
                        {
                            RenewalLeadId = dataRow["LeadID"],
                            StatusId = dataRow["StatusId"],
                            StatusName = dataRow["StatusName"],
                            SubStatusId = dataRow["SubStatusID"],
                            SubStatusName = dataRow["SubStatusName"]
                        };
                        res = JsonConvert.SerializeObject(data);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "GetRenewalLeadData", "WebSiteServiceBLL", "MatrixCoreAPI", string.Empty, "", DateTime.Now, DateTime.Now);
            }
            return res;
        }

        public void SetPetDetails(PetDetails petDetail)
        {
            DateTime requestTime = DateTime.Now;

            try
            {
                WebSiteServicDLL.SetPetDetails(petDetail);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(petDetail.LeadID.ToString(), petDetail.LeadID, ex.ToString(), "SetPetDetails", "WebSiteServiceBLL", "MatrixCore", JsonConvert.SerializeObject(petDetail).ToString(), ex.ToString(), requestTime, DateTime.Now);
            }
        }

        public ResponseData<SmeData> GetSmeLeadData(long leadId)
        {
            var res = new ResponseData<SmeData>()
            {
                Data = new SmeData(),
                Status = false,
                Message = "Error occured"
            };
            try
            {
                if (leadId > 0)
                {
                    DataSet ds = WebSiteServicDLL.GetSmeLeadData(leadId);
                    if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
                    {
                        var table1 = ds.Tables[0];
                        var table2 = ds.Tables[1];
                        var table3 = ds.Tables[2];
                        var table4 = ds.Tables[3];
                        var table5 = ds.Tables[4];

                        if (
                              (table1 == null || table1.Rows == null || table1.Rows.Count == 0) &&
                              (table2 == null || table2.Rows == null || table2.Rows.Count == 0) &&
                              (table3 == null || table3.Rows == null || table3.Rows.Count == 0) &&
                              (table4 == null || table4.Rows == null || table4.Rows.Count == 0) &&
                              (table5 == null || table5.Rows == null || table5.Rows.Count == 0)
                           )
                        {
                            res.Message = "No data found for this Lead";
                        }
                        else
                        {
                            if (table1 != null && table1.Rows != null && table1.Rows.Count > 0)
                            {
                                res.Data.LeaderSupplierId = table1.Rows[0]["SupplierId"] != DBNull.Value ? Convert.ToInt32(table1.Rows[0]["SupplierId"]) : default;
                                res.Data.LeadersPercentage = table1.Rows[0]["LeaderSupplierPercentage"] != DBNull.Value ? Convert.ToDecimal(table1.Rows[0]["LeaderSupplierPercentage"]) : default;
                                res.Data.CoInsurance = table1.Rows[0]["CoInsurance"] != DBNull.Value && Convert.ToBoolean(table1.Rows[0]["CoInsurance"]);
                                res.Data.CoverageType = new CoverageType
                                {
                                    CoverageTypeName = table1.Rows[0]["CoverType"] != DBNull.Value ? Convert.ToString(table1.Rows[0]["CoverType"]) : default,
                                    CoverageTypeId = table1.Rows[0]["CoverTypeId"] != DBNull.Value ? Convert.ToByte(table1.Rows[0]["CoverTypeId"]) : default,
                                };
                            }

                            if (table2 != null && table2.Rows != null && table2.Rows.Count > 0)
                            {
                                res.Data.ShipmentType = table2.Rows[0]["ShipmentType"] != DBNull.Value ? Convert.ToString(table2.Rows[0]["ShipmentType"]) : default;
                            }

                            if (table3 != null && table3.Rows != null && table3.Rows.Count > 0)
                            {
                                FollowerSuppliers(res, table3);
                            }

                            if (table4 != null && table4.Rows != null && table4.Rows.Count > 0)
                            {
                                GetAgentsWithSharePercentage(res, table4);
                            }

                            if (table5 != null && table5.Rows != null && table5.Rows.Count > 0)
                            {
                                GetInstallmentDetails(res, table5);
                            }

                            res.Status = true;
                            res.Message = "Success";
                        }
                    }
                    else
                    {
                        res.Message = "Data for this Lead does not exists";
                    }
                }
                else
                {
                    res.Message = "Invalid LeadId";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "GetSmeLeadData", "WebSiteServiceBLL", "MatrixCoreAPI", string.Empty, "", DateTime.Now, DateTime.Now);
            }
            return res;
        }

        private static void FollowerSuppliers(ResponseData<SmeData> res, DataTable dataTable)
        {
            if (dataTable != null && dataTable.Rows != null && dataTable.Rows.Count > 0)
            {
                res.Data.FollowerSuppliers = dataTable.AsEnumerable().ToList().Select(dr => new FollowerSupplierEntity
                {
                    FollowerPercentage = dr["FollowerPercentage"] != DBNull.Value ? Convert.ToInt16(dr["FollowerPercentage"]) : default,
                    SupplierId = dr["SupplierId"] != DBNull.Value ? Convert.ToInt16(dr["SupplierId"]) : default,
                }).ToList();
            }
        }

        private static void GetInstallmentDetails(ResponseData<SmeData> res, DataTable dataTable)
        {
            if (dataTable != null && dataTable.Rows != null && dataTable.Rows.Count > 0)
            {
                res.Data.Installments = dataTable.AsEnumerable().ToList().Select(dr => new InstallmentDetails
                {
                    Date = dr["InstallmentDate"] != DBNull.Value ? Convert.ToDateTime(dr["InstallmentDate"]) : default,
                    Amount = dr["InstallmentAmount"] != DBNull.Value ? Convert.ToDecimal(dr["InstallmentAmount"]) : default,
                }).ToList();
            }
        }

        private static void GetAgentsWithSharePercentage(ResponseData<SmeData> res, DataTable dataTable)
        {
            if (dataTable != null && dataTable.Rows != null && dataTable.Rows.Count > 0)
            {
                res.Data.SalesAgents = dataTable.AsEnumerable().ToList().Select(dr => new SalesAgents
                {
                    UserId = dr["UserId"] != DBNull.Value ? Convert.ToString(dr["UserId"]) : default,
                    UserName = string.Format("{0} ({1})", dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : default, dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : default),
                    SharePercentage = dr["SharePercentage"] != DBNull.Value ? Convert.ToString(dr["SharePercentage"]) : default,
                    AgentType = dr["AgentType"] != DBNull.Value ? Convert.ToInt16(dr["AgentType"]) : default
                }).ToList();
            }
        }

        public static List<SupplierDTO> GetSuppliers()
        {
            var supplierKey = "GetAllSuppliers";
            var allSuppliers = new List<SupplierDTO>();
            try
            {
                ObjectCache memcache = MemoryCache.Default;
                allSuppliers = (List<SupplierDTO>)memcache.Get(supplierKey);
                if (allSuppliers == null || allSuppliers.Count <= 0)
                {
                    var dataSet = WebSiteServicDLL.GetSuppliers();
                    if (dataSet != null && dataSet.Tables != null && dataSet.Tables[0].Rows != null && dataSet.Tables[0].Rows.Count > 0)
                    {
                        allSuppliers = dataSet.Tables[0].AsEnumerable().ToList().Select(dr => new SupplierDTO
                        {
                            ProductId = dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : default,
                            SupplierId = dr["SupplierId"] != DBNull.Value ? Convert.ToInt64(dr["SupplierId"]) : default,
                            OldSupplierId = dr["OldSupplierId"] != DBNull.Value ? Convert.ToInt64(dr["OldSupplierId"]) : default,
                            SupplierDisplayName = dr["SupplierDisplayName"] != DBNull.Value ? Convert.ToString(dr["SupplierDisplayName"]) : default,
                            SupplierName = dr["SupplierName"] != DBNull.Value ? Convert.ToString(dr["SupplierName"]) : default,
                            SubCategoryId = dr["SubCategoryId"] != DBNull.Value ? Convert.ToInt32(dr["SubCategoryId"]) : default,
                            IsSaleAllowed = dr["IsSaleAllowed"] != DBNull.Value && Convert.ToBoolean(dr["IsSaleAllowed"]),
                            ProductName = dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : default
                        }).ToList();

                        var cachePolicies = new CacheItemPolicy()
                        {
                            AbsoluteExpiration = new DateTimeOffset(DateTime.UtcNow.AddHours(24))
                        };
                        memcache.Add(supplierKey, allSuppliers, cachePolicies);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("0", 0, ex.ToString(), "GetSuppliers", "WebSiteServiceBLL", "MatrixCore", string.Empty, ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return allSuppliers;
        }

        public ResponseData<List<SupplierDTO>> GetSuppliersByLeadId(long leadId, string productId, string leadSource)
        {
            var response = new ResponseData<List<SupplierDTO>>();
            try
            {
                if (leadId > 0)
                {
                    var allSuppliers = GetSuppliers();

                    if (!int.TryParse(productId, out int prodId) || string.IsNullOrEmpty(leadSource))
                    {
                        var dataRow = WebSiteServicDLL.GetLeadBasicInfo(leadId);
                        if (dataRow != null)
                        {
                            prodId = Convert.ToInt32(dataRow["ProductID"]);
                            leadSource = Convert.ToString(dataRow["LeadSource"]);
                        }
                    }

                    if (prodId == 2 && leadSource != "Renewal")
                    {
                        response.Data = allSuppliers.Where(item => item.ProductId == prodId && item.OldSupplierId > 0 && item.IsSaleAllowed).ToList();
                    }
                    else
                    {
                        response.Data = allSuppliers.Where(item => item.ProductId == prodId && item.OldSupplierId > 0).ToList();
                    }

                    if (response.Data != null && response.Data.Count > 0)
                    {
                        response.Status = true;
                        response.Message = "Success";
                    }
                    else
                    {
                        response.Message = "Suppliers are not available";
                    }
                }
                else
                {
                    response.Message = "Invalid LeadId";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(leadId.ToString(), leadId, ex.ToString(), "GetSuppliersByLeadId", "WebSiteServiceBLL", "MatrixCore", string.Empty, ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public ResponseData<List<Plans>> GetPlansByLeadId(long leadId, short supplierId, string productId, string leadSource)
        {
            var response = new ResponseData<List<Plans>>();
            try
            {
                if (leadId > 0)
                {
                    if (!short.TryParse(productId, out short prodId) || string.IsNullOrEmpty(leadSource))
                    {
                        var dataRow = WebSiteServicDLL.GetLeadBasicInfo(leadId);
                        if (dataRow != null)
                        {
                            prodId = Convert.ToInt16(dataRow["ProductID"]);
                            leadSource = Convert.ToString(dataRow["LeadSource"]);
                        }
                    }

                    var allPlans = new MasterBLL().GetProductPlans(prodId, supplierId, "Booking");

                    if (prodId == 2 && leadSource != "Renewal")
                    {
                        response.Data = allPlans.Where(item => item.IsSaleAllowed).ToList();
                    }
                    else
                    {
                        response.Data = allPlans;
                    }

                    if (response.Data != null && response.Data.Count > 0)
                    {
                        response.Status = true;
                        response.Message = "Success";
                    }
                    else
                    {
                        response.Message = "Plans are not available";
                    }
                }
                else
                {
                    response.Message = "Invalid LeadId";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(leadId.ToString(), leadId, ex.ToString(), "GetPlansByLeadId", "WebSiteServiceBLL", "MatrixCore", string.Empty, ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }


        public UserDetails GetAgentDetailsByECode(string EmployeeCode)
        {
            UserDetails userDetails = new();
            try
            {
                var EmployeeMasterDict = MasterData.EmployeeMaster();

                EmployeeMasterDict.TryGetValue(EmployeeCode, out userDetails);

                return userDetails;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetAgentDetailsByECode", "WebSiteServiceBLL", "MatrixCore", "", "", DateTime.Now, DateTime.Now);

            }
            return userDetails;
        }


        public ResponseAPI UpdateSelfiesData(SelfieModel request)
        {
            ResponseAPI oResponseAPI = new() { message = "failed" };
            string err = string.Empty;
            DateTime dt = DateTime.Now;
            try
            {
                oResponseAPI.status = WebSiteServicDLL.UpdateSelfiesData(request);
                oResponseAPI.message = oResponseAPI.status ? "Save Successfully" : "Failed";
            }
            catch (Exception ex)
            {
                err = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", request.AppointmentId, err.ToString(), "UpdateSelfiesData", "WebsiteServiceBll", "MatrixCore", JsonConvert.SerializeObject(request), JsonConvert.SerializeObject(oResponseAPI), dt, DateTime.Now);
            }

            return oResponseAPI;
        }


        public bool SetCustomerCtcClick(long leadId, bool isCallBackScheduled)
        {
            bool result = false;
            try
            {
                WebSiteServicDLL.SetCustomerCtcClick(leadId, isCallBackScheduled);
                result = true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "SetCustomerCtcClick", "WebsiteServiceBll", "MatrixCore", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public bool InsertShortCallData(ShortCallData obj)
        {
            bool result = false;
            try
            {
                result = WebSiteServicDLL.InsertShortCallData(obj);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(obj.LeadID.ToString(), obj.LeadID, ex.ToString(), "InsertShortCallData", "WebsiteServiceBll", "MatrixCore", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public bool PushHWEligibleData(HWEligibleData obj)
        {
            bool result = false;
            DateTime dt = DateTime.Now;
            string error = "";

            try
            {
                WebSiteServicDLL.PushHWEligibleData(obj);
                result = true;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.Log(obj.LeadId.ToString(), obj.LeadId, error, "PushHWEligibleData", "WebSiteServiceBLL", "MatrixCore", JsonConvert.SerializeObject(obj), result.ToString(), dt, DateTime.Now);
            }
            return result;
        }

        public bool SetCouponRedeemValue(long customerId)
        {
            bool result = false;
            DateTime reqTime = DateTime.Now;

            try
            {
                if (customerId > 0)
                {
                    result = WebSiteServicDLL.SetCouponRedeemValue(customerId);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(customerId), 0, ex.ToString(), "SetCouponRedeemValue", "WebSiteServiceBLL", "MatrixCore", "", "", reqTime, DateTime.Now);
            }

            return result;
        }
        public bool GetCouponRedeemValue(long customerId)
        {
            bool result = false;
            DateTime reqTime = DateTime.Now;

            try
            {
                if (customerId > 0)
                {
                    result = WebSiteServicDLL.GetCouponRedeemValue(customerId);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(customerId), 0, ex.ToString(), "GetCouponRedeemValue", "WebSiteServiceBLL", "MatrixCore", "", "", reqTime, DateTime.Now);
            }

            return result;
        }

        public CouponRedeemModel SetCouponRaiseRequest(CouponRedeemModel obj)
        {
            CouponRedeemModel response = new CouponRedeemModel();
            DateTime reqTime = DateTime.Now;

            try
            {
                if (obj.CustomerId > 0 && obj.LeadId > 0)
                {
                    var ds = WebSiteServicDLL.SetCouponRaiseRequest(obj);
                    if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        var dr = ds.Tables[0].Rows[0];
                        response.LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0;
                        response.UserName = dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty;
                        response.EmpCode = dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadId, ex.ToString(), "SetCouponRaiseRequest", "WebSiteServiceBLL", "MatrixCore", "", "", reqTime, DateTime.Now);

            }
            return response;
        }
        public List<CouponRedeemModel> GetCouponRaiseRequest(string UserId)
        {
            DateTime reqTime = DateTime.Now;
            List<CouponRedeemModel> list = new List<CouponRedeemModel>();

            try
            {
                DataSet ds = WebSiteServicDLL.GetCouponRaiseRequest(UserId);
                if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow dr in ds.Tables[0].Rows)
                    {
                        CouponRedeemModel obj = new CouponRedeemModel()
                        {
                            CustomerName = dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : string.Empty,
                            UserName = dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                            EmpCode = dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty,
                            LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                            CustomerId = dr["CustomerId"] != DBNull.Value ? Convert.ToInt64(dr["CustomerId"]) : 0,
                            ParentId = dr["ParentId"] != DBNull.Value ? Convert.ToInt64(dr["ParentId"]) : 0
                        };
                        list.Add(obj);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", list.ElementAt(0).LeadId, ex.ToString(), "GetCouponRaiseRequest", "WebSiteServiceBLL", "MatrixCore", "", "", reqTime, DateTime.Now);

            }
            return list;
        }
        public bool UpdateCouponRaiseRequest(CouponRedeemModel obj)
        {
            bool result = false;
            DateTime reqTime = DateTime.Now;

            try
            {
                if (obj.CustomerId > 0 && obj.LeadId > 0 && obj.ReleasedBy > 0)
                {
                    result = WebSiteServicDLL.UpdateCouponRaiseRequest(obj.CustomerId, obj.ReleasedBy);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadId, ex.ToString(), "UpdateCouponRaiseRequest", "WebSiteServiceBLL", "MatrixCore", "", "", reqTime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadId, "Released by - " + obj.ReleasedBy, "UpdateCouponRaiseRequest", "WebSiteServiceBLL", "MatrixCore", "", "", reqTime, DateTime.Now);
            }
            return result;
        }

        public bool PushVCPitchData(VCPitchData obj)
        {
            bool result = false;
            DateTime dt = DateTime.Now;

            try
            {
                WebSiteServicDLL.PushVCPitchData(obj);
                result = true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(obj.LeadID.ToString(), obj.LeadID, ex.ToString(), "PushVCPitchData", "WebSiteServiceBLL", "MatrixCore", "", "", dt, DateTime.Now);
            }
            return result;
        }

        public ResponseData<string> SaveFeatureData(FeatureData featureData)
        {
            ResponseData<string> result = new ResponseData<string>();
            result.Status = false;
            long custId = 0;
            try
            {
                if (featureData != null && featureData.CustomerId > 0 && !string.IsNullOrEmpty(featureData.Feature))
                {
                    custId = featureData.CustomerId;
                    WebSiteServicDLL.SaveFeatureData(featureData);
                    result.Status = true;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(custId), 0, ex.ToString(), "SaveFeatureData", "WebSiteServiceBLL", "MatrixCore", "", "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public List<AgentCallDetails> GetAgentCallDetails(string Input, string MobileNumber)
        {
            DateTime requesttime = DateTime.Now;
            string exception = string.Empty;
            List<AgentCallDetails> CallDetails = new List<AgentCallDetails>();
            try
            {
                if (string.IsNullOrEmpty(MobileNumber))
                {
                    string EncKey = "MyAccEncKey".AppSettings();
                    string IVKey = "MyAccIVKey".AppSettings();
                    string data = Crypto.decrypt_AES(Input, 256, 128, EncKey, IVKey);
                    dynamic obj = JsonConvert.DeserializeObject(data);
                    MobileNumber = obj.MobileNo;
                }

                if (!string.IsNullOrEmpty(MobileNumber))
                {
                    DataSet oDataSet = LeadPrioritizationDLL.GetCallDetailsByMobile(MobileNumber);
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        CallDetails = oDataSet.Tables[0].AsEnumerable()
                        .Select(dr => new AgentCallDetails()
                        {
                            EmployeeID = dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty,
                            Name = dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                            CallDate = Convert.ToString(dr["CallDate"]),
                            Duration = dr["Duration"] != DBNull.Value ? Convert.ToInt32(dr["Duration"]) : 0,
                            ProductID = dr["ProductID"] != DBNull.Value ? Convert.ToInt32(dr["ProductID"]) : 0,
                            ProductName = dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : string.Empty
                        }).ToList();
                    }
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(MobileNumber, 0, exception, "GetAgentCallDetails", "LeadPrioritizationBLL", "Matrixcore", Input, JsonConvert.SerializeObject(CallDetails), requesttime, DateTime.Now);
            }
            return CallDetails;
        }
        public ResponseData<List<LeadInfo>> GetActiveLeads(string InputValue, int TypeId, string EncKey, string EncIV, string source)
        {
            ResponseData<List<LeadInfo>> response = new ResponseData<List<LeadInfo>>();
            response.Data = new List<LeadInfo>();
            DateTime dt = DateTime.Now;
            string decryptedValue = "";
            LeadInfo item = new LeadInfo();

            try
            {
                if (!string.IsNullOrEmpty(InputValue))
                {
                    decryptedValue = Crypto.Decrytion_Payment_AES(InputValue, "Core", 256, 128, EncKey, EncIV, true);

                    DataSet ds = WebSiteServicDLL.GetActiveLeads(decryptedValue, TypeId, source);
                    if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow dr in ds.Tables[0].Rows)
                        {
                            if (source.ToLower() == "bms")
                            {
                                item = new LeadInfo()
                                {
                                    LeadID = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                                    ProductId = dr["ProductID"] != DBNull.Value ? Convert.ToInt32(dr["ProductID"]) : 0,
                                    ProductName = dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : string.Empty,
                                    IsFresh = dr["IsFresh"] != DBNull.Value ? Convert.ToBoolean(dr["IsFresh"]) : false,
                                    CustomerName = dr["CustomerName"] != DBNull.Value ? Convert.ToString(dr["CustomerName"]) : string.Empty,
                                    EmailId = dr["EmailId"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(Convert.ToString(dr["EmailId"]), "Core", 256, 128, EncKey, EncIV) : string.Empty,
                                    LeadSource = dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : string.Empty,
                                    RegistrationNumber = dr["RegistrationNo"] != DBNull.Value ? Convert.ToString(dr["RegistrationNo"]) : string.Empty,
                                    Make = dr["Make"] != DBNull.Value ? Convert.ToInt32(dr["Make"]) : 0,
                                    RegistrationDate = dr["RegistrationDate"] != DBNull.Value ? Convert.ToDateTime(dr["RegistrationDate"]) : DateTime.MinValue,
                                    MakeName = dr["MakeName"] != DBNull.Value ? Convert.ToString(dr["MakeName"]) : string.Empty,
                                    ModelName = dr["ModelName"] != DBNull.Value ? Convert.ToString(dr["ModelName"]) : string.Empty,
                                    MobileNo = dr["MobileNo"] != DBNull.Value ? Crypto.Encrytion_Payment_AES(Convert.ToString(dr["MobileNo"]), "Core", 256, 128, EncKey, EncIV) : string.Empty,
                                    CountryId = dr["CountryCode"] != DBNull.Value ? Convert.ToInt32(dr["CountryCode"]) : 0,
                                };
                            }
                            else if (source.ToLower() == "pbapp")
                            {
                                item = new LeadInfo()
                                {
                                    LeadID = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                                    ProductId = dr["ProductID"] != DBNull.Value ? Convert.ToInt32(dr["ProductID"]) : 0,
                                    ProductName = dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : string.Empty,
                                    CustomerName = dr["CustomerName"] != DBNull.Value ? Convert.ToString(dr["CustomerName"]) : string.Empty,
                                    LeadSource = dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : string.Empty,
                                    RegistrationNumber = dr["RegistrationNo"] != DBNull.Value ? Convert.ToString(dr["RegistrationNo"]) : string.Empty,
                                    BasicLeadDetail = new BasicLeadDetails()
                                    {
                                        LeadID = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                                        Gender = dr["Gender"] != DBNull.Value ? Convert.ToString(dr["Gender"]) : string.Empty,
                                        DOB = dr["DOB"] != DBNull.Value ? Convert.ToString(dr["DOB"]) : string.Empty,
                                        CreatedON = dr["CreatedON"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedON"]) : DateTime.MinValue,
                                        UpdatedOn = dr["UpdatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["UpdatedOn"]) : DateTime.MinValue,
                                        ParentID = dr["ParentID"] != DBNull.Value ? Convert.ToInt64(dr["ParentID"]) : 0,
                                        IsActive = dr["IsActive"] != DBNull.Value ? Convert.ToInt32(dr["IsActive"]) : 0,
                                        LastVisitedOn = dr["LastVisitedOn"] != DBNull.Value ? Convert.ToDateTime(dr["LastVisitedOn"]) : DateTime.MinValue,
                                        PolicyType = dr["PolicyType"] != DBNull.Value ? Convert.ToString(dr["PolicyType"]) : string.Empty,
                                        TalkTime = dr["TalkTime"] != DBNull.Value ? Convert.ToInt32(dr["TalkTime"]) : 0,
                                        EmployeeId = dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty,
                                        UserName = dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                                        CallScheduleDetails = dr["EventDate"] != DBNull.Value ? Convert.ToDateTime(dr["EventDate"]) : DateTime.MinValue,
                                        UTM_Medium = dr["UTM_Medium"] != DBNull.Value ? Convert.ToString(dr["UTM_Medium"]) : string.Empty,
                                    }
                                };
                            }
                            response.Data.Add(item);
                        }
                        response.Status = true;
                    }
                    else
                    {
                        response.Message = "Data not found!";
                        response.Status = true;
                    }
                }
                else
                {
                    response.Message = "Input Value is null";
                    response.Status = true;
                }
            }
            catch (Exception ex)
            {
                response.Message = ex.Message.ToString();
                response.Status = false;
                LoggingHelper.LoggingHelper.AddloginQueue(InputValue.ToString(), 0, ex.ToString(), "GetActiveLeadsByCustomerId", "WebSiteServiceBLL", "MatrixCore", "", "", dt, DateTime.Now);
            }
            return response;
        }

        public List<Response> RejectAllLeads(string ECode, string source, List<LeadInfo> rejectLeadInfo)
        {
            List<Response> response = new List<Response>();
            string input = string.Empty;
            long leadId = 0;

            try
            {
                input = Newtonsoft.Json.JsonConvert.SerializeObject(rejectLeadInfo);
                if (rejectLeadInfo.Count != 0 && rejectLeadInfo.Count < 11)
                {
                    foreach (var item in rejectLeadInfo)
                    {
                        leadId = item.LeadID;
                        WebSiteServicDLL.RejectAllLeads(ECode, source, item.LeadID, item.CustomerName, item.RejectAll, item.RegistrationNumber, item.RejectionReason, 0);
                        response.Add(new Response
                        {
                            status = true,
                            message = "Rejected",
                            LeadId = item.LeadID.ToString()
                        });
                    }

                }
                else
                {
                    response[0].status = false;
                    if (rejectLeadInfo.Count == 0)
                    {
                        response[0].message = "No leads are found to reject";
                    }
                    else
                    {
                        response[0].message = "Leads are greater than 10.";
                    }

                }

            }
            catch (Exception ex)
            {
                response[0].status = false;
                response[0].message = ex.ToString();

            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, response[0].message, "RejectALlLeads", "WebSiteServiceBLL", "MatrixCoreAPI", input, response[0].ToString(), DateTime.Now, DateTime.Now);
            }

            return response;

        }

        public Response ProcessLeadByCustomerId(long customerId, string regNo, string type, string source, int ReasonId)
        {
            Response res = new Response();
            res.status = false;
            DateTime reqTime = DateTime.Now;
            long leadId = 0;
            string reqjson = "";

            try
            {
                if (source == "motorcj")
                {
                    DataSet ds = WebSiteServicDLL.GetLeadByCustomerId(customerId, regNo);
                    if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        leadId = ds.Tables[0].Rows[0]["LeadId"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["LeadId"]) : 0;
                        if (leadId > 0)
                        {
                            reqjson = "{\"LeadId\":\"" + leadId + ",\"CustomerId\":" + customerId + ",\"Source\":" + source + ",\"RegistrationNo\":" + regNo + ",\"ReasonId\":" + ReasonId + "}";
                            WebSiteServicDLL.RejectAllLeads("", source, leadId, "", false, regNo, "", ReasonId);
                            res.status = true;
                            res.message = "Leads Rejected successfully!";
                        }
                    }
                    else
                    {
                        res.message = "No Lead Found!";
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", customerId, ex.Message, "ProcessLeadByCustomerId", "WebSiteServiceBLL", "MatrixCoreAPI", "", "", reqTime, DateTime.Now);
                res.message = ex.Message;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), customerId, "", "ProcessLeadByCustomerId", "WebSiteServiceBLL", "MatrixCoreAPI", "", reqjson, reqTime, DateTime.Now);
            }

            return res;
        }

        public bool IsCustomerDNC(long mobileNo)
        {
            bool result = false;
            DateTime reqTime = DateTime.Now;
            try
            {
                result = WebSiteServicDLL.IsCustomerDNC(mobileNo);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(mobileNo.ToString(), mobileNo, ex.ToString(), "IsCustomerDNC", "WebSiteServiceBLL", "MatrixCore", string.Empty, string.Empty, reqTime, DateTime.Now);
            }
            return result;
        }

        public bool SaveAssistanceData(AssistanceData obj)
        {
            bool result = false;
            DateTime dt = DateTime.Now;
            string MethodName = "SaveAssistanceData";
            string error = string.Empty;
            string key = string.Empty;
            string res = string.Empty;

            try
            {
                WebSiteServicDLL.SaveAssistanceData(obj);
                if(obj != null && obj.NeedAssistance != null && obj.NeedAssistance.ToLower() == "yes")
                {
                    key = Convert.ToString(obj.CustomerId)+":"+Convert.ToString(obj.ProductID);

                    res = MultiProdRedisHelper.GetMultiProdRedisData(key);
                    MethodName = "SaveAssistanceData-Redis";
                    if (string.IsNullOrEmpty(res))
                    {
                        res = "false";
                    }

                    MultiProdRedisHelper.ClearMultiProdRedisData(key);
                }
                result = true;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            } 
            finally {
                LoggingHelper.LoggingHelper.AddloginQueue(obj.CustomerId.ToString(), obj.CustomerId, error, MethodName, "WebSiteServiceBLL", "MatrixCore", key, res, dt, DateTime.Now);
            }
            return result;
        }

        public CouponRedeemModel GetCouponDataByLeadId(long leadId)
        {
            CouponRedeemModel obj = new CouponRedeemModel();
            DateTime reqTime = DateTime.Now;

            try
            {
                if (leadId > 0)
                {
                    DataSet ds = WebSiteServicDLL.GetCouponDataByLeadId(leadId);
                    if (ds != null && ds.Tables != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        var dr = ds.Tables[0].Rows[0];
                        obj = new CouponRedeemModel()
                        {
                            CustomerName = dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : string.Empty,
                            UserName = dr["UserName"] != DBNull.Value ? Convert.ToString(dr["UserName"]) : string.Empty,
                            EmpCode = dr["EmployeeId"] != DBNull.Value ? Convert.ToString(dr["EmployeeId"]) : string.Empty,
                            LeadId = dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                            CustomerId = dr["CustomerId"] != DBNull.Value ? Convert.ToInt64(dr["CustomerId"]) : 0,
                            ParentId = dr["ParentId"] != DBNull.Value ? Convert.ToInt64(dr["ParentId"]) : 0
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.ToString(), "GetCouponDataByLeadId", "WebSiteServiceBLL", "MatrixCore", string.Empty, string.Empty, reqTime, DateTime.Now);
            }

            return obj;
        }

        public bool InsertIVRFeedBack(string agentid, string leadid, string rating, string source, string process, long MobileNo, int ProductId, long CallDataId, long AppointmentId = 0)
        {
            DateTime dt = DateTime.Now;
            bool res = false;
            String error = string.Empty;

            try
            {
                WebSiteServicDLL.InsertIVRFeedBack(agentid, leadid, rating, source, process, MobileNo, ProductId, CallDataId, AppointmentId);
                res = true;
            }
            catch (Exception ex)
            {
                res = false;
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(agentid, 0, error, "InsertIVRFeedBack", "MatrixCore", "SalesViewBLL", Convert.ToString(leadid), Convert.ToString(res), dt, DateTime.Now);

            }
            return res;
        }

        public VirtualNoAgentDetails GetQueueDetailsForDailer(long leadId, int productId, int subProductId, string leadSource, bool IsFOSIbProcess=false)
        {
            VirtualNoAgentDetails Queuedtl = new VirtualNoAgentDetails();
            DataSet data = WebSiteServicDLL.GetQueueDetailsForDailer(leadId, productId, subProductId, leadSource, IsFOSIbProcess);
            if (data != null && data.Tables != null && data.Tables[0] != null && data.Tables[0].Rows.Count > 0)
            {
                DataRow row = data.Tables[0].Rows[0];                
                Queuedtl = new VirtualNoAgentDetails()
                {
                    Queue = row["Queuename"] != DBNull.Value ? row["Queuename"].ToString() : "",
                    EmployeeId = row["EmployeeID"] != DBNull.Value ? row["EmployeeID"].ToString() : "",
                    OtherLanguaQueue = row["OtherLanguaQueue"] != DBNull.Value ? row["OtherLanguaQueue"].ToString() : "",
                    AgentType = row["AgentType"] != DBNull.Value ? row["AgentType"].ToString() : ""

                };

            }

            return Queuedtl;
        }

        public static void TermSubscribeApi(long leadId, string mobile, int countryId)
        {
            DateTime dt = DateTime.Now;
            string err = string.Empty;

            string result = string.Empty;
            dynamic obj = new ExpandoObject();
            try
            {
                string url = "TermCjAPI".AppSettings() + "/api/dnd/subscribe";
                obj.mobileNo = mobile;
                obj.countryId = countryId;
                obj.source = "matrix";
                obj.isExtraDND = true;
                result = CommonAPICall.CallAPI(url, JsonConvert.SerializeObject(obj), "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);

            }
            catch (Exception ex)
            {
                err = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadId, err.ToString(), "TermSubscribeApi", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), result, dt, DateTime.Now);

            }
        }


        public ResponseData<LeadAssignDetails> AssignLeadWithDetails(long LeadID, string EmpCode, string Source, Int16 LeadType, String CallID, string callDate, string TransferType, string leadRank)
        {

            ResponseData<LeadAssignDetails> respose = new ResponseData<LeadAssignDetails>();
            respose.Data = new LeadAssignDetails();
            int rank = 0;
            int.TryParse(leadRank, out rank);

            bool result = false;
            string error = string.Empty;
            Int16 CallType = 0;
            DateTime dt = DateTime.Now;
            StringBuilder sb = new();
            sb.Append("EmpCode" + EmpCode + " ,Source:" + Source + " ,LeadType:" + LeadType + " CallID:" + CallID + " callDate" + callDate + "TransferType:" + TransferType);

            try
            {
                if (!string.IsNullOrEmpty(Source) && !(Source.ToUpper() == "RENEWAL CALLBACK"))
                {
                    Source = Source.Trim();
                    switch (Source.ToUpper())
                    {
                        case "INBOUND":
                            CallType = 1;
                            break;
                        case "IB":
                            CallType = 1;
                            break;
                        case "CTC":
                            CallType = 2;
                            break;
                        case "C2C":
                            CallType = 2;
                            break;
                        case "PDOB":
                            CallType = 3;
                            break;
                        case "CTCSCHEDULAR":
                            CallType = 4;
                            break;
                        case "CHAT":
                            CallType = 7;
                            break;
                        default:
                            CallType = 0;
                            break;
                    }

                    if (CallType.Equals(7))
                    {
                        WebSiteServicDLL.AssignChatLead(LeadID, EmpCode, 3, rank);
                    }
                    else if (WebSiteServicDLL.AssignLead(LeadID, EmpCode, CallType, TransferType))
                    {
                        DataSet oDataSet = LeadDetailsDLL.GetLeadDetails(LeadID, false);

                        if ((CallType == 1 || CallType == 2) && oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                        {
                            long ParenId = oDataSet.Tables[0].Rows[0]["ParentId"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentId"]);
                            short ProductID = oDataSet.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"]);
                            DialerDispDetails _DispositionUpdate = new()
                            {
                                CallId = CallID,
                                ParentID = ParenId,
                                ProductID = ProductID,
                                AgentCode = EmpCode,
                                IsBMS = false,
                                Status = "16",
                                CallType = Source.ToUpper() == "INBOUND" ? "IB" : Source.ToUpper(),
                                callDate = Convert.ToDateTime(callDate)
                            };
                            WebSiteServicDLL.InsertCallData(_DispositionUpdate);

                        }

                    }
                }
                respose.Data = GetLeadAssignedAgent(LeadID);
                if (respose.Data != null && !string.IsNullOrEmpty(respose.Data.EmployeeId) && respose.Data.EmployeeId.Equals(EmpCode))
                {
                    result = true;
                }

            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadID, error, "AssignLead", "MatrixCore", "BLL", sb.ToString(), result.ToString(), dt, DateTime.Now);
            }
            respose.Status = result;
            return respose;
        }

        public LeadAssignDetails GetLeadAssignedAgent(long LeadID)
        {
            LeadAssignDetails obj = new LeadAssignDetails();
            DataSet data = LeadDetailsDLL.GetleadAssignmentDetails(LeadID);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0 && data.Tables[0].Rows[0] != null)
            {
                obj.EmployeeId = Convert.ToString(data.Tables[0].Rows[0]["EmployeeId"]);
                if (!string.IsNullOrEmpty(obj.EmployeeId))
                {
                    obj.LeadAssignedOn = CoreCommonMethods.ToUnixTime(Convert.ToDateTime(data.Tables[0].Rows[0]["LeadAssignedOn"]));
                }
            }
            return obj;

        }

        public bool IsValidMobileNo(long MobileNo)
        {
            bool IsValid = true;
            if (MobileNo < 10000)
            {
                IsValid = false;
            }
            else
            {
                IsValid = WebSiteServicDLL.IsValidMobileNo(MobileNo);
            }
            return IsValid;
        }

        public ResponseData<Dictionary<object, object>> GetIncomeFromPayUTerm(IncomeFromPayUData obj)
        {
            DateTime reqTime = DateTime.Now;
            var res = new ResponseData<Dictionary<object, object>>()
            {
                Data = new Dictionary<object, object>(),
                Status = false,
                Message = "Error occured"
            };
            try
            {
                if (obj.LeadId <= 0 || obj.CustomerId <= 0 || string.IsNullOrEmpty(obj.MobileNoHash))
                {
                    res.Message = "LeadId,CustomerId and MobileNoHash are mandatory.";
                    return res;
                }

                DataSet payUIncomeDataset = WebSiteServicDLL.GetTermLeadsPayUIncome(obj.CustomerId);
                if (payUIncomeDataset != null)
                {
                    var ds = payUIncomeDataset.Tables[0].Rows[0];

                    string DerivedFrom = ds["Derivedfrom"] == null || ds["Derivedfrom"] == DBNull.Value ? "" : Convert.ToString(ds["Derivedfrom"]);

                    res.Data["AnnualIncome"] = ds["AnnualIncome"] == null || ds["AnnualIncome"] == DBNull.Value || DerivedFrom.ToLower() != "payu" ? 0 : Convert.ToInt64(ds["AnnualIncome"]);
                    res.Status = true;
                    res.Message = "Success";
                }
                else
                {
                    string DerivedFrom = "NA";
                    res.Data["AnnualIncome"] = 0;
                    long PredictedIncome = GetIncomeFromPayUAPI(obj.MobileNoHash, obj.CustomerId);

                    if (PredictedIncome > 0)
                    {
                        DerivedFrom = "PayU";
                        res.Data["AnnualIncome"] = PredictedIncome * 12;
                    }

                    res.Status = true;
                    res.Message = "Success";

                    WebSiteServicDLL.Insert2LacLeadsPushtoTerm(obj.LeadId, Convert.ToInt64(res.Data["AnnualIncome"]), obj.CustomerId, DerivedFrom);
                }

                if (res.Status && res.Data["AnnualIncome"] != null && Convert.ToInt64(res.Data["AnnualIncome"]) > 0)
                {
                    long annualIncome = Convert.ToInt64(res.Data["AnnualIncome"]);
                    if (annualIncome >= 400000 && annualIncome <= 499999)
                    {
                        res.Data["AnnualIncome"] = 700000;
                    }
                    else if (annualIncome >= 500000 && annualIncome <= 799999)
                    {
                        res.Data["AnnualIncome"] = 1000000;
                    }
                    else if (annualIncome >= 800000)
                    {
                        res.Data["AnnualIncome"] = 1500001;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(obj.CustomerId.ToString(), obj.CustomerId, ex.ToString(), "GetIncomeFromPayUTerm", "WebSiteServiceBLL", "MatrixCoreAPI", string.Empty, "", reqTime, DateTime.Now);
            }
            return res;
        }

        public static long GetIncomeFromPayUAPI(string MobileNoHash, long CustomerId)
        {
            DateTime requestTime = DateTime.Now;
            List<KeyValuePair<string, string>> FormData = null;
            string error = string.Empty;
            string response = string.Empty;

            try
            {
                string URL = "Payu_APIUrl".AppSettings() + "daas/";
                Dictionary<object, object> header = new() {
                    { "API-KEY", "Payu_income_APIKey".AppSettings() }, { "API-TOKEN", "Payu_income_APIToken".AppSettings() }
                };

                FormData = new List<KeyValuePair<string, string>>
                {
                    new("mobile", MobileNoHash)
                };

                response = CommonAPICall.PostAPICall_FormDataAsync(URL, FormData, header, 2000);
                dynamic _data = null;
                if (!string.IsNullOrEmpty(response)) _data = JsonConvert.DeserializeObject<dynamic>(response);
                if (_data != null && _data.message == "Data Fetched Successfully!!" && _data.data != null && _data.data.predicted_income != null)
                {
                    return Convert.ToInt64(_data.data.predicted_income);
                }
                else
                {
                    return -2;
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return -1;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CustomerId.ToString(), CustomerId, error, "GetIncomeFromPayUAPI", "WebSiteServiceBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(FormData), response, requestTime, DateTime.Now);
            }
        }

        public ResponseData<AgentProfileData> GetAgentProfileDataByECode(string ECode)
        {
            ResponseData<AgentProfileData> response = new ResponseData<AgentProfileData>();
            response.Data = new AgentProfileData();
            DateTime reqdate = DateTime.Now;
            string url = "GetRealTimeStatusURL".AppSettings();
            try
            {
                DataSet data = WebSiteServicDLL.GetAgentProfileDataByECodeDLL(ECode);

                if (data != null && data.Tables != null && data.Tables.Count > 0)
                {
                    if (data.Tables[0].Rows.Count > 0)
                    {
                        response.Data.AgentName = data.Tables[0].Rows[0]["AgentName"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["AgentName"]);
                        response.Data.AgentEmployeeId = data.Tables[0].Rows[0]["AgentEmployeeId"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["AgentEmployeeId"]);
                        response.Data.AgentProfileImage = data.Tables[0].Rows[0]["AgentProfileImage"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["AgentProfileImage"]);
                        response.Data.PreferredLanguage = data.Tables[0].Rows[0]["PreferredLanguage"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["PreferredLanguage"]);
                        response.Data.TotalPoliciesSold = data.Tables[0].Rows[0]["TotalPoliciesSold"] == DBNull.Value ? 0 : Convert.ToInt32(data.Tables[0].Rows[0]["TotalPoliciesSold"]);
                        response.Data.Tenure = data.Tables[0].Rows[0]["Tenure"] == DBNull.Value ? 0 : Convert.ToInt32(data.Tables[0].Rows[0]["Tenure"]);
                        response.Data.Rewards = data.Tables[0].Rows[0]["Rewards"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["Rewards"]);
                        response.Data.IsAVCertified = data.Tables[0].Rows[0]["IsAVCertified"] == DBNull.Value ? 0 : Convert.ToInt16(data.Tables[0].Rows[0]["IsAVCertified"]);
                        response.Data.IsFOS = data.Tables[0].Rows[0]["IsFOS"] == DBNull.Value ? 0 : Convert.ToInt16(data.Tables[0].Rows[0]["IsFOS"]);
                        response.Data.TotalSumInsured = data.Tables[0].Rows[0]["TotalSumInsured"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(data.Tables[0].Rows[0]["TotalSumInsured"]);
                        if (!string.IsNullOrEmpty(response.Data.AgentEmployeeId))
                        {
                            url = url + response.Data.AgentEmployeeId;
                            var result = CommonAPICall.CallAPI(url, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
                            if (!string.IsNullOrEmpty(result))
                            {
                                var d = JsonConvert.DeserializeObject<dynamic>(result);
                                response.Data.AgentStatus = d.Count > 0 ? Convert.ToString(d[0].Status) : "";
                            }
                        }
                        response.Data.Gender = data.Tables[0].Rows[0]["Gender"] == DBNull.Value ? "" : Convert.ToString(data.Tables[0].Rows[0]["Gender"]);
                    }
                    response.Status = true;
                    response.Message = "Success";
                }
                else
                {
                    response.Status = false;
                    response.Message = "Data does not exist!";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(ECode, 0, ex.Message, "GetAgentProfileDataByECode", "MatrixCore", "GetAgentProfileDataByECode", ECode, ex.ToString(), reqdate, DateTime.Now);
                response.Status = false;
                response.Message = ex.Message;
            }
            return response;
        }
        public static bool SetAgentCallback(long LeadID, long UserId, DateTime dt, byte IsGoogleInvite,string source = "", bool issmeTL = false, string EmailId="")
        {
            string strException = string.Empty;
            var json = "";                        
            try
            {


                string starttime = dt.ToString("MM/dd/yyyy HH:mm");
                string endtime = dt.AddMinutes(3).ToString("MM/dd/yyyy HH:mm");

                var url = "InternalMatrixApiBaseURL".AppSettings();

                var headerParams = new Dictionary<string, string>
                 {
                    { "source", "matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey",  "matrixAPIclientKey".AppSettings() }
                 };

                if (IsGoogleInvite == 1)
                {
                    if (issmeTL && !string.IsNullOrEmpty(EmailId))
                    {
                        json = "{\"AgentId\": \"" + UserId + "\",\"Id\": \"" + LeadID + "\",\"Subject\": \"" + "CTC Callback" + "\",\"EventTypeId\": 4 ,\"StartDate\": \"" + starttime.Replace("-", "/") + "\",\"EndDate\": \"" + endtime.Replace("-", "/") + "\",\"CallBackTypeId\": 1,\"IsPaymentCallback\": false,\"AdminId\": \"" + UserId + "\",\"IsCore\": false,\"NeedId\": 0,\"NeedStatusId\": 0,\"IsGoogleInvite\": true,\"ProductName\": \"SME/GMC\",\"Source\": \"" + source + "\", \"EmailId\": \"" + EmailId + "\"} ";
                    }
                    else
                    {
                        json = "{\"AgentId\": \"" + UserId + "\",\"Id\": \"" + LeadID + "\",\"Subject\": \"" + "CTC Callback" + "\",\"EventTypeId\": 4 ,\"StartDate\": \"" + starttime.Replace("-", "/") + "\",\"EndDate\": \"" + endtime.Replace("-", "/") + "\",\"CallBackTypeId\": 1,\"IsPaymentCallback\": false,\"AdminId\": \"" + UserId + "\",\"Source\": \"" + source + "\",\"IsCore\": false,\"NeedId\": 0,\"NeedStatusId\": 0,\"IsGoogleInvite\": true } ";
                    }
                    
                }
                else
                {
                    json = "{\"AgentId\": \"" + UserId + "\",\"Id\": \"" + LeadID + "\",\"Subject\": \"" + "CTC Callback" + "\",\"EventTypeId\": 4 ,\"StartDate\": \"" + starttime.Replace("-", "/") + "\",\"EndDate\": \"" + endtime.Replace("-", "/") + "\",\"CallBackTypeId\": 1,\"IsPaymentCallback\": false,\"AdminId\": \"" + UserId + "\",\"IsCore\": false,\"Source\": \"" + source + "\",\"NeedId\": 0,\"NeedStatusId\": 0,\"IsGoogleInvite\": false  } ";
                }

                //var json = "{\"calData\": {\"Data\": {\"AgentId\": \"" + UserId + "\",\"Id\": \"" + LeadID + "\",\"Subject\": \"" + "CTC Callback" + "\",\"EventTypeId\": 4 ,\"StartDate\": \"" + starttime + "\",\"EndDate\": \"" + endtime + "\",\"CallBackTypeId\": 1,\"IsPaymentCallback\": false,\"AdminId\": \"" + UserId + "\",\"IsCore\": 0,\"NeedId\": 0,\"NeedStatusId\": 0  }    }   }";
                url = url + "coremrs/api/CallBackSchedular/InsertCallSchedulerEvent";                
                CommonAPICall.PostAPICall(url, 2000, json,string.Empty, headerParams);                
            }
            catch (Exception ex)
            {
                strException = ex.ToString();                
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, strException, "SetAgentCallback", "MatrixCore", "SetAgentCallback", json, string.Empty, DateTime.Now, DateTime.Now);
            }
            return true;
        }
        private static DateTime ConvertToDateTime(double javaTimeStamp)
        {
            // Java timestamp is milliseconds past epoch
            System.DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0).ToLocalTime();
            dtDateTime = dtDateTime.AddSeconds(javaTimeStamp);
            return dtDateTime;
        }
        public static bool ChkRMLead(long leadID, double calltime)
        {
            bool ret = false;
            string Url = string.Empty;
            var content="";
            try
            {                
               
                Url = "serviceapibms".AppSettings() + "api/partner/PushOneLead";
                Dictionary<object, object> header = new Dictionary<object, object>();
                header.Add("AppId", "Matrix1.0");
                header.Add("AppKey", "bmsServiceToken".AppSettings());
                content = "{\"LeadId\":" + leadID + ",\"GroupId\":1,\"UserId\":2,\"RequestType\":3,\"CreatedBy\":4,\"CallBackDate\":" + calltime + "}";
                var result = CommonAPICall.CallAPI(Url, content,"POST",3000, "application/json", header);
                if (!string.IsNullOrEmpty(result))
                {
                    if (result.Contains("true"))
                    {
                        ret = true;
                    }                    
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, leadID, ex.StackTrace.ToString(), "ChkRMLead", "WebSiteServiceBLL", "MatrixCoreAPI", content, null, DateTime.Now, DateTime.Now);
            }

            return ret;
        }

        public static bool PushBMSOneLead(CTCSchdular _CTCSchdular)
        {
            bool result = false;
            BMSCTCSchdularModel objToSend = null;
            string response = "", error = "";
            try
            {
                Dictionary<object, object> headers = new Dictionary<object, object>();
                headers.Add("AppId", "Matrix1.0");
                headers.Add("AppKey", "bmsServiceToken".AppSettings());

                string url = "serviceapibms".AppSettings() + "api/partner/HandleC2CRequest";
                objToSend = new BMSCTCSchdularModel()
                {
                    LeadId = _CTCSchdular.CTCLeadID,
                    GroupCode = _CTCSchdular.GroupID,
                    UserId = 2,
                    RequestType = 3,
                    CreatedBy = 124,
                    Remarks = _CTCSchdular.Comments,
                    LeadSource = _CTCSchdular.LeadSource,
                    Issue = _CTCSchdular.Issue,
                    SubIssue = _CTCSchdular.SubIssue,
                    CallBackDate = _CTCSchdular.ScheduleTime
                };


                response = CommonAPICall.CallAPI(url, JsonConvert.SerializeObject(objToSend), "POST", 6000, "application/json", headers);
                objToSend.logurl = url;
                if (!string.IsNullOrEmpty(response))
                {
                    if (response.Contains("true"))
                    {
                        result = true;
                    }
                }

            }
            catch (Exception ex)
            {
                error = ex.StackTrace.ToString();
                
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, _CTCSchdular.CTCLeadID, error, "PushBMSOneLead1.0", "WebSiteServiceBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(objToSend), response, DateTime.Now, DateTime.Now);
            }
            return result;
        }
        private void ReopenMatrixLead(long leadID)
        {
            string Url = "InternalMatrixApiBaseURL".AppSettings() + "coremrs/api/MRSCore/ReOpenMatrixLead";
            var data = new
            {
                LeadId = Convert.ToInt64(leadID),
                StatusId = 0,
                FetchStatusId = true
            };
            var content = JsonConvert.SerializeObject(data);
            var headerParams = new Dictionary<string, string>
            {
                { "source", "matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey",  "matrixAPIclientKey".AppSettings() }
            };
            CommonAPICall.PostAPICall(Url, 3000, content,string.Empty, headerParams);
        }
        bool CheckProgressiveLeadAssignment(CTCSchdular obj)
        {
            try
            {
                DataSet ds = WebSiteServicDLL.CheckProgressiveLeadAssignment(obj.LeadID, 0);
                LeadDetails objDetails = new LeadDetails();
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    long UserId = Convert.ToInt64(ds.Tables[0].Rows[0]["AssignedToUserID"]);
                    string AgentId = Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]);
                    Int16 ProductID = Convert.ToInt16(ds.Tables[0].Rows[0]["ProductID"]);
                    Int64 CustomerID = Convert.ToInt64(ds.Tables[0].Rows[0]["CustomerID"]);
                    string Name = Convert.ToString(ds.Tables[0].Rows[0]["Name"]);

                    if (UserId > 0 && !string.IsNullOrEmpty(AgentId))
                    {
                        return AddNotifyCTCLeadToQ(obj, UserId, AgentId, ProductID, CustomerID, Name);
                    }
                }
                else if (obj.ProductId == 2 && "CallHealthAllocationAPI".AppSettings() != "false")
                {
                    bool resp = CallHealthAllocationAPI(obj, out long UserId, out short ProductID, out long CustomerID, out string Name);

                    if (resp && UserId > 0)
                    {
                        return AddNotifyCTCLeadToQ(obj, UserId, string.Empty, ProductID, CustomerID, Name);
                    }
                }
                else if (obj.ProductId == 7) 
                {
                    bool resp = CallTermAllocationAPI(obj, out long UserId, out string AgentId, out short ProductID, out long CustomerID, out string Name);
                    
                    if (resp && UserId > 0 && !string.IsNullOrEmpty(AgentId))
                    {
                        return AddNotifyCTCLeadToQ(obj, UserId, AgentId, ProductID, CustomerID, Name);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadID, ex.ToString(), "CheckProgressiveLeadAssignment", "MatrixCore","124", Newtonsoft.Json.JsonConvert.SerializeObject(obj), string.Empty, DateTime.Now,DateTime.Now);
            }
            
            return false;
        }

        private static bool CallTermAllocationAPI(CTCSchdular obj, out long UserId, out string AgentId, out short ProductID, out long CustomerID, out string Name)
        {
            string response = string.Empty;
            DateTime requestTime = DateTime.Now;

            UserId = 0;
            AgentId = string.Empty;
            ProductID = 0;
            CustomerID = 0;
            Name = string.Empty;
            
            try
            {
                string url = "InternalMatrixApiBaseURL".AppSettings() + "allocation/api/TermAllocation/AllocateTermLeadAPI";
                
                var requestObj = new
                {
                    LeadId = obj.LeadID,
                    AssignmentProcess = "CTC"
                };
                
                Dictionary<object, object> headers = new()
                {
                    { "source", "matrix" },
                    { "clientKey", "matrixAPIclientKey".AppSettings() },
                    { "authKey", "matrixAPIauthKey".AppSettings() }
                };
                
                response = CommonAPICall.CallAPI(url, JsonConvert.SerializeObject(requestObj), "POST", 6000, "application/json", headers);
                
                if (!string.IsNullOrEmpty(response))
                {
                    dynamic result = JsonConvert.DeserializeObject<dynamic>(response);
                    
                    if (result != null && result.isSuccess == true && result.leadDetails != null && result.leadDetails.Count > 0)
                    {
                        var leadDetail = result.leadDetails[0];

                        UserId = leadDetail.assigntoUserID;
                        CustomerID = leadDetail.customerId;
                        ProductID = (short)leadDetail.productID;
                        Name = leadDetail.name ?? string.Empty;
                        
                        if (UserId > 0)
                        {
                            DataSet data = WebSiteServicDLL.GetAgentDetails(UserId);
                            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                            {
                                var row = data.Tables[0].Rows[0];
                                AgentId = row["EmployeeId"] != DBNull.Value && row["EmployeeId"] != null ? row["EmployeeId"].ToString() : string.Empty;
                            }
                            
                            return true;
                        }
                    }
                }
                
                return false;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadID, ex.ToString(), "CallTermAllocationAPI", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), response, requestTime, DateTime.Now);
                return false;
            }
        }
        
        private static bool CallHealthAllocationAPI(CTCSchdular obj, out long UserId, out short ProductID, out long CustomerID, out string Name)
        {
            string response = string.Empty;
            DateTime requestTime = DateTime.Now;
            string error = string.Empty;
            bool data = false;

            UserId = 0;
            ProductID = 0;
            CustomerID = 0;
            Name = string.Empty;

            try
            {
                string url = "InternalMatrixApiBaseURL".AppSettings() + "allocation/api/Allocation/AllocateHealthLeads";
                
                var requestObj = new
                {
                    LeadId = obj.LeadID,
                    AssignmentProcess = "CTC",
                    ToAssign = true
                };
                
                Dictionary<object, object> headers = new()
                {
                    { "source", "matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey", "matrixAPIclientKey".AppSettings() }
                };
                
                response = CommonAPICall.CallAPI(url, JsonConvert.SerializeObject(requestObj), "POST", 6000, "application/json", headers);
                
                if (!string.IsNullOrEmpty(response))
                {
                    dynamic result = JsonConvert.DeserializeObject<dynamic>(response);
                    
                    if (result != null && result.IsSuccess == true && result.LeadDetails != null && result.LeadDetails.Count > 0)
                    {
                        var leadDetail = result.LeadDetails[0];

                        UserId = leadDetail.AssigntoUserID;
                        CustomerID = leadDetail.CustomerId;
                        ProductID = (short)leadDetail.ProductID;
                        Name = leadDetail.Name ?? string.Empty;
                        
                        if (UserId > 0)
                        {
                            data = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadID, error, "CallHealthAllocationAPI", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), response, requestTime, DateTime.Now);
            }
            return data;
        }

        private bool AddNotifyCTCLeadToQ(CTCSchdular obj, long UserId, string AgentId, short ProductID, long CustomerID, string Name)
        {
            DateTime reqTime = DateTime.Now;
            try
            {
                UserNext5Leads objNext5Leads = new()
                {
                    UserId = UserId,
                    IsAuto = true,
                    Leads = new List<Next5WidgetLead>()
                };

                Next5WidgetLead objWidgetLead = new Next5WidgetLead
                {
                    LeadId = obj.LeadID,
                    Name = Name,
                    CustomerId = CustomerID,
                    ProductId = ProductID,
                    Reason = "CTC Lead",
                    ReasonId = 35,
                    IsAddLeadtoQueue = 1
                };

                objNext5Leads.Leads.Add(objWidgetLead);

                string Url = "InternalMatrixApiBaseURL".AppSettings() + "onelead/api/LeadPrioritization/ValidateAddLeadToPriorityQueue";
                var content = JsonConvert.SerializeObject(objNext5Leads);
                var headerParams = new Dictionary<string, string>
                {
                    { "source", "matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey", "matrixAPIclientKey".AppSettings() }
                };
                
                string response = CommonAPICall.PostAPICall_Sync(Url, 3000, content, headerParams);
                
                if (!string.IsNullOrEmpty(response) && ProductID == 117 && !string.IsNullOrEmpty(obj.LeadSource) && obj.LeadSource.ToLower().Contains("agentbanner"))
                {
                    AddLeadValidation res = JsonConvert.DeserializeObject<AddLeadValidation>(response);
                    if (res.status == 1 && res.message.Contains("Success"))
                    {
                        string Text = $"{obj.LeadID} : {Name} has requested for a callback.";
                        string Type = "Agent Banner Callback";
                        NotificationType notificationType = NotificationType.Other;
                        PushAgentNotification(AgentId, Type, notificationType, Text);
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", obj.LeadID, ex.ToString(), "AddNotifyCTCLeadToQ", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), string.Empty, reqTime, DateTime.Now);
                return false;
            }
        }

        
        public Response CTCCallBack(CTCSchdular _CTCSchdular,string source = null)
        {
            Response _Response = new Response();
            _Response.status = true;
            string strException = string.Empty;
            DateTime reqDatetime = DateTime.Now;
            try
            {

                /*if lead id is not there and mobile no is there*/
                /* Temp stop */
                if (_CTCSchdular.LeadID==0 && _CTCSchdular.MobileNO > 0)
                {
                    CreateLeadRequest _request = new CreateLeadRequest()
                    {
                        Name = "Inbound",
                        LeadSource = "Inbound",
                        CountryId = 392,
                        Comment = "",
                        Email = "<EMAIL>",
                        MobileNo = _CTCSchdular.MobileNO.ToString(),
                        ReferralLead = 0,
                        SubProductId = 0,
                        UtmMedium = "",
                        UtmSource = _CTCSchdular.GroupID,
                        UtmTerm = "",
                        ProductId = 0,
                        ApiSource = (source ?? string.Empty) + "-CTCCallback"
                    };

                    _request.ProductId = GetProductIdByGroupName(_CTCSchdular.GroupID);

                    if (_request.ProductId > 0)
                    {
                        CreateLeadResponse res = CreateLead(_request);
                        if (res != null && res.LeadId > 0)
                        {
                            _CTCSchdular.LeadID = res.LeadId;
                        }

                    }
                }

                if (_CTCSchdular.LeadID == 0 || string.IsNullOrEmpty(_CTCSchdular.GroupID))
                {
                    _Response.status = false;
                    return _Response;
                }

                if (_CTCSchdular.CallNow == false && string.IsNullOrEmpty(_CTCSchdular.ScheduleTime))
                {
                    _Response.status = false;
                    return _Response;
                }

                if (_CTCSchdular.ProcessName == null)
                {
                    _CTCSchdular.ProcessName = string.Empty;
                }

                long parentId = 0; Int32 ProductID = 0;Int64 AgentID = 0;Int32 GroupID;string EmailId = string.Empty;

                _CTCSchdular.CTCLeadID= _CTCSchdular.LeadID;

                DateTime ScheduledTime = DateTime.Now;
                if (!string.IsNullOrEmpty(_CTCSchdular.ScheduleTime))
                    ScheduledTime = ConvertToDateTime(Double.Parse(_CTCSchdular.ScheduleTime));

                if (_CTCSchdular.LeadID > 0 && !string.IsNullOrEmpty(_CTCSchdular.GroupID) && !_CTCSchdular.GroupID.ToUpper().Contains("SERVICE"))
                {
                    DataSet parentLeadDS = LeadPrioritizationDLL.GetLeadAgentDetails(_CTCSchdular.LeadID);
                    if (parentLeadDS != null && parentLeadDS.Tables != null && parentLeadDS.Tables.Count > 0 && parentLeadDS.Tables[0].Rows.Count > 0)
                    {
                        bool IsInvalidNo = Convert.ToBoolean(parentLeadDS.Tables[0].Rows[0]["IsInvalidNo"]);                        

                        parentId = parentLeadDS.Tables[0].Rows[0]["ParentID"] != DBNull.Value ? Convert.ToInt64(parentLeadDS.Tables[0].Rows[0]["ParentID"]) : 0;
                        if (parentId > 0)
                            _CTCSchdular.LeadID = parentId;
                        ProductID = parentLeadDS.Tables[0].Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(parentLeadDS.Tables[0].Rows[0]["ProductID"]) : 0;
                            _CTCSchdular.ProductId = ProductID;
                        AgentID = parentLeadDS.Tables[0].Rows[0]["UserId"] != DBNull.Value ? Convert.ToInt64(parentLeadDS.Tables[0].Rows[0]["UserId"]) : 0;
                        GroupID = parentLeadDS.Tables[0].Rows[0]["AssignToGroupId"] != DBNull.Value ? Convert.ToInt32(parentLeadDS.Tables[0].Rows[0]["AssignToGroupId"]) : 0;
                        EmailId = parentLeadDS.Tables[0].Rows[0]["EmailId"] != DBNull.Value ? Convert.ToString(parentLeadDS.Tables[0].Rows[0]["EmailId"]) : default;
                        Int64 CustomerID = parentLeadDS.Tables[0].Rows[0]["CustomerID"] != DBNull.Value ? Convert.ToInt64(parentLeadDS.Tables[0].Rows[0]["CustomerID"]) : 0;
                        Int16 Country = parentLeadDS.Tables[0].Rows[0]["Country"] != DBNull.Value ? Convert.ToInt16(parentLeadDS.Tables[0].Rows[0]["Country"]) : (short)0;
                        string LeadSource = parentLeadDS.Tables[0].Rows[0]["LeadSource"] != DBNull.Value ? Convert.ToString(parentLeadDS.Tables[0].Rows[0]["LeadSource"]) : string.Empty;
                        if(ProductID == 2 && LeadSource == "Renewal" && source == "whatsapp")
                        {
                            CommunicationBLL communicationBLL = new();
                            SubscribeCustomer subscribeCustomer = new()
                            {
                                leadID = parentId,
                                custID = CustomerID,
                                productID = Convert.ToInt16(ProductID),
                                countryCode = Country,
                                coolingPeriod = 6,
                                CategoryCode = "RENEWAL",
                                SubSource = "Onelead"
                            };
                            communicationBLL.SubscribeCustomer(subscribeCustomer);
                        }
                        if (IsInvalidNo == true)
                        {
                            _Response.status = true;                             
                            _CTCSchdular.IsPicked = 1;
                            _CTCSchdular.NotPickedReason = "InvalidNoORMultipleHits";
                        }
                        else if (_CTCSchdular.CallNow==false) 
                        {
                            if (AgentID > 0) // Scheduled Call Back)
                            {
                                if (_CTCSchdular.SubIssue == 143)
                                    ReopenMatrixLead(_CTCSchdular.LeadID);
                                _CTCSchdular.IsPicked = 1;
                                _CTCSchdular.NotPickedReason = "AgentCallback";
                                if (!string.IsNullOrEmpty(_CTCSchdular.ProcessName) &&
                                    ((_CTCSchdular.ProcessName.Equals("SmeCJConsultationBook",StringComparison.OrdinalIgnoreCase) ||(_CTCSchdular.ProcessName.Equals("pbsme_ctp_calendar_block", StringComparison.OrdinalIgnoreCase)))))
                                {
                                    SetAgentCallback(parentId, AgentID, ScheduledTime, _CTCSchdular.IsGoogleInvite,!string.IsNullOrEmpty(source) ? source : "System", true,EmailId);
                                }
                                else
                                {
                                    SetAgentCallback(parentId, AgentID, ScheduledTime, _CTCSchdular.IsGoogleInvite, !string.IsNullOrEmpty(source) ? source : "System");
                                }
                            }
                            else if(GroupID==0 && (ProductID == 2 || ProductID == 117) && !_CTCSchdular.GroupID.ToUpper().Contains("RENEWAL")) // fresh lead not assigned yet
                            {
                                _CTCSchdular.IsPicked = 1;
                                _CTCSchdular.NotPickedReason = "frshlead";
                                SetAgentCallback(parentId, AgentID, ScheduledTime, _CTCSchdular.IsGoogleInvite, !string.IsNullOrEmpty(source) ? source : "System");
                            }
                        }                        
                        else if (_CTCSchdular.CallNow == true)
                        {
                            ScheduledTime = DateTime.Now;
                            if (CheckProgressiveLeadAssignment(_CTCSchdular))
                            {
                                _CTCSchdular.IsPicked = 1;
                                _CTCSchdular.NotPickedReason = "AddLeadToProgressiveQueue";
                            }
                        }
                        WebSiteServicDLL.SchCTCConnectCall(_CTCSchdular, ScheduledTime);
                        
                        // Update DNC.LastIbCallTime in mongo LPData collection if DNC field exists
                        WebSiteServicDLL.UpdateDNCLastIbCallTime(_CTCSchdular.LeadID, ScheduledTime);
                    }
                }
                else if (!string.IsNullOrEmpty(_CTCSchdular.GroupID) && _CTCSchdular.LeadID > 0)
                {
                    if (_CTCSchdular.LeadID > 0 && _CTCSchdular.GroupID.ToUpper().Contains("SERVICE")
                        && !_CTCSchdular.ProcessName.ToLower().Contains("bmsrepush")
                        && PushBMSOneLead(_CTCSchdular))
                    {
                        _CTCSchdular.IsPicked = 1;
                        _CTCSchdular.NotPickedReason = "RMLead";
                    }                    
                    WebSiteServicDLL.SchCTCConnectCall(_CTCSchdular, ScheduledTime);
                }                
            }
            catch (Exception ex)
            {
                strException = ex.ToString();
                _Response.status = false;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", _CTCSchdular.LeadID, strException, "CTCCallBack", "MatrixCore", "CTCCallBack", Newtonsoft.Json.JsonConvert.SerializeObject(_CTCSchdular), string.Empty, reqDatetime, DateTime.Now);
            }

            return _Response;
        }

        private int GetProductIdByGroupName(string groupNmae)
        {
            int ProductId = 0;

            if (!string.IsNullOrEmpty(groupNmae))
            {
                switch (groupNmae.ToUpper())
                {
                    case "HEALTH_IB":
                        ProductId = 2;
                        break;
                    case "CAR_IB":
                        ProductId = 117;
                        break;
                    case "TERM_IB":
                        ProductId = 7;
                        break;
                    case "NTM_IB":
                        ProductId = 115;
                        break;
                    case "HOME_IB":
                        ProductId = 101;
                        break;
                    case "TRAVEL_IB":
                        ProductId = 3;
                        break;
                    case "TW_IB":
                        ProductId = 114;
                        break;
                    case "SME_IB":
                        ProductId = 131;
                        break;
                    case "ComVehicle_IB":
                        ProductId = 139;
                        break;
                    case "CANCER_IB":
                        ProductId = 138;
                        break;
                };
            }

            return ProductId;
        }

        public ResponseData<string> SaveFOSApptCallAnalysisAI(FOSApptCallDataAI objFosApptCallData)
        {
            DateTime dt = DateTime.Now;
            ResponseData<string> response = new()
            {
                Status = false,
                Message = "Failed"
            };

            try
            {
                bool result = WebSiteServicDLL.SaveFOSApptCallAnalysisAI(objFosApptCallData);
    
                if(result){
                    response.Status = true;
                    response.Message = "Save Successfully";
                } else {
                    response.Status = false;
                    response.Message = "Failed";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, objFosApptCallData.LeadID, ex.ToString(), "SaveFOSApptCallAnalysisAI", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(objFosApptCallData), string.Empty, dt, DateTime.Now);
            }
            return response;
        }

        public ResponseData<string> InsertFOSApptCallAnalysisAI(FOSApptCallDataAI objFosApptCallData)
        {
            DateTime requestTime = DateTime.Now;
            ResponseData<string> response = new()
            {
                Status = false,
                Message = "Insertion failed"
            };

            try
            {
                bool isInserted = WebSiteServicDLL.InsertFOSApptCallAnalysisAI(objFosApptCallData);

                response.Status = isInserted;
                response.Message = isInserted ? "Data inserted successfully" : "Insertion failed";
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    strUniqueID: string.Empty,
                    LeadID: objFosApptCallData.LeadID,
                    strexCeption: ex.ToString(),
                    strMethodName: "InsertFOSApptCallAnalysisAI",
                    ApplicationName: "MatrixCore",
                    CreatedBy: "WebSiteServiceBLL",
                    strRequestText: JsonConvert.SerializeObject(objFosApptCallData),
                    strResponseText: string.Empty,
                    RequestDateTime: requestTime,
                    ResponseDateTime: DateTime.Now
                );
            }

            return response;
        }

        public ResponseAPI VerifyCallBackDateTime(ShortCallData obj)
        {
            ResponseAPI response = new ResponseAPI()
            {
                status = false,
                message = string.Empty
            };
            DateTime reqTime = DateTime.Now;
            string apiResponse = string.Empty;

            try
            {
                if(obj.LeadID > 0 && obj.CallDataID > 0)
                {
                    DataSet ds = WebSiteServicDLL.VerifyCallBackDateTime(obj);
                    if(ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        int responseDB = ds.Tables[0].Rows[0]["Result"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["Result"]);
                        if (responseDB == 0 || responseDB == 1)
                        {
                            response.status = responseDB == 1 ? true : false;
                            response.message = ds.Tables[0].Rows[0]["Message"] == DBNull.Value ? string.Empty : Convert.ToString(ds.Tables[0].Rows[0]["Message"]);
                        }
                        else if (responseDB == 2)
                        {
                            long UserId = 0;
                            UserId = ds.Tables[0].Rows[0]["AgentId"] == DBNull.Value ? 0 : Convert.ToInt64(ds.Tables[0].Rows[0]["AgentId"]);

                            if(UserId > 0)
                            {
                                string starttime = obj.CallBackTime.ToString("MM/dd/yyyy HH:mm");
                                string endtime = obj.CallBackTime.AddMinutes(3).ToString("MM/dd/yyyy HH:mm");

                                var url = "InternalMatrixApiBaseURL".AppSettings();

                                var headerParams = new Dictionary<string, string>
                                {
                                    { "source", "matrix" },
                                    { "authKey", "matrixAPIauthKey".AppSettings() },
                                    { "clientKey",  "matrixAPIclientKey".AppSettings() }
                                };
                                string source = "AI";
                                string json = "{\"AgentId\": \"" + UserId + "\",\"Id\": \"" + obj.LeadID + "\",\"Subject\": \"" + "Best Guess" + "\",\"EventTypeId\": 4 ,\"StartDate\": \"" + starttime.Replace("-", "/") + "\",\"EndDate\": \"" + endtime.Replace("-", "/") + "\",\"CallBackTypeId\": 3,\"IsPaymentCallback\": false,\"AdminId\": \"" + 100 + "\",\"Source\": \"" + source + "\",\"IsCore\": false,\"NeedId\": 0,\"NeedStatusId\": 0,\"IsGoogleInvite\": false  } ";

                                url = url + "coremrs/api/CallBackSchedular/InsertCallSchedulerEvent";
                                apiResponse = CommonAPICall.PostAPICall_Sync(url, 2000, json, headerParams);

                                response.status = true;
                                response.message = "New CallBack is inserted!";
                            }
                            else
                            {
                                response.message = "Lead Is not assigned!";
                                response.status = true;
                            }
                        }
                    }
                }
                else
                {
                    response.message = "Please Enter valid LeadId";
                }
            }
            catch(Exception ex)
            {
                response.message = "API encountered an error - " + ex.Message;
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, obj.LeadID, ex.ToString(), "VerifyCallBackDateTime", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), JsonConvert.SerializeObject(response) + " - " + apiResponse, reqTime, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, obj.LeadID, "", "VerifyCallBackDateTimeFinally", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(obj), JsonConvert.SerializeObject(response) + " - " + apiResponse, reqTime, DateTime.Now);
            }
            return response;
        }

        public Response  SaveFollowUpComment(FollowUpComment obj)
        {
            Response response = new Response();
            if (obj != null && obj.leadID>0)
            {
                if(obj.userID.Equals(0))
                {
                    obj.userID = 124;
                }
                if (obj.userID.Equals(124) && obj.eventtype.Equals(0))
                {
                    obj.eventtype = 2;
                }
                response.status = WebSiteServicDLL.SaveFollowUpComment(obj.leadID, obj.userID, obj.comment, obj.eventtype);
                
            }
            return response;
        }

        public Response DisableScheduledC2C(long leadId, string source)
        {
            Response response = new Response();
            DateTime reqTime = DateTime.Now;
            try
            {
                if (leadId != null && leadId > 0 && !string.IsNullOrEmpty(source))
                {
                    response.status = WebSiteServicDLL.DisableScheduledC2C(leadId, source);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(leadId), Convert.ToInt64(leadId), ex.ToString(), "DisableScheduledC2C", "MatrixCore", "WebSiteServiceBLL", Convert.ToString(leadId), JsonConvert.SerializeObject(response), reqTime, DateTime.Now);
            }
            return response;
        }

        public void PushAgentNotification(string EmployeeId, string Type, NotificationType notificationType, string Text)
        {
            string error = string.Empty;
            DateTime requestTime = DateTime.Now;
            StringBuilder sb = new StringBuilder();
            try
            {
                string notId = EmployeeId + Guid.NewGuid().ToString();

                List<Dictionary<string, string>> obj = new List<Dictionary<string, string>>();

                Dictionary<string, string> json = new Dictionary<string, string>();
                json.Add("id", notId);
                json.Add("type", Type);
                json.Add("event", Type);
                json.Add("text", Text);
                json.Add("Reason", Type);
                json.Add("empID", EmployeeId);

                obj.Add(json);
                string response = CommonAPICall.WebRequestMethod("notificationurl".AppSettings(), JsonConvert.SerializeObject(obj), "POST", 3000, "application/json", null);

            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(EmployeeId, 0, error.ToString(), "PushAgentNotification", "Communication", "KafkaWrapper", sb.ToString(), "", requestTime, DateTime.Now);
            }
        }

        public ResponseUltraHNICust CheckUltraHNICustomer(long customerId, string mobileNoHash, long leadId, bool skipPayuApiCall)
        {
            ResponseUltraHNICust response = new();
            try
            {
                PayuScores scores = new()
                {
                    affluence_score = -2,
                    pbhealth_gi_propensity_tier_calibrated = -2
                };

                // Special case for specific customer IDs
                if (customerId == 86637547 || customerId == 18321878)
                {
                    response.status = true;
                    response.message = "Success";
                    response.IsUltraHNICustomer = true;
                    response.PayuScores = new()
                    {
                        affluence_score = 5,
                        pbhealth_gi_propensity_tier_calibrated = 10
                    };
                    return response;
                }
                if("HitPayuAPI".AppSettings() == "true"){
                    scores = GetScoreFromPayU(customerId, mobileNoHash, leadId, skipPayuApiCall);
                    response.status = true;
                    response.message = "Success";
                    response.IsUltraHNICustomer = scores.affluence_score >= 5;
                    response.PayuScores = scores;
                }
                else{
                    response.status = true;
                    response.message = "API_DISABLED_ON_REQUEST";
                    response.IsUltraHNICustomer=false;
                    response.PayuScores = scores;
                }

            }
            catch (Exception ex)
            {
                response.status = false;
                response.message = "Failed";

                LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, customerId, ex.ToString(), "CheckUltraHNICustomer", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(customerId), string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static PayuScores GetScoreFromPayU(long customerId, string mobileNoHash, long leadId, bool skipPayuApiCall = false)
        {
            PayuScores scores = new()
            {
                affluence_score = -2,
                pbhealth_gi_propensity_tier_calibrated = -2
            };
            
            DataTable dt = WebSiteServicDLL.GetCustomerFeatureData(customerId);
            if (dt != null)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    switch (dr["Feature"])
                    {
                        case "AffluencePayU":
                            scores.affluence_score = (int)dr["Value"];
                            break;
                        case "PropensityPayU":
                            scores.pbhealth_gi_propensity_tier_calibrated = (int)dr["Value"];
                            break;
                        default:
                            break;
                    }
                }
            }

            // if scores not available in db, call payU api
            if (!skipPayuApiCall && scores.affluence_score == -2 && scores.pbhealth_gi_propensity_tier_calibrated == -2)
            {
                if(string.IsNullOrEmpty(mobileNoHash))
                {
                    DataSet ds = LeadPrioritizationDLL.GetCustDetails(customerId.ToString(), leadId.ToString());

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        mobileNoHash = ds.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? string.Empty : CoreCommonMethods.ComputeSha256Hash(Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]));
                    }
                }

                if(!string.IsNullOrEmpty(mobileNoHash))
                {
                    PayuScoresResponse scoresResponse = GetScoresFromPayuAPI(mobileNoHash, customerId);
                    
                    if (scoresResponse != null && (scoresResponse.message == "Data Fetched Successfully!!" || scoresResponse.message == "Not Found"))
                    {
                        scores = scoresResponse.data;
                        SavePayUScoreData(customerId, scores);
                    }
                    else
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(string.Empty, customerId, "", "GetScoreFromPayU-RespNotFound", "MatrixCore", "WebSiteServiceBLL", JsonConvert.SerializeObject(customerId), JsonConvert.SerializeObject(scoresResponse), DateTime.Now, DateTime.Now);
                    }
                }
            }
            
            return scores;
        }

        public static PayuScoresResponse GetScoresFromPayuAPI(string mobileNoHash, long CustomerId)
        {
            DateTime requestTime = DateTime.Now;
            List<KeyValuePair<string, string>> FormData = null;
            string error = string.Empty;
            PayuScoresResponse response = new()
            {
                data = new()
                {
                    affluence_score = -1,
                    pbhealth_gi_propensity_tier_calibrated = -1
                }
            };

            try
            {
                string URL = "Payu_APIUrl".AppSettings() + "daas/";
                Dictionary<object, object> header = new() {
                    { "API-KEY", "Payu_score_APIKey".AppSettings() }, { "API-TOKEN", "Payu_score_APIToken".AppSettings() }
                };

                FormData = new List<KeyValuePair<string, string>>
                {
                    new("mobile", mobileNoHash)
                };

                string res = CommonAPICall.PostAPICall_FormDataAsync(URL, FormData, header, 2000);
                if (!string.IsNullOrEmpty(res)) 
                    response = JsonConvert.DeserializeObject<PayuScoresResponse>(res);

                return response;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return response;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CustomerId.ToString(), CustomerId, error, "GetScoresFromPayuAPI", "WebSiteServiceBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(FormData), JsonConvert.SerializeObject(response), requestTime, DateTime.Now);
            }
        }

        public static void SavePayUScoreData(long CustomerId, PayuScores scores)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                WebSiteServicDLL.SaveCustomerFeatureData(CustomerId, "AffluencePayU", scores.affluence_score);
                WebSiteServicDLL.SaveCustomerFeatureData(CustomerId, "PropensityPayU", scores.pbhealth_gi_propensity_tier_calibrated);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustomerId, ex.ToString(), "SavePayUScoreData", "WebSiteServiceBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(scores), "", requestTime, DateTime.Now);
            }
        }

        //CCTEC-5448
        public ResponseData<LeadAssignResp> AssignLeadToGroup(long CustomerID, int ProductId, string identifier, int? GroupId, string ApiSource = null)
        {
            string Exception = "";
            var objLeadAssignResp = new ResponseData<LeadAssignResp> { Data = new LeadAssignResp() };
            AllocationLeadData LeadAllocationData = null;
            try
            {
                int GroupID = 0;
                List<Int32> UHNIGroups = "UHNIGroups".AppSettings().Split(',').Select(Int32.Parse).ToList(); ;
                bool IsHNICustomer = false;
                if (identifier == "WAHealthHNI")
                {
                    GroupID = 3494;//PED_UHNI
                    IsHNICustomer = true;
                }
                else if (GroupId.HasValue && GroupId > 0)
                {
                    GroupID = GroupId.Value;
                }

                DataTable dt = WebSiteServicDLL.GetActiveLeadDetails(CustomerID, ProductId,"UHNI");
                if (dt != null && dt.Rows != null && dt.Rows.Count > 0)
                {
                    long LeadID = dt.Rows[0]["LeadID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["LeadID"]) : 0;
                    int IsActiveLead = dt.Rows[0]["IsActiveLead"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["IsActiveLead"]) : 0;
                    int ProductIdDB = dt.Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["ProductID"]) : 0;
                    long AssignedToUserID = dt.Rows[0]["AssignedToUserID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["AssignedToUserID"]) : 0;
                    string AssignedToEcode = dt.Rows[0]["AssignedToEcode"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["AssignedToEcode"]) : "";
                    string AssignedToAgentName = dt.Rows[0]["AssignedToAgentName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["AssignedToAgentName"]) : "";
                    int IsAgentAvailable = dt.Rows[0]["IsAgentActive"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["IsAgentActive"]) : 0;
                    int AssignedToGroupID = dt.Rows[0]["AssignToGroupId"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["AssignToGroupId"]) : 0;
                    int IsUHNIAgent = dt.Rows[0]["IsUHNIAgent"] != DBNull.Value ? Convert.ToInt32(dt.Rows[0]["IsUHNIAgent"]) : 0;

                    //If Active lead exists for the given product, and its assigned to available agent
                    if (ProductIdDB == ProductId && IsActiveLead == 1 && AssignedToUserID > 0 && IsAgentAvailable == 1)
                    {
                        objLeadAssignResp.Data.AssignedAgentECode = AssignedToEcode;
                        objLeadAssignResp.Data.AssignToAgentName = AssignedToAgentName;
                        objLeadAssignResp.Data.LeadID = LeadID;
                        objLeadAssignResp.Data.GroupID = AssignedToGroupID;
                        objLeadAssignResp.Data.ISUHNIAgent = IsUHNIAgent;
                        objLeadAssignResp.Message = "Lead Already exists and already assigned to Agent: " + AssignedToEcode;
                        objLeadAssignResp.Status = true;
                    }
                    //If Active lead is present but either lead is unassigned or current agent is currently not loggedin 
                    else if (ProductIdDB == ProductId && IsActiveLead == 1 && GroupID > 0 && (IsAgentAvailable != 1 || AssignedToUserID == 0))
                    {
                        AssignedToEcode = ""; AssignedToAgentName = "";
                        if (identifier =="WAHealthHNI")
                        {
                            var response = ChatBLL.GetLeadAllocationData(LeadID, "UHNI", true);
                            var _allocateLeadResponse = JsonConvert.DeserializeObject<AllocateLeadResponse>(response);
                            if (_allocateLeadResponse != null && _allocateLeadResponse.LeadDetails != null && _allocateLeadResponse.LeadDetails.Count > 0)
                            {
                                LeadAllocationData = _allocateLeadResponse.LeadDetails[0];
                                AssignedToEcode = LeadAllocationData.assignedToEcode != null ? Convert.ToString(LeadAllocationData.assignedToEcode) : "";
                                AssignedToAgentName = LeadAllocationData.assignedToAgentName != null ? Convert.ToString(LeadAllocationData.assignedToAgentName) : "";
                                GroupID = LeadAllocationData.groupID != 0 ? Convert.ToInt32(LeadAllocationData.groupID) : 0;
                            }
                        }
                        else
                        {
                            DataTable dtassign = WebSiteServicDLL.AssignLeadToAgentByGroupID(GroupID, LeadID, ProductId, 100);
                            if (dtassign != null && dtassign.Rows != null && dtassign.Rows.Count > 0)
                            {
                                AssignedToEcode = dtassign.Rows[0]["AssignedToEcode"] != DBNull.Value ? Convert.ToString(dtassign.Rows[0]["AssignedToEcode"]) : "";
                                AssignedToAgentName = dt.Rows[0]["AssignedToAgentName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["AssignedToAgentName"]) : "";
                            }
                        }

                        if (!string.IsNullOrEmpty(AssignedToEcode))
                        {
                            objLeadAssignResp.Data.AssignedAgentECode = AssignedToEcode;
                            objLeadAssignResp.Data.AssignToAgentName = AssignedToAgentName;
                            objLeadAssignResp.Data.LeadID = LeadID;
                            objLeadAssignResp.Data.GroupID = GroupID;
                            objLeadAssignResp.Data.ISUHNIAgent = UHNIGroups.Contains(objLeadAssignResp.Data.GroupID) ? 1 : 0;
                            objLeadAssignResp.Message = "Lead Already exists and has now been assigned to Agent: " + AssignedToEcode;
                            objLeadAssignResp.Status = true;
                        }
                        else
                        {
                            objLeadAssignResp.Data.AssignedAgentECode = "";
                            objLeadAssignResp.Data.AssignToAgentName = "";
                            objLeadAssignResp.Data.LeadID = LeadID;
                            objLeadAssignResp.Data.GroupID = GroupID;
                            objLeadAssignResp.Data.ISUHNIAgent = 0;
                            objLeadAssignResp.Message = "Lead Already exists, but no active agent is currently available in this group";
                            objLeadAssignResp.Status = true;
                        }

                    }
                    else if(ProductIdDB == ProductId && IsActiveLead == 0)
                    {
                        AssignedToEcode = ""; AssignedToAgentName = "";
                        ReopenMatrixLead(LeadID);
                        var response = ChatBLL.GetLeadAllocationData(LeadID, "UHNI", true);
                        var _allocateLeadResponse = JsonConvert.DeserializeObject<AllocateLeadResponse>(response);
                        if (_allocateLeadResponse != null && _allocateLeadResponse.LeadDetails != null && _allocateLeadResponse.LeadDetails.Count > 0)
                        {
                            LeadAllocationData = _allocateLeadResponse.LeadDetails[0];
                            AssignedToEcode = LeadAllocationData.assignedToEcode != null ? Convert.ToString(LeadAllocationData.assignedToEcode) : "";
                            AssignedToAgentName = LeadAllocationData.assignedToAgentName != null ? Convert.ToString(LeadAllocationData.assignedToAgentName) : "";
                            GroupID = LeadAllocationData.groupID != 0 ? Convert.ToInt32(LeadAllocationData.groupID) : 0;
                        }
                        if (!string.IsNullOrEmpty(AssignedToEcode))
                        {
                            objLeadAssignResp.Data.AssignedAgentECode = AssignedToEcode;
                            objLeadAssignResp.Data.AssignToAgentName = AssignedToAgentName;
                            objLeadAssignResp.Data.LeadID = LeadID;
                            objLeadAssignResp.Data.GroupID = GroupID;
                            objLeadAssignResp.Data.ISUHNIAgent = UHNIGroups.Contains(objLeadAssignResp.Data.GroupID) ? 1 : 0;
                            objLeadAssignResp.Message = "This Lead Already exists and has now been assigned to Agent: " + AssignedToEcode;
                            objLeadAssignResp.Status = true;
                        }
                        else
                        {
                            objLeadAssignResp.Data.AssignedAgentECode = "";
                            objLeadAssignResp.Data.AssignToAgentName = "";
                            objLeadAssignResp.Data.LeadID = LeadID;
                            objLeadAssignResp.Data.GroupID = GroupID;
                            objLeadAssignResp.Data.ISUHNIAgent = 0;
                            objLeadAssignResp.Message = "No Active lead present or no active agent available at this time for this group";
                            objLeadAssignResp.Status = true;
                        }
                    }
                    else
                    {
                        //Create Lead
                        CreateLeadRequest request = new CreateLeadRequest();
                        request = new CreateLeadRequest()
                        {
                            Name = dt.Rows[0]["Name"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["Name"]) : "",
                            ProductId = ProductId,
                            Email = dt.Rows[0]["EmailId"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["EMAILID"]) : "",
                            UtmSource = "Whatsapp_CJ",
                            LeadSource = "whatsapp",
                            MobileNo = dt.Rows[0]["MobileNo"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["MobileNo"]) : "",
                            DateofBirth = dt.Rows[0]["DOB"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["DOB"]) : "",
                            Source = "WAHNICreateLead",
                            AssignedGroupId = GroupID,
                            IsHNICustomer = IsHNICustomer,
                            ApiSource = (ApiSource ?? string.Empty) + "-AssignLeadToGroup"
                        };

                        CreateLeadResponse objCreateLeadResponse = new CreateLeadResponse() { AgentDetails = new VirtualNoAgentDetails() };
                        objCreateLeadResponse = CreateLead(request);

                        if (!string.IsNullOrEmpty(objCreateLeadResponse.AgentDetails.EmployeeId))
                        {
                            objLeadAssignResp.Data.AssignedAgentECode = objCreateLeadResponse.AgentDetails.EmployeeId;
                            objLeadAssignResp.Data.AssignToAgentName = objCreateLeadResponse.AgentDetails.EmployeeName;
                            objLeadAssignResp.Data.LeadID = objCreateLeadResponse.LeadId;
                            objLeadAssignResp.Data.GroupID = objCreateLeadResponse.AgentDetails.GroupID;
                            objLeadAssignResp.Data.ISUHNIAgent = objCreateLeadResponse.AgentDetails.GroupID > 0 ? 1 : 0;
                            objLeadAssignResp.Message = "Lead Created and assigned successfully";
                            objLeadAssignResp.Status = true;
                        }
                        else
                        {
                            objLeadAssignResp.Data.AssignedAgentECode = "";
                            objLeadAssignResp.Data.AssignToAgentName = "";
                            objLeadAssignResp.Data.LeadID = LeadID;
                            objLeadAssignResp.Data.GroupID = objCreateLeadResponse.AgentDetails.GroupID > 0 ? 1 : 0;
                            objLeadAssignResp.Data.ISUHNIAgent = 0;
                            objLeadAssignResp.Message = "Lead Created, but no active agent is currently available in this group";
                            objLeadAssignResp.Status = true;
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                Exception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustomerID, Exception.ToString(), "AssignLeadToGroup", "MatrixCore", "WebSiteServiceBLL", ProductId.ToString(), JsonConvert.SerializeObject(objLeadAssignResp), DateTime.Now, DateTime.Now);
            }
            return objLeadAssignResp;
        }

        public List<LeadDeatils> GetActiveLeadsForCustomer(string mobileNo, short subProductId, string source, string encKey, string encIV)
        {
            var result = new List<LeadDeatils>();
            try
            {
                DataSet dataSet = WebSiteServicDLL.GetActiveLeadsForCustomer(Crypto.Decrytion_Payment_AES(mobileNo, "Core", 256, 128, encKey, encIV, true), subProductId);

                if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in dataSet.Tables[0].Rows)
                    {
                        var details = new LeadDeatils()
                        {
                            UTMSource = row["Utm_source"] == DBNull.Value ? "" : Convert.ToString(row["Utm_source"]),
                            UTMTerm = row["Utm_term"] == DBNull.Value ? "" : Convert.ToString(row["Utm_term"]),
                            UTMMedium = row["UTM_Medium"] == DBNull.Value ? "" : Convert.ToString(row["UTM_Medium"]),
                            UTMCampaign = row["Utm_campaign"] == DBNull.Value ? "" : Convert.ToString(row["Utm_campaign"]),
                            Name = row["Name"] == DBNull.Value ? "" : Convert.ToString(row["Name"]),
                            CityID = (row["CityID"] != null && row["CityID"] != DBNull.Value) ? Convert.ToInt16(row["CityID"]) : 0,
                            StateID = (row["StateID"] != null && row["StateID"] != DBNull.Value) ? Convert.ToInt16(row["StateID"]) : 0,
                            ProductID = row["ProductID"] == DBNull.Value ? 0 : Convert.ToInt16(row["ProductID"]),
                            CustomerID = row["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(row["CustomerID"]),
                            LeadId = row["LeadId"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadId"]),
                            LeadCreatedOn = row["CreatedOn"] == DBNull.Value ? default : Convert.ToDateTime(row["CreatedOn"]),
                            AssignedAgentId = row["AssignedAgentId"] == DBNull.Value ? 0 : Convert.ToInt64(row["AssignedAgentId"]),
                            LeadSource = row["LeadSource"] == DBNull.Value ? "" : Convert.ToString(row["LeadSource"]),
                            StatusId = row["StatusID"] != DBNull.Value ? Convert.ToInt16(row["StatusID"]) : default,
                            ParentLeadId = row["ParentId"] != DBNull.Value ? Convert.ToInt64(row["ParentId"]) : default,
                        };
                        result.Add(details);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetActiveLeadsForCustomer", "MatrixCore", "WebSiteServiceBLL", string.Empty, "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public ResponseData<object> LeadDetailsForWhatsappBot(long customerId, short productId)
        {
            DateTime requestTime = DateTime.Now;
            ResponseData<object> response = new()
            {
                Status = false,
                Message = "Failed"
            };

            try {
                DataSet dataSet = WebSiteServicDLL.LeadDetailsForWhatsappBot(customerId, productId);

                if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                {
                    response.Data = JsonConvert.DeserializeObject<object>(JsonConvert.SerializeObject(dataSet.Tables[0]));
                    response.Status = true;
                    response.Message = "Success";
                } 
                else {
                    response.Status = true;
                    response.Message = "No Data Found";
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(customerId.ToString(), customerId, ex.ToString(), "LeadDetailsForWhatsappBot", "MatrixCore", "WebSiteServiceBLL", string.Empty, "", requestTime, DateTime.Now);
            }

            return response;
        }

        public ResponseData<object> GetLeadDetailsForAI(long leadId)
        {
            DateTime requestTime = DateTime.Now;
            ResponseData<object> response = new()
            {
                Status = false,
                Message = "Failed"
            };

            try {
                DataSet dataSet = WebSiteServicDLL.GetLeadDetailsForAI(leadId);

                if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                {
                    response.Data = JsonConvert.DeserializeObject<object>(JsonConvert.SerializeObject(dataSet.Tables[0]));
                    response.Status = true;
                    response.Message = "Success";
                } 
                else {
                    response.Status = true;
                    response.Message = "No Data Found";
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "GetLeadDetailsForAI", "MatrixCore", "WebSiteServiceBLL", string.Empty, "", requestTime, DateTime.Now);
            }

            return response;
        }
        public bool CheckIsLeadInvalidAI(long leadId)
        {
            DateTime reqTime = DateTime.Now;
            try 
            {
                if(leadId > 0)
                {
                    return WebSiteServicDLL.CheckIsLeadInvalidAI(Convert.ToInt64(leadId));
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.ToString(), "CheckIsLeadInvalidAI", "MatrixCore","WebSiteServiceBLL", string.Empty, string.Empty, reqTime , DateTime.Now);
            }

            return false;
        }
        public ResponseData<List<KeyValuePair<string, string>>> GetAdditionalLeadDetailsFromCJ(long LeadId)
        {
            DateTime dt = DateTime.Now;
            var response = new ResponseData<List<KeyValuePair<string, string>>>
            {
                Status = true,
                Message = "No Data Found",
                Data = new List<KeyValuePair<string, string>>()
            };

            try
            {
                string apiUrl = "HealthApiBaseUrl".AppSettings() + "applicationservices/HealthPrequotes.svc/GetGuidedQuestionsAnswers?LeadId=" + LeadId;
                var header = new Dictionary<object, object>();
                header.Add("apikey", "healthApiKey".AppSettings());

                string apiResponse = CommonAPICall.CallAPI(apiUrl, null, "GET", 5000, "application/json", header);

                if (!string.IsNullOrEmpty(apiResponse))
                {
                    var parsedResponse = JsonConvert.DeserializeObject<dynamic>(apiResponse);

                    if (parsedResponse != null && parsedResponse.Data != null)
                    {
                        foreach (var section in parsedResponse.Data[0].GenericSections)
                        {
                            if (section.Header == "Guided Questions")
                            {
                                foreach (var row in section.Rows)
                                {
                                    foreach (var field in row.Fields)
                                    {
                                        response.Data.Add(new KeyValuePair<string, string>(
                                            field.Name.ToString(),
                                            field.Value.ToString()
                                        ));
                                    }
                                }
                            }
                        }

                        response.Status = true;
                        response.Message = "Data fetched successfully";
                    }
                }
            }
            catch (Exception ex)
            {
                response.Status = false;
                response.Data = new List<KeyValuePair<string, string>>();
                response.Message = "Failed To fetch Data";
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), 0, ex.ToString(), "GetAdditionalLeadDetailsFromCJ", "WebSiteServiceBLL", "MatrixCoreAPI", string.Empty, ex.ToString(), dt, DateTime.Now);
            }

            return response;
        }

        public ResponseData<object> GetCustomerLeadCityforAI(long customerId)
        {
            DateTime requestTime = DateTime.Now;
            ResponseData<object> response = new()
            {
                Status = false,
                Message = "Failed"
            };

            try {
                DataSet dataSet = WebSiteServicDLL.GetCustomerLeadCityforAI(customerId);

                if (dataSet != null && dataSet.Tables.Count > 0 && dataSet.Tables[0].Rows.Count > 0)
                {
                    response.Data = JsonConvert.DeserializeObject<object>(JsonConvert.SerializeObject(dataSet.Tables[0]));
                    response.Status = true;
                    response.Message = "Success";
                } 
                else {
                    response.Status = true;
                    response.Message = "No Data Found";
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(customerId.ToString(), customerId, ex.ToString(), "GetCustomerLeadCityforAI", "MatrixCore", "WebSiteServiceBLL", string.Empty, "", requestTime, DateTime.Now);
            }

            return response;
        }

        public static async Task GetMotorEnquiryIDFromCJ(long LeadId, long CustomerId, long EnquiryId)
        {
            string Exception = "";
            await Task.Run(() =>
            {
                string NewEnquiryId = "";
                DateTime requestTime = DateTime.Now;
                try
                {
                    if (EnquiryId == 0)
                    {
                       
                        DataTable dt = WebSiteServicDLL.GetActiveLeadDetails(CustomerId, 117, "MotorEnquiryDetails");
                        if (dt != null && dt.Rows != null && dt.Rows.Count > 0)
                        {
                            long OldEnquiryID = dt.Rows[0]["EnquiryID"] != DBNull.Value ? Convert.ToInt64(dt.Rows[0]["EnquiryID"]) : 0;

                            if(OldEnquiryID > 0)
                            {
                                string InternalAPIAuthKey = ConnectionClass.getCommonSecretKeyFromAWS("InternalAPIAuthKey", "CarAWSEnv".AppSettings());
                                string InternalAPIClientKey = ConnectionClass.getCommonSecretKeyFromAWS("InternalAPIClientKey", "CarAWSEnv".AppSettings());

                                string EncryptKey = ConnectionClass.getCommonSecretKeyFromAWS("CrmAesKeyForLead", "CarAWSEnv".AppSettings());
                                string EncryptIV = ConnectionClass.getCommonSecretKeyFromAWS("CrmAesIvForLead", "CarAWSEnv".AppSettings());

                                string EncryptedOldEnquiryId = Crypto.encrypt_AES(OldEnquiryID.ToString(), 128, 128, EncryptKey, EncryptIV);
                                string EncryptedLeadId = Crypto.encrypt_AES(LeadId.ToString(), 128, 128, EncryptKey, EncryptIV);
                                string EncryptedCustomerId = Crypto.encrypt_AES(CustomerId.ToString(), 128, 128, EncryptKey, EncryptIV);

                                string baseUrl = "MotorInfraBaseURL".AppSettings();
                                string url = $"{baseUrl}/apicarinfra/Internal/InternalCreateLeadToPitch";

                                var headers = new Dictionary<object, object>
                                {
                                    { "accept", "text/plain" },
                                    { "AuthKey", InternalAPIAuthKey },
                                    { "ClientKey", InternalAPIClientKey },
                                    { "Content-Type", "application/json-patch+json" }
                                };

                                var requestBody = new
                                {
                                    newEncLeadId = EncryptedLeadId,
                                    oldEncCustId = EncryptedCustomerId,
                                    oldEncEnquiryId = EncryptedOldEnquiryId
                                };
                                string jsonBody = JsonConvert.SerializeObject(requestBody);

                                string apiResponse = CommonAPICall.CallAPI(url, jsonBody, "POST", 3000, "application/json-patch+json", headers);

                                if (!string.IsNullOrEmpty(apiResponse))
                                {
                                    dynamic resp = JsonConvert.DeserializeObject(apiResponse);
                                    if (resp != null && resp.responseCode == 1)
                                    {
                                        NewEnquiryId = resp.data;
                                        if (!string.IsNullOrEmpty(NewEnquiryId))
                                        {
                                            string EnquiryId= Crypto.decrypt_AES(NewEnquiryId, 128, 128, EncryptKey, EncryptIV);
                                            if (!string.IsNullOrEmpty(EnquiryId))
                                            {
                                                WebSiteServicDLL.UpdateLeadDetailsOnCreateLead(LeadId, Convert.ToInt64(EnquiryId));
                                            }
                                        }
                                        else
                                        {
                                            Exception = "blank enquiryId from API";
                                        }
                                    }
                                }
                                else
                                {
                                    Exception = "No Response from API";
                                }
                            }
                            else
                            {
                                Exception = "No EnquiryId exists for this customer";
                            }
                        }
                        else
                        {
                            Exception = "No Data for this customer";
                        }
                    }
                    else
                    {
                        Exception = "EnquiryId already exists";
                    }

                }
                catch (Exception ex)
                {
                    Exception = Exception + "Exception-  " + ex.ToString();
                }
                finally
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, Exception, "GetMotorEnquiryIDFromCJ", "MatrixCore", "WebSiteServiceBLL", EnquiryId.ToString(), NewEnquiryId, requestTime, DateTime.Now);
                }
            });
        }

        public SaveInfo GetCustRecordingConcern(LeadsSubStatusModel CustResponse)
        {
            SaveInfo oSaveInfo = new();
            DateTime dt = DateTime.Now;
            try
            {
                LeadsSubStatusModel LeadsSubStatusModel = new()
                {
                    AppointmentId = CustResponse.AppointmentId,
                    Comments = Convert.ToString(CustResponse.Comments),
                    UserID = 4020, //response from customer
                    EventId = 73,
                    Source = CustResponse.Source,
                    AppointmentDateTime = new DateTime(1753, 1, 1)
                  

                };
                long Id = FOSDLL.SaveAppointmentHistory(LeadsSubStatusModel);

                if (Id > 0)
                {
                    oSaveInfo.IsSaved = true;
                    oSaveInfo.Message = "Save Successfully";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustResponse.AppointmentId, ex.ToString(), "GetCustRecordingConcern", "Matrixcore", "FOSBLL", Convert.ToString(CustResponse), Convert.ToString(oSaveInfo), dt, DateTime.Now);
            }
            return oSaveInfo;
        }
       
    }
}
