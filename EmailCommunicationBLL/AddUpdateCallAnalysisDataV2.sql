
-- =============================================================      
-- Author: <PERSON><PERSON>
-- Create date: 26-Nov-2024
-- Description: Add Update Call Analysis Data
-- =============================================================

-- EXEC [MTX].[AddUpdateCallAnalysisDataV2]  45148869,102800,150
ALTER PROCEDURE [MTX].[AddUpdateCallAnalysisDataV2] 
(
	@LeadId BIGINT,
	@CallDataId BIGINT,
	@CallStatusId SMALLINT
)
AS
BEGIN
	SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED
	SET DEADLOCK_PRIORITY LOW
	SET NOCOUNT OFF;

    DECLARE @TempTable TABLE (
        ErrorMessage VARCHAR(50),
        CustomerId BIGINT,
        CallDate DATETIME,
        IsBMS BIT, 
        EmpId VARCHAR(10)
    );

    DECLARE @CustId BIGINT = 0;
    DECLARE @ProductId INT = 0;
    DECLARE @LeadSource VARCHAR(100) = '';
    DECLARE @talktime INT = 0;
    

    SELECT @CustId = ISNULL(CustomerID,0), @ProductId = ProductID, @LeadSource = LeadSource FROM Matrix.CRM.LeadDetails150 (NOLOCK) WHERE LeadID = @LeadId;
    IF ISNULL(@CustId,0)=0
        BEGIN
            SELECT @CustId = ISNULL(CustomerID,0), @ProductId = ProductID, @LeadSource = LeadSource FROM Matrix.CRM.Leaddetails (NOLOCK) WHERE LeadID = @LeadId;
        END


	-- INSERT INTO @TempTable VALUES ('', @CustId, ISNULL((SELECT CallDate FROM MTX.CallDataHistory WHERE CallDataID = @CallDataId AND LeadID = @LeadId), NULL));
	INSERT INTO @TempTable 
        SELECT '', @CustId, cdh.CallDate, cdh.IsBMS, ud.EmployeeId
        FROM MTX.CallDataHistory cdh
        JOIN CRM.UserDetails ud on cdh.UserID=ud.UserID
        WHERE CallDataID = @CallDataId AND LeadID = @LeadId

	IF EXISTS(SELECT top(1) 1 FROM @TempTable WHERE CustomerId > 0)
        BEGIN

            UPDATE [MTX].[CallDataHistory]
            SET talktime = 0, Disposition = 'VOICEMAIL',[Status] = @CallStatusId, UpdatedOn = GETDATE()
            WHERE CallDataID = @CallDataId AND LeadID = @LeadId
            

            UPDATE MTX.CallAnalysisData
            SET CallStatusId=@CallStatusId,
                UpdatedOn=GETDATE(),
                UpdatedBy=124,
                IsActive=1
            WHERE LeadId=@LeadId AND CallDataId=@CallDataId

            IF(@@ROWCOUNT=0)
            BEGIN
                INSERT INTO MTX.CallAnalysisData
                (LeadId,CallDataId,CallStatusId,CreatedOn,CreatedBy,IsActive)
                VALUES
                (@LeadId,@CallDataId,@CallStatusId,GETDATE(),124,1)
            END

            SELECT @talktime = SUM(talktime) FROM [MTX].[CallDataHistory] WHERE LeadID = @LeadId
            IF(@talktime < 30 AND @ProductId = 2 AND @LeadSource = 'Renewal')
            BEGIN
                UPDATE CRM.LeadStatus set SubStatusID = (select SubStatusID from CRM.SubstatusMaster where SubStatusName ='Voice Mail'),UpdatedOn = GETDATE() 
                    WHERE LeadID = @LeadId AND IsLastStatus = 1 AND StatusID in(2,3)

                INSERT INTO CRM.LeadHistory (
	            	LeadID
	            	,UserID
	            	,Comments
	            	,EventType
	            	,CreatedOn
	            	,StatusID
	            	,SubStatusID
	            	,UpdatedOn
	            	)
	            SELECT @LeadID
	            	,124
	            	,'Substatus Changed to voice mail'
	            	,7
	            	,GETDATE()
	            	,StatusID
	            	,SubStatusID
	            	,GETDATE()
	            FROM CRM.LeadStatus(NOLOCK) A
	            WHERE IsLastStatus = 1
	            	AND Leadid = @LeadID AND StatusID in(2,3);
            END
        END
	ELSE 
        BEGIN
            UPDATE @TempTable SET ErrorMessage='LeadId does not exists'
        END

    SELECT * FROM @TempTable;
END