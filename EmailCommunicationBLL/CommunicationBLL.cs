﻿using Helper;
using System;
using System.Web;
using System.Data;
using System.Text;
using PropertyLayers;
using MongoConfigProject;
using Newtonsoft.Json;
using DataAccessLayer;
using DataAccessLibrary;
using System.Collections.Generic;
using System.Linq;
using EmailCommunicationBLL.Helpers;
using System.Dynamic;

namespace EmailCommunicationBLL
{
    public class CommunicationBLL : ICommunicationBLL
    {
        public CtpResponse GetLeadDetails(CtpRequest request)
        {
            var response = new CtpResponse()
            {
                IsSuccess = true
            };
            try
            {
                if (request != null && request.LeadId > 0 && request.ProductId > 0)
                {
                    string assignGroupName = string.Empty;
                    if (request.ProductId == 7)
                        assignGroupName = "TERM_IB";
                    if (request.ProductId == 115)
                        assignGroupName = "NTM_IB";

                    var basicData = CommunicationDLL.GetBasicLeadDetails(request.LeadId);

                    string leadSource = basicData.Rows[0]["LeadSource"] != DBNull.Value ?
                                basicData.Rows[0]["LeadSource"].ToString() :
                                default;

                    if (basicData != null && basicData.Rows != null && basicData.Rows.Count > 0)
                    {
                        if (request.ProductId == 117)
                        {
                            FetchMotorDetails(basicData, response, request);
                        }
                        else if (request.ProductId == 7)
                        {
                            FetchTermDetails(basicData, response, assignGroupName, request);
                        }
                        else if (request.ProductId == 115)
                        {
                            // Data to be sent is same as term
                            FetchTermDetails(basicData, response, assignGroupName, request);
                        }
                        else if ((request.ProductId == 2 ||
                            request.ProductId == 106 ||
                            request.ProductId == 118 ||
                            request.ProductId == 130) && leadSource.ToLower() == "renewal")
                        {
                            IUpdateRenewalLeadsBLL objRenewalBLL = new UpdateRenewalLeadsBLL();
                            response.ContinueLink = objRenewalBLL.GetCJUrl(request.LeadId.ToString(), 2, 0, "renewal", request.IsAgentUrl);
                            if (!IsValidCJUrl(response.ContinueLink))
                            {
                                response.ContinueLink = "";
                            }
                        }
                        else if (request.ProductId == 2)
                        {
                            string exitPointURL = basicData.Rows[0]["ExitPointURL"] != DBNull.Value ?
                                    basicData.Rows[0]["ExitPointURL"].ToString() :
                                    default;
                            if (IsValidCJUrl(exitPointURL))
                            {
                                response.ContinueLink = exitPointURL;
                                if (string.IsNullOrEmpty(request.IsAgentUrl))
                                {
                                    response.ContinueLink = GetHealthCustomerExitPoint(basicData, request.LeadId);
                                }
                            }
                            else
                            {
                                response.ContinueLink = "";
                            }

                            if (!string.IsNullOrEmpty(request.HealthUrl))
                            {
                                FetchHealthDetails(response, request.HealthUrl, basicData, request.LeadId);
                            }
                        }
                        else if (request.ProductId == 3)
                        {
                            FetchTravelDetails(basicData, response, request);
                        }
                        else
                        {
                            response.IsSuccess = true;
                            response.ContinueLink = basicData.Rows[0]["ExitPointURL"] != DBNull.Value ?
                                    basicData.Rows[0]["ExitPointURL"].ToString() :
                                    "";
                        }


                        if (request.ProductId == 101)
                        {
                            FetchHomeDetails(response);
                        }
                    }
                    else
                    {
                        response.IsSuccess = false;
                        response.ErrorMessage = "Lead data not found";
                    }
                }
                else
                {
                    response.IsSuccess = false;
                    response.ErrorMessage = "Invalid input parameters";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(request.LeadId.ToString(), request.LeadId, ex.Message, "GetLeadDetails", "MatrixCore", "CommunicationBLL", "", string.Empty, DateTime.Now, DateTime.Now);
                response.IsSuccess = false;
                response.ErrorMessage = "Error occured";
            }
            return response;
        }


        private static string GetHealthCustomerExitPoint(DataTable basicData, long leadId)
        {
            LeadDetailsBLL leadDetailsBLL = new LeadDetailsBLL();
            string role = "customer";
            string url = string.Empty;
            string exitPointURL = basicData.Rows[0]["ExitPointURL"] != DBNull.Value ? Convert.ToString(basicData.Rows[0]["ExitPointURL"].ToString()) : string.Empty;

            string LeadCreationSource = basicData.Rows[0]["LeadCreationSource"] != DBNull.Value ?
                                  basicData.Rows[0]["LeadCreationSource"].ToString() :
                                  default;

            if (LeadCreationSource.Contains("PBCromaService"))
            {
                url = leadDetailsBLL.GetHealthCustomerExitPoint(exitPointURL, leadId, role);
            }

            return url;
        }

        private static void FetchHealthDetails(CtpResponse response, string url, DataTable basicData, long leadId)
        {
            response.HealthInfo = new HealthInfo();

            string enquiryId = string.Empty;
            string profileId = string.Empty;
            string plandId1 = string.Empty;
            int sumInsured1 = 0;
            string plandId2 = string.Empty;
            int sumInsured2 = 0;



            var uri = new Uri(url);
            var query = HttpUtility.ParseQueryString(uri.Query);
            enquiryId = query.Get("enquiryid");
            if (!string.IsNullOrEmpty(enquiryId))
            {
                enquiryId = Encoding.UTF8.GetString(Convert.FromBase64String(enquiryId));
            }

            profileId = query.Get("profileid");
            string[] plan = query.GetValues("plan");
            if (plan != null && plan.Length > 1)
            {
                string[] plandata = plan[0].Split("-");
                plandId1 = plandata[0];
                sumInsured1 = Convert.ToInt32(plandata[1]);
                plandata = plan[1].Split("-");
                plandId2 = plandata[0];
                sumInsured2 = Convert.ToInt32(plandata[1]);

                var header = new Dictionary<object, object>
                {
                    { "Content-type", "application/json" },
                    { "apikey", "HealthApiBaseHeaderKey".AppSettings() }

            };

                string json = "{\"EnquiryID\":" + enquiryId + ",\"ProfileID\":" + profileId + ",\"ComparedPlans\":[{\"SumInsured\":" + sumInsured1 + ",\"PlanID\":" + plandId1 + "},{\"SumInsured\":" + sumInsured2 + ",\"PlanID\":" + plandId2 + "}]}";
                var result = CommonAPICall.CallAPI("HealthApiBaseUrl".AppSettings() + "applicationservices/masterservice.svc/GetComaprePlanDetailsForMatrix",
                                                   json,
                                                   "POST",
                                                   3000,
                                                   header);
                var planResponse = JsonConvert.DeserializeObject<ComparePlanResponse>(result);

                if (planResponse != null)
                {
                    if (planResponse.Quotes.Length.Equals(2))
                    {
                        Quote plan1Quote = planResponse.Quotes[0];
                        Quote plan2Quote = planResponse.Quotes[1];

                        response.HealthInfo.PremiumPlan1 = plan1Quote.FinalPremium / 12;
                        response.HealthInfo.SAPlan1 = sumInsured1 / 100000;
                        response.HealthInfo.PremiumPlan2 = plan2Quote.FinalPremium / 12;
                        response.HealthInfo.SAPlan2 = sumInsured2 / 100000;
                        response.HealthInfo.Insurer1LogoUrl = plan1Quote.InsurerLogo;
                        response.HealthInfo.Insurer2LogoUrl = plan2Quote.InsurerLogo;

                        var PlanFeatureList = new List<PlanFeature>();
                        var PlanFeatureList2 = new List<PlanFeature>();

                        if (plan1Quote.TopFeatures != null)
                        {
                            Topfeature[] TopFeatures = plan1Quote.TopFeatures;
                            foreach (var currentFeature in TopFeatures)
                            {
                                PlanFeatureList.Add(new PlanFeature
                                {
                                    Name = currentFeature.FeatureShortName,
                                    FeatureShortText1 = currentFeature.FeatureShortText,
                                    FeatureID = currentFeature.FeatureID,
                                    FeatureOrder = currentFeature.FeatureOrder
                                });
                            }
                        }
                        if (plan2Quote.TopFeatures != null)
                        {
                            Topfeature[] TopFeatures = plan2Quote.TopFeatures;
                            foreach (var currentFeature in TopFeatures)
                            {
                                PlanFeatureList2.Add(new PlanFeature
                                {
                                    Name = currentFeature.FeatureShortName,
                                    FeatureShortText2 = currentFeature.FeatureShortText,
                                    FeatureID = currentFeature.FeatureID,
                                    FeatureOrder = currentFeature.FeatureOrder
                                });
                            }

                            /*Merge Plan data*/
                            PlanFeatureList.ForEach(selection =>
                            {
                                int FeatureID = selection.FeatureID;
                                PlanFeature feature = PlanFeatureList2.Find(s => s.FeatureID == FeatureID);
                                selection.FeatureShortText2 = feature.FeatureShortText2;
                            });

                            response.HealthInfo.PlanFeatureList = PlanFeatureList;
                        }
                    }
                }
            }
        }

        private static void FetchHomeDetails(CtpResponse response)
        {
            response.TollFreeNo = "1800-258-7202";
        }

        private static void FetchTravelDetails(DataTable basicData, CtpResponse response, CtpRequest request)
        {
            int ProductID = basicData.Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(basicData.Rows[0]["ProductID"].ToString()) : 0;
            string LeadSource = basicData.Rows[0]["LeadSource"] != DBNull.Value ? Convert.ToString(basicData.Rows[0]["LeadSource"].ToString()) : string.Empty;
            string LeadCreationSource = basicData.Rows[0]["LeadCreationSource"] != DBNull.Value ? Convert.ToString(basicData.Rows[0]["LeadCreationSource"].ToString()) : string.Empty;
            long EnquiryID = basicData.Rows[0]["EnquiryID"] != DBNull.Value ? Convert.ToInt64(basicData.Rows[0]["EnquiryID"].ToString()) : 0;
            LeadDetailsBLL leadDetailsBLL = new LeadDetailsBLL();
            UrlResponse output = leadDetailsBLL.GetCommonCJUrl(Convert.ToString(request.LeadId), ProductID, 0, LeadSource, "", LeadCreationSource, Convert.ToString(EnquiryID), request.IsAgentUrl);

            if (IsValidCJUrl(output.ExitPointURL))
            {
                response.ContinueLink = output.ExitPointURL;
            }
            else
            {
                response.ContinueLink = "";
            }
        }

        private static void FetchTermDetails(DataTable basicData, CtpResponse response, string assignGroupName, CtpRequest request)
        {
            string encMobileNo = basicData.Rows[0]["MobileNo"] != DBNull.Value ?
                                 Crypto.encrypt_AES(basicData.Rows[0]["MobileNo"].ToString(), "MyAccEncKey".AppSettings(), "MyAccIVKey".AppSettings(), 128, 128) :
                                 string.Empty;

            long custId = basicData.Rows[0]["CustomerID"] != DBNull.Value ?
                          Convert.ToInt64(basicData.Rows[0]["CustomerID"].ToString()) :
                          default;
            if (request.ProductId == 7)
            {
                response.ScheduleCallbackLink = string.Format("{0}/schedulecall/?leadId={1}&mobileNo={2}&group={3}",
                                                          "TermCjAPI".AppSettings().ToString(),
                                                          request.LeadId,
                                                          encMobileNo,
                                                          assignGroupName);
            }

            string exitPointURL = basicData.Rows[0]["ExitPointURL"] != DBNull.Value ?
                                    basicData.Rows[0]["ExitPointURL"].ToString() :
                                    default;

            string LeadCreationSource = basicData.Rows[0]["LeadCreationSource"] != DBNull.Value ?
                                    basicData.Rows[0]["LeadCreationSource"].ToString() :
                                    default;


            if (IsValidCJUrl(exitPointURL))
            {
                if (LeadCreationSource.Contains("PBCromaService") && string.IsNullOrEmpty(request.IsAgentUrl))
                {
                    LeadDetailsBLL leadDetailsBll = new LeadDetailsBLL();
                    if (request.ProductId == 7)
                    {
                        response.ContinueLink = leadDetailsBll.GetTermCustomerExitPoint(exitPointURL, request.LeadId);
                    }
                    else if (request.ProductId == 115)
                    {
                        response.ContinueLink = leadDetailsBll.GetInvestmentCustomerExitPoint(exitPointURL, request.LeadId);
                    }
                }
                else
                    response.ContinueLink = exitPointURL;
            }
            else
            {
                response.ContinueLink = "";
            }



            var callBackData = CommunicationDLL.CallBackData(request.LeadId, custId, request.ProductId);

            if (callBackData != null && callBackData.Rows != null && callBackData.Rows.Count > 0)
            {
                response.CallBackDate = callBackData.Rows[0]["EventDate"] != DBNull.Value ?
                                        Convert.ToDateTime(callBackData.Rows[0]["EventDate"]).ToString("dd/MM/yyyy") :
                                        default;
                response.CallBackTime = callBackData.Rows[0]["EventDate"] != DBNull.Value ?
                                        Convert.ToDateTime(callBackData.Rows[0]["EventDate"]).TimeOfDay.ToString() :
                                        default;
            }
        }

        private static void FetchMotorDetails(DataTable basicData, CtpResponse response, CtpRequest request)
        {
            response.CarInfo = new CarInfo();
            if (basicData != null && basicData.Rows != null && basicData.Rows.Count > 0)
            {
                string encLeadId = Convert.ToBase64String(Encoding.UTF8.GetBytes(request.LeadId.ToString()));
                string encEnquiryId = basicData.Rows[0]["EnquiryID"] != DBNull.Value ?
                                   Convert.ToBase64String(Encoding.UTF8.GetBytes(basicData.Rows[0]["EnquiryID"].ToString())) :
                                   string.Empty;
                string validationKey = basicData.Rows[0]["EncryptedLeadId"] != DBNull.Value ?
                                       basicData.Rows[0]["EncryptedLeadId"].ToString() :
                                       string.Empty;
                response.ScheduleCallbackLink = string.Format("{0}/schedule?enquiryId={1}&leadid={2}&frame=true&flag=true&ref=p&IdSub=true&ValidationKey={3}",
                                                              "CarCJPortal".AppSettings().ToString(),
                                                              encEnquiryId,
                                                              encLeadId,
                                                              validationKey);
                int ProductID = basicData.Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(basicData.Rows[0]["ProductID"].ToString()) : 0;
                string LeadSource = basicData.Rows[0]["LeadSource"] != DBNull.Value ? Convert.ToString(basicData.Rows[0]["LeadSource"].ToString()) : string.Empty;
                string LeadCreationSource = basicData.Rows[0]["LeadCreationSource"] != DBNull.Value ? Convert.ToString(basicData.Rows[0]["LeadCreationSource"].ToString()) : string.Empty;
                long EnquiryID = basicData.Rows[0]["EnquiryID"] != DBNull.Value ? Convert.ToInt64(basicData.Rows[0]["EnquiryID"].ToString()) : 0;


                if (EnquiryID > 0)
                {
                    LeadDetailsBLL leadDetailsBLL = new LeadDetailsBLL();
                    UrlResponse output = leadDetailsBLL.GetCommonCJUrl(Convert.ToString(request.LeadId), ProductID, 0, LeadSource, "", LeadCreationSource, Convert.ToString(EnquiryID), request.IsAgentUrl);

                    if (IsValidCJUrl(output.ExitPointURL))
                    {
                        response.ContinueLink = output.ExitPointURL;
                    }
                    else
                    {
                        response.ContinueLink = "";
                    }
                }
                else
                {
                    response.ContinueLink = "";
                }

            }

            var carData = CommunicationDLL.GetCarDetails(request.LeadId);

            if (carData != null && carData.Rows != null && carData.Rows.Count > 0)
            {
                response.CarInfo.RegistrationNo = carData.Rows[0]["RegistrationNo"] != DBNull.Value ? carData.Rows[0]["RegistrationNo"].ToString() : default;
                response.CarInfo.VariantName = carData.Rows[0]["VehicleVariantName"] != DBNull.Value ? carData.Rows[0]["VehicleVariantName"].ToString() : default;
                response.CarInfo.ModelName = carData.Rows[0]["VehicleModelName"] != DBNull.Value ? carData.Rows[0]["VehicleModelName"].ToString() : default;
                response.CarInfo.MakeName = carData.Rows[0]["MakeName"] != DBNull.Value ? carData.Rows[0]["MakeName"].ToString() : default;
                response.CarInfo.PreviousPolicyExpiryDate = carData.Rows[0]["PreviousPolicyExpiryDate"] != DBNull.Value ? carData.Rows[0]["PreviousPolicyExpiryDate"].ToString() : default;
            }
        }

        public List<AgentCommLogs> GetAgentCommLogs(long userId)
        {
            DateTime reqTime = DateTime.Now;
            var commHistory = new List<AgentCommLogs>();
            try
            {
                DataSet dataSet = CommunicationDLL.GetAgentCommLogs(userId);
                if (dataSet != null && dataSet.Tables.Count > 0)
                {
                    if (dataSet.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow dr in dataSet.Tables[0].Rows)
                        {
                            var agentCommLog = new AgentCommLogs();

                            DateTime createdOn = dr["CreatedOn"] != null && dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : DateTime.MinValue;
                            int duration = dr["Duration"] != null && dr["Duration"] != DBNull.Value ? Convert.ToInt32(dr["Duration"]) : 0;
                            int talktime = dr["talktime"] != null && dr["talktime"] != DBNull.Value ? Convert.ToInt32(dr["talktime"]) : 0;
                            EnumCommTypes commType = dr["CommType"] != null && dr["CommType"] != DBNull.Value ? (EnumCommTypes)Convert.ToInt32(dr["CommType"]) : 0;
                            string callStatus = dr["CallStatus"] != null && dr["CallStatus"] != DBNull.Value ? Convert.ToString(dr["CallStatus"]) :
                                                talktime > 0 ? "Call ANSWERED" : "Phone ringing and not answered";
                            string conversationTypeValue = dr["ConversationTypeValue"] != null && dr["ConversationTypeValue"] != DBNull.Value ? Convert.ToString(dr["ConversationTypeValue"]) : string.Empty;
                            string commTypeValue = dr["CommType"] != null && dr["CommType"] != DBNull.Value ? CommHelper.GetCommValue((EnumCommTypes)Convert.ToInt32(dr["CommType"])) : string.Empty;
                            string triggerName = dr["TriggerName"] != null && dr["TriggerName"] != DBNull.Value ? Convert.ToString(dr["TriggerName"]) : String.Empty;
                            var dispositionDetails = dr["Duration"] != null && dr["Duration"] != DBNull.Value && Convert.ToInt32(dr["Duration"]) > 0
                                                     ? CommHelper.GetDispositions(createdOn, createdOn.AddSeconds(Convert.ToInt32(duration)), talktime > 0 ? 16 : 18, commType, callStatus)
                                                     : CommHelper.DefaultDisp(createdOn, commType, callStatus);

                            agentCommLog.LeadId = dr["LeadID"] != null && dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : 0;
                            agentCommLog.CustomerId = dr["CustomerId"] != null && dr["CustomerId"] != DBNull.Value ? Convert.ToInt64(dr["CustomerId"]) : 0;
                            agentCommLog.CompanyName = dr["CompanyName"] != null && dr["CompanyName"] != DBNull.Value ? Convert.ToString(dr["CompanyName"]) : default;
                            agentCommLog.TimeStamp = createdOn;
                            agentCommLog.Talktime = talktime;
                            agentCommLog.CommType = commType.ToString();
                            agentCommLog.Comments = GetComments(dispositionDetails, talktime);

                            commHistory.Add(agentCommLog);
                        }
                    }

                    commHistory = commHistory.OrderByDescending(p => p.TimeStamp).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", userId, ex.Message, "GetAgentCommLogs", "MatrixCore", "CommunicationBLL", "", string.Empty, reqTime, DateTime.Now);
            }
            return commHistory;
        }

        private static string GetComments(List<Disposition> dispositionDetails, int callDuration)
        {
            string comments = string.Empty;

            if (dispositionDetails != null && dispositionDetails.Count > 0)
            {
                foreach (var data in dispositionDetails)
                {
                    comments += data.StatusName + " at " + data.CreatedOn.ToString("dd/MM/yyyy HH:mm:ss") + " | ";
                }
            }

            if (callDuration > 0)
            {
                comments += "Total call duration: " + string.Format("{0:0.00}", callDuration / 60) + " minutes (" + callDuration + " seconds).";
            }

            comments = comments.Replace("Outbound Call INITIATED", "VIDEOMEET INITIATED");

            return comments;
        }

        private static bool IsValidCJUrl(string CJUrl)
        {
            if (!string.IsNullOrEmpty(CJUrl) &&
                (CJUrl.Contains("http://")
                   || CJUrl.Contains("https://")
                   || CJUrl.Contains("www.")
                )
                )
            {
                return true;
            }
            else
            {
                return false;
            }

        }


        public List<CTCSchdular> CallSchedular(long leadId)
        {
            List<CTCSchdular> response = new List<CTCSchdular>();
            DateTime reqTime = DateTime.Now;

            try
            {
                DataSet ds = CommunicationDLL.CallSchedular(leadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        CTCSchdular ctcSchdular = new CTCSchdular();
                        ctcSchdular.LeadID = Convert.ToInt32(row["LeadID"]);
                        ctcSchdular.GroupID = Convert.ToString(row["GroupID"]);
                        ctcSchdular.LeadSource = Convert.ToString(row["LeadSource"]);
                        ctcSchdular.ScheduleTime = Convert.ToDateTime(row["ScheduleTime"]).ToString("yyyy-MM-dd HH:mm:ss");
                        ctcSchdular.CreatedOn = Convert.ToDateTime(row["CreatedOn"]).ToString("yyyy-MM-dd HH:mm:ss");
                        ctcSchdular.SourceIP = Convert.ToString(row["SourceIP"]);
                        response.Add(ctcSchdular);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.Message, "CallSchedular", "MatrixCore", "CommunicationBLL", "", string.Empty, reqTime, DateTime.Now);
            }
            return response;
        }

        public string SubscribeCustomer(SubscribeCustomer subscribeCustomer)
        {
            dynamic dataToPost = new ExpandoObject();
            string response = "API_NOT_CALLED: lead/cust not Unsubsubscribed";
            string error = "";
            DateTime RequestTime = DateTime.Now;
            try
            {

                List<short> products = new() { subscribeCustomer.productID };

                List<dynamic> CommPreferences = new();
                Dictionary<object, object> ObjHeaders = new();

                string? MobileNo = "0";
                string? EncrytPhoneNo = string.Empty;
                DataTable dt = CommunicationDLL.GetLeadBasicDetails(subscribeCustomer.leadID);
                if (dt != null && dt.Rows.Count > 0)
                {
                    MobileNo = Convert.ToString(dt.Rows[0]["MobileNo"]);
                }
                if (!CoreCommonMethods.IsValidString(MobileNo) && MobileNo != "0")
                {
                    throw new Exception("Mobile Number not found");
                }
                else
                {
                    EncrytPhoneNo = Crypto.AESencrypt_Communication(MobileNo);
                }


                dataToPost.CustomerId = subscribeCustomer.custID;
                dataToPost.MobileNo = EncrytPhoneNo;
                dataToPost.CountryId = subscribeCustomer.countryCode;
                dataToPost.SubSource = subscribeCustomer.SubSource + "-MATRIX";
                dataToPost.CoolingPeriod = subscribeCustomer.coolingPeriod;
                CommPreferences.Add(new ExpandoObject());
                CommPreferences[0].CategoryCode = subscribeCustomer.CategoryCode;
                CommPreferences[0].ChannelCode = "CALL";
                CommPreferences[0].Subscribed = true;
                CommPreferences[0].ProductIds = products;

                dataToPost.CommPreferences = CommPreferences;

                string commUrl = "commserviceapi".AppSettings() + "api/unsubscribe/SaveUnsubscriptionStatus";

                ObjHeaders.Add("AppName", "Matrix");
                ObjHeaders.Add("AppKey", "CommToken".AppSettings());
                response = CommonAPICall.CallAPI(commUrl, JsonConvert.SerializeObject(dataToPost), "POST", 2000, "application/json", ObjHeaders);
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", subscribeCustomer.custID, error, "SavesubscriptionStatusAPI", "CommunicationBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(dataToPost), response, RequestTime, DateTime.Now);
            }
            return response;
        }

        public List<CTCSchdular> GetCallSchedularByCustId(long customerId)
        {
            List<CTCSchdular> response = new List<CTCSchdular>();
            DateTime reqTime = DateTime.Now;

            try
            {
                DataSet ds = CommunicationDLL.GetCallSchedularByCustId(customerId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        CTCSchdular ctcSchdular = new CTCSchdular();
                        ctcSchdular.LeadID = row["LeadID"] != DBNull.Value ? Convert.ToInt64(row["LeadID"]) : 0;
                        ctcSchdular.GroupID = row["GroupID"] != DBNull.Value ? Convert.ToString(row["GroupID"]) : string.Empty;
                        ctcSchdular.LeadSource = row["LeadSource"] != DBNull.Value ? Convert.ToString(row["LeadSource"]) : string.Empty;
                        ctcSchdular.ScheduleTime = row["ScheduleTime"] != DBNull.Value ? Convert.ToDateTime(row["ScheduleTime"]).ToString("yyyy-MM-dd HH:mm:ss") : string.Empty;
                        ctcSchdular.CreatedOn = row["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(row["CreatedOn"]).ToString("yyyy-MM-dd HH:mm:ss") : string.Empty;
                        ctcSchdular.SourceIP = row["SourceIP"] != DBNull.Value ? Convert.ToString(row["SourceIP"]): string.Empty;
                        ctcSchdular.ProductId = row["ProductID"] != DBNull.Value ? Convert.ToInt32(row["ProductID"]): 0;
                        ctcSchdular.CTCLeadID = row["CTCLeadId"] != DBNull.Value ? Convert.ToInt64(row["CTCLeadId"]): 0;
                        response.Add(ctcSchdular);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", customerId, ex.Message, "GetCallSchedularByCustId", "MatrixCore", "CommunicationBLL", "", string.Empty, reqTime, DateTime.Now);
            }
            return response;
        }
    }
}