﻿using System;
using System.Collections.Generic;
using DataAccessLibrary;
using Helper;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PropertyLayers;

using MongoConfigProject;
using System.Linq;
using System.Data;

namespace EmailCommunicationBLL
{
    public class TicketBLL : ITicket
    {

        public string GetTicketListMyAccV2(string CustId, string userid, string empolyeeid, string employeename, string rolename)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;
            string Url = "TicketService".AppSettings() + "GetTicketListForMatrix/" + CustId + "/" + userid + "/" + empolyeeid + "/" + employeename + "/" + rolename;
            string TicketToken = "TicketToken".AppSettings();

            Dictionary<object, object> header = new Dictionary<object, object>();
            header.Add("Token", TicketToken);
            header.Add("App", "Matrix");

            try
            {
                TicketDataResponse objTicketResponse = null;
                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                var data = CommonAPICall.CallAPI(Url, "", "GET", timeout, "application/json", header);
                if (CoreCommonMethods.IsValidString(data))
                {
                    objTicketResponse = JsonConvert.DeserializeObject<TicketDataResponse>(data);
                    UpdateLeadStatusIds(objTicketResponse);
                    result = JsonConvert.SerializeObject(objTicketResponse);
                }
                else
                {
                    result = "No Data found";
                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
                //LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(CustId), Error, "GetTicketListMyAccV2", "TicketBLL", "GetTicketListMyAccV2", JsonConvert.SerializeObject(CustId), result, dt, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(CustId), Error, "GetTicketListMyAccV2", "TicketBLL", "GetTicketListMyAccV2", JsonConvert.SerializeObject(CustId), result, dt, DateTime.Now);
            }
            return result;
        }
        private static void UpdateLeadStatusIds(TicketDataResponse objTicketResponse)
        {
            // Pick top 50 Tickets only
            objTicketResponse.Data = objTicketResponse.Data.OrderByDescending(ticket => ticket.TicketId).Take(50).ToList();

            List<int> leadList = objTicketResponse?.Data?.Select(item => item.LeadID).ToList();

            if (leadList != null && leadList.Count > 0)
            {
                string inputLeadIds = string.Join(",", leadList);
                DataSet ds = SalesViewDLL.GetLeadStatusID(inputLeadIds);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (var item in objTicketResponse.Data)
                    {
                        long leadID = item.LeadID;
                        DataRow resultRow = ds.Tables[0].AsEnumerable()
                            .FirstOrDefault(row => row.Field<long>("LEADID") == leadID);

                        if (resultRow != null && !resultRow.IsNull("StatusID"))
                        {
                            item.LeadStatusID = Convert.ToInt32(resultRow.Field<byte>("StatusID"));
                            item.LeadStatus = Convert.ToString(resultRow.Field<string>("StatusName"));
                            DateTime statusupdatedon = Convert.ToDateTime(resultRow.Field<DateTime>("StatusUpdatedOn"));
                            item.LeadStatusUpdatedOn = statusupdatedon.ToString("dd/MM/yyyy");
                        }
                        else
                        {
                            item.LeadStatusID = 0;
                            item.LeadStatus = "";
                            item.LeadStatusUpdatedOn = "";
                        }
                    }
                }
            }
        }
        public string UpdateTicketRemarksMyAcc(TicketData oTicketModal)
        {
            string result = string.Empty;
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            try
            {
                string url = "TicketService".AppSettings() + "UpdateTicketRemarksMyAcc";

                string dataToPost = "{\"LeadID\":\"" + oTicketModal.LeadID + "\",\"TicketId\":\"" + oTicketModal.TicketId + "\",\"Comment\":\"" + oTicketModal.Comment + "\",\"CommentBy\":\"" + oTicketModal.CommentBy + "\",\"ActionType\":\"" + oTicketModal.ActionType + "\",\"TicketdetailId\":\"" + oTicketModal.TicketdetailId + "\",\"Source\":\"" + oTicketModal.Source + "\",\"SubSource\":\"" + oTicketModal.SubSource + "\",\"EmployeeID\":\"" + oTicketModal.EmployeeID + "\",\"EmployeeName\":\"" + oTicketModal.EmployeeName + "\"}";
                string TicketToken = "TicketToken".AppSettings();

                Dictionary<object, object> header = new Dictionary<object, object>();
                header.Add("Token", TicketToken);
                header.Add("App", "Matrix");

                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                var data = CommonAPICall.CallAPI(url, dataToPost, "POST", timeout, "application/json", header);
                //var data = CommonAPICall.PostAPICallWithResult(url, Convert.ToInt32("DialerAPITimeout".AppSettings()), dataToPost, "", header);
                if (CoreCommonMethods.IsValidString(data))
                {
                    result = data;
                }
                else
                {
                    result = "No Data found";

                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
                //LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oTicketModal.LeadID), Error, "UpdateTicketRemarksMyAcc", "TicketBLL", "UpdateTicketRemarksMyAcc", JsonConvert.SerializeObject(oTicketModal), result, dt, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oTicketModal.LeadID), Error, "UpdateTicketRemarksMyAcc", "TicketBLL", "UpdateTicketRemarksMyAcc", JsonConvert.SerializeObject(oTicketModal), result, dt, DateTime.Now);
            }
            return result;
        }

        public string ResendPolicyCopy(PolicyData oPolicyData)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;

            string url = "PbServiceAPI".AppSettings() + "communication/resendpolicycopy";
            string token = "PbServiceAPIToken".AppSettings();
            try
            {
                Dictionary<object, object> header = new Dictionary<object, object>();
                header.Add("requestingsystem", "Matrix");
                header.Add("token", token);

                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                string dataToPost = "{\"CommunicationType\":\"" + oPolicyData.CommunicationType + "\",\"LeadID\":\"" + oPolicyData.LeadID + "\",\"ProductId\":\"" + oPolicyData.ProductId + "\"}";

                var data = CommonAPICall.CallAPI(url, dataToPost, "POST", timeout, "application/json", header);
                //var data = CommonAPICall.PostAPICallWithResult(url, Convert.ToInt32("DialerAPITimeout".AppSettings()), dataToPost, "", header);
                if (CoreCommonMethods.IsValidString(data))
                {
                    result = data;
                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
                //LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oPolicyData.LeadID), Error, "ResendPolicyCopy", "TicketBLL", "ResendPolicyCopy", JsonConvert.SerializeObject(oPolicyData), result, dt, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oPolicyData.LeadID), Error, "ResendPolicyCopy", "TicketBLL", "ResendPolicyCopy", JsonConvert.SerializeObject(oPolicyData), result, dt, DateTime.Now);
            }
            return result;
        }

        public string GetTicketCount(string CustId)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;
            string Url = "TicketService".AppSettings() + "GetTicketCount/" + CustId;
            string TicketToken = "TicketToken".AppSettings();

            Dictionary<object, object> header = new Dictionary<object, object>();
            header.Add("Token", TicketToken);
            header.Add("App", "Matrix");

            try
            {
                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                var data = CommonAPICall.CallAPI(Url, "", "GET", timeout, "application/json", header);
                if (CoreCommonMethods.IsValidString(data))
                {
                    result = data;
                }
                else
                {
                    result = "No Data found";
                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(CustId), Error, "GetTicketCount", "MatrixCore", "TicketBLL", JsonConvert.SerializeObject(CustId), result, dt, DateTime.Now);
            }
            return result;
        }

        public bool IsCreateTicketAllowed(string LeadId)
        {
            bool response = false;
            DateTime dt = DateTime.Now;
            try
            {
                response = SalesViewDLL.IsCreateTicketAllowed(LeadId);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), ex.ToString(), "IsCreateTicketAllowed", "TicketBLL", "IsCreateTicketAllowed", JsonConvert.SerializeObject(LeadId), "", dt, DateTime.Now);
            }
            return response;

        }

        public IssueTypeByLeadIdResp GetIssueTypeByLeadId(long LeadId) {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            IssueTypeByLeadIdResp response = new();

            try
            {
                DataSet ds = TicketDLL.CheckLeadBookingExist(LeadId);

                if(ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0]["LeadId"] != DBNull.Value && Convert.ToInt64(ds.Tables[0].Rows[0]["LeadId"]) > 0)
                {
                    string Url = "TicketService".AppSettings() + "GetIssueTypeByLeadIdV1/" + LeadId + "/1";
                    string TicketToken = "TicketToken".AppSettings();

                    Dictionary<object, object> header = new()
                    {
                        { "Token", TicketToken },
                        { "App", "Matrix" }
                    };

                    int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                    var data = CommonAPICall.CallAPI(Url, "", "GET", timeout, "application/json", header);

                    if (CoreCommonMethods.IsValidString(data))
                    {
                        response.data = data;
                        response.status = true;
                    }
                    else
                    {
                        response.data = "No Data found";
                        response.status = true;
                    }
                } else {
                    response.data = "LeadId does not exist";
                    response.status = true;
                }
            }
            catch (Exception ex)
            {
                response.status = false;
                response.data = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), Error, "GetIssueTypeByLeadId", "MatrixCore", "TicketBLL", JsonConvert.SerializeObject(LeadId), response.data, dt, DateTime.Now);
            }
            return response;
        }

        public string GetMyTicketList(MyTicketReq obj)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;
            string Url = "TicketService".AppSettings() + "GetMyTicketList";
            string TicketToken = "TicketToken".AppSettings();

            Dictionary<object, object> header = new()
            {
                { "Token", TicketToken },
                { "App", "Matrix" }
            };

            try
            {
                string Json = JsonConvert.SerializeObject(obj);
                var data = CommonAPICall.CallAPI(Url, Json, "POST", Convert.ToInt32("BMSTicketTimeOut".AppSettings()), "application/json", header);

                if (CoreCommonMethods.IsValidString(data))
                {
                    dynamic res = JObject.Parse(data);

                    if (res.Data is JArray dataArray && dataArray.Count > 0)
                    {
                        List<string> bookingIds = new();
                        List<string> ticketIds = new();
                        long threeDaysAgoEpoch = DateTimeOffset.UtcNow.AddDays(-3).ToUnixTimeMilliseconds();

                        foreach (var item in dataArray)
                        { 
                            bookingIds.Add( item["BookingID"]?.ToString() ?? string.Empty );
                            ticketIds.Add( item["TicketDetailsID"]?.ToString() ?? string.Empty );
                        }

                        DataSet ds = TicketDLL.SalesCustTicketDetailsForEscalation(string.Join(",", bookingIds), string.Join(",", ticketIds));

                        if (ds != null && ds.Tables.Count > 0)
                        {
                            DataTable bookingDetail = ds.Tables[0];
                            DataTable escalatedTicketDetails = ds.Tables[1];

                            List<string> escalatedTicketIds = new();
                            foreach (DataRow row in escalatedTicketDetails.Rows)
                            {
                                escalatedTicketIds.Add(row["RefTicketID"].ToString());
                            }

                            Dictionary<string, Dictionary<string, long>> leadPremiumMap = new();
                            foreach (DataRow row in bookingDetail.Rows)
                            {
                                leadPremiumMap[row["LEADID"].ToString()] = new Dictionary<string, long>
                                {
                                    { "TotalPremium", row["TotalPremium"] != DBNull.Value ? Convert.ToInt64(row["TotalPremium"]) : 0 },
                                    { "ProductId", row["ProductID"] != DBNull.Value ? Convert.ToInt64(row["ProductID"]) : 0 },
                                    { "Country", row["Country"] != DBNull.Value ? Convert.ToInt64(row["Country"]) : 0 }
                                };
                            }

                            JArray updatedData = new();
                            short[] productIds50K = { 2, 7 };
                            short[] validProductIds = { 2, 7, 115, 117 };
                            string[] IndiaCodes = { "392", "91", "999", "INDIA", "0", "NULL", "" };

                            foreach (var item in dataArray)
                            {
                                string ticketDetailsId = item["TicketDetailsID"]?.ToString() ?? string.Empty;
                                string bookingId = item["BookingID"]?.ToString() ?? string.Empty;
                                long createdOn = Convert.ToInt64(item["CreatedOn"]);
                                string status = item["Status"]?.ToString().ToLower() ?? string.Empty;
                                long totalPremium = leadPremiumMap[bookingId]["TotalPremium"];
                                short productId = Convert.ToInt16(leadPremiumMap[bookingId]["ProductId"]);

                                short escalationAllowed = 0;
                                string escalationNotAllowedMsg = "";
                                long APE = 0;

                                if(!"closed, resolved".Contains(status)) 
                                {
                                    string Country = Convert.ToString(leadPremiumMap[bookingId]["Country"]);

                                    // Check if the ticket is already escalated
                                    if (escalatedTicketIds.Contains(ticketDetailsId))
                                    {
                                        escalationAllowed = 2;
                                    }
                                    // Check escalation conditions
                                    else if (leadPremiumMap.ContainsKey(bookingId))
                                    {
                                        if(validProductIds.Contains(productId))
                                        {
                                            switch(productId)
                                            {
                                                case 2:     APE = 50000;
                                                            escalationNotAllowedMsg = "Health tickets can be escalated only after 3 days of creation and if the APE is >= ₹50,000";
                                                            break;
                                                case 7:     APE = 50000;
                                                            escalationNotAllowedMsg = "Term tickets can be escalated only after 3 days of creation and if the APE is >= ₹50,000";
                                                            break;
                                                case 115:   if(IndiaCodes.Contains(Country)) {
                                                                APE = 100000;
                                                                escalationNotAllowedMsg = "Savings DOM tickets can be escalated only after 3 days of creation and if the APE is >= ₹1,00,000";
                                                            } else {
                                                                APE = 300000;
                                                                escalationNotAllowedMsg = "Savings NRI tickets can be escalated only after 3 days of creation and if the APE is >= ₹3,00,000";
                                                            }
                                                            break;
                                                case 117:   APE = 20000;
                                                            escalationNotAllowedMsg = "Motor tickets can be escalated only after 3 days of creation and if the APE is >= ₹20,000";
                                                            break;
                                            }
                                            if (
                                                createdOn <= threeDaysAgoEpoch && 
                                                totalPremium >= APE
                                            )
                                            {
                                                escalationAllowed = 1;
                                            } 
                                            else
                                            {
                                                escalationAllowed = 3;
                                            }
                                        }
                                    }
                                }

                                item["escalationAllowed"] = escalationAllowed;
                                item["escalationNotAllowedReason"] = escalationNotAllowedMsg;
                                item["APE"] = totalPremium;
                                item["ProductId"] = productId;
                                updatedData.Add(item);
                            }

                            res.Data = updatedData;
                            result = JsonConvert.SerializeObject(res);
                        }
                        else
                        {
                            result = data;
                        }
                    } 
                    else {
                        result = "No Data found";
                    }
                } 
                else {
                    result = "No Data found";
                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(obj.UserID), Error, "GetMyTicketList", "MatrixCore", "TicketBLL", JsonConvert.SerializeObject(obj.UserID), result, dt, DateTime.Now);
            }
            return result;
        }

        public string GetAllTicketList(long BookingId)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;
            string Url = "TicketService".AppSettings() + "GetAllTicketList/" + BookingId + "/6";
            string TicketToken = "TicketToken".AppSettings();

            Dictionary<object, object> header = new()
            {
                { "Token", TicketToken },
                { "App", "Matrix" }
            };

            try
            {
                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                var data = CommonAPICall.CallAPI(Url, "", "GET", timeout, "application/json", header);
                if (CoreCommonMethods.IsValidString(data))
                {
                    result = data;
                }
                else
                {
                    result = "No Data found";
                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(BookingId), Error, "GetAllTicketList", "MatrixCore", "TicketBLL", BookingId.ToString(), result, dt, DateTime.Now);
            }
            return result;
        }

        public object CreateTicketBMS(object request)
        {
            DateTime requestTime = DateTime.Now;
            string response = null;
            dynamic payload = null;
            try
            {
                payload = JsonConvert.DeserializeObject<dynamic>(request.ToString());
                
                if (payload != null && payload.LeadId != null)
                {
                    long leadId = Convert.ToInt64(payload.LeadID);
                    DataSet ds = TicketDLL.CheckLeadBookingExist(leadId);
                    
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        string EmailID = ds.Tables[0].Rows[0]["EmailID"] != DBNull.Value ? ds.Tables[0].Rows[0]["EmailID"].ToString() : string.Empty;
                        payload.EmailID = EmailID;
                    }
                }
                
                var header = new Dictionary<object, object>
                {
                    { "Token", "TicketToken".AppSettings() },
                    { "App", "Matrix" }
                };
                response = CommonAPICall.CallAPI("TicketService".AppSettings() + "CreateTicketForCare",
                                        JsonConvert.SerializeObject(payload),
                                        "POST",
                                        Convert.ToInt32("BMSTicketTimeOut".AppSettings()),
                                        "application/json", header);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.Message, "CreateTicketBMS", "MatrixCore", "TicketBLL", JsonConvert.SerializeObject(payload), response, requestTime, DateTime.Now);
            }
            return response;
        }

        public string GetTicketURL(object request)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;
            dynamic payload = null;

            try
            {
                var header = new Dictionary<object, object>
                {
                    { "Token", "TicketToken".AppSettings() },
                    { "App", "Matrix" }
                };

                payload = JsonConvert.DeserializeObject<dynamic>(request.ToString());
                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                var data = CommonAPICall.CallAPI("TicketService".AppSettings() + "GetTicketURL", 
                                        JsonConvert.SerializeObject(payload), 
                                        "POST", 
                                        Convert.ToInt32("BMSTicketTimeOut".AppSettings()), "application/json", 
                                        header);
                if (CoreCommonMethods.IsValidString(data))
                {
                    result = data;
                }
                else
                {
                    result = "No Data found";
                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, Error, "GetTicketURL", "MatrixCore", "TicketBLL", JsonConvert.SerializeObject(payload), result, dt, DateTime.Now);
            }
            return result;
        }

        public string GetTicketDetailsByTicketId(object request)
        {
            string Error = string.Empty;
            DateTime dt = DateTime.Now;
            string result = string.Empty;
            dynamic payload = null;

            try
            {
                var header = new Dictionary<object, object>
                {
                    { "Token", "TicketToken".AppSettings() },
                    { "App", "Matrix" }
                };

                payload = JsonConvert.DeserializeObject<dynamic>(request.ToString());
                int timeout = Convert.ToInt32("BMSTicketTimeOut".AppSettings());
                var data = CommonAPICall.CallAPI("TicketServiceExternal".AppSettings() + "GetTicketDetails", 
                                        JsonConvert.SerializeObject(payload), 
                                        "POST", 
                                        Convert.ToInt32("BMSTicketTimeOut".AppSettings()), "application/json", 
                                        header);
                if (CoreCommonMethods.IsValidString(data))
                {
                    result = data;
                }
                else
                {
                    result = "No Data found";
                }
            }
            catch (Exception ex)
            {
                result = "No Data found";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, Error, "GetTicketDetailsByTicketId", "MatrixCore", "TicketBLL", JsonConvert.SerializeObject(payload), result, dt, DateTime.Now);
            }
            return result;
        }
    }
}