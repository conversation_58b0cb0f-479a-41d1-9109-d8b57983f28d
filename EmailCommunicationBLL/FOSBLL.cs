using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Runtime.Caching;
using System.Text;
using System.Threading.Tasks;
using AppointmentFactory;
using DataAccessLayer;
using DataAccessLibrary;
using Helper;
using Microsoft.AspNetCore.Mvc;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using Redis;

using System.Dynamic;


namespace EmailCommunicationBLL
{
    public class FOSBLL : IFOSBLL
    {

        public bool SaveLeadsSubStatus(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            string Error = String.Empty;
            string AppointmentStartStatus = "AppointmentStartStatus".AppSettings();
            string AppointmentEndStatus = "AppointmentEndStatus".AppSettings();
            StringBuilder sb = new StringBuilder();
            sb.Append("enter in API " + "\r\n");
            bool response = false;
            try
            {
                response = FOSDLL.SaveLeadsSubStatus(oLeadsSubStatusModel);
                sb.Append("response: " + response + "\r\n");
                if (response)
                {
                    SetAppointmentSubStatus(oLeadsSubStatusModel);

                    FOSDLL.SetCustomerComment(oLeadsSubStatusModel, 47, "Status updated by FOSAgent", null);

                    sb.Append("After Save comments " + "\r\n");

                    if (oLeadsSubStatusModel.Latitude != 0 && oLeadsSubStatusModel.Longitude != 0 && AppointmentStartStatus.Contains("," + oLeadsSubStatusModel.SubStatusId + ","))
                    {
                        oLeadsSubStatusModel.Type = 1;
                        FOSDLL.SaveFOSAppLatAndLongitude(oLeadsSubStatusModel);
                        sb.Append("After App start save Lat and Lon " + "\r\n");
                    }
                    else if (oLeadsSubStatusModel.Latitude != 0 && oLeadsSubStatusModel.Longitude != 0 && AppointmentEndStatus.Contains("," + oLeadsSubStatusModel.SubStatusId + ","))
                    {
                        oLeadsSubStatusModel.Type = 2;
                        FOSDLL.SaveFOSAppLatAndLongitude(oLeadsSubStatusModel);
                        sb.Append("After App end save Lat and Lon " + "\r\n");
                    }

                }
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
                //Console.WriteLine("Exception in SaveLeadsSubStatus." + ex.ToString());
                response = false;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(oLeadsSubStatusModel.LeadID.ToString(), Convert.ToInt64(oLeadsSubStatusModel.LeadID), Error, "SaveLeadsSubStatus", "FOSBLL", "SaveLeadsSubStatus", JsonConvert.SerializeObject(oLeadsSubStatusModel), sb.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;

        }
        public List<AppointmentsDataModel> GetAgentAppointments(string UserID)
        {
            string Error = string.Empty;
            List<string> SourceArr = "CustomerSourceArr".AppSettings().Split(',').ToList();
            List<AppointmentsDataModel> lstAppointmentsDataModel = new List<AppointmentsDataModel>();
            List<AppointmentsDataModel> lstAppointmentsDataModelV2 = new List<AppointmentsDataModel>();
            try
            {
                //BMS List
                //List<PIVCData> oPIVCModal = CallPIVC(Convert.ToInt64(UserID));
                List<PIVCData> oPIVCModal = null;

                //FOS List
                DataSet oDataSet = FOSDLL.GetAgentAppointments(Convert.ToInt64(UserID));
                 if (oDataSet != null && oDataSet.Tables.Count > 0)
                {

                    lstAppointmentsDataModel = (from dr in oDataSet.Tables[0].AsEnumerable()
                                                select new AppointmentsDataModel
                                                {
                                                    LeadID = dr["LeadID"] != null && dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : 0,
                                                    CustomerID = dr["CustomerID"] != null && dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["CustomerID"]) : 0,
                                                    AppointmentType = dr["AppointmentTypeId"] == DBNull.Value ? 0 : dr.Field<Int32>("AppointmentTypeId"),
                                                    AppointmentDateTime = dr["AppointmentDateTime"] != null && dr["AppointmentDateTime"] != DBNull.Value ? Convert.ToDateTime(dr["AppointmentDateTime"]) : DateTime.MinValue,
                                                    CreatedOn = dr["CreatedOn"] != null && dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : DateTime.MinValue,
                                                    CustomerName = dr["Name"] != null && dr["Name"] != DBNull.Value ? Convert.ToString(dr["Name"]) : string.Empty,
                                                    Status = dr["StatusName"] != null && dr["StatusName"] != DBNull.Value ? Convert.ToString(dr["StatusName"]) : string.Empty,
                                                    SubStatus = dr["SubStatusName"] != null && dr["SubStatusName"] != DBNull.Value ? Convert.ToString(dr["SubStatusName"]) : string.Empty,
                                                    AppointmentId = dr["AppointmentId"] != null && dr["AppointmentId"] != DBNull.Value ? Convert.ToInt64(dr["AppointmentId"]) : 0,
                                                    StatusId = dr["StatusId"] != null && dr["StatusId"] != DBNull.Value ? Convert.ToInt32(dr["StatusId"]) : 0,
                                                    SubStatusID = dr["SubStatusID"] != null && dr["SubStatusID"] != DBNull.Value ? Convert.ToInt32(dr["SubStatusID"]) : 0,
                                                    AppointmentVal = dr["AppointmentTypeVal"] != null && dr["AppointmentTypeVal"] != DBNull.Value ? Convert.ToString(dr["AppointmentTypeVal"]) : string.Empty,
                                                    location = new PlaceLatLongModel()
                                                    {
                                                        Lat = dr["lat"] != null && dr["lat"] != DBNull.Value ? Convert.ToDecimal(dr["lat"]) : 0,
                                                        Long = dr["long"] != null && dr["long"] != DBNull.Value ? Convert.ToDecimal(dr["long"]) : 0
                                                    },
                                                    ReferralID = dr["ReferralID"] != null && dr["ReferralID"] != DBNull.Value ? Convert.ToInt64(dr["ReferralID"]) : 0,
                                                    LeadSource = dr["LeadSource"] != null && dr["LeadSource"] != DBNull.Value ? Convert.ToString(dr["LeadSource"]) : string.Empty,
                                                    ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                                                    ActionDoneBy = dr["Source"] != null && dr["Source"] != DBNull.Value && SourceArr.Contains(dr["Source"].ToString().ToLower()) ? 1 : 0,
                                                    IsCustReschedule = dr["IsCustReschedule"] != null && dr["IsCustReschedule"] != DBNull.Value ? Convert.ToBoolean(dr["IsCustReschedule"]) : false,
                                                    AppCreatedSource = dr["AppCreatedSource"] != null && dr["AppCreatedSource"] != DBNull.Value ? Convert.ToString(dr["AppCreatedSource"]) : string.Empty,
                                                    IsRenewal = dr["IsRenewal"] != null && dr["IsRenewal"] != DBNull.Value ? Convert.ToInt16(dr["IsRenewal"]) : Convert.ToInt16(0),
                                                    //Gender = dr["Gender"] != null && dr["Gender"] != DBNull.Value ? Convert.ToInt16(dr["Gender"]) : Convert.ToInt16(0)
                                                    canCreateMotorAppt = dr["canCreateMotorAppt"] != null && dr["canCreateMotorAppt"] != DBNull.Value ? Convert.ToBoolean(dr["canCreateMotorAppt"]) : true,
                                                    RescheduleCount = dr["RescheduleCount"] != null && dr["RescheduleCount"] != DBNull.Value ? Convert.ToInt16(dr["RescheduleCount"]) : Convert.ToInt16(0),

                                                    Portability = dr["IsPortable"] != null && dr["IsPortable"] != DBNull.Value ? Convert.ToBoolean(dr["IsPortable"]) : null,
                                                    PolicyExpiryDate = dr["PolicyExpiryDate"] != null && dr["PolicyExpiryDate"] != DBNull.Value ? Convert.ToDateTime(dr["PolicyExpiryDate"]) : DateTime.MinValue,
                                                    AffluencePayU= dr["AffluencePayU"] != null && dr["AffluencePayU"] != DBNull.Value ? Convert.ToBoolean(dr["AffluencePayU"]) : false,
                                                }).ToList();



                }
                lstAppointmentsDataModelV2 = lstAppointmentsDataModel;
                if (oPIVCModal != null && oPIVCModal.Count > 0)
                {
                    foreach (var item in oPIVCModal)
                    {
                        if (lstAppointmentsDataModelV2.Where(x => x.LeadID == item.ParentLeadId) != null)
                        {
                            var _list = lstAppointmentsDataModelV2.Where(x => x.LeadID == item.ParentLeadId).ToList();

                            if (_list != null && _list.Count() > 0)
                            {
                                var obj = _list.OrderByDescending(x => x.CreatedOn).First();

                                if (obj != null && obj.LeadID == item.ParentLeadId && obj.BookingId == 0)
                                {
                                    obj.BookingId = item.LeadId;
                                    obj.PivcLink = item.PivcLink;
                                    obj.PivcStatus = item.PivcStatus;
                                    obj.VerificationStatus = item.VerificationStatus;
                                    obj.ApplicationNo = item.ApplicationNo;
                                }
                                else
                                {
                                    var oResponse = lstAppointmentsDataModelV2.Where(x => x.LeadID == item.ParentLeadId).ToList().First();

                                    AppointmentsDataModel oAppointmentsDataModel = new AppointmentsDataModel
                                    {
                                        LeadID = item.ParentLeadId,
                                        BookingId = item.LeadId,
                                        PivcStatus = item.PivcStatus,
                                        VerificationStatus = item.VerificationStatus,
                                        ApplicationNo = item.ApplicationNo,
                                        CustomerID = obj.CustomerID,
                                        PlanList = null,
                                        CustomerName = obj.CustomerName,
                                        Status = obj.Status,
                                        SubStatus = obj.SubStatus,
                                        AppointmentId = obj.AppointmentId,
                                        AppointmentDateTime = obj.AppointmentDateTime,
                                        CreatedOn = obj.CreatedOn,
                                        StatusId = obj.StatusId,
                                        SubStatusID = obj.SubStatusID,
                                        UserId = obj.UserId,
                                        CustomerId = obj.CustomerId,
                                        ParentId = obj.ParentId,
                                        AppointmentType = obj.AppointmentType,
                                        OfflineCityId = obj.OfflineCityId,

                                    };
                                    lstAppointmentsDataModelV2.Add(oAppointmentsDataModel);

                                }

                            }
                        }

                    }


                }

                return lstAppointmentsDataModelV2;
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(UserID.ToString(), Convert.ToInt64(UserID), Error, "GetAgentAppointments", "FOSBLL", "GetAgentAppointments", JsonConvert.SerializeObject(lstAppointmentsDataModel), JsonConvert.SerializeObject(lstAppointmentsDataModel), DateTime.Now, DateTime.Now);
                return lstAppointmentsDataModel;
            }


        }

        public CustInfoModel GetFOSCustInfo(string LeadId)
        {
            string error = string.Empty;
            CustInfoModel oCustInfoModel = new CustInfoModel();
            try
            {
                DataSet oDataSet = FOSDLL.GetFOSCustInfo(Convert.ToInt64(LeadId));
                if (oDataSet != null && oDataSet.Tables.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {

                        oCustInfoModel.CustomerID = row["CustomerID"] == DBNull.Value ? 0 : Convert.ToInt64(row["CustomerID"]);
                        oCustInfoModel.CustomerName = row["Name"] == DBNull.Value ? "" : Convert.ToString(row["Name"]);
                        oCustInfoModel.MobileNo = row["MobileNo"] == DBNull.Value ? "" : Crypto.MaskMobileNo(Convert.ToString(row["MobileNo"]));
                        oCustInfoModel.Email = row["Email"] == DBNull.Value ? "" : Convert.ToString(row["Email"]);

                        oCustInfoModel.Address = row["Address"] == DBNull.Value ? "" : Convert.ToString(row["Address"]);
                        oCustInfoModel.Address1 = row["Address1"] == DBNull.Value ? "" : Convert.ToString(row["Address1"]);
                        oCustInfoModel.Pincode = row["Pincode"] == DBNull.Value ? "" : Convert.ToString(row["Pincode"]);
                        oCustInfoModel.Landmark = row["Landmark"] == DBNull.Value ? "" : Convert.ToString(row["Landmark"]);
                        oCustInfoModel.City = row["City"] == DBNull.Value ? "" : Convert.ToString(row["City"]);
                        oCustInfoModel.State = row["State"] == DBNull.Value ? "" : Convert.ToString(row["State"]);


                    }

                }
                return oCustInfoModel;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                //Console.WriteLine("Exception in GetFOSCustInfo." + ex.ToString());
                return oCustInfoModel;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), Convert.ToInt64(LeadId), error, "GetFOSCustInfo", "FOSBLL", "GetFOSCustInfo", JsonConvert.SerializeObject(LeadId), JsonConvert.SerializeObject(oCustInfoModel), DateTime.Now, DateTime.Now);
            }


        }

        public List<ActvityHistoryModel> GetFOSActivityHistory(string LeadId, string AppointmentId)
        {
            string Error = string.Empty;
            List<ActvityHistoryModel> lstActvityHistoryModel = new List<ActvityHistoryModel>();
            try
            {
                DataSet oDataSet = FOSDLL.GetFOSActivityHistory(Convert.ToInt64(LeadId), Convert.ToInt64(AppointmentId));
                if (oDataSet != null && oDataSet.Tables.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        ActvityHistoryModel oActvityHistoryModel = new ActvityHistoryModel();
                        oActvityHistoryModel.LeadId = row["LeadId"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadId"]);
                        oActvityHistoryModel.SubStatusName = row["SubStatusName"] == DBNull.Value ? "" : Convert.ToString(row["SubStatusName"]);
                        oActvityHistoryModel.AppointmentDateTime = row["AppointmentDateTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(row["AppointmentDateTime"]);
                        lstActvityHistoryModel.Add(oActvityHistoryModel);
                    }

                }
                return lstActvityHistoryModel;
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), Convert.ToInt64(LeadId), Error, "GetFOSActivityHistory", "FOSBLL", "GetFOSActivityHistory", JsonConvert.SerializeObject(LeadId), JsonConvert.SerializeObject(lstActvityHistoryModel), DateTime.Now, DateTime.Now);
                return lstActvityHistoryModel;
            }


        }
        public static bool SetFOSComment(AppointmentsDataModel oAppointmentDataModel)
        {
            bool response = false;
            try
            {

                response = FOSDLL.SetFOSComment(oAppointmentDataModel);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oAppointmentDataModel.ParentId), ex.ToString(), "SetFOSComment", "FOSBLL", "SetFOSComment", JsonConvert.SerializeObject(oAppointmentDataModel.ParentId), response.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;

        }
        public APIResponse MarkAttendance(LogInDTO oLogInDTO)
        {
            APIResponse response = new APIResponse();
            StringBuilder sb = new StringBuilder();
            bool result = false;

            response.statusCode = 502; //bad request
            response.Result = false;
            response.Msg = "Data Not Saved";

            try
            {
                oLogInDTO.Process = "FOSAPP";
                if (oLogInDTO.IsLogout)
                {

                    result = FOSDLL.Logout(oLogInDTO);
                    if (result)
                    {
                        UpdateAgentStatus(oLogInDTO, "LOGOUT");
                    }
                }
                else
                {
                    result = FOSDLL.MarkAttendance(oLogInDTO);

                    if (result)
                    {

                        UpdateAgentStatus(oLogInDTO, "IDLE");
                    }
                }
                if (result)
                {
                    DataSet ds = FOSDLL.GetAgentProfileData(oLogInDTO.UserId);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        bool IsActive = ds.Tables[0].Rows[0]["IsActive"] == DBNull.Value ? false : Convert.ToBoolean(ds.Tables[0].Rows[0]["IsActive"]);
                        if (IsActive)
                        {
                            response.URL = ds.Tables[0].Rows[0]["URL"] == DBNull.Value ? string.Empty : Convert.ToString(ds.Tables[0].Rows[0]["URL"]);
                            DateTime SelfieCreatedOn = ds.Tables[0].Rows[0]["CreatedOn"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(ds.Tables[0].Rows[0]["CreatedOn"]);

                            if (SelfieCreatedOn != DateTime.MinValue)
                                response.SelfieExpiryDate = SelfieCreatedOn.AddDays(Convert.ToInt16("UserSelfieExpiryDays".AppSettings()));
                        }
                    }

                    response.statusCode = 200;
                    response.Result = true;
                    response.Msg = "Data Saved Successfully";
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(oLogInDTO.UserId.ToString(), Convert.ToInt64(oLogInDTO.UserId), ex.ToString(), "MarkAttendance", "FOSBLL", "MarkAttendance", JsonConvert.SerializeObject(oLogInDTO), JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }
            return response;

        }
        public bool IsMarkAttendance(long userId)
        {
            StringBuilder sb = new StringBuilder();
            bool response = false;
            try
            {
                response = FOSDLL.IsMarkAttendance(userId);
                FOSDLL.SaveProgressiveTracking(userId, Convert.ToInt16(EnumAppEvents.KillApp));
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(userId), ex.ToString(), "IsMarkAttendance", "FOSBLL", "MatrixCore", "", "", DateTime.Now, DateTime.Now);
            }

            return response;

        }
        public bool SaveComments(CommentModal oCommentModal)
        {
            bool response = false;
            try
            {
                response = FOSDLL.SaveComments(oCommentModal);
            }
            catch (Exception ex)
            {

                response = false;
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oCommentModal.LeadID), ex.ToString(), "SaveComments", "FOSBLL", "SaveComments", JsonConvert.SerializeObject(oCommentModal), "", DateTime.Now, DateTime.Now);
                //Console.WriteLine("Exception in SaveComments." + ex.ToString());
            }

            return response;

        }

        public static bool SetAppointmentSubStatus(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            bool response = false;

            try
            {
                string ReasonSubsStatusIds = "ReasonSubsStatusIds".AppSettings();

                List<Int32> AppointmentCompletedStatus = "AppointmentCompletedStatus".AppSettings().Split(',').Select(Int32.Parse).ToList();
                bool IsAppDone = AppointmentCompletedStatus.Contains(oLeadsSubStatusModel.SubStatusId);
                oLeadsSubStatusModel.ParentID = oLeadsSubStatusModel.ParentID == 0 ? oLeadsSubStatusModel.LeadID : oLeadsSubStatusModel.ParentID;


                oLeadsSubStatusModel = getAppointmentData(oLeadsSubStatusModel);

                PriorityModel AppPriorityModel = getAppPriorityModel(oLeadsSubStatusModel);
                if (AppPriorityModel != null && AppPriorityModel.Appointment != null && AppPriorityModel.Appointment.AppointmentId > 0)
                {
                    var appointmentprovider = AppointmentProvider.getAppointmentProvide(Convert.ToInt32(oLeadsSubStatusModel.SubStatusId), oLeadsSubStatusModel.Source);
                    appointmentprovider.UpdateAppSubstausInDB(AppPriorityModel, IsAppDone, oLeadsSubStatusModel.Source);

                    UpdateLeadPriorityModel(AppPriorityModel); //--- Update in  Mongo 

                    //Save cancel reason 
                    if (oLeadsSubStatusModel.CancelReasonId > 0 && ReasonSubsStatusIds.Contains("," + oLeadsSubStatusModel.SubStatusId + ","))
                        SaveCancallationReason(oLeadsSubStatusModel);


                    if(oLeadsSubStatusModel.SendRescheduleTrigger) 
                    {
                        sendcommunicationResponse osendcommunicationResponse = CoreCommonMethods.ISLeadDividedby10(oLeadsSubStatusModel.LeadID, oLeadsSubStatusModel.ProductId, Convert.ToInt64("leadIdDivisior".AppSettings())) ? SendCommunicationToCustNew(oLeadsSubStatusModel) : SendCommunicationToCust(oLeadsSubStatusModel);
                        if (osendcommunicationResponse != null)
                            appointmentprovider.SendCommunication(osendcommunicationResponse);
                    }


                    //------------ Save AppointmentComplete comment in AppHistory----------//
                    if (oLeadsSubStatusModel.EventId == (short)EnumAppEvents.FOS_Appointment_Complete) {
                        FOSDLL.SaveAppointmentHistory(oLeadsSubStatusModel);
                    }

                    //------------Save in apphistory----------//
                    oLeadsSubStatusModel.EventId = Convert.ToInt16(EnumAppEvents.Status_Update);
                    List<string> SourceArr = "SourceArr".AppSettings().Split(',').ToList();
                    List<Int32> RestrictStatusForLogs = "RestrictStatusForLogs".AppSettings().Split(',').Select(Int32.Parse).ToList();
                    //In case of cust save confirm entry 
                    if (CoreCommonMethods.IsValidString(oLeadsSubStatusModel.Source) && SourceArr.Contains(oLeadsSubStatusModel.Source.ToLower()))
                        oLeadsSubStatusModel.SubStatusId = AppPriorityModel.Appointment.StatusID;

                    if (!RestrictStatusForLogs.Contains(oLeadsSubStatusModel.SubStatusId))
                    {
                        oLeadsSubStatusModel.Comments = ((EnumAppSusbtatus)oLeadsSubStatusModel.SubStatusId == EnumAppSusbtatus.Cancelled || (EnumAppSusbtatus)oLeadsSubStatusModel.SubStatusId == EnumAppSusbtatus.ReScheduled)
                            ? oLeadsSubStatusModel.ReasonText
                             : "-";

                        FOSDLL.SaveAppointmentHistory(oLeadsSubStatusModel);
                    }

                    response = true;

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oLeadsSubStatusModel.LeadID), ex.ToString(), "SetAppointmentSubStatus", "FOSBLL", "SetAppointmentSubStatus", JsonConvert.SerializeObject(oLeadsSubStatusModel), "", DateTime.Now, DateTime.Now);
            }
            return response;

        }

        public bool UpdateAppointmentStatus(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            bool response = false;
            try
            {
                //Save data in leadstatus
                if (oLeadsSubStatusModel.Source.ToLower() == "customerwhatsapp" || oLeadsSubStatusModel.Source.ToLower() == "customersms" || oLeadsSubStatusModel.Source.ToLower() == "whatsappnative")
                {
                    List<Int32> AppMarkedStatusIds = "AppMarkedStatusIds".AppSettings().Split(',').Select(Int32.Parse).ToList();

                    if (AppMarkedStatusIds.Contains(oLeadsSubStatusModel.StatusId))
                        FOSDLL.SaveLeadsSubStatus(oLeadsSubStatusModel); //Save data in leadstatus
                }

                response = SetAppointmentSubStatus(oLeadsSubStatusModel); //Save Status in mongo and db in appsubstatus
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oLeadsSubStatusModel.LeadID), ex.ToString(), "MatrixCore", "FOSBLL", "UpdateAppointmentStatus", JsonConvert.SerializeObject(oLeadsSubStatusModel), "", DateTime.Now, DateTime.Now);
            }

            return response;

        }

        public AppointmentsDataModel GetAppointmentDetails(string CustomerId, string ParentId, string AppointmentId)
        {


            AppointmentsDataModel objAppointmentDataModel = new AppointmentsDataModel();
            try
            {

                DataSet oDataSet = FOSDLL.GetAppointmentDetails(CustomerId, ParentId, AppointmentId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {

                        objAppointmentDataModel.AppointmentId = row["AppointmentId"] == DBNull.Value ? 0 : Convert.ToInt32(row["AppointmentId"]);
                        objAppointmentDataModel.AppointmentDateTime = row["AppointmentDateTime"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["AppointmentDateTime"]);
                        objAppointmentDataModel.Address = row["Address"] == DBNull.Value ? "" : Convert.ToString(row["Address"]);
                        objAppointmentDataModel.Pincode = row["Pincode"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["Pincode"]);
                        objAppointmentDataModel.CityId = row["CityId"] == DBNull.Value ? 0 : Convert.ToInt32(row["CityId"]);
                        objAppointmentDataModel.AppointmentType = row["AppointmentType"] == DBNull.Value ? 0 : Convert.ToInt32(row["AppointmentType"]);
                        objAppointmentDataModel.OfflineCityId = row["OfflineCityId"] == DBNull.Value ? 0 : Convert.ToInt32(row["OfflineCityId"]);
                        objAppointmentDataModel.Address1 = row["Address1"] == DBNull.Value ? "" : Convert.ToString(row["Address1"]);
                        objAppointmentDataModel.Landmark = row["Landmark"] == DBNull.Value ? "" : Convert.ToString(row["Landmark"]);
                        objAppointmentDataModel.Comments = row["Comments"] == DBNull.Value ? "" : Convert.ToString(row["Comments"]);
                        objAppointmentDataModel.ZoneId = row["ZoneId"] == DBNull.Value ? 0 : Convert.ToInt32(row["ZoneId"]);
                        objAppointmentDataModel.AssignmentId = row["AssignmentId"] == DBNull.Value ? 0 : Convert.ToInt32(row["AssignmentId"]);
                        objAppointmentDataModel.Reason = row["Reason"] == DBNull.Value ? "" : Convert.ToString(row["Reason"]);
                        objAppointmentDataModel.ReasonId = row["ReasonId"] == DBNull.Value ? 0 : Convert.ToInt32(row["ReasonId"]);
                        objAppointmentDataModel.AppointmentStart = row["AppointmentStart"] == DBNull.Value ? null : Convert.ToDateTime(row["AppointmentStart"]);
                        objAppointmentDataModel.CustomerId = Convert.ToInt64(CustomerId);
                        objAppointmentDataModel.ParentId = Convert.ToInt64(ParentId);
                        objAppointmentDataModel.subStatusId = row["subStatusId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["subStatusId"]);
                        objAppointmentDataModel.StatusId = row["StatusId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["StatusId"]);
                    }
                }

                //get data from AppointmentPlan

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[1].Rows.Count > 0)
                {
                    //List<Recommendation> lstRecommendation = new List<Recommendation>();
                    objAppointmentDataModel.PlanList = new List<Recommendation>();
                    foreach (DataRow row in oDataSet.Tables[1].Rows)
                    {
                        Recommendation objRecommendation = new Recommendation();
                        objRecommendation.supplierId = row["SupplierId"] == DBNull.Value ? "" : Convert.ToString(row["SupplierId"]);
                        objRecommendation.planId = row["PlanId"] == DBNull.Value ? "" : Convert.ToString(row["PlanId"]);
                        objRecommendation.planName = row["planName"] == DBNull.Value ? "" : Convert.ToString(row["planName"]);
                        objRecommendation.supplierName = row["supplierName"] == DBNull.Value ? "" : Convert.ToString(row["supplierName"]);
                        objAppointmentDataModel.PlanList.Add(objRecommendation);
                    }

                    // objAppointmentDataModel.PlanList.Add(List<lstRecommendation>);
                }

                return objAppointmentDataModel;
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetAppointmentData in FOSBLL." + ex);
                return null;
            }

        }

        public string GetAppToken(string userId)
        {
            string response = string.Empty;
            try
            {
                string Key = $"{RedisCollection.AppToken()}:{userId.Trim()}";
                //string AppToken = userId + DateTime.Now.ToString("yyyyMMddHHmmss");
                //string AppToken = DateTime.Now.ToString("yyyyMMddHHmmss");
                Guid obj = Guid.NewGuid();
                string AppToken = obj.ToString() + "-" + DateTime.Now.ToString("HHmmss");
                RedisHelper.SetRedisData(Key, AppToken, new TimeSpan(12, 0, 0));
                response = AppToken;
            }
            catch (Exception ex)
            {
                //LoggingHelper.LoggingHelper.AddloginQueue("", userId, ex.ToString(), "GetAppToken", "MatrixCore", "GetAppToken", userId.ToString(), JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
                // Console.WriteLine("Exception in GetAppToken." + ex.ToString());
            }

            return response;

        }
        public Data ldapValidationV2(EmpData oEmpData)
        {
            bool response = false;
            Data obj = null;
            DateTime dt = DateTime.Now;
            DateTime PasswordExpDate = DateTime.MinValue;
            Int16 ExpiryDaysLeft = 0;
            try
            {
                DataSet oDataSet = FOSDLL.GetUserDetails(oEmpData.empId);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    bool IsldapUser = oDataSet.Tables[0].Rows[0]["ISLDapEnable"] == DBNull.Value ? false : Convert.ToBoolean(oDataSet.Tables[0].Rows[0]["ISLDapEnable"]);

                    obj = new Data()
                    {
                        UserId = oDataSet.Tables[0].Rows[0]["userid"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["userid"])
                    };

                    obj.data = new data()
                    {
                        givenName = oDataSet.Tables[0].Rows[0]["domainusername"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["domainusername"]),
                        displayName = oDataSet.Tables[0].Rows[0]["UserName"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["UserName"]),
                        description = oDataSet.Tables[0].Rows[0]["description"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["description"]),
                        mail = oDataSet.Tables[0].Rows[0]["email"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["email"]),
                        ProductId = oDataSet.Tables[0].Rows[0]["ProductId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductId"]),
                        ProcessId = oDataSet.Tables[0].Rows[0]["ProcessId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProcessId"]),
                        VirtualNo = oDataSet.Tables[0].Rows[0]["VirtualNo"] != null && oDataSet.Tables[0].Rows[0]["VirtualNo"] != DBNull.Value ? Convert.ToString(oDataSet.Tables[0].Rows[0]["VirtualNo"]) : "",
                        IsRM = oDataSet.Tables[0].Rows[0]["IsRM"] == DBNull.Value ? false : Convert.ToBoolean(oDataSet.Tables[0].Rows[0]["IsRM"]),
                        DIDNo = oDataSet.Tables[0].Rows[0]["DIDNo"] == DBNull.Value ? "" : Convert.ToString(oDataSet.Tables[0].Rows[0]["DIDNo"]),

                    };
                    PasswordExpDate = oDataSet.Tables[0].Rows[0]["PasswordExpDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(oDataSet.Tables[0].Rows[0]["PasswordExpDate"]);
                    ExpiryDaysLeft = GetExpiryDaysLeft(PasswordExpDate, ExpiryDaysLeft);
                    obj.PassExpDaysLeft = ExpiryDaysLeft;
                    // Isislap is off chk from db
                    if (!IsldapUser)
                    {
                        DataSet ds = FOSDLL.GetValidUser(oEmpData.empId, oEmpData.password);
                        if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                            response = ds.Tables[0].Rows[0]["IsValidatedUser"] == DBNull.Value ? false : Convert.ToBoolean(ds.Tables[0].Rows[0]["IsValidatedUser"]);

                    }
                    else
                        response = IsldapValidate(obj.data.givenName, oEmpData.password, oEmpData.empId);


                    if (response && obj.UserId > 0)
                    {
                        LogInDTO oLogInDTO = new() { EmployeeId = oEmpData.empId };
                        UpdateAgentStatus(oLogInDTO, "IDLE");
                        obj.statusCode = 200;
                        obj.Result = true;
                        obj.AgentType = "Sales";

                        DataSet ds = FOSDLL.GetAgentType(obj.UserId);

                        if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                        {
                            string agentType = Convert.ToString(ds.Tables[0].Rows[0]["GroupProcess"]);
                            if (CoreCommonMethods.IsValidString(agentType))
                                obj.AgentType = agentType.Trim();
                        }

                        DataSet oDataSet1 = FOSDLL.GetGroupsByUserId(obj.UserId);
                        if (oDataSet1 != null && oDataSet1.Tables.Count > 0 && oDataSet1.Tables[0].Rows.Count > 0)
                        {
                            obj.data.Groups = (from dr in oDataSet1.Tables[0].AsEnumerable()
                                               select new GroupDetails()
                                               {
                                                   GroupId = dr["GroupId"] != null && dr["GroupId"] != DBNull.Value ? Convert.ToInt32(dr["GroupId"]) : 0,
                                                   GroupName = dr["GroupName"] != null && dr["GroupName"] != DBNull.Value ? Convert.ToString(dr["GroupName"]) : string.Empty,
                                               }).ToList();

                            //Skip customer otp validation for specific sme groups
                            if (obj.data.ProductId == 131 && obj.data.Groups != null && obj.data.Groups.Count > 0)
                            {
                                string skipOtpGroupId = "SkipOtpGroupIds".AppSettings();
                                if (!string.IsNullOrEmpty(skipOtpGroupId))
                                {
                                    List<string> skipOtpGroupIds = skipOtpGroupId.Split(',').ToList();

                                    var matchingGroups = obj.data.Groups
                                    .Where(g => skipOtpGroupIds.Contains(g.GroupId.ToString()))
                                    .ToList();

                                    obj.data.IsSkipCustOtp = matchingGroups != null && matchingGroups.Count > 0;
                                }
                            }
                        }
                        obj.data.UserProfileData = GetUserProfileData(obj.UserId);

                    }
                    else
                    {
                        obj = new Data() { statusCode = 404, Result = false, PassExpDaysLeft = ExpiryDaysLeft };
                    }


                }
                else
                    obj = new Data() { statusCode = 401, Result = false };

            }
            catch (Exception ex)
            {
                obj = new Data() { statusCode = 401, Result = false };
                LoggingHelper.LoggingHelper.AddloginQueue(oEmpData.empId, 0, ex.ToString(), "ldapValidationV2", "MatrixCore", "ldapValidationV2", JsonConvert.SerializeObject(oEmpData), response.ToString(), dt, DateTime.Now);
            }
            return obj;
        }
        public bool IsldapValidate(string username, string password, string empId)
        {
            bool result = false;
            try
            {

                string dataToPost = "{\"username\":\"" + username + "\",\"password\":\"" + password + "\"}";
                var data = CommonAPICall.CallAPI("LdapAPI".AppSettings(), dataToPost, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);

                if (CoreCommonMethods.IsValidString(data))
                {
                    LdapModal res = JsonConvert.DeserializeObject<LdapModal>(data);
                    if (res != null && res.authenticated)
                    {
                        result = res.authenticated;
                        if (res.user != null && !string.IsNullOrEmpty(res.user.givenName))
                        {
                            result = res.authenticated;
                            DateTime PassLastSet = DateTime.FromFileTime(Convert.ToInt64(res.user.pwdLastSet));
                            bool IsUpdate = FOSDLL.UpdateUserExpPassword(empId, PassLastSet);
                            // sb.Append(" ,IsPasswordUpdateinDB " + IsUpdate);
                        }


                    }
                }
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in IsldapValidate." + ex.ToString());
            }
            return result;
        }



        public List<ReminderModal> GetReminderData(ReminderModal objReminderModal)
        {
            List<ReminderModal> lstReminderModal = new List<ReminderModal>();
            try
            {
                DataSet oDataSet = FOSDLL.GetReminderData(objReminderModal);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    //lstReminderModal = new List<ReminderModal>();
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        ReminderModal oReminderModal = new ReminderModal();
                        oReminderModal.LeadId = row["LeadId"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadId"]);
                        oReminderModal.FosReminderId = row["FosReminderId"] == DBNull.Value ? 0 : Convert.ToInt64(row["FosReminderId"]);
                        oReminderModal.ReminderTopic = row["ReminderTopic"] == DBNull.Value ? string.Empty : Convert.ToString(row["ReminderTopic"]);
                        oReminderModal.AppointmentId = row["AppointmentId"] == DBNull.Value ? 0 : Convert.ToInt64(row["AppointmentId"]);
                        oReminderModal.ReminderDateTime = row["ReminderDateTime"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["ReminderDateTime"]);
                        oReminderModal.CreatedOn = row["CreatedOn"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["CreatedOn"]);
                        oReminderModal.UpdatedOn = row["UpdatedOn"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["UpdatedOn"]);
                        oReminderModal.UserId = row["UserId"] == DBNull.Value ? 0 : Convert.ToInt64(row["UserId"]);
                        oReminderModal.Description = row["Message"] == DBNull.Value ? string.Empty : Convert.ToString(row["Message"]);
                        oReminderModal.IsMarked = row["IsMarked"] == DBNull.Value ? false : Convert.ToBoolean(row["IsMarked"]);
                        lstReminderModal.Add(oReminderModal);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", objReminderModal.UserId, ex.ToString(), "GetReminderData", "MatrixCore", "SetReminderData", objReminderModal.UserId.ToString(), JsonConvert.SerializeObject(lstReminderModal), DateTime.Now, DateTime.Now);
            }

            return lstReminderModal;

        }

        public bool SetReminderData(ReminderModal oReminderModal)
        {
            bool response = false;
            try
            {
                if (oReminderModal.AppointmentId > 0)
                {
                    response = FOSDLL.SetReminderData(oReminderModal);
                }
            }
            catch (Exception ex)
            {

                response = false;
                //  Console.WriteLine("Exception in SetReminderData in BLL." + ex.ToString());
                LoggingHelper.LoggingHelper.AddloginQueue("", oReminderModal.AppointmentId, ex.ToString(), "SetReminderData", "MatrixCore", "SetReminderData", oReminderModal.AppointmentId.ToString(), JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }

            return response;

        }


        public bool UpdateReminderData(ReminderUpdateModal oReminderModal)
        {
            bool response = false;
            try
            {
                if (oReminderModal.FosReminderId > 0)
                {
                    response = FOSDLL.UpdateReminderData(oReminderModal);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", oReminderModal.FosReminderId, ex.ToString(), "UpdateReminderData", "MatrixCore", "FOSBLL", oReminderModal.FosReminderId.ToString(), JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }

            return response;

        }

        public List<CustomerCommentModal> GetAgentCommments(string LeadId)
        {
            List<CustomerCommentModal> lstCustomerCommentModal = new List<CustomerCommentModal>();
            try
            {
                DataSet oDataSet = FOSDLL.GetAgentCommments(LeadId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {

                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        CustomerCommentModal oCustomerCommentModal = new CustomerCommentModal();
                        oCustomerCommentModal.LeadID = row["LeadID"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadID"]);
                        oCustomerCommentModal.Comments = row["Comments"] == DBNull.Value ? string.Empty : Convert.ToString(row["Comments"]);
                        oCustomerCommentModal.CreatedOn = row["CreatedOn"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["CreatedOn"]);
                        oCustomerCommentModal.CreatedBy = row["CreatedBy"] == DBNull.Value ? string.Empty : Convert.ToString(row["CreatedBy"]);
                        lstCustomerCommentModal.Add(oCustomerCommentModal);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), ex.ToString(), "GetAgentCommments", "MatrixCore", "GetAgentCommments", LeadId.ToString(), JsonConvert.SerializeObject(lstCustomerCommentModal), DateTime.Now, DateTime.Now);
            }

            return lstCustomerCommentModal;

        }
        public bool UpdateAppLocation(AppointmentLocation oAppLocation)
        {
            bool response = false;
            DateTime dt = DateTime.Now;
            string comments = string.Empty;
            try
            {
                response = FOSDLL.UpdateAppLocation(oAppLocation);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", oAppLocation.AppointmentId, ex.ToString(), "UpdateAppLocation", "MatrixCore", "FOSBLL", oAppLocation.AppointmentId.ToString(), JsonConvert.SerializeObject(response), dt, DateTime.Now);
            }
            return response;

        }

        public SaveInfo SetAppointmentDataV2(AppointmentsDataModel objAppointmentData, Int16 AgentProcessId)
        {

            SaveInfo oSaveInfo = new SaveInfo();
            oSaveInfo.StatusCode = 400;
            oSaveInfo.data = new AppResponse();
            DateTime dt = DateTime.Now;
            Int64 appointmentId = 0;
            Int16 ProductId = 0;
            StringBuilder sb = new();
            bool IsUpdatedatainMongo = false;
            sb.Append("Requested Payload LeadId: " + objAppointmentData.ParentId + "\r\n");
            sb.Append("  ,Requested Payload: " + JsonConvert.SerializeObject(objAppointmentData) + "\r\n");
            try
            {
                //string Message = AppointmentValidation(objAppointmentData.AppointmentDateTime, "setappointment", 0, objAppointmentData.Source);
                string Message = string.Empty;


                //if substatus id 0 thn consider booked
                objAppointmentData.subStatusId = objAppointmentData.subStatusId == 0 ? 2002 : objAppointmentData.subStatusId;
                // Calculating Parent ID and Product ID by LeadId
                DataSet oDataSet = FOSDLL.GetLeadBasicInfo(objAppointmentData.ParentId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    objAppointmentData.ProductId = (oDataSet.Tables[0].Rows[0]["ProductID"] != null && oDataSet.Tables[0].Rows[0]["ProductID"] != DBNull.Value) ? Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"]) : Convert.ToInt16(0);
                    objAppointmentData.ParentId = (oDataSet.Tables[0].Rows[0]["ParentID"] != null && oDataSet.Tables[0].Rows[0]["ParentID"] != DBNull.Value) ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentID"]) : objAppointmentData.ParentId;
                    objAppointmentData.CustomerId = objAppointmentData.CustomerId > 0 ? objAppointmentData.CustomerId : ((oDataSet.Tables[0].Rows[0]["CustomerID"] != null && oDataSet.Tables[0].Rows[0]["CustomerID"] != DBNull.Value) ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["CustomerID"]) : Convert.ToInt64(0));
                    objAppointmentData.Gender = objAppointmentData.Gender > 0 ? objAppointmentData.Gender : ((oDataSet.Tables[0].Rows[0]["Gender"] != null && oDataSet.Tables[0].Rows[0]["Gender"] != DBNull.Value) ? Convert.ToInt16(oDataSet.Tables[0].Rows[0]["Gender"]) : Convert.ToInt16(0));
                    objAppointmentData.VirtualProductId = (oDataSet.Tables[0].Rows[0]["ProductID"] != null && oDataSet.Tables[0].Rows[0]["ProductID"] != DBNull.Value) ? (Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"]) == 2 && Convert.ToString(oDataSet.Tables[0].Rows[0]["LeadSource"]) == "Renewal" ? Convert.ToInt16(147) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["ProductID"])) : Convert.ToInt16(0);
                }
                sb.Append("  ,Requested Payload with updated ParentId: " + JsonConvert.SerializeObject(objAppointmentData) + "\r\n");

                //---------------SaveData in apphistory-----------//
                LeadsSubStatusModel responseLeadsSubStatusModel = new LeadsSubStatusModel()
                {
                    ParentID = objAppointmentData.ParentId,
                    SubStatusId = objAppointmentData.subStatusId,
                    UserID = objAppointmentData.UserId,
                    Source = objAppointmentData.Source,
                    ProductId = objAppointmentData.ProductId
                };
                responseLeadsSubStatusModel = getAppointmentData(responseLeadsSubStatusModel);

                if (objAppointmentData != null && CoreCommonMethods.IsValidString(objAppointmentData.Source))
                    objAppointmentData = UpdateAppointmentAssignmentCity(objAppointmentData, oSaveInfo);

                if (!string.IsNullOrEmpty(oSaveInfo.Message))
                    return oSaveInfo;

                PriorityModel respPriorityModel = GetPriorityModelMongo(objAppointmentData.ParentId);
                oSaveInfo = ValidateSaveAppointmentData(respPriorityModel, responseLeadsSubStatusModel, objAppointmentData, AgentProcessId, oSaveInfo);

                if (!string.IsNullOrEmpty(oSaveInfo.Message))
                    return oSaveInfo;


                appointmentId = SalesViewDLL.SetAppointmentData(objAppointmentData);
                objAppointmentData.AppointmentId = appointmentId;
                sb.Append("  ,Updated Model: " + JsonConvert.SerializeObject(objAppointmentData) + "\r\n");

                // Setting portability and Policyexpirydate
                if (objAppointmentData.Portability != null) {
                    if (objAppointmentData.PolicyExpiryDate == DateTime.MinValue)
                    {
                        objAppointmentData.PolicyExpiryDate = null;
                    }
                    SalesViewDLL.SetHealthAppointmentData(objAppointmentData);
                }

                if (appointmentId > 0 && objAppointmentData.PlanList != null && objAppointmentData.PlanList.Count > 0)
                {
                    for (int i = 0; i < objAppointmentData.PlanList.Count; i++)
                    {
                        Int64 supplierId = objAppointmentData.PlanList[i].supplierId != "" ? Convert.ToInt64(objAppointmentData.PlanList[i].supplierId) : 0;
                        Int64 planId = objAppointmentData.PlanList[i].planId != "" ? Convert.ToInt64(objAppointmentData.PlanList[i].planId) : 0;
                        if (supplierId > 0 && planId > 0)
                        {
                            SalesViewDLL.SetAppointmentPlans(supplierId, planId, appointmentId, objAppointmentData.UserId);
                        }
                    }

                }

                if (appointmentId > 0)
                {
                    if (!string.IsNullOrEmpty(objAppointmentData.Source) && objAppointmentData.Source.ToLower() == "matrix" && objAppointmentData.subStatusId == 2002)
                    {
                        string s = FOSBLL.PushAppDatatoAITeam(objAppointmentData);
                    }


                    //----------------Save in mongo and trigger communicaion to cust--------------------//
                    if (objAppointmentData.AppointmentDateTime != responseLeadsSubStatusModel.AppointmentDateTime || objAppointmentData.AppointmentId != responseLeadsSubStatusModel.AppointmentId)
                    {
                        IsUpdatedatainMongo = true;
                        SavesubStatusForCj(objAppointmentData);
                    }

                    SetAppointmentHistory(responseLeadsSubStatusModel, objAppointmentData);

                    //Saving customer history location
                    FOSDLL.SaveCustomerLocationHistory(objAppointmentData);

                    //----------------Save Lat long against PlaceId ------------------//
                    if (CoreCommonMethods.IsValidString(objAppointmentData.place_id))
                    {
                        SavePlacecodeLatLongMapping(objAppointmentData.place_id);
                        if (!IsUpdatedatainMongo)
                            updateAppInMongo(responseLeadsSubStatusModel);
                    }



                    oSaveInfo.IsSaved = true;
                    oSaveInfo.StatusCode = 200;
                    oSaveInfo.Message = "Data Save Successfully";

                    oSaveInfo.data.AppointmentId = Convert.ToString(appointmentId);

                }

                //----------------Save Data in leadhistory for FOS APP-----------------//
                if (CoreCommonMethods.IsValidString(objAppointmentData.Comments) && (!CoreCommonMethods.IsValidString(responseLeadsSubStatusModel.CustomerInstructions) || (objAppointmentData.Comments.Trim().Length != responseLeadsSubStatusModel.CustomerInstructions.Trim().Length)))
                    FOSBLL.SetFOSComment(objAppointmentData);



            }
            catch (Exception ex)
            {
                oSaveInfo.IsSaved = false;
                oSaveInfo.StatusCode = 400;
                oSaveInfo.Message = "failed";

                LoggingHelper.LoggingHelper.AddloginQueue("", objAppointmentData.ParentId, ex.ToString(), "SetAppointmentDataV2", "FOSBLL", "MatrixCoreAPI", sb.ToString(), JsonConvert.SerializeObject(oSaveInfo), dt, DateTime.Now);
            }


            finally
            {

                LoggingHelper.LoggingHelper.AddloginQueue("", objAppointmentData.ParentId, "", "SetAppointmentDataV2", "FOSBLL", "MatrixCoreAPI", sb.ToString(), JsonConvert.SerializeObject(oSaveInfo), dt, DateTime.Now);
            }
            return oSaveInfo;
        }

        public static bool sendSMSOrEmail(string TriggerName, Int16 commType, Int64 MobileNo, Int64 LeadId, Int16 ProductId, string AgentName, string AgentEcode, string CustomerName, string VCURL, string EmailId, string url, string ScheduleDate, string ScheduleDay, string ScheduleTime)

        {
            bool result = false;
            string Json = string.Empty;
            string strexception = string.Empty;


            try
            {
                sendcommunicationResponse obj = new sendcommunicationResponse();
                obj.TriggerName = TriggerName;
                obj.LeadId = LeadId;
                obj.CommunicationType = commType;
                obj.MobileNo = MobileNo;
                obj.ProductId = ProductId;
                obj.InputData = new Inputdata();
                obj.AgentEcode = AgentEcode;
                obj.AgentName = AgentName;
                obj.To = new string[] { EmailId };
                obj.InputData = new Inputdata();

                obj.InputData.CustomerName = CustomerName;
                obj.InputData.ScheduleDate = ScheduleDate;
                obj.InputData.ScheduleDay = ScheduleDay;
                obj.InputData.ScheduleTime = ScheduleTime;
                obj.InputData.AgentName = AgentName;
                Json = Newtonsoft.Json.JsonConvert.SerializeObject(obj);
                var test = Json;
                url = "pbserviceapi".AppSettings();

                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"REQUESTINGSYSTEM", "Matrix"},{"TOKEN", "pbservicetoken".AppSettings()}};
                CommonAPICall.CallAPI(url, Json, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", header);
                result = true;
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                //  Console.WriteLine("Exception in sendSMSOrEmail Method." + ex.ToString());
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadId), LeadId, strexception, "sendSMSOrEmail", "SalesViewBLL", "MatrixCore", url, Json, DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public static async void SendSMSAndEmailToCust(AppointmentsDataModel objAppointmentData)
        {

            string ScheduleDay = "";
            string ScheduleTime = "";
            string ScheduleDate = "";
            string MobileNo = string.Empty;
            string ProductId = string.Empty;
            string Name = string.Empty;
            string url = "pbserviceapi".AppSettings();
            string substatusForBooked = "AppointmentTriggerForBooked".AppSettings();
            string substatus = "AppointmentTriggerforConfirmation".AppSettings();
            await Task.Run(() =>
            {
                try
                {
                    DataSet ds = SalesViewDLL.GetCustDetails(Convert.ToString(objAppointmentData.CustomerId), Convert.ToString(objAppointmentData.ParentId));

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        MobileNo = ds.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]);
                        ProductId = ds.Tables[0].Rows[0]["ProductId"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["ProductId"]);
                        Name = ds.Tables[0].Rows[0]["Name"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["Name"]);
                    }

                    if (!string.IsNullOrEmpty(MobileNo) && objAppointmentData != null && objAppointmentData.AppointmentDateTime != DateTime.MinValue)
                    {
                        ScheduleDay = objAppointmentData.AppointmentDateTime.Date.DayOfWeek.ToString();
                        ScheduleTime = objAppointmentData.AppointmentDateTime.ToString("h:mm tt");
                        ScheduleDate = objAppointmentData.AppointmentDateTime.Date.ToString("dd MMMM yyyy");

                        if (substatusForBooked.Contains("," + objAppointmentData.subStatusId + ","))
                        {
                            sendSMSOrEmail("All_Mtx_Sms_FOSAppointmentScheduled", 2, Convert.ToInt64(MobileNo), Convert.ToInt64(objAppointmentData.ParentId), 0, "", "", Name, "", "", url, ScheduleDate, ScheduleDay, ScheduleTime);
                        }
                        else if (substatus.Contains("," + objAppointmentData.subStatusId + ","))
                        {
                            sendSMSOrEmail("All_Mtx_SMS_FOS_AppointmentScheduled", 2, Convert.ToInt64(MobileNo), Convert.ToInt64(objAppointmentData.ParentId), 0, "", "", Name, "", "", url, ScheduleDate, ScheduleDay, ScheduleTime);
                            sendSMSOrEmail("All_Mtx_WA_FOS_AppointmentScheduled", 8, Convert.ToInt64(MobileNo), Convert.ToInt64(objAppointmentData.ParentId), 0, "", "", Name, "", "", url, ScheduleDate, ScheduleDay, ScheduleTime);

                        }
                    }

                }
                catch (Exception ex)
                {

                }
            });
        }

        public static void SavesubStatusForCj(AppointmentsDataModel objAppointmentData)
        {
            LeadsSubStatusModel oLeadsSubStatusModel = new LeadsSubStatusModel()
            {
                LeadID = objAppointmentData.ParentId,
                SubStatusId = objAppointmentData.subStatusId,
                UserID = objAppointmentData.UserId,
                CreatedBy = objAppointmentData.UserId,
                Source = objAppointmentData.Source,
                AppointmentDateTime = objAppointmentData.AppointmentDateTime,
                AppointmentId = objAppointmentData.AppointmentId,
                CancelReasonId = objAppointmentData.CancelReasonId,
                StatusId = objAppointmentData.StatusId,
                Pincode = objAppointmentData.Pincode,
                ProductId = objAppointmentData.ProductId
            };
            FOSBLL.SetAppointmentSubStatus(oLeadsSubStatusModel);
        }

        public static string AppointmentValidation(DateTime AppointmentDataTime, string Type, Int64 subStatusId = 0, string Source = "Matrix")
        {
            string msg = string.Empty;
            if (Type.ToLower() == "setappointment")
            {
                //if (AppointmentDataTime.Hour < 7 || AppointmentDataTime.Hour > 21)
                //{
                //    msg = "An appointment can be scheduled for timeslots between 7am to 10pm.";
                //}

                //else if (AppointmentDataTime < DateTime.Now.AddHours(1) && Source.ToUpper() != "FOSAPP")
                //{
                //    msg = "Appointment can only be scheduled after 1 hours from now.";
                //}
            }


            else if (subStatusId > 0 && Type.ToLower() == "setsubstatus")
            {
                string StartSubstatus = ",2124,";
                if (subStatusId == 2024 && AppointmentDataTime.AddHours(-1) < AppointmentDataTime && AppointmentDataTime.AddHours(2) > AppointmentDataTime)
                {
                    msg = "Appointment scheduled for future. Please reschedule to the current time to start the appointment.";
                }
            }



            return msg;
        }

        public SaveInfo SaveLeadsSubStatusV2(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            SaveInfo oSaveInfo = new SaveInfo();
            oSaveInfo.StatusCode = 400;
            DateTime dt = DateTime.Now;

            try
            {
                //Chk AppointmentDateTime from api if not exists thn get  from db
                if (oLeadsSubStatusModel.AppointmentDateTime == DateTime.MinValue)
                {
                    DataSet oDataSet = FOSDLL.getAppointmentData(oLeadsSubStatusModel.ParentID);
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        oLeadsSubStatusModel.CreatedOn = oDataSet.Tables[0].Rows[0]["createdon"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(oDataSet.Tables[0].Rows[0]["createdon"]);
                        oLeadsSubStatusModel.AppointmentDateTime = oDataSet.Tables[0].Rows[0]["AppointmentDateTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(oDataSet.Tables[0].Rows[0]["AppointmentDateTime"]);
                        oLeadsSubStatusModel.AppointmentId = oDataSet.Tables[0].Rows[0]["AppointmentId"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["AppointmentId"]);
                    }
                }

                if (oLeadsSubStatusModel.AppointmentDateTime != DateTime.MinValue)
                {
                    string Message = AppointmentValidation(Convert.ToDateTime(oLeadsSubStatusModel.AppointmentDateTime), "setsubstatus", oLeadsSubStatusModel.SubStatusId);
                    if (!string.IsNullOrEmpty(Message))
                    {
                        oSaveInfo.Message = Message;
                        return oSaveInfo;
                    }
                }


                string statusIds = "StatusIds".AppSettings();
                if (statusIds.Contains("," + oLeadsSubStatusModel.StatusId + ","))
                {
                    bool response = FOSDLL.SaveLeadsSubStatus(oLeadsSubStatusModel);
                }


                //if ((EnumAppSusbtatus)oLeadsSubStatusModel.SubStatusId != EnumAppSusbtatus.Booked)
                SetAppointmentSubStatus(oLeadsSubStatusModel);

                FOSDLL.SetCustomerComment(oLeadsSubStatusModel, 47, "Status updated by FOSAgent", null);
                oSaveInfo.StatusCode = 200;
                oSaveInfo.IsSaved = true;
                oSaveInfo.Message = "Save Successfully";

            }
            catch (Exception ex)
            {
                oSaveInfo.Message = "failed";
                LoggingHelper.LoggingHelper.AddloginQueue(oLeadsSubStatusModel.LeadID.ToString(), Convert.ToInt64(oLeadsSubStatusModel.LeadID), ex.ToString(), "SaveLeadsSubStatusV2", "FOSBLL", "SaveLeadsSubStatus", JsonConvert.SerializeObject(oLeadsSubStatusModel), JsonConvert.SerializeObject(oSaveInfo), dt, DateTime.Now);
            }


            finally
            {


                LoggingHelper.LoggingHelper.AddloginQueue(oLeadsSubStatusModel.LeadID.ToString(), Convert.ToInt64(oLeadsSubStatusModel.LeadID), "", "SaveLeadsSubStatusV2", "FOSBLL", "SaveLeadsSubStatus", JsonConvert.SerializeObject(oLeadsSubStatusModel), JsonConvert.SerializeObject(oSaveInfo), dt, DateTime.Now);
            }

            return oSaveInfo;

        }

        public List<LeadAttributesModel> GetLeadAttributes(long UserId)
        {
            DateTime dt = DateTime.Now;
            List<LeadAttributesModel> lstLeadAttributesModel = new List<LeadAttributesModel>();
            Dictionary<string, string> objDict = new Dictionary<string, string>();
            List<Tuple<string, Dictionary<string, string>>> _dictCollection = new List<Tuple<string, Dictionary<string, string>>>();

            try
            {
                var oDataSet = FOSDLL.GetLeadAttributes(UserId);

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    var UniqueRecords = (from r in oDataSet.Tables[0].AsEnumerable()
                                         select r["LeadId"]).Distinct().ToList();


                    foreach (var item in UniqueRecords)
                    {
                        LeadAttributesModel objLeadAttributesModel = new LeadAttributesModel();
                        objDict = new Dictionary<string, string>(); ;
                        foreach (DataRow row in oDataSet.Tables[0].Rows)
                        {
                            if (Convert.ToInt64(item) == Convert.ToInt64((row["leadId"])))
                            {
                                string Attribute = row["AtributeName"] == DBNull.Value ? "" : Convert.ToString(row["AtributeName"]);
                                string value = row["Value"] == DBNull.Value ? "" : Convert.ToString(row["Value"]);

                                if (!objDict.ContainsKey(Attribute))
                                    objDict.Add(Attribute, value);
                            }
                        }
                        objLeadAttributesModel.LeadId = Convert.ToInt64(item);
                        objLeadAttributesModel.data = objDict;
                        lstLeadAttributesModel.Add(objLeadAttributesModel);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(UserId), ex.ToString(), "GetLeadAttributes", "MatrixCore", "GetLeadAttributes", UserId.ToString(), JsonConvert.SerializeObject(lstLeadAttributesModel), dt, DateTime.Now);
            }

            return lstLeadAttributesModel;

        }

        public static void UpdateAgentLoginStatus(LogInDTO oLogInDTO)
        {
            try
            {
                if (CoreCommonMethods.IsValidString(oLogInDTO.EmployeeId))
                {
                    string url = "CommServerBaseUrl".AppSettings() + "Asteriskevent.svc/updateagentloginstatus";
                    string dataToPost = "{\"AgentCode\":\"" + oLogInDTO.EmployeeId + "\",\"status\":\"IDLE\"}";
                    var data = CommonAPICall.CallAPI(url, dataToPost, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
                }
            }
            catch (Exception ex)
            {

            }
        }
        public AppLocationsModal GetAppointnmentLocations(long AppointmentId, Int16 Type)
        {
            AppLocationsModal oAppLocationsModal = new AppLocationsModal();
            DateTime dt = DateTime.Now;
            try
            {
                DataSet oDataSet = FOSDLL.GetAppointnmentLocations(AppointmentId, Type);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        oAppLocationsModal.Latitude = row["Latitude"] == DBNull.Value ? 0 : Convert.ToDecimal(row["Latitude"]);
                        oAppLocationsModal.Longitude = row["Longitude"] == DBNull.Value ? 0 : Convert.ToDecimal(row["Longitude"]);
                    }
                }

                return oAppLocationsModal;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AppointmentId.ToString(), Convert.ToInt64(AppointmentId), ex.ToString(), "GetAppointnmentLocations", "FOSBLL", "SaveLeadsSubStatus", JsonConvert.SerializeObject(AppointmentId), JsonConvert.SerializeObject(oAppLocationsModal), dt, DateTime.Now);
                return null;
            }

        }


        public List<CustomerCommentModal> GetActivityHistory(string LeadId)
        {
            List<CustomerCommentModal> lstCustomerCommentModal = new List<CustomerCommentModal>();
            try
            {
                DataSet oDataSet = FOSDLL.GetActivityHistory(LeadId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {

                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {
                        CustomerCommentModal oCustomerCommentModal = new CustomerCommentModal();
                        oCustomerCommentModal.LeadID = row["LeadID"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadID"]);
                        oCustomerCommentModal.Comments = row["Comments"] == DBNull.Value ? string.Empty : Convert.ToString(row["Comments"]);
                        oCustomerCommentModal.CreatedOn = row["CreatedOn"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["CreatedOn"]);
                        oCustomerCommentModal.CreatedBy = row["CreatedBy"] == DBNull.Value ? string.Empty : Convert.ToString(row["CreatedBy"]);
                        lstCustomerCommentModal.Add(oCustomerCommentModal);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), ex.ToString(), "GetActivityHistory", "MatrixCore", "GetActivityHistory", LeadId.ToString(), JsonConvert.SerializeObject(lstCustomerCommentModal), DateTime.Now, DateTime.Now);
            }

            return lstCustomerCommentModal;

        }

        public static void UpdateAgentStatus(LogInDTO oLogInDTO, String Status)
        {
            string dataToPost = string.Empty;
            try
            {
                if (oLogInDTO!=null && CoreCommonMethods.IsValidString(oLogInDTO.EmployeeId))
                {
                    oLogInDTO.Process = "FOSAPP";
                    string baseUrl = "InternalMatrixApiBaseURL".AppSettings();
                    string url = $"{baseUrl}onelead/api/Dialer/updateAgentLoginStatus";

                    var headers = new Dictionary<object, object>
                    {
                        { "source", "Matrix" },
                        { "authKey", "matrixAPIauthKey".AppSettings() },
                        { "clientKey", "matrixAPIclientKey".AppSettings() }
                    };
                     dataToPost = "{\"AgentCode\":\"" + oLogInDTO.EmployeeId + "\",\"Process\":\"" + oLogInDTO.Process + "\",\"status\":\"" + Status + "\"}";
                    var data = CommonAPICall.CallAPI(url, dataToPost, "POST", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", headers);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(oLogInDTO.EmployeeId.ToString(), 0, ex.ToString(), "UpdateAgentLoginStatus", "FOSBLL", "MatrixCoreAPI", dataToPost.ToString(), "", DateTime.Now, DateTime.Now);
            }
        }

        public static List<PIVCData> CallPIVC(long UserId)
        {
            DateTime dt = DateTime.Now;
            List<PIVCData> obj = new List<PIVCData>();
            try
            {
                if (UserId > 0)
                {
                    //string url = "PIVC".AppSettings() + "GetPIVCVerificationDetailsByAgentId";

                    //Dictionary<string, string> header = new Dictionary<string, string>();
                    //header.Add("token", "PIVCToken".AppSettings());

                    //string dataToPost = "{\"AgentId\":\"" + UserId + "\"}";

                    //var data = CommonAPICall.PostAPICallWithResult(url, Convert.ToInt32("BMSTicketTimeOut".AppSettings()), dataToPost, "", header);


                    //if (!string.IsNullOrEmpty(data))
                    //{
                    //    dynamic obj1 = JsonConvert.DeserializeObject(data);
                    //    obj = JsonConvert.DeserializeObject<List<PIVCData>>(obj1.Data.ToString());
                    //}

                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), Convert.ToInt64(UserId), ex.ToString(), "CallPIVC", "FOSBLL", "CallPIVC", JsonConvert.SerializeObject(UserId), JsonConvert.SerializeObject(obj), dt, DateTime.Now);
            }
            return obj;
        }
        public static void SaveCancallationReason(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            AppCancelReasonModel oAppCancelReasonModel = new AppCancelReasonModel();

            DataSet oDataSet = FOSDLL.getCancallationReason(Convert.ToInt16(oLeadsSubStatusModel.CancelReasonId));

            if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
            {
                oAppCancelReasonModel.textValue = oDataSet.Tables[0].Rows[0]["Reason"] == DBNull.Value ? string.Empty : Convert.ToString(oDataSet.Tables[0].Rows[0]["Reason"]);
            }

            oAppCancelReasonModel.Leads = Convert.ToString(oLeadsSubStatusModel.LeadID);

            oAppCancelReasonModel.PropertyId = oLeadsSubStatusModel.SubStatusId == 2004 ? 7 : 8;


            oAppCancelReasonModel.value = oLeadsSubStatusModel.CancelReasonId.ToString();
            oAppCancelReasonModel.UserID = Convert.ToInt32(oLeadsSubStatusModel.UserID);
            oAppCancelReasonModel.SubStatusId = Convert.ToInt32(oLeadsSubStatusModel.SubStatusId);

            SalesViewDLL.SaveAppCancelReason(oAppCancelReasonModel);
            oLeadsSubStatusModel.ReasonText = oAppCancelReasonModel.textValue;
        }

        public List<ReasonCancelModal> GetCancelReasons()
        {
            List<ReasonCancelModal> lstReasonCancelModal = new();
            DateTime dt = DateTime.Now;
            try
            {
                string Key = $"{RedisCollection.FOSCancellationReason()}";
                if (MemoryCache.Default[Key] != null)
                {
                    lstReasonCancelModal = new List<ReasonCancelModal>();
                    lstReasonCancelModal = (List<ReasonCancelModal>)MemoryCache.Default.Get(Key);
                }
                else
                {
                    DataSet oDataSet = FOSDLL.GetCancelReasonMaster();
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        foreach (DataRow row in oDataSet.Tables[0].Rows)
                        {
                            ReasonCancelModal oReasonCancelModal = new()
                            {
                                ReasonId = row["ResaonId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(row["ResaonId"]),
                                Reason = row["Reason"] == DBNull.Value ? string.Empty : Convert.ToString(row["Reason"]),
                                ProductId = row["ProductId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(row["ProductId"]),
                                subStatusId = row["SubstatusId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["SubstatusId"]),
                                Type = row["Type"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(row["Type"]),
                                IsShow = row["IsShow"] == DBNull.Value ? false : Convert.ToBoolean(row["IsShow"])
                            };
                            lstReasonCancelModal.Add(oReasonCancelModal);
                        }
                        CommonCache.GetOrInsertIntoCache(lstReasonCancelModal, Key, 8 * 60);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetCancelReasons", "Matrixcore", "FOSBLL", string.Empty, JsonConvert.SerializeObject(lstReasonCancelModal), dt, DateTime.Now);

            }
            return lstReasonCancelModal;
        }

        public AppointmentsDataModel GetAppointmentDetailsV2(string CustomerId, string ParentId, string AppointmentId, string BookingId)
        {
            AppointmentsDataModel objAppointmentDataModel = new AppointmentsDataModel();
            DateTime dt = DateTime.Now;
            try
            {
                //Call PolicyStatus API 
                //if (!string.IsNullOrEmpty(BookingId))
                //{
                //    GetPolicyStatusByLeadIDResult oGetPolicyStatusFromBMS = GetPolicyStatusFromBMS(Convert.ToInt64(BookingId));

                //    if (oGetPolicyStatusFromBMS != null && oGetPolicyStatusFromBMS.Data != null)
                //    {
                //        objAppointmentDataModel.PivcLink = oGetPolicyStatusFromBMS.Data.PIWCLink;
                //        objAppointmentDataModel.VerificationStatus = oGetPolicyStatusFromBMS.Data.VerificationStatus;
                //        objAppointmentDataModel.PivcStatus = oGetPolicyStatusFromBMS.Data.PIWCStatus;
                //        objAppointmentDataModel.ApplicationNo = oGetPolicyStatusFromBMS.Data.ApplicationNo;
                //    }
                //}

                DataSet oDataSet = FOSDLL.GetAppointmentDetails(CustomerId, ParentId, AppointmentId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in oDataSet.Tables[0].Rows)
                    {

                        objAppointmentDataModel.location = new PlaceLatLongModel();
                        objAppointmentDataModel.AppointmentId = row["AppointmentId"] == DBNull.Value ? 0 : Convert.ToInt32(row["AppointmentId"]);
                        objAppointmentDataModel.AppointmentDateTime = row["AppointmentDateTime"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(row["AppointmentDateTime"]);
                        objAppointmentDataModel.Address = row["Address"] == DBNull.Value ? "" : Convert.ToString(row["Address"]);
                        objAppointmentDataModel.Pincode = row["Pincode"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["Pincode"]);
                        objAppointmentDataModel.CityId = row["CityId"] == DBNull.Value ? 0 : Convert.ToInt32(row["CityId"]);
                        objAppointmentDataModel.AppointmentType = row["AppointmentType"] == DBNull.Value ? 0 : Convert.ToInt32(row["AppointmentType"]);
                        objAppointmentDataModel.OfflineCityId = row["OfflineCityId"] == DBNull.Value ? 0 : Convert.ToInt32(row["OfflineCityId"]);
                        objAppointmentDataModel.Address1 = row["Address1"] == DBNull.Value ? "" : Convert.ToString(row["Address1"]);
                        objAppointmentDataModel.Landmark = row["Landmark"] == DBNull.Value ? "" : Convert.ToString(row["Landmark"]);
                        objAppointmentDataModel.Comments = row["Comments"] == DBNull.Value ? "" : Convert.ToString(row["Comments"]);
                        objAppointmentDataModel.ZoneId = row["ZoneId"] == DBNull.Value ? 0 : Convert.ToInt32(row["ZoneId"]);
                        objAppointmentDataModel.AssignmentId = row["AssignmentId"] == DBNull.Value ? 0 : Convert.ToInt32(row["AssignmentId"]);
                        objAppointmentDataModel.Reason = row["Reason"] == DBNull.Value ? "" : Convert.ToString(row["Reason"]);
                        objAppointmentDataModel.ReasonId = row["ReasonId"] == DBNull.Value ? 0 : Convert.ToInt32(row["ReasonId"]);
                        objAppointmentDataModel.AppointmentStart = row["AppointmentStart"] == DBNull.Value ? null : Convert.ToDateTime(row["AppointmentStart"]);
                        objAppointmentDataModel.CustomerID = Convert.ToInt64(CustomerId);
                        objAppointmentDataModel.LeadID = Convert.ToInt64(ParentId);
                        objAppointmentDataModel.subStatusId = row["subStatusId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["subStatusId"]);
                        objAppointmentDataModel.StatusId = row["StatusId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["StatusId"]);
                        objAppointmentDataModel.ProductId = row["ProductId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["ProductId"]);
                        objAppointmentDataModel.SlotId = row["SlotId"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(row["SlotId"]);
                        objAppointmentDataModel.location.place_id = row["PlaceId"] == DBNull.Value ? "" : Convert.ToString(row["PlaceId"]);
                        objAppointmentDataModel.location.Lat = row["lat"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(row["lat"]);
                        objAppointmentDataModel.location.Long = row["long"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(row["long"]);
                        objAppointmentDataModel.NearBy = row["NearBy"] == DBNull.Value ? "" : Convert.ToString(row["NearBy"]);
                        objAppointmentDataModel.EducationId = row["EducationId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["EducationId"]);
                        objAppointmentDataModel.IncomeId = row["IncomeId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["IncomeId"]);
                        objAppointmentDataModel.IncomeDocsId = row["IncomeDocsId"] == DBNull.Value ? Convert.ToInt32(0) : Convert.ToInt32(row["IncomeDocsId"]);
                        objAppointmentDataModel.CustomerName = row["Name"] == DBNull.Value ? "" : Convert.ToString(row["Name"]);
                        objAppointmentDataModel.MobileNo = row["MobileNo"] == DBNull.Value ? "" : Crypto.MaskMobileNo(Convert.ToString(row["MobileNo"]));
                        objAppointmentDataModel.Email = row["Email"] == DBNull.Value ? "" : Crypto.MaskEmailID(Convert.ToString(row["Email"]));
                        objAppointmentDataModel.CbTime = row["CbTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(row["CbTime"]);
                        objAppointmentDataModel.OldAppointmentDateTime = row["OldAppointmentDateTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(row["OldAppointmentDateTime"]);
                        objAppointmentDataModel.AssignTo = row["AssignTo"] == DBNull.Value ? 0 : Convert.ToInt64(row["AssignTo"]);
                        objAppointmentDataModel.Gender = (row["Gender"] == null || row["Gender"] == DBNull.Value) ? Convert.ToInt16(0) : Convert.ToInt16(row["Gender"]);
                        objAppointmentDataModel.AppCreatedBy = row["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt64(row["CreatedBy"]);
                        objAppointmentDataModel.CountryCode = row["CountryCode"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(row["CountryCode"]);
                        objAppointmentDataModel.IsCustConfirmed = row["LastActionBy"] != null && row["LastActionBy"] != DBNull.Value && row["subStatusId"] != DBNull.Value && Convert.ToInt64(row["LastActionBy"]) == Convert.ToInt64(EnumAppUserID.Customer) && Convert.ToInt32(row["subStatusId"]) == Convert.ToInt32(EnumAppSusbtatus.Confirmed) ? true : false;
                        objAppointmentDataModel.InvestmentType = row["InvestmentType"] == DBNull.Value ? "" : Convert.ToString(row["InvestmentType"]);
                        objAppointmentDataModel.RescheduleCount = row["RescheduleCount"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(row["RescheduleCount"]);
                        objAppointmentDataModel.PincodeLatLong = new LatLongModel()
                        {
                            Lat = row["PinLat"] != null && row["PinLat"] != DBNull.Value ? Convert.ToDecimal(row["PinLat"]) : 0,
                            Long = row["PinLong"] != null && row["PinLong"] != DBNull.Value ? Convert.ToDecimal(row["PinLong"]) : 0
                        };
                        objAppointmentDataModel.CustResponse = row["CustResponse"] != null && row["CustResponse"] != DBNull.Value ? Convert.ToString(row["CustResponse"]) : string.Empty;

                    }
                }

                //get data from AppointmentPlan

                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[1].Rows.Count > 0)
                {
                    objAppointmentDataModel.PlanList = new List<Recommendation>();
                    foreach (DataRow row in oDataSet.Tables[1].Rows)
                    {
                        Recommendation objRecommendation = new Recommendation();
                        objRecommendation.supplierId = row["SupplierId"] == DBNull.Value ? "" : Convert.ToString(row["SupplierId"]);
                        objRecommendation.planId = row["PlanId"] == DBNull.Value ? "" : Convert.ToString(row["PlanId"]);
                        objRecommendation.planName = row["planName"] == DBNull.Value ? "" : Convert.ToString(row["planName"]);
                        objRecommendation.supplierName = row["supplierName"] == DBNull.Value ? "" : Convert.ToString(row["supplierName"]);
                        objAppointmentDataModel.PlanList.Add(objRecommendation);
                    }
                }

                //--ai audio files
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[2].Rows.Count > 0)
                {
                    objAppointmentDataModel.AudioFiles = oDataSet.Tables[2].AsEnumerable().ToList().Select(dr => new AIAudioModel()
                    {
                        CallDataID = dr["CallDataID"] != DBNull.Value ? Convert.ToInt64(dr["CallDataID"]) : default,
                        audioClip = dr["AudioFilePath"] != DBNull.Value ? Convert.ToString(dr["AudioFilePath"]) : string.Empty,
                        audioCreatedOn = dr["audioCreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["audioCreatedOn"]) : DateTime.MinValue,

                    }).ToList();
                }


                return objAppointmentDataModel;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(ParentId.ToString(), Convert.ToInt64(ParentId), ex.ToString(), "GetAppointmentDetailsV2", "FOSBLL", "SaveLeadsSubStatus", JsonConvert.SerializeObject(objAppointmentDataModel), "", dt, DateTime.Now);
                return null;
            }

        }

        public static GetPolicyStatusByLeadIDResult GetPolicyStatusFromBMS(long LeadId)
        {
            DateTime dt = DateTime.Now;

            GetPolicyStatusByLeadIDResult oGetPolicyStatusByLeadIDResult = new GetPolicyStatusByLeadIDResult();
            try
            {
                if (LeadId > 0)
                {
                    string url = "PIVC".AppSettings() + "GetPolicyStatusByLeadID";

                    Dictionary<object, object> header = new Dictionary<object, object>();
                    header.Add("token", "PIVCToken".AppSettings());

                    string dataToPost = "{\"LeadId\":\"" + LeadId + "\"}";

                    var data = CommonAPICall.CallAPI(url, dataToPost, "POST", Convert.ToInt32("BMSTicketTimeOut".AppSettings()), "application/json", header);

                    if (!string.IsNullOrEmpty(data))
                    {
                        dynamic obj1 = JsonConvert.DeserializeObject(data);
                        if (obj1 != null && obj1.GetPolicyStatusByLeadIDResult != null)
                            oGetPolicyStatusByLeadIDResult = JsonConvert.DeserializeObject<GetPolicyStatusByLeadIDResult>(obj1.GetPolicyStatusByLeadIDResult.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), Convert.ToInt64(LeadId), ex.ToString(), "GetPolicyStatusFromBMS", "MatrixCore", "GetPolicyStatusFromBMS", JsonConvert.SerializeObject(LeadId), JsonConvert.SerializeObject(oGetPolicyStatusByLeadIDResult), dt, DateTime.Now);
            }
            return oGetPolicyStatusByLeadIDResult;
        }

        public static void SavePlacecodeLatLongMapping(string PlaceId)
        {


            try
            {

                List<PlaceLatLongModel> lstPlaceLatLongMstr = GetPlaceLatLongMaster();

                if (lstPlaceLatLongMstr != null && lstPlaceLatLongMstr.Count > 0)
                {
                    var IsPlaceIdExist = lstPlaceLatLongMstr.Where(x => x.place_id == PlaceId).FirstOrDefault();

                    if (IsPlaceIdExist == null)
                    {
                        PlaceLatLongModel placeLatLongModel = new PlaceLatLongModel();
                        placeLatLongModel = CallGoogleDetailAPI(PlaceId);

                        //------------------ Save in Cache and DB PlaceMaster -----------------------//
                        if (placeLatLongModel.Lat > 0 && placeLatLongModel.Long > 0)
                        {
                            string PlaceMasterKey = $"{RedisCollection.PlaceIdMaster()}";
                            lstPlaceLatLongMstr.Add(placeLatLongModel);

                            FOSDLL.SavePlacecodeLatLongMaster(placeLatLongModel);

                        }

                    }

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(PlaceId, 0, ex.ToString(), "SavePlacecodeLatLongMapping", "FOSBLL", "FOSBLL", JsonConvert.SerializeObject(PlaceId), "", DateTime.Now, DateTime.Now);
            }
        }
        public static PlaceLatLongModel CallGoogleDetailAPI(string PlaceId)
        {
            PlaceLatLongModel placeLatLongModel = new PlaceLatLongModel();
            placeLatLongModel.place_id = PlaceId;
            try
            {
                string url = "GoogleDeatilsAPI".AppSettings() + "?place_id=" + PlaceId
                       + "&key=" + "GoogleAPIKey".AppSettings();

                var jsondata = CommonAPICall.CallAPI(url, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);

                if (jsondata != null)
                {
                    GoogleDetailModel obj = JsonConvert.DeserializeObject<GoogleDetailModel>(jsondata);

                    if (obj != null && obj.result != null && obj.result.geometry != null && obj.result.geometry.location != null && obj.result.geometry.location.lat > 0 && obj.result.geometry.location.lng > 0)
                    {
                        placeLatLongModel.Lat = Convert.ToDecimal(obj.result.geometry.location.lat);
                        placeLatLongModel.Long = Convert.ToDecimal(obj.result.geometry.location.lng);
                    }
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(PlaceId, 0, ex.ToString(), "CallGoogleDetailAPI", "FOSBLL", "FOSBLL", JsonConvert.SerializeObject(PlaceId), "", DateTime.Now, DateTime.Now);
            }
            return placeLatLongModel;

        }

        public static List<PlaceLatLongModel> GetPlaceLatLongMaster()
        {
            List<PlaceLatLongModel> lstPlaceIdLatLongModel = new List<PlaceLatLongModel>();
            string Key = $"{RedisCollection.PlaceIdMaster()}";
            try
            {
                if (MemoryCache.Default[Key] != null)
                {
                    lstPlaceIdLatLongModel = (List<PlaceLatLongModel>)(MemoryCache.Default.Get(Key));
                }
                else
                {
                    DataSet oDataSet = FOSDLL.GetPlaceLatLongMaster();
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        lstPlaceIdLatLongModel = (from dr in oDataSet.Tables[0].AsEnumerable()
                                                  select new PlaceLatLongModel
                                                  {
                                                      place_id = dr.Field<string>("PlaceId"),
                                                      Lat = dr.Field<decimal>("Lat"),
                                                      Long = dr.Field<decimal>("Long"),

                                                  }).ToList();

                    }
                    CommonCache.GetOrInsertIntoCache(lstPlaceIdLatLongModel, Key, 72 * 60);

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "GetPlaceLatLongMaster", "FOSBLL", "FOSBLL", "", "", DateTime.Now, DateTime.Now);
            }
            return lstPlaceIdLatLongModel;
        }


        public static void UpdateLeadPriorityModel(PriorityModel reqPriorityModel)
        {
            StringBuilder sb = new StringBuilder();
            string Error = string.Empty;
            DateTime reqDateTime = DateTime.Now;

            IMongoQuery varquery;
            UpdateBuilder<PriorityModel> update = null;
            varquery = Query<PriorityModel>.EQ(p => p.LeadID, reqPriorityModel.LeadID);
            try
            {
                sb.Append("LeadId: " + reqPriorityModel.LeadID + ", Event= " + reqPriorityModel.EventType + "\r\n");

                PriorityModel respPriorityModel = GetPriorityModelMongo(reqPriorityModel.LeadID);// Mongo StoredObject
                if (respPriorityModel != null)
                {
                    switch (reqPriorityModel.AppointmentTypeEnum)
                    {
                        case AppointmentTypeEnum.setAppointment:
                            if (respPriorityModel.Appointment != null)
                            {
                                update = Update<PriorityModel>
                                           .Set(p => p.Appointment.AppointmentId, reqPriorityModel.Appointment.AppointmentId)
                                           .Set(p => p.Appointment.ScheduledOn, reqPriorityModel.Appointment.ScheduledOn)
                                           .Set(p => p.Appointment.StatusID, reqPriorityModel.Appointment.StatusID)
                                           .Set(p => p.Appointment.UpdatedOn, DateTime.Now)
                                           .Set(p => p.Appointment.UserID, reqPriorityModel.Appointment.UserID)
                                           .Set(p => p.Appointment.Source, reqPriorityModel.Appointment.Source)
                                           .Set(p => p.Appointment.CreatedOn, reqPriorityModel.Appointment.CreatedOn)
                                           .Set(p => p.Appointment.PCode, reqPriorityModel.Appointment.PCode)//Last  Appcreatedon  added here//Last  Appcreatedon  added here
                                           .Set(p => p.Appointment.Lat, reqPriorityModel.Appointment.Lat)
                                           .Set(p => p.Appointment.Long, reqPriorityModel.Appointment.Long)
                                           .Set(p => p.Appointment.LastVisited, reqPriorityModel.Appointment.StatusID == Convert.ToInt16(EnumAppSusbtatus.start) ? reqPriorityModel.Appointment.ScheduledOn : respPriorityModel.Appointment.LastVisited)
                                           .Set(p => p.Appointment.RescheduleCount,
                                                    reqPriorityModel.Appointment.StatusID == Convert.ToInt16(EnumAppSusbtatus.ReScheduled) ? (respPriorityModel.Appointment?.RescheduleCount ?? 0) + 1
                                                : reqPriorityModel.Appointment.StatusID == Convert.ToInt16(EnumAppSusbtatus.Booked) ? 0
                                                : (respPriorityModel.Appointment?.RescheduleCount ?? 0));

                            }
                            else
                            {
                                AppointmentData appointmentData = new AppointmentData()
                                {
                                    AppointmentId = reqPriorityModel.Appointment.AppointmentId,
                                    ScheduledOn = reqPriorityModel.Appointment.ScheduledOn,
                                    StatusID = Convert.ToInt16(reqPriorityModel.Appointment.StatusID),
                                    UserID = reqPriorityModel.Appointment.UserID,
                                    Source = reqPriorityModel.Appointment.Source,
                                    CreatedOn = reqPriorityModel.Appointment.CreatedOn,
                                    PCode = reqPriorityModel.Appointment.PCode,
                                    Lat = reqPriorityModel.Appointment.Lat,
                                    Long = reqPriorityModel.Appointment.Long,
                                    RescheduleCount=0,
                                    LastVisited = respPriorityModel.Appointment != null ? respPriorityModel.Appointment.LastVisited : DateTime.MinValue

                                };

                                update = Update<PriorityModel>.Set(p => p.Appointment, appointmentData);
                            }
                           

                            //----------- update PriorirtyModel Mongo ------------------//
                            if (varquery != null && update != null)
                            {
                                MongoDLL.UpdateDocument(varquery, update);
                                sb.Append("Update Query - " + update.ToString());
                            }
                            break;

                        default:
                            // code block
                            break;
                    }
                    if (respPriorityModel.Appointment != null && respPriorityModel.Appointment.AppointmentId > 0)
                    {
                        AppointmentsDataModel appointmentsDataModel = new()
                        {
                            AppointmentId = respPriorityModel.Appointment.AppointmentId,
                            AppointmentDateTime = respPriorityModel.Appointment.ScheduledOn,
                            UserId = respPriorityModel.Appointment.UserID,
                            SubStatusID = respPriorityModel.Appointment.StatusID,
                        };
                        //PushAppData(appointmentsDataModel);
                    }
                }
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", reqPriorityModel.LeadID, Error, "UpdateAppointmentModel", "MatrixCore", "FOSBLL", JsonConvert.SerializeObject(reqPriorityModel), "", reqDateTime, DateTime.Now);
            }
        }

        public static PriorityModel GetPriorityModelMongo(Int64 LeadId)
        {
            PriorityModel oPriorityModel = new PriorityModel();
            oPriorityModel = LeadPrioritizationDLL.GetPriorityModelMongo(LeadId);
            return oPriorityModel;
        }

        public List<CustLeadInfo> getCustLeadInfo(long CustomerID)
        {
            List<CustLeadInfo> info = null;
            DateTime requestTime = DateTime.Now;

            try
            {
                DataSet oDataSet = FOSDLL.GetLeadInfo(CustomerID);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    info = (from dr in oDataSet.Tables[0].AsEnumerable()
                            select new CustLeadInfo
                            {
                                AgentName = Convert.ToString(dr["UserName"]),
                                CustomerName = Convert.ToString(dr["Name"]),
                                EmployeeId = Convert.ToString(dr["EmployeeId"]),
                                ProductID = Convert.ToInt16(dr["ProductID"]),
                                UserID = Convert.ToInt64(dr["UserID"]),
                                LeadID = Convert.ToInt64(dr["LeadID"])
                            }).ToList();

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustomerID, ex.ToString(), "getCustLeadInfo", "MatrixCore", "FOSBLL", string.Empty, string.Empty, requestTime, DateTime.Now);
            }

            return info;
        }



        public static sendcommunicationResponse SendCommunicationToCust(LeadsSubStatusModel _leadsSubStatusModel)
        {
            DateTime requestTime = DateTime.Now;
            string SMSURL = string.Empty;
            string WhatsappURL = string.Empty;
            sendcommunicationResponse osendcommunicationResponse = null;

            try
            {
                DataSet ds = FOSDLL.GetLeadDetails(Convert.ToInt64(_leadsSubStatusModel.ParentID));

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    string MobileNo = ds.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]);
                    string Name = ds.Tables[0].Rows[0]["Name"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["Name"]);
                    string ProductName = ds.Tables[0].Rows[0]["ProductName"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["ProductName"]);
                    string CustomerID = ds.Tables[0].Rows[0]["CustomerID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["CustomerID"]);
                    string ProductID = ds.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["ProductID"]);
                    string LatestAppId = ds.Tables[0].Rows[0]["LatestAppId"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["LatestAppId"]);
                    string LatestAppUID = ds.Tables[0].Rows[0]["LatestAppUID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["LatestAppUID"]);

                    _leadsSubStatusModel.AppointmentDateTime = ds.Tables[0].Rows[0]["LatestAppDateTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(ds.Tables[0].Rows[0]["LatestAppDateTime"]);



                    if (CoreCommonMethods.IsValidString(MobileNo) && _leadsSubStatusModel.AppointmentDateTime != DateTime.MinValue)
                    {

                        string URL = GenerateRescheduleLink(Convert.ToString(_leadsSubStatusModel.ParentID), Convert.ToInt64(CustomerID), Convert.ToInt16(ProductID));
                        if (!string.IsNullOrEmpty(URL))
                        {
                            SMSURL = GetShortLink(URL + "&src=customersms", _leadsSubStatusModel.ParentID, Convert.ToInt16(ProductID), "FOS");
                            WhatsappURL = GetShortLink(URL + "&src=customerwhatsapp", _leadsSubStatusModel.ParentID, Convert.ToInt16(ProductID), "FOS");
                        }

                        osendcommunicationResponse = new sendcommunicationResponse()
                        {
                            LeadId = _leadsSubStatusModel.ParentID,
                            MobileNo = Convert.ToInt64(MobileNo),
                            ProductId = 0,
                            SMSURL = SMSURL,
                            WhatsappURL = WhatsappURL,

                            InputData = new Inputdata()
                            {
                                ProductName = Convert.ToInt32(ProductID) == 117 ? FOSDLL.getProductName(Convert.ToInt32(ProductID)) : ProductName,
                                CustomerName = Name,
                                AgentName = string.Empty,
                                CustomerAddress = _leadsSubStatusModel.Address + " " + _leadsSubStatusModel.Address1 + " " + _leadsSubStatusModel.Landmark + " " + _leadsSubStatusModel.Pincode,
                                AppointmentDateTime = _leadsSubStatusModel.AppointmentDateTime.ToString("dd-MMM-yyyy hh:mm tt"),
                                AppointmentId = Convert.ToString(LatestAppId),
                                AppointmentUID = Convert.ToString(LatestAppUID),
                                AppointmentSubStatusID = ""
                            }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", _leadsSubStatusModel.ParentID, ex.ToString(), "SendCommunicationToCust", "MatrixCore", "FOSBLL", string.Empty, string.Empty, requestTime, DateTime.Now);
            }
            return osendcommunicationResponse;
        }

        public static bool SendCommunication(sendcommunicationResponse oSendcommunicationResponse)

        {
            bool result = false;
            string Json = string.Empty;
            string strexception = string.Empty;
            string url = "pbserviceapi".AppSettings();

            try
            {
                Json = JsonConvert.SerializeObject(oSendcommunicationResponse);

                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"REQUESTINGSYSTEM", "Matrix"},{"TOKEN", "pbservicetoken".AppSettings()}};
                CommonAPICall.CallAPI(url, Json, "POST", Convert.ToInt32("BMSTicketTimeOut".AppSettings()), "application/json", header);
                result = true;
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(oSendcommunicationResponse.LeadId), oSendcommunicationResponse.LeadId, strexception, "SendCommunication", "FOSBLL", "MatrixCoreAPI", JsonConvert.SerializeObject(Json), "", DateTime.Now, DateTime.Now);
            }

            return result;
        }

        public static string GenerateRescheduleLink(string LeadId, long CustomerId = 0, Int16 ProductID = 0)
        {
            string result = string.Empty;

            string url = string.Empty;
            Random generator = new Random();
            Int64 Token = generator.Next(110000, 1000000);

            CustomerAuthenticateData customerAuthenticateData = null;
            try
            {
                string encryptLeadId = Crypto.Encrytion_Payment_AES(LeadId, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings(), false);

                if (CustomerId > 0 && ProductID > 0)
                {

                    url = "FOSCreationURL".AppSettings() + "/RescheduleAppointment" + "?l=" + encryptLeadId + "&t=" + Token + "&c=" + Crypto.Encrytion_Payment_AES(Convert.ToString(CustomerId), "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings(), false) + "&p=" + ProductID;
                    customerAuthenticateData = new CustomerAuthenticateData
                    {
                        encryptLeadId = encryptLeadId,
                        Token = Token,
                        Url = url,
                        ts = DateTime.Now,
                        LeadId = LeadId
                    };

                    FOSDLL.SaveCustomerAuthenticateData(customerAuthenticateData);
                    result = url;

                    // result = GetShortLink(url, Convert.ToInt64(LeadId), ProductID, "FOS");


                }
            }

            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), ex.ToString(), "GenerateRescheduleLink", "MatrixCore", "FOSBLL", JsonConvert.SerializeObject(customerAuthenticateData), "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public static string GetShortLink(string url, long LeadID, int ProductID = 0, string LeadSource = null, int ExpireInDay = 0)
        {
            string result = string.Empty;
            if (ExpireInDay <= 0)
            {
                ExpireInDay = GetExpiryDays(ProductID, LeadSource);
            }
            string json = "{\"LongURL\":\"" + url + "\",\"Source\":\"MATRIX\",\"SubSource\":\"Core\",\"ExpireInDay\":\"" + ExpireInDay + "\",\"DefaultURL\":\"" + GetDefaultURL(ProductID, LeadSource) + "\",\"UID\":\"" + LeadID + "\",\"ProductID\":\"" + ProductID + "\",\"TenentID\":\"0\"}";
            try
            {

                string bitlyUrl = "PBShortUrl".AppSettings();
                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"app", "Matrix"},{"token", "PBShortUrlToken".AppSettings()}};


                result = CommonAPICall.CallAPI(bitlyUrl, json, "POST", Convert.ToInt32("MRSTimeout".AppSettings()), "application/json", header);

                if (!string.IsNullOrEmpty(result))
                {
                    var data = Newtonsoft.Json.JsonConvert.DeserializeObject<ShortLinkResponse>(result);
                    result = data.url;
                }
                else
                {
                    result = url;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(LeadID.ToString(), 0, ex.ToString(), "GetShortLink", "FOSBLL", "MatrixCoreAPI", json, ex.ToString(), DateTime.Now, DateTime.Now);
                result = url;
            }
            return result;
        }
        public static string GetDefaultURL(int ProductId = 0, string LeadSource = null)
        {
            if (LeadSource == "FOS")
            {
                return "FOSDefaultURL".AppSettings();
            }
            if ((ProductId == 2 || ProductId == 106 || ProductId == 118 || ProductId == 130) && CoreCommonMethods.IsValidString(LeadSource) && LeadSource.ToUpper() == "RENEWAL")
            {
                return "HealthRenewalDefaultURL".AppSettings();
            }
            if (ProductId > 0)
            {
                string url = ("DefaultURL_" + ProductId).AppSettings();
                if (!string.IsNullOrEmpty(url))
                {
                    return url;
                }
            }
            return "DefaultURL".AppSettings();
        }

        public static int GetExpiryDays(int ProductId = 0, string LeadSource = null)
        {
            if (LeadSource == "FOS")
            {
                return 15;
            }
            if ((ProductId == 2 || ProductId == 106 || ProductId == 118 || ProductId == 130) && CoreCommonMethods.IsValidString(LeadSource) && LeadSource.ToUpper() == "RENEWAL")
            {
                return 45;
            }
            return 15;
        }

        public static PriorityModel getAppPriorityModel(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            PriorityModel _PriorityModel = null;
            try
            {
                _PriorityModel = new PriorityModel()
                {
                    LeadID = oLeadsSubStatusModel.ParentID,
                    AppointmentTypeEnum = AppointmentTypeEnum.setAppointment,
                    Appointment = new AppointmentData
                    {
                        AppointmentId = oLeadsSubStatusModel.AppointmentId,
                        ScheduledOn = oLeadsSubStatusModel.AppointmentDateTime,
                        StatusID = Convert.ToInt16(oLeadsSubStatusModel.SubStatusId),
                        UpdatedOn = DateTime.Now,
                        UserID = oLeadsSubStatusModel.CreatedBy > 0 ? oLeadsSubStatusModel.CreatedBy : oLeadsSubStatusModel.UserID,
                        CreatedOn = oLeadsSubStatusModel.CreatedOn,
                        PCode = oLeadsSubStatusModel.Pincode,
                        Lat = oLeadsSubStatusModel.AppointmentLatLong != null && oLeadsSubStatusModel.AppointmentLatLong.Lat > 0 ? oLeadsSubStatusModel.AppointmentLatLong.Lat : 0,
                        Long = oLeadsSubStatusModel.AppointmentLatLong != null && oLeadsSubStatusModel.AppointmentLatLong.Long > 0 ? oLeadsSubStatusModel.AppointmentLatLong.Long : 0,
                    }
                };
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(oLeadsSubStatusModel.ParentID), ex.ToString(), "SaveAppStatusinDB", "MatrixCore", "FOSBLL", JsonConvert.SerializeObject(oLeadsSubStatusModel), "", DateTime.Now, DateTime.Now);
            }
            return _PriorityModel;

        }

        public static LeadsSubStatusModel getAppointmentData(LeadsSubStatusModel oLeadsSubStatusModel)
        {

            DataSet oDataSet = FOSDLL.getAppointmentData(oLeadsSubStatusModel.ParentID);
            if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
            {
                oLeadsSubStatusModel.CreatedOn = oDataSet.Tables[0].Rows[0]["createdon"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(oDataSet.Tables[0].Rows[0]["createdon"]);
                oLeadsSubStatusModel.AppointmentDateTime = oDataSet.Tables[0].Rows[0]["AppointmentDateTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(oDataSet.Tables[0].Rows[0]["AppointmentDateTime"]);
                oLeadsSubStatusModel.AppointmentId = oDataSet.Tables[0].Rows[0]["AppointmentId"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["AppointmentId"]);
                oLeadsSubStatusModel.Address = oDataSet.Tables[0].Rows[0]["Address"] == DBNull.Value ? String.Empty : Convert.ToString(oDataSet.Tables[0].Rows[0]["Address"]);
                oLeadsSubStatusModel.AppointmentUID = oDataSet.Tables[0].Rows[0]["AppointmentUID"] == DBNull.Value ? 0 : Convert.ToInt64(oDataSet.Tables[0].Rows[0]["AppointmentUID"]);
                //oLeadsSubStatusModel.SubStatusId = oDataSet.Tables[0].Rows[0]["SubstatusId"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["SubstatusId"]);
                oLeadsSubStatusModel.Address1 = oDataSet.Tables[0].Rows[0]["Address1"] == DBNull.Value ? String.Empty : Convert.ToString(oDataSet.Tables[0].Rows[0]["Address1"]);

                oLeadsSubStatusModel.Landmark = oDataSet.Tables[0].Rows[0]["Landmark"] == DBNull.Value ? String.Empty : Convert.ToString(oDataSet.Tables[0].Rows[0]["Landmark"]);
                oLeadsSubStatusModel.Pincode = oDataSet.Tables[0].Rows[0]["Pincode"] == DBNull.Value ? 0 : Convert.ToInt32(oDataSet.Tables[0].Rows[0]["Pincode"]);
                oLeadsSubStatusModel.IsActive = oDataSet.Tables[0].Rows[0]["IsActive"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["IsActive"]);
                oLeadsSubStatusModel.Gender = oDataSet.Tables[0].Rows[0]["Gender"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(oDataSet.Tables[0].Rows[0]["Gender"]);
                oLeadsSubStatusModel.CustomerInstructions = oDataSet.Tables[0].Rows[0]["Comments"] != null && oDataSet.Tables[0].Rows[0]["Comments"] != DBNull.Value ? Convert.ToString(oDataSet.Tables[0].Rows[0]["Comments"]) : String.Empty;
                oLeadsSubStatusModel.AppointmentLatLong = new PlaceLatLongModel()
                {
                    Lat = oDataSet.Tables[0].Rows[0]["Lat"] != null && oDataSet.Tables[0].Rows[0]["Lat"] != DBNull.Value ? Convert.ToDecimal(oDataSet.Tables[0].Rows[0]["Lat"]) : 0,
                    Long = oDataSet.Tables[0].Rows[0]["Long"] != null && oDataSet.Tables[0].Rows[0]["Long"] != DBNull.Value ? Convert.ToDecimal(oDataSet.Tables[0].Rows[0]["Long"]) : 0
                };
            }
            return oLeadsSubStatusModel;
        }

        public static bool getTrigger(long LeadId)
        {

            bool res = false;
            try
            {
                Int64 Istrigger = FOSDLL.IsTriger(LeadId);
                if (Istrigger > 0)
                    res = true;

            }
            catch (Exception ex)
            {

            }
            return res;
        }

        public SurveyInfo CheckSurveyAgent(long UserId)
        {
            SurveyInfo response = new SurveyInfo();
            CheckSurveyAgentResponse obj = new CheckSurveyAgentResponse();
            try
            {
                DataSet ds = FOSDLL.CheckSurveyAgent(UserId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    obj.Eligible = ds.Tables[0].Rows[0]["Eligible"] == DBNull.Value ? false : Convert.ToBoolean(ds.Tables[0].Rows[0]["Eligible"]);
                    obj.IsComplete = ds.Tables[0].Rows[0]["IsComplete"] == DBNull.Value ? false : Convert.ToBoolean(ds.Tables[0].Rows[0]["IsComplete"]);
                    obj.SurveyId = ds.Tables[0].Rows[0]["SurveyId"] == DBNull.Value ? 0 : Convert.ToInt64(ds.Tables[0].Rows[0]["SurveyId"]);

                    if (obj != null && obj.Eligible && obj.IsComplete == false)
                    {
                        response.SurveyId = obj.SurveyId;
                        response.Status = true;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", UserId, ex.ToString(), "CheckSurveyAgent", "MatrixCore", "FOSBLL", " ", "", DateTime.Now, DateTime.Now);
            }
            return response;
        }


        public Int16 IsUserActive(long userId)
        {
            Int16 statusCode = 0;
            try
            {
                DataSet ds = FOSDLL.IsUserActive(userId);

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count == 0)
                    statusCode = 401;

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, userId, ex.ToString(), "IsUserActive", "FOSBLL", "MatrixCore", "", "", DateTime.Now, DateTime.Now);
            }

            return statusCode;

        }

        public ResponseAPI ValidateQRCode(AppointmentsDataModel oAppointmentsDataModel)
        {
            string Error = string.Empty;
            ResponseAPI response = new() { status = false, message = "QR Code has either expired or doesn't match please try with recent QR Code" };
            try
            {
                Int64 LeadId = FOSDLL.ValidateQRCode(oAppointmentsDataModel);
                if (LeadId > 0)
                {

                    LeadsSubStatusModel oLeadsSubStatusModel = new LeadsSubStatusModel()
                    {
                        SubStatusId = (int)EnumAppSusbtatus.start,
                        AppointmentId = oAppointmentsDataModel.AppointmentId,
                        UserID = oAppointmentsDataModel.UserId,
                        ParentID = LeadId,
                        StatusId = 0, // StatusId only used for leadStatus
                        Source = oAppointmentsDataModel.Source
                    };

                    SaveInfo obj = SaveLeadsSubStatusV2(oLeadsSubStatusModel);
                    if (obj != null && obj.IsSaved)
                    {
                        response.status = obj.IsSaved;
                        response.message = "QRCode Successfully Validated";
                    }

                }

            }
            catch (Exception ex)
            {
                Error = ex.ToString();
                //LoggingHelper.LoggingHelper.AddloginQueue(null, oAppointmentsDataModel.AppointmentId, ex.ToString(), "ValidateQRCode", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(oAppointmentsDataModel), "", DateTime.Now, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, oAppointmentsDataModel.AppointmentId, Error, "ValidateQRCode", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(oAppointmentsDataModel), JsonConvert.SerializeObject(response), DateTime.Now, DateTime.Now);
            }
            return response;

        }

        public QRCodeModel GenerateQRCode(string encryptAppointmentId)
        {
            string AppointmentId = String.Empty;
            string Error = string.Empty;

            QRCodeModel oQRCodeModel = new QRCodeModel() { message = "AppointmentId is not valid", isSuccess = false };
            try
            {
                Random generator = new Random();
                Int64 Token = generator.Next(110000, 1000000);
                string generatedCode = Token + DateTime.Now.ToString("yyyyMMddHHmmss");
                string message = String.Empty;

                AppointmentId = Crypto.Decrytion_Payment_AES(encryptAppointmentId, "Core", 256, 128, "QREncKey".AppSettings(), "QRivKey".AppSettings());


                if (CoreCommonMethods.IsValidInteger(AppointmentId) > 0)
                {
                    DataSet ds = FOSDLL.GenerateQRCode(Convert.ToInt64(AppointmentId), generatedCode);

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {

                        Int32 ErrorCode = (ds.Tables[0].Rows[0]["ErrorCode"] == null || ds.Tables[0].Rows[0]["ErrorCode"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["ErrorCode"]));


                        if (ErrorCode < 0 && dictionaries.QRErrorCodeMapping.ContainsKey(ErrorCode))
                            message = dictionaries.QRErrorCodeMapping[ErrorCode];

                        if (String.IsNullOrEmpty(message))
                        {
                            oQRCodeModel = new QRCodeModel()
                            {
                                QRCode = ds.Tables[0].Rows[0]["QrCode"] == DBNull.Value ? string.Empty : Convert.ToString(ds.Tables[0].Rows[0]["QrCode"]),
                                PendingTime = ds.Tables[0].Rows[0]["PendingTime"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["PendingTime"]),
                                message = "",
                                isSuccess = true
                            };
                        }
                        else
                        {
                            oQRCodeModel.message = message;
                        }

                        //oQRCodeModel.ServerTime = DateTime.Now;
                        //sendcommunicationResponse oSendcommunicationResponse = PrepareModel(AppointmentId); ;
                        //FOSDLL.SendCommunication(oSendcommunicationResponse);
                    }
                }

            }
            catch (Exception ex)
            {
                Error = ex.ToString();
                //LoggingHelper.LoggingHelper.AddloginQueue(null, oAppointmentsDataModel.AppointmentId, ex.ToString(), "ValidateQRCode", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(oAppointmentsDataModel), "", DateTime.Now, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AppointmentId, 0, Error, "GenerateQRCode", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(encryptAppointmentId), JsonConvert.SerializeObject(oQRCodeModel), DateTime.Now, DateTime.Now);
            }

            return oQRCodeModel;

        }

        public static bool CompareString(string input1, string input2)
        {
            bool result = false;
            if (!string.IsNullOrEmpty(input1) && !string.IsNullOrEmpty(input2) && input1.Trim().ToLower() == input2.Trim().ToLower())
                result = true;
            return result;

        }
        public static bool CompareInterger(Int32 input1, Int32 input2)
        {
            bool result = false;
            if (input1 > 0 && input2 > 0 && input1 == input2)
                result = true;
            return result;


        }
        public void SetAppointmentHistory(LeadsSubStatusModel responseLeadsSubStatusModel, AppointmentsDataModel reqAppointmentData)
        {

            try
            {
                bool updateLoghistory = false;

                //In case of app new app handle 
                //if (reqAppointmentData.Source.ToLower().Trim() == "fosapp" && reqAppointmentData.AppointmentId != responseLeadsSubStatusModel.AppointmentId)
                //  responseLeadsSubStatusModel.SubStatusId = Convert.ToInt16(EnumAppSusbtatus.Booked);

                //In case of booked take event new appointment
                if (reqAppointmentData.AppointmentId != responseLeadsSubStatusModel.AppointmentId)
                {
                    responseLeadsSubStatusModel.EventId = Convert.ToInt16(EnumAppEvents.New_Appointment);
                    updateLoghistory = true;
                }
                //Chk address change or not 
                else if ((reqAppointmentData.AppointmentId == responseLeadsSubStatusModel.AppointmentId)
                    && (!CompareString(responseLeadsSubStatusModel.Address, reqAppointmentData.Address)
                    || !CompareString(responseLeadsSubStatusModel.Landmark, reqAppointmentData.Landmark))
                  )
                {
                    responseLeadsSubStatusModel.EventId = Convert.ToInt16(EnumAppEvents.Address_Change);
                    updateLoghistory = true;
                }



                if (updateLoghistory)
                {
                    responseLeadsSubStatusModel.Comments = "Appointment Address" + ": " + reqAppointmentData.Address + " " + reqAppointmentData.Address1 + " " + reqAppointmentData.Landmark + " " + reqAppointmentData.Pincode;
                    responseLeadsSubStatusModel.AppointmentDateTime = reqAppointmentData.AppointmentDateTime;
                    responseLeadsSubStatusModel.AppointmentId = reqAppointmentData.AppointmentId;
                    long Id = FOSDLL.SaveAppointmentHistory(responseLeadsSubStatusModel);



                    if (Id > 0)
                        CallAddressVerfiedAPI(Id, reqAppointmentData);
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, reqAppointmentData.ParentId, ex.ToString(), "SetAppointmentHistory", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(responseLeadsSubStatusModel), "", DateTime.Now, DateTime.Now);
            }
        }
        public static AppointmentsDataModel UpdateAppointmentAssignmentCity(AppointmentsDataModel objAppointmentData, SaveInfo oSaveInfo)
        {
            List<string> AssignmentSources = "AssignmentSources".AppSettings().Split(',').ToList();

            string source = objAppointmentData.Source.ToLower();
            source = !string.IsNullOrEmpty(source) && AssignmentSources.Contains(source) ? source : (CoreCommonMethods.IsValidInteger(Convert.ToString(objAppointmentData.AssignmentId)) > 0 ? "" : "default");
            if (source != "")
            {
                DataSet ds = FOSDLL.GetProductSourceAssignmentMapping(source, Convert.ToInt16(objAppointmentData.VirtualProductId), objAppointmentData.OfflineCityId, objAppointmentData.Pincode);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    objAppointmentData.AssignmentId = ds.Tables[0].Rows[0]["AssignmentId"] != null && ds.Tables[0].Rows[0]["AssignmentId"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["AssignmentId"]) : 0;
                    objAppointmentData.OfflineCityId = ds.Tables[0].Rows[0]["CityId"] != null && ds.Tables[0].Rows[0]["CityId"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["CityId"]) : 0; ;
                }
                else
                {
                    oSaveInfo.StatusCode = 501;
                    oSaveInfo.Message = "The city or Pincode is not serviceable";
                }
            }

            return objAppointmentData;

        }

        public static SaveInfo ValidateSaveAppointmentData(PriorityModel respPriorityModel, LeadsSubStatusModel responseLeadsSubStatusModel, AppointmentsDataModel objAppointmentData, Int16 AgentProcessId, SaveInfo oSaveInfo)
        {
            string msg = string.Empty;


            if (objAppointmentData != null && objAppointmentData.SlotId == 7)
            {
                oSaveInfo.Message = ValidateFemaleCustomer(responseLeadsSubStatusModel, objAppointmentData);

                if (string.IsNullOrEmpty(oSaveInfo.Message))
                    oSaveInfo.Message = ValidateFemaleAgent(responseLeadsSubStatusModel, objAppointmentData, AgentProcessId);

            }
            if ("IsDNCApplicable".AppSettings() == "true" && string.IsNullOrEmpty(oSaveInfo.Message) && objAppointmentData.Source?.ToLower() == "matrix" && respPriorityModel != null && respPriorityModel.DNC != null && respPriorityModel.DNC.ts != DateTime.MinValue && DateTime.Now.Subtract(respPriorityModel.DNC.ts).Days <= respPriorityModel.DNC.CoolingPeriod)
            {
                oSaveInfo.Message = "This customer is on the Do Not Call (DNC) list.";
            }
            if (string.IsNullOrEmpty(oSaveInfo.Message))//check city available or not
            {
                oSaveInfo.Message = ValidatePrdCityConfigured(objAppointmentData);
                if (!string.IsNullOrEmpty(oSaveInfo.Message))
                    oSaveInfo.StatusCode = 501;// Not Implemented;
            }

            return oSaveInfo;
        }

        public static string ValidateFemaleCustomer(LeadsSubStatusModel responseLeadsSubStatusModel, AppointmentsDataModel objAppointmentData)
        {
            string msg = string.Empty;
            if (responseLeadsSubStatusModel != null && objAppointmentData != null && objAppointmentData.SlotId == 7 &&
                ((responseLeadsSubStatusModel.IsActive == 0 && objAppointmentData.Gender == 2)
                || (responseLeadsSubStatusModel.IsActive == 1 && responseLeadsSubStatusModel.AppointmentId > 0 && responseLeadsSubStatusModel.Gender == 2))
                )
                msg = "Appointment cannot be scheduled for this slot, please select another slot";
            return msg;


        }
        public static string ValidateFemaleAgent(LeadsSubStatusModel responseLeadsSubStatusModel, AppointmentsDataModel objAppointmentData, Int16 AgentProcessId)
        {
            string msg = string.Empty;
            if (responseLeadsSubStatusModel != null && objAppointmentData != null && objAppointmentData.SlotId == 7)
            {
                bool res = responseLeadsSubStatusModel.IsActive == 0 ? RestrictNewAppointment(objAppointmentData, AgentProcessId) : RestrictOldAppointment(objAppointmentData);
                msg = res ? "Appointment cannot be scheduled for this slot, please select another slot." : "";
            }
            return msg;
        }

        public static bool RestrictNewAppointment(AppointmentsDataModel objAppointmentData, Int16 AgentProcessId)
        {
            bool res = false;
            StringBuilder sb = new();
            string exception = string.Empty;
            try
            {
                bool IsProcessRestricted = CheckAgentRestriction(objAppointmentData, AgentProcessId);
                sb.Append("AgentProcessId " + AgentProcessId + "\r\n");
                sb.Append("IsProcessRestricted " + IsProcessRestricted + "\r\n");

                if (IsProcessRestricted) // for new appointment check if restrictions are available or not
                {
                    DataSet ds = FOSDLL.GetUserBasicDetails(objAppointmentData.UserId);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        Int16 Gender = ds.Tables[0].Rows[0]["Gender"] != null && ds.Tables[0].Rows[0]["Gender"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["Gender"]) : Convert.ToInt16(0);
                        sb.Append(" Agent's Gender: " + Gender + "\r\n");
                        res = (EnumGender)Gender == EnumGender.Female ? true : false;  // check for Female Gender
                        return res;
                    }
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
                //LoggingHelper.LoggingHelper.AddloginQueue(null, objAppointmentData.ParentId, ex.ToString(), "RestrictNewAppointment", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(objAppointmentData), sb.ToString(), DateTime.Now, DateTime.Now);

            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, objAppointmentData.ParentId, exception, "RestrictNewAppointment", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(objAppointmentData), sb.ToString(), DateTime.Now, DateTime.Now);
            }
            return res;
        }

        public static bool RestrictOldAppointment(AppointmentsDataModel objAppointmentData)
        {
            bool res = false;
            StringBuilder sb = new();
            string exception = string.Empty;
            try
            {
                DataSet ds = FOSDLL.GetLeadAssignDetails(objAppointmentData.ParentId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    Int64 AssignedToUserID = ds.Tables[0].Rows[0]["AssignedToUserID"] != null && ds.Tables[0].Rows[0]["AssignedToUserID"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["AssignedToUserID"]) : Convert.ToInt64(0);
                    sb.Append("AssignedToUserID: " + AssignedToUserID + "\r\n");
                    if (AssignedToUserID > Convert.ToInt64(0)) // If the lead is assigned to any user or not
                    {
                        DataSet os = FOSDLL.GetUserBasicDetails(AssignedToUserID);
                        if (os != null && os.Tables.Count > 0 && os.Tables[0].Rows.Count > 0)
                        {
                            Int16 Gender = os.Tables[0].Rows[0]["Gender"] != null && os.Tables[0].Rows[0]["Gender"] != DBNull.Value ? Convert.ToInt16(os.Tables[0].Rows[0]["Gender"]) : Convert.ToInt16(0);
                            sb.Append(" Agent's Gender: " + Gender + "\r\n");
                            res = (EnumGender)Gender == EnumGender.Female ? true : false;
                            return res;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                exception = ex.ToString();
                // LoggingHelper.LoggingHelper.AddloginQueue(null, objAppointmentData.ParentId, ex.ToString(), "RestrictOldAppointment", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(objAppointmentData), sb.ToString(), DateTime.Now, DateTime.Now);
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, objAppointmentData.ParentId, exception, "RestrictOldAppointment", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(objAppointmentData), sb.ToString(), DateTime.Now, DateTime.Now);
            }
            return res;

        }
        public static bool CheckAgentRestriction(AppointmentsDataModel objAppointmentData, Int16 AgentProcessId)
        {
            bool res = false;
            if ((EnumFOSUserProcessId)AgentProcessId == EnumFOSUserProcessId.CallingAgent || (EnumFOSUserProcessId)AgentProcessId == EnumFOSUserProcessId.AdminOrTL)
            {
                res = false;
            }
            else if ((EnumFOSUserProcessId)AgentProcessId == EnumFOSUserProcessId.Fos && ((EnumAssignmentType)objAppointmentData.AssignmentId == EnumAssignmentType.DedicatedFOS) || (EnumAssignmentType)objAppointmentData.AssignmentId == EnumAssignmentType.HealthDedicatedFOS) // Dedicated or fos
            {
                res = true;
            }
            else if ((EnumFOSUserProcessId)AgentProcessId == EnumFOSUserProcessId.store && (EnumAssignmentType)objAppointmentData.AssignmentId == EnumAssignmentType.StoreFOS) // Store 
            {
                res = true;
            }
            else if ((EnumFOSUserProcessId)AgentProcessId == EnumFOSUserProcessId.Self && (EnumAssignmentType)objAppointmentData.AssignmentId == EnumAssignmentType.Self) //Self
            {
                res = true;
            }

            return res;
        }
        public void updateAppInMongo(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            oLeadsSubStatusModel = getAppointmentData(oLeadsSubStatusModel);

            PriorityModel AppPriorityModel = getAppPriorityModel(oLeadsSubStatusModel);
            if (AppPriorityModel != null && AppPriorityModel.Appointment != null && AppPriorityModel.Appointment.AppointmentId > 0)
                UpdateLeadPriorityModel(AppPriorityModel); //--- Update in  Mongo 
        }


        public static string ValidatePrdCityConfigured(AppointmentsDataModel objAppointmentData)
        {
            string msg = string.Empty;
            DateTime dt = DateTime.Now;
            try
            {
                if (objAppointmentData.Source.ToLower() == "pbwebsite")
                {
                    List<FOSCityMaster> fOSCityMaster = GetFOSCityPrdMaster();
                    if (fOSCityMaster.Count > 0 && !(fOSCityMaster.Where(x => x.ProductId == objAppointmentData.ProductId && x.CityId == objAppointmentData.OfflineCityId).ToList().Count() > 0))
                        msg = "We don't have presence in your city";

                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, objAppointmentData.ParentId, ex.ToString(), "ValidatePrdCityConfigured", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(objAppointmentData), "", dt, DateTime.Now);
            }
            return msg;


        }

        public List<AppointmentsDataModel> GetAppointmentSummary(string InputParm, Int16 Type, Int16 NoOfDays)
        {
            List<AppointmentsDataModel> lstAppointmentsDataModel = null;

            try
            {
                NoOfDays = (short)(NoOfDays == 0 ? 15 : NoOfDays);//by default value
                DataSet oDataSet = FOSDLL.GetAppointmentSummary(InputParm, Type, @NoOfDays);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    lstAppointmentsDataModel = new List<AppointmentsDataModel>();
                    lstAppointmentsDataModel = (from dr in oDataSet.Tables[0].AsEnumerable()
                                                select new AppointmentsDataModel
                                                {
                                                    AppointmentId = dr["AppointmentId"] != null && dr["AppointmentId"] != DBNull.Value ? Convert.ToInt64(dr["AppointmentId"]) : 0,
                                                    LeadID = dr["LeadID"] != null && dr["LeadID"] != DBNull.Value ? Convert.ToInt64(dr["LeadID"]) : 0,
                                                    AssignTo = dr["AssignTo"] != null && dr["AssignTo"] != DBNull.Value ? Convert.ToInt64(dr["AssignTo"]) : 0,
                                                    Address = dr["Address"] != null && dr["Address"] != DBNull.Value ? Convert.ToString(dr["Address"]) : string.Empty,
                                                    Address1 = dr["Address1"] != null && dr["Address1"] != DBNull.Value ? Convert.ToString(dr["Address1"]) : string.Empty,
                                                    Landmark = dr["Landmark"] != null && dr["Landmark"] != DBNull.Value ? Convert.ToString(dr["Landmark"]) : string.Empty,
                                                    Pincode = dr["Pincode"] != null && dr["Pincode"] != DBNull.Value ? Convert.ToInt32(dr["Pincode"]) : 0,
                                                    flag = dr["flag"] != null && dr["flag"] != DBNull.Value && Convert.ToBoolean(dr["flag"]),
                                                    FirstReporting = dr["FirstReporting"] != null && dr["FirstReporting"] != DBNull.Value ? Convert.ToInt64(dr["FirstReporting"]) : 0,
                                                    SecondReporting = dr["SecondReporting"] != null && dr["SecondReporting"] != DBNull.Value ? Convert.ToInt64(dr["SecondReporting"]) : 0,
                                                    ThirdReporting = dr["ThirdReporting"] != null && dr["ThirdReporting"] != DBNull.Value ? Convert.ToInt64(dr["ThirdReporting"]) : 0,
                                                    ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                                                    ProductName = dr["ProductName"] != null && dr["ProductName"] != DBNull.Value ? Convert.ToString(dr["ProductName"]) : string.Empty,
                                                    FirstReportingValue = dr["FirstReportingValue"] != null && dr["FirstReportingValue"] != DBNull.Value ? Convert.ToString(dr["FirstReportingValue"]) : string.Empty,
                                                    SecondReportingValue = dr["SecondReportingValue"] != null && dr["SecondReportingValue"] != DBNull.Value ? Convert.ToString(dr["SecondReportingValue"]) : string.Empty,
                                                    ThirdReportingValue = dr["ThirdReportingValue"] != null && dr["ThirdReportingValue"] != DBNull.Value ? Convert.ToString(dr["ThirdReportingValue"]) : string.Empty,
                                                    EmployeeId = dr["AssignToValue"] != null && dr["AssignToValue"] != DBNull.Value ? Convert.ToString(dr["AssignToValue"]) : string.Empty,
                                                    AppointmentDateTime = dr["AppointmentDateTime"] != null && dr["AppointmentDateTime"] != DBNull.Value ? Convert.ToDateTime(dr["AppointmentDateTime"]) : DateTime.MinValue,
                                                    LinkedLeadId = dr["LinkedLeadId"] != null && dr["LinkedLeadId"] != DBNull.Value ? Convert.ToInt64(dr["LinkedLeadId"]) : 0,
                                                    GenderName = dr["Gender"] != null && dr["Gender"] != DBNull.Value ? Convert.ToString(dr["Gender"]) : default,
                                                    Status = dr["SubStatusName"] != null && dr["SubStatusName"] != DBNull.Value ? Convert.ToString(dr["SubStatusName"]) : default,
                                                    CustomerID = dr["CustomerID"] != null && dr["CustomerID"] != DBNull.Value ? Convert.ToInt64(dr["CustomerID"]) : 0,
                                                    ReferralID = dr["ReferralID"] != null && dr["ReferralID"] != DBNull.Value ? Convert.ToInt64(dr["ReferralID"]) : 0
                                                }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(InputParm), ex.ToString(), "GetAppointmentSummary", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(lstAppointmentsDataModel), JsonConvert.SerializeObject(Type), DateTime.Now, DateTime.Now);
            }
            return lstAppointmentsDataModel;
        }

        public static List<FOSCityMaster> GetFOSCityPrdMaster()
        {
            List<FOSCityMaster> fOSCityPrdMaster = null;
            DateTime dt = DateTime.Now;
            try
            {
                string Key = $"{RedisCollection.FOSCityPrdMaster()}";

                if (MemoryCache.Default[Key] != null)
                    fOSCityPrdMaster = (List<FOSCityMaster>)(MemoryCache.Default.Get(Key));
                else
                {
                    DataSet data = FOSDLL.GetFosCityMaster();

                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        fOSCityPrdMaster = (from dr in data.Tables[0].AsEnumerable()
                                            select new FOSCityMaster
                                            {
                                                CityId = dr["CityId"] != null && dr["CityId"] != DBNull.Value ? Convert.ToInt32(dr["CityId"]) : 0,
                                                ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt32(dr["ProductId"]) : 0,
                                            }).ToList();

                        if (fOSCityPrdMaster.Count > 0)
                            CommonCache.GetOrInsertIntoCache(fOSCityPrdMaster, Key, 4 * 60);
                    }
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetFOSCityPrdMaster", "FOSBLL", "MatrixCore", "", "", dt, DateTime.Now);

            }
            return fOSCityPrdMaster;

        }

        public List<PriorityModel> GetAgentAssignedLeads(Int64 UserID, Int16 NoOfDays)
        {
            List<PriorityModel> lsAssignedLead = null;
            try
            {
                DataSet oDataSet = FOSDLL.GetAgentAssignedLeads(UserID, NoOfDays);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    lsAssignedLead = new List<PriorityModel>();
                    lsAssignedLead = (from dr in oDataSet.Tables[0].AsEnumerable()
                                      select new PriorityModel
                                      {
                                          LeadID = dr.Field<Int64>("LeadID"),
                                          CustID = dr.Field<Int64>("CustomerID"),
                                          CustName = dr.Field<string>("Name"),
                                          LeadCreatedOn = dr.Field<DateTime>("LeadCreatedOn"),
                                          LeadStatus = new LeadStatusData() { Status = dr.Field<string>("StatusName") },
                                          ProductID = dr.Field<byte>("ProductID")
                                      }).ToList();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, UserID, ex.ToString(), "GetAgentAssignedLeads", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(UserID), string.Empty, DateTime.Now, DateTime.Now);
            }
            return lsAssignedLead;

        }

        public Envelope<bool> LinkAppointment(LinkAppointmentRequest request, string agentId)
        {
            var response = new Envelope<bool>();
            try
            {
                if (request != null && request.LeadId > 0 && request.ApptId > 0)
                {
                    FOSDLL.LinkAppointment(request, agentId);
                    response.Data = true;
                }
                else
                {
                    response.Error = "Invalid inputs";
                }
            }
            catch (Exception ex)
            {
                response.Error = "Error occured";
                LoggingHelper.LoggingHelper.AddloginQueue(null, request.LeadId, ex.ToString(), "LinkAppointment", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(request), string.Empty, DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static List<Int32> GetAssignmentAvailableList(DataSet oDataSet)
        {
            List<Int32> AssignmentAvailableList = new List<Int32>();
            if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow row in oDataSet.Tables[0].Rows)
                {
                    AssignmentAvailableList.Add(Convert.ToInt32(row["AssignmentId"]));
                }
            }
            return AssignmentAvailableList;

        }

        public PlaceLatLongModel GetLatLongByPlaceId(string PlaceId)
        {
            PlaceLatLongModel placeLatLongModel = new PlaceLatLongModel();
            try
            {
                List<PlaceLatLongModel> lstPlaceLatLongMstr = GetPlaceLatLongMaster();

                if (lstPlaceLatLongMstr != null && lstPlaceLatLongMstr.Count > 0)
                {
                    var IsPlaceIdExist = lstPlaceLatLongMstr.Where(x => x.place_id == PlaceId).FirstOrDefault();
                    if (IsPlaceIdExist != null)
                    {
                        placeLatLongModel.place_id = IsPlaceIdExist.place_id;
                        placeLatLongModel.Lat = IsPlaceIdExist.Lat;
                        placeLatLongModel.Long = IsPlaceIdExist.Long;
                    }
                    else
                    {
                        placeLatLongModel = CallGoogleDetailAPI(PlaceId);
                        //------------------ Save in Cache and DB PlaceMaster -----------------------//
                        if (placeLatLongModel.Lat > 0 && placeLatLongModel.Long > 0)
                        {
                            string PlaceMasterKey = $"{RedisCollection.PlaceIdMaster()}";
                            lstPlaceLatLongMstr.Add(placeLatLongModel);
                            FOSDLL.SavePlacecodeLatLongMaster(placeLatLongModel);
                        }
                    }
                }
                return placeLatLongModel;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(PlaceId, 0, ex.ToString(), "SavePlacecodeLatLongMapping", "FOSBLL", "FOSBLL", JsonConvert.SerializeObject(PlaceId), "", DateTime.Now, DateTime.Now);
                return placeLatLongModel;
            }
        }

        public static sendcommunicationResponse SendCommunicationToCustNew(LeadsSubStatusModel _leadsSubStatusModel)
        {
            DateTime requestTime = DateTime.Now;
            string SMSURL = string.Empty;
            string WhatsappURL = string.Empty;
            sendcommunicationResponse osendcommunicationResponse = null;
            try
            {
                DataSet ds = FOSDLL.GetLeadDetails(Convert.ToInt64(_leadsSubStatusModel.ParentID));

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    string MobileNo = ds.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]);
                    string Name = ds.Tables[0].Rows[0]["Name"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["Name"]);
                    string ProductName = ds.Tables[0].Rows[0]["ProductDisplayName"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["ProductDisplayName"]);
                    string CustomerID = ds.Tables[0].Rows[0]["CustomerID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["CustomerID"]);
                    string ProductID = ds.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["ProductID"]);
                    string LatestAppId = ds.Tables[0].Rows[0]["LatestAppId"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["LatestAppId"]);
                    string LatestAppUID = ds.Tables[0].Rows[0]["LatestAppUID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["LatestAppUID"]);
                    _leadsSubStatusModel.AppointmentDateTime = ds.Tables[0].Rows[0]["LatestAppDateTime"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(ds.Tables[0].Rows[0]["LatestAppDateTime"]);
                    if (CoreCommonMethods.IsValidString(MobileNo) && _leadsSubStatusModel.AppointmentDateTime != DateTime.MinValue)
                    {
                        string URL = GenerateRescheduleLinkNew(Convert.ToString(_leadsSubStatusModel.ParentID), Convert.ToInt64(CustomerID), Convert.ToInt16(ProductID));
                        if (!string.IsNullOrEmpty(URL))
                        {
                            SMSURL = GetShortLink("FOSCustomerCreationURL".AppSettings() + URL + "&src=customersms", _leadsSubStatusModel.ParentID, Convert.ToInt16(ProductID), "FOS");
                            WhatsappURL = _leadsSubStatusModel.SubStatusId == 2002 ? URL + "&src=customerwhatsapp" : GetShortLink("FOSCustomerCreationURL".AppSettings() + URL + "&src=customerwhatsapp", _leadsSubStatusModel.ParentID, Convert.ToInt16(ProductID), "FOS");
                        }
                        osendcommunicationResponse = new sendcommunicationResponse()
                        {
                            LeadId = _leadsSubStatusModel.ParentID,
                            MobileNo = Convert.ToInt64(MobileNo),
                            ProductId = Convert.ToInt32(ProductID),
                            SMSURL = SMSURL,
                            WhatsappURL = WhatsappURL,
                            InputData = new Inputdata()
                            {
                                ProductName = FOSDLL.getProductName(Convert.ToInt32(ProductID)),//ProductName,
                                CustomerName = Name,
                                AgentName = string.Empty,
                                CustomerAddress = _leadsSubStatusModel.Address + " " + _leadsSubStatusModel.Address1 + " " + _leadsSubStatusModel.Landmark + " " + _leadsSubStatusModel.Pincode,
                                AppointmentDateTime = _leadsSubStatusModel.AppointmentDateTime.ToString("dd-MMM-yyyy hh:mm tt"),
                                AppointmentId = Convert.ToString(LatestAppId),
                                AppointmentUID = Convert.ToString(LatestAppUID),
                                AppointmentSubStatusID = ""
                            }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", _leadsSubStatusModel.ParentID, ex.ToString(), "SendCommunicationToCustNew", "MatrixCore", "FOSBLL", string.Empty, string.Empty, requestTime, DateTime.Now);
            }
            return osendcommunicationResponse;
        }
        public static string GenerateRescheduleLinkNew(string LeadId, long CustomerId = 0, Int16 ProductID = 0, string Source = "")
        {
            string result = string.Empty;
            string url = string.Empty;
            string routeUrl = string.Empty;
            Random generator = new Random();
            Int64 Token = generator.Next(110000, 1000000);
            CustomerAuthenticateData customerAuthenticateData = null;
            try
            {
                string encryptLeadId = Crypto.Encrytion_Payment_AES(LeadId, "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings(), false);
                if (CustomerId > 0 && ProductID > 0)
                {
                    //   routeUrl = !string.IsNullOrEmpty(Source) && Source.ToLower() == "hyperlocal" ? "/RescheduleAppointmentV2" : "/RescheduleAppointment";
                    routeUrl = GetRouteURL(Convert.ToInt64(LeadId), Source);
                    url = "FOSCustomerMidURL".AppSettings() + routeUrl + "?l=" + encryptLeadId + "&t=" + Token + "&c=" + Crypto.Encrytion_Payment_AES(Convert.ToString(CustomerId), "Core", 256, 128, "encKey".AppSettings(), "ivKey".AppSettings(), false) + "&p=" + ProductID;
                    customerAuthenticateData = new CustomerAuthenticateData
                    {
                        encryptLeadId = encryptLeadId,
                        Token = Token,
                        Url = "FOSCustomerCreationURL".AppSettings() + url,
                        ts = DateTime.Now,
                        LeadId = LeadId
                    };
                    FOSDLL.SaveCustomerAuthenticateData(customerAuthenticateData);
                    result = url;
                    // result = GetShortLink(url, Convert.ToInt64(LeadId), ProductID, "FOS");
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), ex.ToString(), "GenerateRescheduleLinkNew", "MatrixCore", "FOSBLL", JsonConvert.SerializeObject(customerAuthenticateData), "", DateTime.Now, DateTime.Now);
            }
            return result;
        }

        public async void CallAddressVerfiedAPI(long Id, AppointmentsDataModel reqAppointmentData)
        {
            string result = string.Empty;
            DateTime requestTime = DateTime.Now;
            string err = string.Empty;
            try
            {
                await Task.Run(() =>
                 {
                     string url = "AddressVerifiedURL".AppSettings() + "get_address_completness";
                     Dictionary<object, object> header = new();
                     header.Add("x-access-token", "AddressVerifiedKey".AppSettings());

                     string dataToPost = "{\"address_list\": [{\"address\":\"" + reqAppointmentData.Address + "\",\"Id\": " + Id + "}]}";

                     result = CommonAPICall.CallAPI(url, dataToPost, "POST", Convert.ToInt32("500"), "application/json", header);
                 });
            }
            catch (Exception ex)
            {
                err = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Id, err, "CallAddressVerfiedAPI", "MatrixCore", "FOSBLL", string.Empty, result, requestTime, DateTime.Now);
            }
        }
        public ResponseData<string> SaveCustomerLocationData(CustomerLocationModel customerLocationObj)
        {
            ResponseData<string> result = new() { Status = false, Message = "Invalid Inputs" };
            DateTime dt = DateTime.Now;
            try
            {
                bool res = FOSDLL.SaveCustomerLocationData(customerLocationObj);
                result.Status = true;
                result.Message = "Save Successfully";
            }
            catch (Exception ex)
            {
                result.Status = false;
                result.Message = "Something went wrong";
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(customerLocationObj.LeadId), ex.ToString(), "SaveCustomerLocationData", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(customerLocationObj), "", dt, DateTime.Now);
            }
            return result;
        }

        public ResponseData<string> TriggerSaveCustomerLocationData(CustomerLocationModel customerLocationObj)
        {
            ResponseData<string> result = new() { Status = false, Message = "Invalid Inputs" };
            DateTime dt = DateTime.Now;
            try
            {
                bool res = FOSDLL.SaveCustomerLocationData(customerLocationObj);
                sendcommunicationResponse oSendcommunicationResponse = SendManualDropLocation(customerLocationObj);
                result.Status = true;
                result.Message = "Save Successfully";
            }
            catch (Exception ex)
            {
                result.Status = false;
                result.Message = "Something went wrong";
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(customerLocationObj.LeadId), ex.ToString(), "TriggerSaveCustomerLocationData", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(customerLocationObj), "", dt, DateTime.Now);
            }
            return result;
        }

        public ResponseData<CustomerLocationModel> GetCustomerLocationData(AppointmentsDataModel objAppointmentData)
        {
            DateTime dt = DateTime.Now;
            ResponseData<CustomerLocationModel> res = new ResponseData<CustomerLocationModel>() { Status = false, Message = "" };
            CustomerLocationModel customerLocationModel = new();
            try
            {
                DataSet ds = FOSDLL.GetCustomerLocationData(Convert.ToInt64(objAppointmentData.ParentId));
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    customerLocationModel.CityId = ds.Tables[0].Rows[0]["CityID"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["CityID"]);
                    customerLocationModel.City = ds.Tables[0].Rows[0]["City"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["City"]);
                    customerLocationModel.CountryId = ds.Tables[0].Rows[0]["CountryId"] == DBNull.Value ? Convert.ToInt64(0) : Convert.ToInt64(ds.Tables[0].Rows[0]["CountryId"]);
                    customerLocationModel.PlaceId = ds.Tables[0].Rows[0]["PlaceId"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["PlaceId"]);
                    customerLocationModel.Landmark = ds.Tables[0].Rows[0]["Landmark"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["Landmark"]);
                    customerLocationModel.Address = ds.Tables[0].Rows[0]["Address"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["Address"]);
                    customerLocationModel.Pincode = ds.Tables[0].Rows[0]["Pincode"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["Pincode"]);
                    customerLocationModel.Lat = ds.Tables[0].Rows[0]["lat"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(ds.Tables[0].Rows[0]["lat"]);
                    customerLocationModel.Long = ds.Tables[0].Rows[0]["long"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(ds.Tables[0].Rows[0]["long"]);
                    customerLocationModel.Gender = ds.Tables[0].Rows[0]["Gender"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["Gender"]);
                    res.Data = customerLocationModel;
                    res.Status = true;
                    res.Message = "Success";
                }
            }
            catch (Exception ex)
            {
                res.Status = false;
                res.Message = "Error";
                res.Data = null;
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(objAppointmentData.ParentId), ex.ToString(), "GetCustomerLocationData", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(customerLocationModel), dt, DateTime.Now);
            }

            return res;

        }

        public static sendcommunicationResponse SendManualDropLocation(CustomerLocationModel customerLocationObj)
        {
            sendcommunicationResponse oSendcommunicationResponse = null;
            string SMSURL = string.Empty;
            string WhatsappURL = string.Empty;
            //Int16 GroupId = 0;
            DataSet ds = FOSDLL.GetLeadBasicInfo(customerLocationObj.LeadId);
            //DataSet UserDs = FOSDLL.GetGroupsByUserId(customerLocationObj.CreatedBy);
            //if (UserDs != null && UserDs.Tables.Count > 0 && UserDs.Tables[0].Rows.Count > 0)
            //{
            //    GroupId = UserDs.Tables[0].Rows[0]["GroupId"] != null && UserDs.Tables[0].Rows[0]["GroupId"] != DBNull.Value ? Convert.ToInt16(UserDs.Tables[0].Rows[0]["GroupId"]) : Convert.ToInt16(0);
            //}
            //List<Int16> WhatsAppLocationGroups = "WhatsAppLocationGroups".AppSettings().Split(',').Select(Int16.Parse).ToList();
            List<Int32> WhatsAppLocationProductId = "WhatsAppLocationProductIds".AppSettings().Split(',').Select(Int32.Parse).ToList();
            string SMSTrigger = "ALL_MTX_Sms_DropLocation";
            string WhatsAppTrigger = "ALL_MTX_WA_Manual_Drop_Location_Trigger";

            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                string MobileNo = ds.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]);
                string ProductID = ds.Tables[0].Rows[0]["ProductID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["ProductID"]);
                string CustomerID = ds.Tables[0].Rows[0]["CustomerID"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["CustomerID"]);
                Int32 CountryCode = ds.Tables[0].Rows[0]["CountryCode"] == DBNull.Value ? 91 : Convert.ToInt32(ds.Tables[0].Rows[0]["CountryCode"]);

                if (CoreCommonMethods.IsValidString(MobileNo))
                {
                    if (WhatsAppLocationProductId.Contains(Convert.ToInt32(ProductID)))
                    {
                        WhatsAppTrigger = "ALL_MTX_WA_Manual_Drop_Location_WhatsApp_Native_Tr";
                    }
                    string URL = GenerateRescheduleLinkNew(Convert.ToString(customerLocationObj.LeadId), Convert.ToInt64(CustomerID), Convert.ToInt16(ProductID), customerLocationObj.Source);
                    if (!string.IsNullOrEmpty(URL) && !string.IsNullOrEmpty(customerLocationObj.Source) && customerLocationObj.Source.ToLower() == "hyperlocal")
                    {
                        URL = URL + "&TriggeredSource=hyperlocal";
                        SMSTrigger = "ALL_MTX_Sms_fos_appointment_creation";
                        WhatsAppTrigger = "ALL_MTX_WA_fos_appointment_creation";
                    }
                    if (!string.IsNullOrEmpty(URL))
                    {
                        SMSURL = GetShortLink("FOSCustomerCreationURL".AppSettings() + URL + "&src=customersms", customerLocationObj.LeadId, Convert.ToInt16(ProductID), "FOS");
                        WhatsappURL = URL + "&src=customerwhatsapp";
                    }

                    oSendcommunicationResponse = new sendcommunicationResponse()
                    {
                        LeadId = customerLocationObj.LeadId,
                        MobileNo = Convert.ToInt64(MobileNo),
                        ProductId = Convert.ToInt32(ProductID),
                        CountryCode = CountryCode,
                        InputData = new Inputdata()
                        {
                            ProductName = FOSDLL.getProductName(Convert.ToInt32(ProductID))
                        }
                    };
                    SendCommunicationOnMedium(oSendcommunicationResponse, SMSTrigger, 2, SMSURL);
                    SendCommunicationOnMedium(oSendcommunicationResponse, WhatsAppTrigger, 8, WhatsappURL);

                }
            }
            return oSendcommunicationResponse;
        }

        public static void SendCommunicationOnMedium(sendcommunicationResponse oSendcommunicationResponse, string TriggerName, Int16 Commtype, string url)
        {
            oSendcommunicationResponse.TriggerName = TriggerName;
            oSendcommunicationResponse.CommunicationType = Commtype;
            oSendcommunicationResponse.InputData.DynamicUrlData = url;
            FOSDLL.SendCommunication(oSendcommunicationResponse);
        }

        public bool CheckCustomerLocationAvailable(long LeadId)
        {
            bool res = false;
            DateTime dt = DateTime.Now;
            try
            {
                DataSet ds = FOSDLL.CheckCustomerLocationAvailable(LeadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    res = true;
                }
            }
            catch (Exception ex)
            {
                res = false;
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "CheckCustomerLocationAvailable", "FOSBLL", "MatrixCore", "", "", dt, DateTime.Now);
            }
            return res;
        }

        public ResponseData<dynamic> IsAppointmentCreated(long LeadId)
        {
            DateTime dt = DateTime.Now;
            dynamic obj = new ExpandoObject();
            ResponseData<dynamic> res = new ResponseData<dynamic>();
            try
            {
                DataSet ds = FOSDLL.IsAppointmentCreated(LeadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    obj.LeadId = ds.Tables[0].Rows[0]["LeadId"] != null && ds.Tables[0].Rows[0]["LeadId"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["LeadId"]) : 0;
                    obj.AppointmentCount = ds.Tables[0].Rows[0]["AppointmentCount"] != null && ds.Tables[0].Rows[0]["AppointmentCount"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["AppointmentCount"]) : 0;
                    res.Status = true;
                    res.Message = "success";
                    res.Data = obj;
                }
            }
            catch (Exception ex)
            {
                res.Status = false;
                res.Message = "error";
                res.Data = obj;
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "IsAppointmentCreated", "FOSBLL", "MatrixCore", "", "", dt, DateTime.Now);
            }
            return res;
        }

        public bool CheckAgentAvailabilityInCity(long LeadId, Int32 CityId, Int16 SlotId, DateTime AppointmentDateTime, long UserId)
        {
            bool IsAvailable = true;
            DateTime dt = DateTime.Now;
            try
            {
                DataSet ds = FOSDLL.CheckAgentAvailabilityInCity(LeadId, CityId, SlotId, AppointmentDateTime, UserId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    IsAvailable = ds.Tables[0].Rows[0]["IsAvailable"] != null && ds.Tables[0].Rows[0]["IsAvailable"] != DBNull.Value && Convert.ToBoolean(ds.Tables[0].Rows[0]["IsAvailable"]) ? true : false;
                }
            }
            catch (Exception ex)
            {
                IsAvailable = true;
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "CheckAgentAvailabilityInCity", "FOSBLL", "MatrixCore", "", "", dt, DateTime.Now);
            }
            return IsAvailable;
        }

        public bool SendCommToOfflineLeadsInCity()
        {
            bool res = false;
            DateTime dt = DateTime.Now;
            try
            {
                DataSet ds = FOSDLL.GetOfflineLeadsInCity();
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        CustomerLocationModel customerLocationModel = new();
                        customerLocationModel.LeadId = row["LeadId"] == DBNull.Value ? 0 : Convert.ToInt64(row["LeadId"]);
                        customerLocationModel.CityId = row["CityId"] == DBNull.Value ? 0 : Convert.ToInt32(row["CityId"]);
                        customerLocationModel.City = row["City"] == DBNull.Value ? "" : Convert.ToString(row["City"]);
                        customerLocationModel.CountryId = row["CountryId"] == DBNull.Value ? 0 : Convert.ToInt64(row["CountryId"]);
                        customerLocationModel.CreatedBy = row["CreatedBy"] == DBNull.Value ? 0 : Convert.ToInt64(row["CreatedBy"]);
                        customerLocationModel.Source = row["Source"] == DBNull.Value ? "" : Convert.ToString(row["Source"]);
                        TriggerSaveCustomerLocationData(customerLocationModel);

                    }
                }
                res = true;
            }
            catch (Exception ex)
            {
                res = false;
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "SendCommToOfflineLeadsInCity", "FOSBLL", "MatrixCore", "", "", dt, DateTime.Now);
            }
            return res;
        }




        public UserProfileData GetUserProfileData(long UserId)
        {
            DateTime requestTime = new DateTime();
            UserProfileData userProfileData = null;

            try
            {

                DataSet ds = FOSDLL.GetAgentProfileData(UserId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    bool IsActive = ds.Tables[0].Rows[0]["IsActive"] == DBNull.Value ? false : Convert.ToBoolean(ds.Tables[0].Rows[0]["IsActive"]);
                    if (IsActive)
                    {
                        DateTime SelfieCreatedOn = ds.Tables[0].Rows[0]["CreatedOn"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(ds.Tables[0].Rows[0]["CreatedOn"]);
                        if (SelfieCreatedOn != DateTime.MinValue)
                        {
                            userProfileData = new UserProfileData()
                            {
                                URL = ds.Tables[0].Rows[0]["URL"] == DBNull.Value ? string.Empty : Convert.ToString(ds.Tables[0].Rows[0]["URL"]),
                                SelfieExpiryDate = SelfieCreatedOn.AddDays(Convert.ToInt16("UserSelfieExpiryDays".AppSettings()))
                            };
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", UserId, ex.ToString(), "GetUserProfileData", "MatrixCore", "FOSBLL", string.Empty, string.Empty, requestTime, DateTime.Now);

            }
            return userProfileData;
        }

        public ResponseData<List<DateAvailableModel>> getAgentAvailabilityInCityMaster(Int32 CityId)
        {
            DateTime ct = DateTime.Now;
            ResponseData<List<DateAvailableModel>> result = new ResponseData<List<DateAvailableModel>>() { Status = false, Message = "" };
            List<DateAvailableModel> AgentAvailableMasterList = new List<DateAvailableModel>();
            try
            {
                DataSet ds = FOSDLL.getAgentAvailabilityInCityMaster(CityId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    AgentAvailableMasterList = (from dr in ds.Tables[0].AsEnumerable()
                                                select new DateAvailableModel
                                                {
                                                    AppointmentDay = Convert.ToInt32(dr["AppointmentDay"]),
                                                    AppointmentDate = Convert.ToDateTime(dr["AppointmentDate"]),
                                                    AvailableZone = Convert.ToInt16(dr["AvailableZone"])
                                                }).ToList();
                    result.Data = AgentAvailableMasterList;
                    result.Status = true;
                    result.Message = "success";
                }
                else
                {
                    result.Status = false;
                    result.Message = "not found";
                }

            }
            catch (Exception ex)
            {
                result.Status = false;
                result.Message = "error";
                LoggingHelper.LoggingHelper.AddloginQueue(null, CityId, ex.ToString(), "IsAppointmentCreated", "FOSBLL", "MatrixCore", "", "", ct, DateTime.Now);
            }
            return result;
        }

        public Int16 GetExpiryDaysLeft(DateTime PasswordExpDate, Int16 ExpiryDaysLeft)
        {
            try
            {
                if (PasswordExpDate != DateTime.MinValue)
                {
                    DateTime? CurentDate = DateTime.Now;
                    TimeSpan difference = (TimeSpan)(CurentDate - PasswordExpDate);
                    ExpiryDaysLeft = Convert.ToInt16(difference.TotalDays);
                }
            }
            catch (Exception ex) { }
            return ExpiryDaysLeft;
        }
        public GeoCodeData GetGeoLocationByLatLong(LatLongModel latlngModel, Int64 LeadId)
        {

            StringBuilder sb = new StringBuilder();
            DateTime dt = DateTime.Now;
            string Error = string.Empty;
            string url = string.Empty;

            string geoLocationGoogleAPI = string.Empty;
            string googleAPIKey = string.Empty;

            GeoCodeData geoCodeData = new();
            List<Int32> _CityList = new List<int>();
            Int32 CityId = 0;
            dynamic obj = new ExpandoObject();
            PlaceLatLongModel placeLatLongModel = new();
            try
            {
                geoLocationGoogleAPI = "GeoLocationGoogleAPI".AppSettings();
                googleAPIKey = "GoogleAPIKey".AppSettings();

                if (!string.IsNullOrEmpty(geoLocationGoogleAPI) && !string.IsNullOrEmpty(googleAPIKey))
                {
                    url = geoLocationGoogleAPI + "?latlng=" + latlngModel.Lat + "," + latlngModel.Long
                   + "&key=" + googleAPIKey;

                    var jsondata = CommonAPICall.CallAPI(url, "", "GET", Convert.ToInt32("DialerAPITimeout".AppSettings()), "application/json", null);
                    if (jsondata != null)
                    {

                        sb.Append("googleResult: " + JsonConvert.SerializeObject(jsondata) + "\r\n");


                        obj = JsonConvert.DeserializeObject<dynamic>(jsondata);

                        if (obj != null && obj.status.Value != "ZERO_RESULTS" && obj.results != null && obj.results[0] != null && obj.results[0].formatted_address != null && obj.results[0].place_id != null && obj.results[0].address_components != null)
                        {
                            geoCodeData.formatted_address = Convert.ToString(obj.results[0].formatted_address);
                            geoCodeData.place_id = Convert.ToString(obj.results[0].place_id);

                            Boolean flag = false;
                            for (int i = 0; i < obj.results.Count; i++)
                            {
                                if (obj.results[i].address_components != null)
                                {
                                    foreach (var item in obj.results[i].address_components)
                                    {
                                        if (item.types != null && item.types.Count > 0 && item.types[0] == "postal_code")
                                        {
                                            geoCodeData.Pincode = Convert.ToInt32(item.long_name);
                                            flag = true;
                                            break;
                                        }
                                    }
                                }
                                if (flag == true)
                                    break;
                            }


                            DataSet odt = FOSDLL.GetCityIdWithPincode(geoCodeData.Pincode);

                            if (odt != null && odt.Tables.Count > 0 && odt.Tables[0].Rows.Count > 0)
                            {
                                foreach (DataRow row in odt.Tables[0].Rows)
                                {
                                    CityId = row["CityID"] == DBNull.Value ? 0 : Convert.ToInt32(row["CityID"]);
                                    _CityList.Add(CityId);
                                }
                                geoCodeData.CityList = _CityList;
                            }
                            placeLatLongModel.Lat = latlngModel.Lat;
                            placeLatLongModel.Long = latlngModel.Long;
                            placeLatLongModel.place_id = geoCodeData.place_id;
                            FOSDLL.SavePlacecodeLatLongMaster(placeLatLongModel);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
                geoCodeData = null;
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, Error, "GetGeoLocationByLatLong", "FOSBLL", "GetGeoLocationByLatLong", url, JsonConvert.SerializeObject(obj), DateTime.Now, DateTime.Now);

            }

            return geoCodeData;
        }

        public ResponseData<EnumAppValidation> SendCustLocationOnWhatsapp(string LeadId, LatLongModel latLong, string Address = "")
        {
            ResponseData<EnumAppValidation> oResponseAPI = new ResponseData<EnumAppValidation>() { Status = false, Message = "Something went wrong", Data = EnumAppValidation.SOMETHINGWRONG };
            DateTime requestTime = DateTime.Now;
            string Error = string.Empty;
            CustomerLocationModel customerLocationObj = new CustomerLocationModel();
            try
            {
                Boolean IsActiveAppointment = FOSDLL.IsActiveAppointment(Convert.ToInt64(LeadId));

                if (IsActiveAppointment)
                {
                    AppointmentsDataModel AppointmentsDataModel = SalesViewBLL.GetAppointmentDataHelper(0, Convert.ToInt64(LeadId));
                    GeoCodeData geoCodeData = GetGeoLocationByLatLong(latLong, Convert.ToInt64(LeadId));
                    //  if (AppointmentsDataModel.OfflineCityId == geoCodeData.CityId)
                    if (geoCodeData != null && geoCodeData.CityList != null && geoCodeData.CityList.Count > 0 && geoCodeData.CityList.Contains(AppointmentsDataModel.OfflineCityId))
                    {
                        AppointmentsDataModel.ParentId = Convert.ToInt64(LeadId);
                        AppointmentsDataModel.UserId = 4020;
                        AppointmentsDataModel.Landmark = geoCodeData.formatted_address;
                        AppointmentsDataModel.Pincode = geoCodeData.Pincode;
                        AppointmentsDataModel.place_id = geoCodeData.place_id;
                        AppointmentsDataModel.Landmark = geoCodeData.formatted_address;
                        AppointmentsDataModel.Address = !string.IsNullOrEmpty(Address) ? Address : AppointmentsDataModel.Address;
                        AppointmentsDataModel.IsDropLocationConfirm = 1;
                        AppointmentsDataModel.Source = "WhatsAppNative";
                        SaveInfo SaveInfo = SetAppointmentDataV2(AppointmentsDataModel, 0);
                        if (SaveInfo.IsSaved == true)
                        {
                            LeadsSubStatusModel leadsSubStatusModel = new LeadsSubStatusModel();
                            leadsSubStatusModel.LeadID = Convert.ToInt64(LeadId);
                            leadsSubStatusModel.CustomerID = AppointmentsDataModel.CustomerID;
                            leadsSubStatusModel.UserID = 4020;
                            leadsSubStatusModel.StatusId = AppointmentsDataModel.StatusId > 0 ? AppointmentsDataModel.StatusId : 4;
                            leadsSubStatusModel.SubStatusId = 2088;
                            leadsSubStatusModel.Source = "WhatsAppNative";
                            UpdateAppointmentStatus(leadsSubStatusModel);

                            oResponseAPI.Status = true;
                            oResponseAPI.Message = "succces";
                            oResponseAPI.Data = EnumAppValidation.None;
                        }
                    }
                    else
                    {
                        oResponseAPI.Status = false;
                        oResponseAPI.Message = "The city does not match with the city requested for Appointment";
                        oResponseAPI.Data = EnumAppValidation.CITYMISMATCH;
                    }


                }
                else
                {
                    GeoCodeData geoCodeData = GetGeoLocationByLatLong(latLong, Convert.ToInt64(LeadId));
                    DataSet ds = FOSDLL.GetCustomerLocationDataV1(Convert.ToInt64(LeadId));

                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        customerLocationObj.CityId = ds.Tables[0].Rows[0]["CityId"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["CityId"]);
                        customerLocationObj.City = ds.Tables[0].Rows[0]["City"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["City"]);
                        customerLocationObj.CountryId = ds.Tables[0].Rows[0]["CountryId"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["CountryId"]);
                    }
                    if (geoCodeData != null && geoCodeData.CityList != null && geoCodeData.CityList.Count > 0 && geoCodeData.CityList.Contains(customerLocationObj.CityId))

                    {

                        customerLocationObj.LeadId = Convert.ToInt64(LeadId);
                        customerLocationObj.Lat = latLong.Lat;
                        customerLocationObj.Long = latLong.Long;
                        customerLocationObj.Landmark = geoCodeData.formatted_address;
                        customerLocationObj.Pincode = geoCodeData.Pincode;
                        customerLocationObj.PlaceId = geoCodeData.place_id;
                        customerLocationObj.Address = Address;
                        customerLocationObj.CreatedBy = 4020;
                        customerLocationObj.ResponseSource = "WhatsAppNative";
                        FOSDLL.SaveCustomerLocationData(customerLocationObj);
                        oResponseAPI.Status = true;
                        oResponseAPI.Message = "succces";
                        oResponseAPI.Data = EnumAppValidation.None;
                    }
                    else
                    {
                        oResponseAPI.Status = false;
                        oResponseAPI.Message = "The city does not match with the city requested for Appointment";
                        oResponseAPI.Data = EnumAppValidation.CITYMISMATCH;
                    }
                }
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(LeadId), Error, "SendCustLocationOnWhatsapp", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(oResponseAPI), requestTime, DateTime.Now);
            }
            return oResponseAPI;
        }

        public bool PushAppDataToKafka(AppointmentsDataModel appointmentsDataModel)
        {
            PushAppData(appointmentsDataModel);
            return true;
        }

        public async static void PushAppData(AppointmentsDataModel appointmentsDataModel)
        {
            StringBuilder sb = new();
            sb.Append("ENTER PushAppDataToKafka" + DateTime.Now);
            string result = string.Empty;
            DateTime requestTime = DateTime.Now;
            string err = string.Empty;
            try
            {

                //await Task.Run(() =>
                //{


                if (appointmentsDataModel.UserId > 0)
                {
                    DataSet ds = FOSDLL.GetRealTimeData(appointmentsDataModel.UserId, appointmentsDataModel.ParentId);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        appointmentsDataModel.TTAttempts = ds.Tables[0].Rows[0]["TTAttempts"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["TTAttempts"]);
                        appointmentsDataModel.TTTalkTime = ds.Tables[0].Rows[0]["TTTalkTime"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["TTTalkTime"]);
                        appointmentsDataModel.LastAttempt = ds.Tables[0].Rows[0]["LastAttempt"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(ds.Tables[0].Rows[0]["LastAttempt"]);
                        appointmentsDataModel.OTPVerified = ds.Tables[0].Rows[0]["OTPVerified"] == DBNull.Value ? Convert.ToInt16(0) : Convert.ToInt16(ds.Tables[0].Rows[0]["OTPVerified"]);
                        appointmentsDataModel.EmployeeId = ds.Tables[0].Rows[0]["EmployeeId"] == DBNull.Value ? String.Empty : Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]);
                        appointmentsDataModel.UserName = ds.Tables[0].Rows[0]["UserName"] == DBNull.Value ? String.Empty : Convert.ToString(ds.Tables[0].Rows[0]["UserName"]);
                        appointmentsDataModel.ManagerId = ds.Tables[0].Rows[0]["ManagerId"] == DBNull.Value ? 0 : Convert.ToInt64(ds.Tables[0].Rows[0]["ManagerId"]);
                        appointmentsDataModel.IdleTime = ds.Tables[0].Rows[0]["IdleTime"] == DBNull.Value ? 0 : Convert.ToDecimal(ds.Tables[0].Rows[0]["IdleTime"]);
                        appointmentsDataModel.CustomerName = ds.Tables[0].Rows[0]["CustomerName"] == DBNull.Value ? String.Empty : Convert.ToString(ds.Tables[0].Rows[0]["CustomerName"]);
                        appointmentsDataModel.location = new PlaceLatLongModel();
                        appointmentsDataModel.location.Lat = ds.Tables[0].Rows[0]["AppEndLat"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(ds.Tables[0].Rows[0]["AppEndLat"]);
                        appointmentsDataModel.location.Long = ds.Tables[0].Rows[0]["AppEndLong"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(ds.Tables[0].Rows[0]["AppEndLong"]);
                        appointmentsDataModel.AgentBaseLocation = new PlaceLatLongModel();
                        appointmentsDataModel.AgentBaseLocation.Lat = ds.Tables[0].Rows[0]["UserLat"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(ds.Tables[0].Rows[0]["UserLat"]);
                        appointmentsDataModel.AgentBaseLocation.Long = ds.Tables[0].Rows[0]["UserLong"] == DBNull.Value ? Convert.ToDecimal(0) : Convert.ToDecimal(ds.Tables[0].Rows[0]["UserLong"]);
                        appointmentsDataModel.CustomerId = ds.Tables[0].Rows[0]["CustId"] == DBNull.Value ? 0 : Convert.ToInt64(ds.Tables[0].Rows[0]["CustId"]);
                        appointmentsDataModel.ProductId = ds.Tables[0].Rows[0]["ProductId"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["ProductId"]);

                    }
                    sb.Append("--Get UserId Db Data --- " + DateTime.Now + JsonConvert.SerializeObject(appointmentsDataModel));

                    List<AppRealTimeStatus> lstAppRealTimeStatus = GetRealStatusMaster();

                    sb.Append("--Get RealTime status-- " + DateTime.Now + JsonConvert.SerializeObject(lstAppRealTimeStatus));

                    if (lstAppRealTimeStatus != null)
                    {
                        if (appointmentsDataModel.subStatusId > 0)
                        {
                            var result1 = lstAppRealTimeStatus.Where(x => x.AppStatusID == appointmentsDataModel.subStatusId).FirstOrDefault();
                            if (result1 != null)
                            {
                                appointmentsDataModel.RealTimeStatusId = Convert.ToInt16(result1.RealTimeStatusId);
                                appointmentsDataModel.RealTimeStatus = Convert.ToString(result1.RealTimeStatus);
                            }
                            else
                            {
                                appointmentsDataModel.RealTimeStatusId = Convert.ToInt16(RealTimeAppointmentstatusEnum.Idle);
                                appointmentsDataModel.RealTimeStatus = "Idle";
                            }
                        }

                        else if (appointmentsDataModel.subStatusId == 0 && appointmentsDataModel.Islogout)
                        {
                            appointmentsDataModel.RealTimeStatusId = Convert.ToInt16(RealTimeAppointmentstatusEnum.Logout);
                            appointmentsDataModel.RealTimeStatus = "Logout";
                            appointmentsDataModel.StatusChangedOn = DateTime.Now;
                        }
                        else
                        {
                            appointmentsDataModel.StatusChangedOn = DateTime.Now;
                            appointmentsDataModel.RealTimeStatusId = Convert.ToInt16(RealTimeAppointmentstatusEnum.Idle);
                            appointmentsDataModel.RealTimeStatus = "Idle";
                        }

                        sb.Append("--Start pushing To kafka,Mongo-- " + DateTime.Now);
                        UpdateRealStatusMaster(appointmentsDataModel);
                        sb.Append("--End-- " + DateTime.Now);
                    }
                }
                //});
            }
            catch (Exception ex)
            {
                err = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", appointmentsDataModel.UserId, err, "PushAppData", "MatrixCore", "FOSBLL", sb.ToString(), "", requestTime, DateTime.Now);
            }

        }

        public static List<AppRealTimeStatus> GetRealStatusMaster()
        {
            List<AppRealTimeStatus> fOSCityPrdMaster = null;
            DateTime dt = DateTime.Now;
            try
            {
                string Key = $"{RedisCollection.FOSCityPrdMaster()}";

                if (MemoryCache.Default[Key] != null)
                    fOSCityPrdMaster = (List<AppRealTimeStatus>)(MemoryCache.Default.Get(Key));
                else
                {
                    DataSet data = FOSDLL.GetRealStatusMaster();

                    if (data != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
                    {
                        fOSCityPrdMaster = (from dr in data.Tables[0].AsEnumerable()
                                            select new AppRealTimeStatus
                                            {
                                                AppStatusID = dr["AppStatusID"] != null && dr["AppStatusID"] != DBNull.Value ? Convert.ToInt16(dr["AppStatusID"]) : Convert.ToInt16(0),
                                                RealTimeStatusId = dr["RealTimeStatusId"] != null && dr["RealTimeStatusId"] != DBNull.Value ? Convert.ToInt16(dr["RealTimeStatusId"]) : Convert.ToInt16(0),
                                                RealTimeStatus = dr["RealTimeStatus"] != null && dr["RealTimeStatus"] != DBNull.Value ? Convert.ToString(dr["RealTimeStatus"]) : string.Empty,
                                            }).ToList();

                        if (fOSCityPrdMaster.Count > 0)
                            CommonCache.GetOrInsertIntoCache(fOSCityPrdMaster, Key, 24 * 60);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetRealStatusMaster", "FOSBLL", "MatrixCore", "", "", dt, DateTime.Now);

            }
            return fOSCityPrdMaster;

        }


        public static void UpdateRealStatusMaster(AppointmentsDataModel appointmentsDataModel)
        {
            StringBuilder sb = new StringBuilder();
            string Error = string.Empty;
            DateTime reqDateTime = DateTime.Now;
            IMongoQuery varquery;
            UpdateBuilder<AppointmentsDataModel> update = null;

            try
            {
                appointmentsDataModel.UpdatedOn = DateTime.Now;
                sb.Append("---UpdateRealStatusMaster---" + JsonConvert.SerializeObject(appointmentsDataModel) + "---" + DateTime.Now);

                varquery = Query.And(Query<AppointmentsDataModel>.EQ(p => p.UserId, appointmentsDataModel.UserId),
                                      Query<AppointmentsDataModel>.EQ(p => p.AppointmentId, appointmentsDataModel.AppointmentId));


                AppointmentsDataModel respAppointmentsModel = FOSDLL.GetAgentRealTimeData(appointmentsDataModel.UserId, varquery);// Mongo StoredObject

                if (respAppointmentsModel != null)
                {
                    sb.Append("--Start update Data and old data--- " + JsonConvert.SerializeObject(respAppointmentsModel) + "---" + DateTime.Now);

                    if (respAppointmentsModel.RealTimeStatusId > 0 && respAppointmentsModel.RealTimeStatusId != appointmentsDataModel.RealTimeStatusId)
                    {
                        appointmentsDataModel.StatusChangedOn = DateTime.Now;
                        sb.Append("--Create Query--- " + DateTime.Now);
                        update = Update<AppointmentsDataModel>
                                   .Set(p => p.RealTimeStatusId, appointmentsDataModel.RealTimeStatusId)
                                   .Set(p => p.RealTimeStatus, appointmentsDataModel.RealTimeStatus)
                                   .Set(p => p.subStatusId, appointmentsDataModel.subStatusId)
                                   .Set(p => p.StatusChangedOn, DateTime.Now)
                                   .Set(p => p.distanceFromCustomer, appointmentsDataModel.distanceFromCustomer)
                                   .Set(p => p.overallDistance, appointmentsDataModel.overallDistance)
                                   .Set(p => p.distance, appointmentsDataModel.distance)
                                   .Set(p => p.OTPVerified, appointmentsDataModel.OTPVerified)
                                   .Set(p => p.TTAttempts, appointmentsDataModel.TTAttempts)
                                   .Set(p => p.TTTalkTime, appointmentsDataModel.TTTalkTime)
                                   .Set(p => p.LastAttempt, appointmentsDataModel.LastAttempt)
                                   .Set(p => p.IdleTime, appointmentsDataModel.IdleTime)
                                   .Set(p => p.AgentCurrentStatus, appointmentsDataModel.AgentCurrentStatus)
                                   .Set(p => p.UpdatedOn, DateTime.Now);

                    }
                    else
                    {
                        appointmentsDataModel.StatusChangedOn = respAppointmentsModel.StatusChangedOn;
                        sb.Append("--Create Query else Part  --- " + DateTime.Now);
                        update = Update<AppointmentsDataModel>
                                   .Set(p => p.distanceFromCustomer, appointmentsDataModel.distanceFromCustomer)
                                   .Set(p => p.overallDistance, appointmentsDataModel.overallDistance)
                                   .Set(p => p.distance, appointmentsDataModel.distance)
                                   .Set(p => p.OTPVerified, appointmentsDataModel.OTPVerified)
                                   .Set(p => p.TTAttempts, appointmentsDataModel.TTAttempts)
                                   .Set(p => p.TTTalkTime, appointmentsDataModel.TTTalkTime)
                                   .Set(p => p.LastAttempt, appointmentsDataModel.LastAttempt)
                                   .Set(p => p.IdleTime, appointmentsDataModel.IdleTime)
                                   .Set(p => p.AgentCurrentStatus, appointmentsDataModel.AgentCurrentStatus)
                                   .Set(p => p.StatusChangedOn, respAppointmentsModel.StatusChangedOn)
                                   .Set(p => p.UpdatedOn, DateTime.Now)
                                   ;
                    }

                    //----------- update PriorirtyModel Mongo ------------------//
                    if (varquery != null && update != null)
                    {
                        sb.Append("---Update Final Query---" + DateTime.Now);
                        FOSDLL.UpdateRealTimeStatusDocument(varquery, update);
                        sb.Append("---Final Query Updated---" + DateTime.Now);
                    }

                    //appointmentsDataModel = UpdateUserDistance(appointmentsDataModel, respAppointmentsModel.RealTimeDistance);
                }
                else
                {
                    sb.Append("---Insert Query ---" + DateTime.Now);
                    appointmentsDataModel.StatusChangedOn = DateTime.Now;
                    appointmentsDataModel.CreatedOn = DateTime.Now;
                    appointmentsDataModel.UpdatedOn = DateTime.Now;

                    bool Mongoresult = FOSDLL.UpdateAgentRealTimeData(appointmentsDataModel);  //Insert data
                    sb.Append("---Mongoresult---" + Mongoresult + " -- " + DateTime.Now);
                }

                _ = KafkaWrapper.PushToKafka(appointmentsDataModel, "fos-appointmentData-topic");

                sb.Append("---Push successfully ---" + DateTime.Now);
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", appointmentsDataModel.UserId, Error, "UpdateRealStatusMaster", "MatrixCore", "FOSBLL", sb.ToString(), "", reqDateTime, DateTime.Now);
            }
        }

        //public static AppointmentsDataModel UpdateUserDistance(AppointmentsDataModel appointmentsDataModel, Decimal RealTimeDistance)
        //{
        //    DateTime reqDateTime = DateTime.Now;
        //    IMongoQuery varquery;
        //    UpdateBuilder<AppointmentsDataModel> update = null;

        //    try
        //    {
        //        if (appointmentsDataModel.RealTimeDistance > 0)
        //        {
        //            varquery = Query.And(Query<AppointmentsDataModel>.EQ(p => p.UserId, appointmentsDataModel.UserId),
        //                               Query<AppointmentsDataModel>.EQ(p => p.AppointmentId, appointmentsDataModel.AppointmentId));

        //            update = Update<AppointmentsDataModel>
        //                       .Set(p => p.RealTimeDistance, appointmentsDataModel.RealTimeDistance);

        //            if (varquery != null && update != null)
        //                FOSDLL.UpdateRealTimeStatusDocument(varquery, update);

        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        LoggingHelper.LoggingHelper.AddloginQueue("", appointmentsDataModel.UserId, ex.ToString(), "UpdateUserDistance", "MatrixCore", "FOSBLL", JsonConvert.SerializeObject(appointmentsDataModel), "", reqDateTime, DateTime.Now);
        //    }
        //    return appointmentsDataModel;
        //}


        public bool SaveCoreAddressUsage(FOSCoreAddressModel FOSCoreAddressModel)
        {
            bool response = false;
            DateTime dt = DateTime.Now;
            try
            {
                FOSDLL.SaveCoreAddressUsage(Convert.ToInt64(FOSCoreAddressModel.LeadId), FOSCoreAddressModel.PreviousAddressClicked, FOSCoreAddressModel.IsAddressUsed);
                response = true;

            }
            catch (Exception ex)
            {
                response = false;
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(FOSCoreAddressModel.LeadId), ex.ToString(), "SaveCoreAddressUsage", "FOSBLL", "MatrixCore", JsonConvert.SerializeObject(FOSCoreAddressModel), Convert.ToString(response), dt, DateTime.Now);
            }
            return response;
        }

        public ResponseData<List<DateAvailableModel>> GetTotalAppointmentsByCityId(Int32 CityId)
        {
            DateTime ct = DateTime.Now;
            ResponseData<List<DateAvailableModel>> result = new ResponseData<List<DateAvailableModel>>() { Status = false, Message = "" };
            List<DateAvailableModel> TotalAppointmentsByCityId = new List<DateAvailableModel>();
            try
            {
                DataSet ds = FOSDLL.GetTotalAppointmentsByCityId(CityId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    TotalAppointmentsByCityId = (from dr in ds.Tables[0].AsEnumerable()
                                                 select new DateAvailableModel
                                                 {
                                                     AppointmentDay = Convert.ToInt32(dr["AppointmentDay"]),
                                                     AppointmentDate = Convert.ToDateTime(dr["AppointmentDate"]),
                                                     WeekDays = Convert.ToString(dr["WeekDays"]),
                                                     TotalAppointments = Convert.ToInt32(dr["TotalAppointments"])
                                                 }).ToList();
                    result.Data = TotalAppointmentsByCityId;
                    result.Status = true;
                    result.Message = "success";
                }
                else
                {
                    result.Status = false;
                    result.Message = "not found";
                }

            }
            catch (Exception ex)
            {
                result.Status = false;
                result.Message = "error";
                LoggingHelper.LoggingHelper.AddloginQueue(null, CityId, ex.ToString(), "GetTotalAppointmentsByCityId", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(result), ct, DateTime.Now);
            }
            return result;
        }

        public ResponseData<CarDataModel> GetCarDetails(long LeadId)
        {

            DateTime dt = DateTime.Now;
            CarDataModel _CarDataModel = new();
            ResponseData<CarDataModel> _response = new();
            try
            {
                DataSet ds = FOSDLL.GetCarDetails(LeadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    _CarDataModel.LeadId = ds.Tables[0].Rows[0]["LeadId"] == DBNull.Value ? 0 : Convert.ToInt64(ds.Tables[0].Rows[0]["LeadId"]);
                    _CarDataModel.PolicyType = ds.Tables[0].Rows[0]["PolicyType"] == DBNull.Value ? "" : Convert.ToString(ds.Tables[0].Rows[0]["PolicyType"]);
                    _CarDataModel.PreviousPolicyExpiryDate = ds.Tables[0].Rows[0]["PreviousPolicyExpiryDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(ds.Tables[0].Rows[0]["PreviousPolicyExpiryDate"]);
                    _CarDataModel.ExpectedDeliveryDate = ds.Tables[0].Rows[0]["ExpectedDeliveryDate"] == DBNull.Value ? DateTime.MinValue : Convert.ToDateTime(ds.Tables[0].Rows[0]["ExpectedDeliveryDate"]);
                }
                _response.Status = true;
                _response.Data = _CarDataModel;
                _response.Message = "Success";

            }
            catch (Exception ex)
            {
                _response.Status = false;
                _response.Message = "error";
                _response.Data = null;
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "GetCarDetails", "FOSBLL", "MatrixCore", Convert.ToString(LeadId), JsonConvert.SerializeObject(_response), dt, DateTime.Now);
            }
            return _response;
        }

        public ResponseData<bool> IsActiveAppointment(long LeadId)
        {
            DateTime ct = DateTime.Now;
            ResponseData<bool> res = new ResponseData<bool>() { Status = false, Data = false };
            try
            {
                Boolean IsActiveAppointment = FOSDLL.IsActiveAppointment(LeadId);
                res.Status = true;
                res.Data = IsActiveAppointment;
                res.Message = "success";
            }
            catch (Exception ex)
            {
                res.Status = false;
                res.Data = false;
                res.Message = "Something Went Wrong";
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "IsActiveAppointment", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(res), ct, DateTime.Now);

            }
            return res;
        }
        public ResponseData<AppointmentURLData> GetAppointmentURL(long LeadId, long CustomerId = 0, Int16 ProductID = 0, string Source = "")
        {
            DateTime ct = DateTime.Now;
            ResponseData<AppointmentURLData> res = new ResponseData<AppointmentURLData>() { Status = false, Data = null };
            try
            {
                AppointmentURLData _urlData = new AppointmentURLData();
                DataSet oDataSet = FOSDLL.GetLeadBasicInfo(LeadId);
                if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                {
                    LeadId = (oDataSet.Tables[0].Rows[0]["ParentID"] != null && oDataSet.Tables[0].Rows[0]["ParentID"] != DBNull.Value) ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["ParentID"]) : LeadId;
                }

                string URL = GenerateRescheduleLinkNew(Convert.ToString(LeadId), CustomerId, ProductID, Source);
                _urlData.AppointmentURL = URL + "&src=customerwhatsapp";
                DataSet ds = FOSDLL.IsAppointmentCreated(LeadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    Int32 AppointmentCount = ds.Tables[0].Rows[0]["AppointmentCount"] == DBNull.Value ? 0 : Convert.ToInt32(ds.Tables[0].Rows[0]["AppointmentCount"]);
                    _urlData.IsAppointmentCreated = AppointmentCount > 0 ? true : false;
                }
                if (!_urlData.IsAppointmentCreated)
                {
                    CustomerLocationModel customerLocationObj = new CustomerLocationModel();
                    customerLocationObj.LeadId = LeadId;
                    customerLocationObj.Source = "HyperLocalRenewal";
                    customerLocationObj.ResponseSource = "HyperLocalRenewal";
                    customerLocationObj.CreatedBy = 124;
                    customerLocationObj.CityId = 0;
                    FOSDLL.SaveCustomerLocationData(customerLocationObj);
                }
                res.Data = _urlData;
                res.Status = true;
                res.Message = "success";
            }
            catch (Exception ex)
            {
                res.Status = false;
                res.Data = null;
                res.Message = "Something Went Wrong";
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "GetAppointmentURL", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(res), ct, DateTime.Now);

            }
            return res;

        }

        public static string GetRouteURL(long LeadId, string Source)
        {
            string url = "";
            try
            {
                switch (Source.ToLower())
                {
                    case "hyperlocal":
                        url = "/RescheduleAppointmentV2";
                        break;
                    case "hyperlocalrenewal":
                        url = "/fosAppointment/HealthRenewal";
                        break;
                    default:
                        url = "/RescheduleAppointment";
                        break;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "GetAppointmentURL", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(url), DateTime.Now, DateTime.Now);
            }

            return url;
        }


        public ResponseData<dynamic> GetDataByKey(string Key)
        {
            ResponseData<dynamic> res = new() { Data = null, Message = "Something went wrong", Status = false };
            DateTime dt = DateTime.Now;
            try
            {
                if (MemoryCache.Default[Key] != null)
                {
                    dynamic obj = MemoryCache.Default.Get(Key);
                    if (obj != null)
                    {
                        res.Data = obj;
                        res.Message = "success";
                        res.Status = true;
                    }
                    else
                    {
                        res.Data = null;
                        res.Message = "Data not found";
                        res.Status = false;
                    }
                }

                else
                {
                    res.Data = null;
                    res.Message = "key not found";
                    res.Status = false;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, Convert.ToInt64(Key), ex.ToString(), "GetDataByKey", "FOS", "FOSBLL", "", "", dt, DateTime.Now);
            }
            return res;

        }

        public ResponseData<List<string>> GetAllKeys()
        {
            ResponseData<List<string>> res = new ResponseData<List<string>>() { Data = null, Message = "something went wrong", Status = false };
            DateTime dt = DateTime.Now;
            try
            {
                var cacheKeys = MemoryCache.Default.Select(kvp => kvp.Key).ToList();
                if (cacheKeys != null)
                {
                    res.Data = cacheKeys;
                    res.Message = "success";
                    res.Status = true;
                }
                else
                {
                    res.Data = null;
                    res.Message = "not found";
                    res.Status = false;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "GetAllKeys", "FOS", "FOSBLL", "", "", dt, DateTime.Now);
            }
            return res;
        }

        public ResponseData<bool> SaveFOSIntent(long LeadId, string source)
        {
            DateTime ct = DateTime.Now;
            ResponseData<bool> res = new ResponseData<bool>() { Status = false, Data = false };
            try
            {
                Boolean IsSaved = FOSDLL.SaveFOSIntent(LeadId, source);
                res.Status = true;
                res.Data = true;
                res.Message = "success";
            }
            catch (Exception ex)
            {
                res.Status = false;
                res.Data = false;
                res.Message = "Something Went Wrong";
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.ToString(), "SaveFOSIntent", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(res), ct, DateTime.Now);

            }
            return res;

        }
        public ResponseData<List<AppointmentDataModelAI>> GetAppointmentDetailsforAI()
        {
            DateTime ct = DateTime.Now;
            ResponseData<List<AppointmentDataModelAI>> result = new ResponseData<List<AppointmentDataModelAI>>() { Status = false, Message = "" };
            List<AppointmentDataModelAI> _AppointmentDetailsforAI = new List<AppointmentDataModelAI>();
            try
            {
                DataSet ds = FOSDLL.GetAppointmentDetailsforAI(0, 0);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    _AppointmentDetailsforAI = (from dr in ds.Tables[0].AsEnumerable()
                                                select new AppointmentDataModelAI
                                                {
                                                    AppointmentId = dr["Id"] != null && dr["Id"] != DBNull.Value ? Convert.ToInt64(dr["Id"]) : 0,
                                                    LeadID = dr["LeadId"] != null && dr["LeadId"] != DBNull.Value ? Convert.ToInt64(dr["LeadId"]) : 0,
                                                    ProductId = dr["ProductId"] != null && dr["ProductId"] != DBNull.Value ? Convert.ToInt16(dr["ProductId"]) : default,
                                                    AppointmentDateTime = dr["AppointmentDateTime"] != null && dr["AppointmentDateTime"] != DBNull.Value ? Convert.ToDateTime(dr["AppointmentDateTime"]) : default,
                                                    Address = dr["Address"] != null && dr["Address"] != DBNull.Value ? Convert.ToString(dr["Address"]) : default,
                                                    Pincode = dr["Pincode"] != null && dr["Pincode"] != DBNull.Value ? Convert.ToInt32(dr["Pincode"]) : default,
                                                    OfflineCityId = dr["OfflineCityId"] != null && dr["OfflineCityId"] != DBNull.Value ? Convert.ToInt16(dr["OfflineCityId"]) : default,
                                                    City = dr["City"] != null && dr["City"] != DBNull.Value ? Convert.ToString(dr["City"]) : default,
                                                    CreatedOn = dr["CreatedOn"] != null && dr["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CreatedOn"]) : default,
                                                    UpdatedOn = dr["UpdatedOn"] != null && dr["UpdatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["UpdatedOn"]) : default,
                                                    IsActive = dr["IsActive"] != null && dr["IsActive"] != DBNull.Value ? Convert.ToBoolean(dr["IsActive"]) : false,
                                                    AppointmentType = dr["AppointmentType"] != null && dr["AppointmentType"] != DBNull.Value ? Convert.ToInt16(dr["AppointmentType"]) : default,
                                                    Landmark = dr["Landmark"] != null && dr["Landmark"] != DBNull.Value ? Convert.ToString(dr["Landmark"]) : default,
                                                    AssignmentId = dr["AssignmentId"] != null && dr["AssignmentId"] != DBNull.Value ? Convert.ToInt16(dr["AssignmentId"]) : default,
                                                    ProcessId = dr["ProcessId"] != null && dr["ProcessId"] != DBNull.Value ? Convert.ToInt16(dr["ProcessId"]) : default,
                                                    NearBy = dr["NearBy"] != null && dr["NearBy"] != DBNull.Value ? Convert.ToString(dr["NearBy"]) : default,
                                                    CallDate = dr["CallDate"] != null && dr["CallDate"] != DBNull.Value ? Convert.ToDateTime(dr["CallDate"]) : default,
                                                    CallDataID = dr["CallDataID"] != null && dr["CallDataID"] != DBNull.Value ? Convert.ToInt64(dr["CallDataID"]) : default,
                                                    UserID = dr["UserID"] != null && dr["UserID"] != DBNull.Value ? Convert.ToInt64(dr["UserID"]) : default,
                                                    talktime = dr["talktime"] != null && dr["talktime"] != DBNull.Value ? Convert.ToInt16(dr["talktime"]) : default,
                                                    CallCreatedOn = dr["CallCreatedOn"] != null && dr["CallCreatedOn"] != DBNull.Value ? Convert.ToDateTime(dr["CallCreatedOn"]) : default
                                                }).ToList();
                    result.Data = _AppointmentDetailsforAI;
                    result.Status = true;
                    result.Message = "success";
                }
                else
                {
                    result.Status = false;
                    result.Message = "not found";
                }

            }
            catch (Exception ex)
            {
                result.Status = false;
                result.Message = "error";
                LoggingHelper.LoggingHelper.AddloginQueue(null, 123, ex.ToString(), "GetAppointmentDetailsforAI", "FOSBLL", "MatrixCore", "", JsonConvert.SerializeObject(result), ct, DateTime.Now);
            }
            return result;
        }

        public static string PushAppDatatoAITeam(AppointmentsDataModel objAppointmentData)
        {
            if (CoreCommonMethods.GetEnvironmentVar().ToLower() != "live")
            {
                return "PushAppDatatoAITeam: Run on Live env only";
            }

            try
            {
                DataSet ds = FOSDLL.GetAppointmentDetailsforAI(objAppointmentData.ParentId, objAppointmentData.AppointmentId);
                dynamic msgObj = new ExpandoObject();

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    msgObj.AppointmentId = ds.Tables[0].Rows[0]["Id"] != null && ds.Tables[0].Rows[0]["Id"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["Id"]) : 0;
                    msgObj.LeadID = ds.Tables[0].Rows[0]["LeadId"] != null && ds.Tables[0].Rows[0]["LeadId"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["LeadId"]) : 0;
                    msgObj.ProductId = ds.Tables[0].Rows[0]["ProductId"] != null && ds.Tables[0].Rows[0]["ProductId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["ProductId"]) : default;
                    msgObj.AppointmentDateTime = ds.Tables[0].Rows[0]["AppointmentDateTime"] != null && ds.Tables[0].Rows[0]["AppointmentDateTime"] != DBNull.Value ? Convert.ToDateTime(ds.Tables[0].Rows[0]["AppointmentDateTime"]) : default;
                    msgObj.Address = ds.Tables[0].Rows[0]["Address"] != null && ds.Tables[0].Rows[0]["Address"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Address"]) : default;
                    msgObj.Pincode = ds.Tables[0].Rows[0]["Pincode"] != null && ds.Tables[0].Rows[0]["Pincode"] != DBNull.Value ? Convert.ToInt32(ds.Tables[0].Rows[0]["Pincode"]) : default;
                    msgObj.OfflineCityId = ds.Tables[0].Rows[0]["OfflineCityId"] != null && ds.Tables[0].Rows[0]["OfflineCityId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["OfflineCityId"]) : default;
                    msgObj.City = ds.Tables[0].Rows[0]["City"] != null && ds.Tables[0].Rows[0]["City"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["City"]) : default;
                    msgObj.CreatedOn = ds.Tables[0].Rows[0]["CreatedOn"] != null && ds.Tables[0].Rows[0]["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(ds.Tables[0].Rows[0]["CreatedOn"]) : default;
                    msgObj.UpdatedOn = ds.Tables[0].Rows[0]["UpdatedOn"] != null && ds.Tables[0].Rows[0]["UpdatedOn"] != DBNull.Value ? Convert.ToDateTime(ds.Tables[0].Rows[0]["UpdatedOn"]) : default;
                    msgObj.IsActive = ds.Tables[0].Rows[0]["IsActive"] != null && ds.Tables[0].Rows[0]["IsActive"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["IsActive"]) : false;
                    msgObj.AppointmentType = ds.Tables[0].Rows[0]["AppointmentType"] != null && ds.Tables[0].Rows[0]["AppointmentType"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["AppointmentType"]) : default;
                    msgObj.Landmark = ds.Tables[0].Rows[0]["Landmark"] != null && ds.Tables[0].Rows[0]["Landmark"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Landmark"]) : default;
                    msgObj.AssignmentId = ds.Tables[0].Rows[0]["AssignmentId"] != null && ds.Tables[0].Rows[0]["AssignmentId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["AssignmentId"]) : default;
                    msgObj.ProcessId = ds.Tables[0].Rows[0]["ProcessId"] != null && ds.Tables[0].Rows[0]["ProcessId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["ProcessId"]) : default;
                    msgObj.NearBy = ds.Tables[0].Rows[0]["NearBy"] != null && ds.Tables[0].Rows[0]["NearBy"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["NearBy"]) : default;
                    msgObj.CallDataId = ds.Tables[0].Rows[0]["CallDataId"] != null && ds.Tables[0].Rows[0]["CallDataId"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["CallDataId"]) : default;



                    // push to CJ

                    FOSDLL.SendAppointmentEventToAI(msgObj);

                }
            }
            catch (Exception error)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", objAppointmentData.ParentId, error.ToString(), "PushAppDatatoAITeam LeadError", "FOSBLL", "", "", "", DateTime.Now, DateTime.Now);
            }

            return "done";
        }

        public ResponseData<dynamic> GetAppointmentExistanceStatus(long LeadId)
        {
            DateTime dt = DateTime.Now;
            ResponseData<dynamic> res = new ResponseData<dynamic>() { Status = false,Message= "something went wrong", Data=null};
          
            dynamic msgObj = new ExpandoObject();
            try
            {
                DataSet ds = FOSDLL.GetAppointmentExistanceStatus(LeadId);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    msgObj.IsExist = ds.Tables[0].Rows[0]["IsExist"] != null && ds.Tables[0].Rows[0]["IsExist"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["IsExist"]) : false;
                    if(msgObj.IsExist)
                    {
                        msgObj.IsActive= ds.Tables[0].Rows[0]["IsActive"] != null && ds.Tables[0].Rows[0]["IsActive"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["IsActive"]) : false;
                        msgObj.IsCompleted = ds.Tables[0].Rows[0]["IsCompleted"] != null && ds.Tables[0].Rows[0]["IsCompleted"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["IsCompleted"]) : false;
                        msgObj.IsCancelled = ds.Tables[0].Rows[0]["IsCancelled"] != null && ds.Tables[0].Rows[0]["IsCancelled"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["IsCancelled"]) : false;
                    }
                    res.Status = true;
                    res.Message = "success";
                    res.Data = msgObj;
                }
                }
            catch (Exception error)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, error.ToString(), "GetAppointmentExistanceStatus LeadError", "FOSBLL", "", "", "", dt, DateTime.Now);
            }
            return res;
        }

        public ResponseData<string> RescheduleAppointmentCustWA(RescheduleApptData rescheduleAppt)
        {
            ResponseData<string> response = new() {
                Status = false,
                Message = "Appointment Prepone Failed"
            };
            DateTime dt = DateTime.Now;
            try
            {
                object result = FOSDLL.RescheduleAppointment(rescheduleAppt);
                if(Convert.ToInt16(result) == 1) {
                    long CustomerId = 0;

                    DataSet oDataSet = FOSDLL.GetLeadBasicInfo(rescheduleAppt.LeadID);
                    if (oDataSet != null && oDataSet.Tables.Count > 0 && oDataSet.Tables[0].Rows.Count > 0)
                    {
                        CustomerId = oDataSet.Tables[0].Rows[0]["CustomerID"] != null && oDataSet.Tables[0].Rows[0]["CustomerID"] != DBNull.Value ? Convert.ToInt64(oDataSet.Tables[0].Rows[0]["CustomerID"]) : 0;
                    }
                    LeadsSubStatusModel rescheduleSubStatus = new()
                    {
                        LeadID = Convert.ToInt64(rescheduleAppt.LeadID),
                        CustomerID = CustomerId,
                        UserID = 4020,
                        StatusId = 4,
                        SubStatusId = 2005,
                        Source = "WhatsAppNative",
                        CancelReasonId = 0,
                        SendRescheduleTrigger = false
                    };
                    UpdateAppointmentStatus(rescheduleSubStatus);

                    LeadsSubStatusModel confirmSubStatus = new()
                    {
                        LeadID = Convert.ToInt64(rescheduleAppt.LeadID),
                        CustomerID = CustomerId,
                        UserID = 4020,
                        StatusId = 4,
                        SubStatusId = 2088,
                        Source = "WhatsAppNative",
                        SendRescheduleTrigger = false
                    };
                    UpdateAppointmentStatus(confirmSubStatus);
                    FOSDLL.TrackAppointmentPrepone(rescheduleAppt.LeadID,"PrePone");
                    response.Status = true;
                    response.Message = "Appointment Preponed successfully";


                } 
                else if(Convert.ToInt16(result) == 2)
                {
                    response.Status = false;
                    response.Message = "Cannot Prepone appointment on same day or day before after 9pm";
                }
                else if (Convert.ToInt16(result) == 3)
                {
                    response.Status = false;
                    response.Message = "Appointment cannot be scheduled for this slot, please select another slot";
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", rescheduleAppt.LeadID, ex.ToString(), "RescheduleAppointment", "MatrixCore", "FOSBLL", rescheduleAppt.LeadID.ToString(), response.ToString(), dt, DateTime.Now);
            }
            return response;
        }

        public CallIdResult GetCallId(CallIdModel callId)
        {
            DateTime dt = DateTime.Now;
            string Error = string.Empty;
            CallIdResult response = new()
            {
                Status = false,
                Message = "Failed to save",
                StatusCode = 502
            };

            try
            {
                DialerDispDetails _DispositionUpdate = new()
                {
                    CallId = "0",
                    ParentID = callId.ParentID,
                    ProductID = callId.ProductID,
                    AgentCode = callId.EmpCode,
                    IsBMS = false,
                    Status = "0",
                    CallType = "FOSVISIT",
                    callDate = Convert.ToDateTime(DateTime.Now)
                };
                string CallDataId = WebSiteServicDLL.InsertCallData(_DispositionUpdate);
                if (!string.IsNullOrEmpty(CallDataId))
                {
                    response.Status = true;
                    response.CallId = CallDataId;
                    response.Message = "Saved";
                    response.StatusCode = 200;
                }
            }
            catch (Exception ex)
            {
                response.Message = "An error occurred while saving callDataId";
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(
                    callId?.ParentID.ToString() ?? "0",
                    callId?.ParentID ?? 0,
                    Error,
                    "InsertRecordingLogs",
                    "FOSBLL",
                     callId?.UserId?.ToString() ?? "0",
                    JsonConvert.SerializeObject(callId),
                    JsonConvert.SerializeObject(response),
                    dt,
                    DateTime.Now);
            }

            return response;
        }
    }
}