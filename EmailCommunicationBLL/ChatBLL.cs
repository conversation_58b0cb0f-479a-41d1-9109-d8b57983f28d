﻿using DataAccessLibrary;
using MongoConfigProject;
using PropertyLayers;
using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Redis;
using System.Data;
using System.Dynamic;
using Newtonsoft.Json;
using Helper;

namespace EmailCommunicationBLL
{
    public class ChatBLL : IChatBLL
    {
        public bool SyncChatData(List<ChatDataModel> _ChatDataModellst)
        {
            {
                string url = string.Empty;
                string Response = string.Empty;
                DateTime RequestDatetime = DateTime.Now;
                string strexception = string.Empty;

                try
                {
                    if (_ChatDataModellst == null || _ChatDataModellst.Count == 0)
                    {
                        Response = "Input data is null";
                        return false;
                    }
                    foreach (ChatDataModel _ChatDataModel in _ChatDataModellst)
                    {
                        if (string.IsNullOrEmpty(_ChatDataModel.rid))
                        {
                            Response = Response + "Roomid is not available";
                            return false;
                        }
                        if (ChatDLL.IsRoomExists(_ChatDataModel.rid) == true)
                        {
                            ChatDLL.UpdateChatData(_ChatDataModel);
                        }
                        else
                            ChatDLL.InsertChatData(_ChatDataModel);
                    }
                }
                catch (Exception ex)
                {
                    strexception = ex.ToString();
                    LoggingHelper.LoggingHelper.Log(string.Empty, 0, strexception, "SyncChatData", "ChatBLL", "", "", "", DateTime.Now, DateTime.Now);
                    return false;
                }
                finally
                {
                    LoggingHelper.LoggingHelper.Log(string.Empty, 0, strexception, "SyncChatData", "ChatBLL", "", "", Response, RequestDatetime, DateTime.Now);
                }
                return true;
            }
        }
        public LeadData GetcustInfo(string EncodedLeadID, Int16 isservice, string mobileno, string product)
        {
            LeadDetailsBLL objLeaddetailsBLL = new LeadDetailsBLL();
            string strException = string.Empty;
            var indianCode = ",0,91,+91,INDIA,392,OTHERS,India";
            AllocationLeadData LeadAllocationData = null;
            long LeadID = 0;
            try
            {
                if ((EncodedLeadID == "0" || string.IsNullOrEmpty(EncodedLeadID)))
                {
                    bool serviceChk = false;
                    if (isservice == 1)
                        serviceChk = true;
                    var ds = ChatDLL.getCustinfobyMobileNo(mobileno, product, 0, serviceChk);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        return new LeadData { LeadID = Convert.ToInt64(ds.Tables[0].Rows[0]["LeadID"]), ParentLeadID = Convert.ToInt64(ds.Tables[0].Rows[0]["ParentID"]), CustID = Convert.ToInt64(ds.Tables[0].Rows[0]["CustomerID"]) };
                    }
                    else
                        return null;
                }
                else
                {
                    var invalidMobileNos = MasterData.InvalidMobileList();
                    List<int> TamilRegional = new List<int>() { 30, 26, 17, 16, 1, 37 };
                    List<int> TamilExcludeCity = new List<int>() { 6, 837, 853, 18, 843, 571, 845 };

                    LeadID = Convert.ToInt64(Encoding.UTF8.GetString(Convert.FromBase64String(EncodedLeadID)));
                    if (LeadID == 0)
                        return null;
                    //LeadID = Convert.ToInt64(EncodedLeadID);                   
                    LeadDetailResponse _LeadDetailResponse = ChatDLL.getLeadDetailsforChat(LeadID,0);

                    string utmSource = "";
                 

                    if (_LeadDetailResponse == null || _LeadDetailResponse.LeadData == null)
                        return null;



                    _LeadDetailResponse.LeadData.CountryCode = (_LeadDetailResponse.LeadData.CountryCode == 0 ? 91 : _LeadDetailResponse.LeadData.CountryCode);


                    if (_LeadDetailResponse != null && _LeadDetailResponse.LeadData != null)
                    {

                        if (_LeadDetailResponse.LeadData.ProductName.ToUpper() == "NEWCAR" && isservice == 0)
                        {
                            var ParentLeadSource = string.Empty;
                            if (_LeadDetailResponse.LeadData.ParentLeadID == _LeadDetailResponse.LeadData.LeadID || _LeadDetailResponse.LeadData.LeadSource.ToUpper() == "RENEWAL")
                            {
                                ParentLeadSource = _LeadDetailResponse.LeadData.LeadSource;
                                utmSource = _LeadDetailResponse.LeadData.UtmSource;
                            }
                            else
                            {

                                var dssource = ChatDLL.GetParentLeadSource(_LeadDetailResponse.LeadData.ParentLeadID);
                                if (dssource != null && dssource.Tables.Count > 0 && dssource.Tables[0].Rows.Count > 0)
                                {
                                    ParentLeadSource = Convert.ToString(dssource.Tables[0].Rows[0]["Leadsource"]);
                                    if (dssource.Tables[0].Rows[0]["Leadsource"] != null && dssource.Tables[0].Rows[0]["Leadsource"] != DBNull.Value)
                                    {
                                        utmSource = Convert.ToString(dssource.Tables[0].Rows[0]["Utm_source"]);
                                    }
                                }
                            }
                            bool isexpired = false;
                            bool NewCar = false;
                            if (ParentLeadSource.ToUpper() == "RENEWAL")
                                _LeadDetailResponse.LeadData.ProductName = "NewCar_Renewal";
                            //else if (ParentLeadSource.ToUpper() == "REOPEN" || (!string.IsNullOrEmpty(utmSource) && ("CRMSMS,CRMPMAILER".Contains(utmSource.ToUpper()))))
                            else if (_LeadDetailResponse.LeadData.LeadSource.ToUpper() == "REOPEN")
                                _LeadDetailResponse.LeadData.ProductName = "Retainers";
                            else if ("carmumbaigroups".AppSettings().Contains("," + ChatDLL.GetAssignedGroup(_LeadDetailResponse.LeadData.ParentLeadID) + ","))
                            {
                                _LeadDetailResponse.LeadData.GroupType = "mumbai";
                            }
                            else
                            {
                                isexpired = ChatDLL.IsExpiry(_LeadDetailResponse.LeadData.ParentLeadID, ref NewCar);
                                _LeadDetailResponse.LeadData.newcar = NewCar;
                            }

                            //if (NewCar==false && TamilRegional.Contains(Convert.ToInt32(_LeadDetailResponse.LeadData.State)) && !TamilExcludeCity.Contains(Convert.ToInt32(_LeadDetailResponse.LeadData.City)))
                            if (NewCar == false && TamilRegional.Contains(Convert.ToInt32(_LeadDetailResponse.LeadData.State)))
                            {
                                Int32 state = Convert.ToInt32(_LeadDetailResponse.LeadData.State);
                                List<Int32> tamil = new List<Int32>() { 30, 26 };
                                List<Int32> telgu = new List<Int32>() { 1, 37 };
                                List<Int32> kanad = new List<Int32>() { 16 };
                                List<Int32> malyalam = new List<Int32>() { 17 };
                                if (tamil.Contains(state))
                                {
                                    _LeadDetailResponse.LeadData.GroupType = "tamil";
                                }
                                else if (telgu.Contains(state))
                                {
                                    _LeadDetailResponse.LeadData.GroupType = "telgu";
                                }
                                else if (kanad.Contains(state))
                                {
                                    _LeadDetailResponse.LeadData.GroupType = "kanad";
                                }
                                else if (malyalam.Contains(state))
                                {
                                    _LeadDetailResponse.LeadData.GroupType = "malyalam";
                                }

                            }
                            /*stopped on 01-aug-2022*/
                            //else
                            //{
                            //    _LeadDetailResponse.LeadData.isexpired = isexpired;
                            //}
                        }
                        else if (_LeadDetailResponse.LeadData.ProductName.ToUpper() == "HEALTH" && isservice == 0)
                        {
                            var ParentLeadSource = string.Empty;
                            if (_LeadDetailResponse.LeadData.ParentLeadID == _LeadDetailResponse.LeadData.LeadID || _LeadDetailResponse.LeadData.LeadSource.ToUpper() == "RENEWAL")
                            {
                                ParentLeadSource = _LeadDetailResponse.LeadData.LeadSource;
                                utmSource = _LeadDetailResponse.LeadData.UtmSource;
                            }
                            else
                            {

                                var dssource = ChatDLL.GetParentLeadSource(_LeadDetailResponse.LeadData.ParentLeadID);
                                if (dssource != null && dssource.Tables.Count > 0 && dssource.Tables[0].Rows.Count > 0)
                                {
                                    ParentLeadSource = Convert.ToString(dssource.Tables[0].Rows[0]["Leadsource"]);
                                    if (dssource.Tables[0].Rows[0]["Leadsource"] != null && dssource.Tables[0].Rows[0]["Leadsource"] != DBNull.Value)
                                    {
                                        utmSource = Convert.ToString(dssource.Tables[0].Rows[0]["Utm_source"]);
                                    }
                                }
                            }

                            if (ParentLeadSource.ToUpper() == "RENEWAL")
                                _LeadDetailResponse.LeadData.ProductName = "HealthRenewal";
                            else if (!string.IsNullOrEmpty(_LeadDetailResponse.LeadData.UtmSource) && "ChatRetainersUtmSource".AppSettings().Contains("," + _LeadDetailResponse.LeadData.UtmSource.ToUpper() + ","))
                            {
                                _LeadDetailResponse.LeadData.GroupType = "Health_Retainers";
                            }
                            else if (!string.IsNullOrEmpty(_LeadDetailResponse.LeadData.UtmSource) && _LeadDetailResponse.LeadData.UtmSource.ToUpper() == "H_WHATSAPP" && _LeadDetailResponse.LeadData.LeadSource.ToUpper() == "WHATSAPP")
                            {
                                _LeadDetailResponse.LeadData.ProductName = "Health_WA_Facebook";
                            }
                            else if (!indianCode.Contains("," + _LeadDetailResponse.LeadData.Country + ","))
                            {
                                _LeadDetailResponse.LeadData.ProductName = "Health_NRI";
                            }
                            //else if("healthretentiongroups".AppSettings().Contains("," + ChatData.GetAssignedGroup(_LeadDetailResponse.LeadData.ParentLeadID) + ","))
                            //{
                            //    _LeadDetailResponse.LeadData.ProductName = "Health_Retention"; 
                            //}
                            //else if(",1,37,30,17,".Contains("," + Convert.ToString(_LeadDetailResponse.LeadData.State) + ","))
                            //{
                            //    _LeadDetailResponse.LeadData.ProductName = "Health_Regional"; 
                            //}
                            //else
                            //{
                            //    _LeadDetailResponse.LeadData.isexpired = ChatData.IsExpiry(_LeadDetailResponse.LeadData.ParentLeadID);
                            //}
                        }
                        if (isservice == 1)
                        {
                            _LeadDetailResponse.LeadData.ProductName = _LeadDetailResponse.LeadData.ProductName + "_Service";
                            _LeadDetailResponse.LeadData.ParentLeadID = _LeadDetailResponse.LeadData.LeadID;
                        }

                        _LeadDetailResponse.LeadData.ChatDepartmentID = ChatDLL.getDepartmentID(_LeadDetailResponse.LeadData.ProductName);
                        _LeadDetailResponse.LeadData.LeadID = _LeadDetailResponse.LeadData.ParentLeadID;
                        
                        if (_LeadDetailResponse.LeadData.ProductID == 115)
                        {
                            if (!indianCode.Contains("," + _LeadDetailResponse.LeadData.Country + ","))
                            {
                                _LeadDetailResponse.LeadData.Country = "NRI";
                                _LeadDetailResponse.LeadData.ProductName = "Investments_Nri";
                            }
                            else
                                _LeadDetailResponse.LeadData.Country = "India";
                        }
                        if (_LeadDetailResponse.LeadData.ProductID == 7)
                        {
                            if (!indianCode.Contains("," + _LeadDetailResponse.LeadData.Country + ","))
                            {
                                _LeadDetailResponse.LeadData.Country = "NRI";
                                _LeadDetailResponse.LeadData.ProductName = "TermLife_Nri";
                            }
                            else
                                _LeadDetailResponse.LeadData.Country = "India";

                            if (_LeadDetailResponse.LeadData.DOB != DateTime.MinValue)
                            {
                                int days = DateTime.Now.Day;
                                if (days > 28 && DateTime.Now.Month == 2)
                                    days = 28;
                                DateTime currentdate = new DateTime(_LeadDetailResponse.LeadData.DOB.Year, DateTime.Now.Month, days);
                                //DateTime currentdate = DateTime.Now;
                                var daysdiff = (_LeadDetailResponse.LeadData.DOB - currentdate).Days;
                                if ((daysdiff >= 0 && daysdiff < 45) || (daysdiff <= -320 && daysdiff > -365))
                                    _LeadDetailResponse.LeadData.welcomemessage = ChatDLL.getWelcomeMessage(_LeadDetailResponse.LeadData.ProductName + "_DOB");
                            }
                        }
                        if (_LeadDetailResponse.LeadData.ProductID == 155 || _LeadDetailResponse.LeadData.ProductID == 156)
                        {
                            _LeadDetailResponse.LeadData.ProductName = "InternationalLife";
                        }
                        if (_LeadDetailResponse.LeadData.ProductID == 160 || _LeadDetailResponse.LeadData.ProductID == 159 || _LeadDetailResponse.LeadData.ProductID == 158)
                        {
                            _LeadDetailResponse.LeadData.ProductName = "InternationalBanking";
                        }
                        if (_LeadDetailResponse.LeadData.ProductID == 191)
                        {
                            _LeadDetailResponse.LeadData.ProductName = "InternationalWarranty";
                        }
                        _LeadDetailResponse.LeadData.ChatDepartmentID = ChatDLL.getDepartmentID(_LeadDetailResponse.LeadData.ProductName);
                        if (invalidMobileNos != null && invalidMobileNos.ContainsKey(_LeadDetailResponse.LeadData.MobileNo))
                        {
                            _LeadDetailResponse.LeadData.invflag = 1;
                        }

                        #region HEALTH LEADRANK FROM LEADSCORE
                        try
                        {
                            string LRError = string.Empty;
                            if ("stopGetLeadAllocationDataAPI".AppSettings() != "true"
                                    && _LeadDetailResponse != null
                                    && _LeadDetailResponse.LeadData != null
                                    && _LeadDetailResponse.LeadData.ProductName.ToUpper() == "HEALTH"
                                    && _LeadDetailResponse.LeadData.LeadRank!=110
                                )
                            {
                                try
                                {

                                    // update KFA
                                    ChatDLL.UpdateKFAforLead(_LeadDetailResponse.LeadData.ParentLeadID, _LeadDetailResponse.LeadData.CustID, _LeadDetailResponse.LeadData.ProductID, "CHAT");


                                    // Get Ranks
                                    var response = GetLeadAllocationData(_LeadDetailResponse.LeadData.ParentLeadID, "CHAT");
                                    var _allocateLeadResponse = JsonConvert.DeserializeObject<AllocateLeadResponse>(response);
                                    if (_allocateLeadResponse != null && _allocateLeadResponse.LeadDetails != null && _allocateLeadResponse.LeadDetails.Count > 0)
                                    {
                                        LeadAllocationData = _allocateLeadResponse.LeadDetails[0];
                                    }


                                }
                                catch (Exception ex)
                                {
                                    LRError = ex.ToString();
                                }
                                finally
                                {
                                    var chatLeadRank = 0;
                                    if (LeadAllocationData != null && LeadAllocationData.ChatLeadRank > 0)
                                    {
                                        chatLeadRank = LeadAllocationData.ChatLeadRank;
                                    }
                                    else
                                    {
                                        // get KFA data, if not available
                                        LeadAllocationData = new();
                                        var KFAdataset = ChatDLL.GetLeadAllocationKeyFactors(_LeadDetailResponse.LeadData.ParentLeadID);

                                        if (KFAdataset != null)
                                        {
                                            var leadKFA = KFAdataset.Tables[0].Rows[0];
                                            LeadAllocationData.PreviousBooking = (leadKFA["PreviousBooking"] == null || leadKFA["PreviousBooking"] == DBNull.Value) ? String.Empty : Convert.ToString(Convert.ToInt16(leadKFA["PreviousBooking"]));
                                            LeadAllocationData.RepeatCustomer = (leadKFA["RepeatCustomer"] == null || leadKFA["RepeatCustomer"] == DBNull.Value) ? String.Empty : Convert.ToString(Convert.ToInt16(leadKFA["RepeatCustomer"]));
                                            LeadAllocationData.InsurerID = (leadKFA["InsurerID"] == null || leadKFA["InsurerID"] == DBNull.Value) ? 0 : (Int32)leadKFA["InsurerID"];
                                        }
                                        else
                                        {
                                            LeadAllocationData.PreviousBooking = "0";
                                            LeadAllocationData.RepeatCustomer = "0";
                                            LeadAllocationData.InsurerID = 0;
                                        }
                                    }

                                    if (chatLeadRank > 0) { }
                                    else if (LeadAllocationData != null 
                                        && (
                                            LeadAllocationData.InsurerID > 0
                                            || (CoreCommonMethods.IsValidInteger(LeadAllocationData.RepeatCustomer) > 0)
                                        )
                                    )
                                    {
                                        chatLeadRank = LeadID % 2 == 0 ? 81 : 82;
                                    }
                                    else
                                    {
                                        chatLeadRank = LeadID % 2 == 0 ? 83 : 84;
                                    }
                                    var leadid = _LeadDetailResponse.LeadData.ParentLeadID != 0 ? _LeadDetailResponse.LeadData.ParentLeadID : LeadID;

                                    // Update leadrank
                                    _LeadDetailResponse.LeadData.LeadRank = chatLeadRank;

                                    LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(leadid), LRError, "GetLeadAllocationData", "MatrixCoreAPI", "ChatBll", leadid.ToString(), chatLeadRank.ToString(), DateTime.Now, DateTime.Now);
                                }
                            }
                        }
                        catch { }
                        #endregion

                        //empty mobile No
                        _LeadDetailResponse.LeadData.MobileNo = "";
                        return _LeadDetailResponse.LeadData;
                    }
                    else
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, strException, "GetcustInfo-Chat-NoData", "communication", string.Empty, string.Empty, "Lead Data Not Found", DateTime.Now, DateTime.Now);
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                strException = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, strException, "GetcustInfo-Chat", "communication", string.Empty, string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                return null;
                //throw ex;
            }
        }

        public static string GetLeadAllocationData(long leadID, string assignmentProcess, bool toAssign = false, bool hitPayUScoreAPI = false)
        {
            DateTime requestTime = DateTime.Now;
            string data = string.Empty;
            string reqData = "";
            string Exception ="";
            try
            {
                string url = "AllocationApi".AppSettings();
                Dictionary<object, object> _Dict = new()
                {
                    { "source", "matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey", "matrixAPIclientKey".AppSettings() }
                };
                int timeout = Convert.ToInt32(1000);
                url += "Allocation/AllocateHealthLeads";

                dynamic dataToPost = new ExpandoObject();
                dataToPost.leadId = leadID;
                dataToPost.toAssign = toAssign;
                dataToPost.assignmentProcess = assignmentProcess;
                dataToPost.hitPayUScoreAPI = hitPayUScoreAPI;
                reqData = JsonConvert.SerializeObject(dataToPost);
                
                if (!string.IsNullOrEmpty(url))
                    data = CommonAPICall.CallAPI(url, reqData, "POST", timeout, "application/json", _Dict);

            }
            catch (Exception ex)
            {
                Exception = ex.ToString();
                data = string.Empty;
            }
            finally 
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(leadID), Exception.ToString(), "AllocateHealthLeadsApi", "MatrixCore", "ChatBLL", reqData, data.ToString(), requestTime, DateTime.Now);
            }
            return data;
        }

        public string getSalesViewURl(string LeadID, string CustId, string ChatuserID, string EmployeeId)
        {
            try
            {
                return "salesviewurl".AppSettings() + "?c=" + CustId + "&l=" + Convert.ToBase64String(Encoding.UTF8.GetBytes(LeadID.ToString())) + "&u=" + EmployeeId + "&p=123456&ct=chat";
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadID), ex.ToString(), "getSalesViewURl", "MatrixCoreAPI", string.Empty, string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                return null;
                //throw ex;
            }
        }
        public bool IsInvalidMobile(string mobileno)
        {
            try
            {
                var invalidMobileNos = MasterData.InvalidMobileList();
                return invalidMobileNos != null && invalidMobileNos.ContainsKey(mobileno) ? true : false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public bool IsChatAllowed(long LeadID, string IP)
        {
            string IsHealthConditionToCheck = Convert.ToString("IsHealthConditionToCheck".AppSettings());
            string strResponse = string.Empty;
            DateTime RequestDateTime = DateTime.Now;
            string strException = string.Empty;
            Int16 ProductID = 0;
            if (LeadID == 0)
                return true;

            try
            {
                if (LeadPrioritizationDLL.IsIntrnalIP(IP) == true)
                {
                    strResponse = "Internal IP - " + IP;
                    return false;
                }

                var _LeadDetailResponse = ChatDLL.getLeadDetailsforChat(LeadID,0);
                if (_LeadDetailResponse != null && _LeadDetailResponse.LeadData != null)
                {
                    if (_LeadDetailResponse.LeadData.LeadSource == "ACAFF" || _LeadDetailResponse.LeadData.LeadSource == "ACAFFAPP") // offline affiliate
                    {
                        strResponse = "Offline Affiliate - ";
                        return false;
                    }

                    if (_LeadDetailResponse.LeadData.ProductID == 2)
                    {
                        ProductID = 2;
                        if (IsHealthConditionToCheck == "0") // no need to check condition
                            return true;

                        if (IsInvalidMobile(_LeadDetailResponse.LeadData.MobileNo) == true)
                        {
                            strResponse = "INvalid MobileNo";
                            return false;
                        }
                        var ParentLeadSource = string.Empty;
                        if (_LeadDetailResponse.LeadData.ParentLeadID == _LeadDetailResponse.LeadData.LeadID || _LeadDetailResponse.LeadData.LeadSource.ToUpper() == "RENEWAL")
                        {
                            ParentLeadSource = _LeadDetailResponse.LeadData.LeadSource;
                        }
                        else
                        {

                            var dssource = ChatDLL.GetParentLeadSource(_LeadDetailResponse.LeadData.ParentLeadID);
                            if (dssource != null && dssource.Tables.Count > 0 && dssource.Tables[0].Rows.Count > 0)
                            {
                                ParentLeadSource = Convert.ToString(dssource.Tables[0].Rows[0]["Leadsource"]);
                            }
                        }

                        if (ParentLeadSource.ToUpper() == "RENEWAL")
                        {
                            strResponse = "renewal lead";
                            return false;
                        }
                        else
                            return true;
                    }

                    return true;
                }
                else
                {
                    strResponse = "lead not found";
                    return true;
                }
            }
            catch (Exception ex)
            {
                strException = string.Empty;
                return true;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, strException, "IsChatAllowed-" + Convert.ToString(ProductID), "communication", string.Empty, string.Empty, strResponse, RequestDateTime, DateTime.Now);
            }
        }
        public CarInfo getCarInfo(Int64 LeadID)
        {
            try
            {
                DataSet ds = ChatDLL.getCarInfo(LeadID);
                CarInfo oCarInfo = new CarInfo();
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {

                    oCarInfo.ModelName = Convert.ToString(ds.Tables[0].Rows[0]["ModelName"]).Trim();
                    oCarInfo.MakeName = Convert.ToString(ds.Tables[0].Rows[0]["MakeName"]).Trim();
                    oCarInfo.PreviousPolicyExpiryDate = Convert.ToDateTime(ds.Tables[0].Rows[0]["PreviousPolicyExpiryDate"]).ToString("dd-MMM-yyyy");
                }

                return oCarInfo;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, ex.ToString(), "getCarInfo", "matrixcore", string.Empty, string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                return null;
            }
        }
        public ChatHealthInfo getHealthInfo(Int64 LeadID)
        {
            try
            {
                DataSet ds = ChatDLL.getHealthInfo(LeadID);
                ChatHealthInfo oHealthInfo = new ChatHealthInfo();

                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    oHealthInfo.LeadID = Convert.ToString(ds.Tables[0].Rows[0]["LeadID"]).Trim();
                    oHealthInfo.AnnualIncome = Convert.ToString(ds.Tables[0].Rows[0]["AnnualIncome"]).Trim();
                    oHealthInfo.City = Convert.ToString(ds.Tables[0].Rows[0]["City"]).Trim();
                    oHealthInfo.AgeOfAllMembers = Convert.ToString(ds.Tables[0].Rows[0]["AgeOfAllMembers"]).Trim();
                }
                return oHealthInfo;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, ex.ToString(), "getHealthInfo", "matrixcore", string.Empty, string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                return null;
            }
        }
        public ResponseData<string> IsWhatsappAllowed(string LeadID, string UserID)
        {
            ResponseData<string> result = new() { Status = false, Message = "Invalid Inputs" };
            bool status = false;
            string message = string.Empty;
            if (Convert.ToInt64(LeadID) == Convert.ToInt64(0))
                return result;
            try
            {
                short IsEnableChat = 0;
                DataSet ds = ChatDLL.IsUser_WAEligible(UserID, LeadID);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    IsEnableChat = Convert.ToInt16(ds.Tables[0].Rows[0]["IsEnableChat"]);
                    message = (ds.Tables[0].Rows[0]["MESSAGE"]).ToString();
                }
                if (IsEnableChat == 1)
                {

                    string Key = RedisCollection.IsWhatsAPPAllowed() + ":" + LeadID;
                    string obj = RedisHelper.GetRedisData(Key);
                    if (obj == null)
                    {
                        DataSet LoginDataDS = ChatDLL.ChkWhatsAppAllowed(Convert.ToInt64(LeadID));
                        if (LoginDataDS != null && LoginDataDS.Tables.Count > 0 && LoginDataDS.Tables[0].Rows.Count > 0)
                        {
                            if (Convert.ToBoolean(LoginDataDS.Tables[0].Rows[0]["IsWhatsAPPAllowed"]) == true)
                            {
                                status = true;
                                message = "Read-write Access";
                            }
                            else 
                            {
                                IsEnableChat = 3;
                                status = false;
                                message = Convert.ToInt64(LoginDataDS.Tables[0].Rows[0]["ProductID"]) == 131 ? "You can't send a WhatsApp template to the customer as you don't have 40 seconds of talk time" : "You can't send a WhatsApp template to the customer as you don't have 120 seconds of talk time";
                            }
                        }
                        if (status)
                        {
                            RedisHelper.SetRedisData(Key, Convert.ToString(status), new TimeSpan(7, 0, 0, 0));
                            ChatDLL.LogWAEligibleTime(Convert.ToInt64(LeadID));
                        }
                    }
                    else
                    {
                        status = Convert.ToBoolean(obj);
                        message = status ? "Read-write Access" : "You can't send a WhatsApp template to the customer as you don't have sufficient talk time";
                        IsEnableChat = status ? Convert.ToInt16(1) : Convert.ToInt16(3);
                    }
                }
                result = new()
                {
                    Code = IsEnableChat,
                    Status = IsEnableChat != 0,
                    Message = message
                };

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadID, Convert.ToInt64(LeadID), ex.ToString(), "IsWhatsappAllowed", "MatrixCore", "ChatBLL", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
            return result;

        }
        public LeadData GetAssignDataforchat(string leadId)
        {
            try
            {
                DataSet ds = ChatDLL.GetAssignDataforchat(Convert.ToInt64(leadId));
                var objLeadData = new LeadData();
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    objLeadData.UserID = Convert.ToInt64(ds.Tables[0].Rows[0]["AssignedToUserID"]);
                    objLeadData.AgentID = Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]);
                }
                return objLeadData;
                //return new LeadData();
            }
            catch (Exception ex)
            {
                return new LeadData();
            }
        }
        public ResponseData<CustomerActiveLeads> GetCustomerActiveLeads(string CustomerID)
        {
            ResponseData<CustomerActiveLeads> result = new() { Status = false, Message = "Invalid Inputs", Data = new() };
            DateTime request = DateTime.Now;
            if (Convert.ToInt64(CustomerID) == Convert.ToInt64(0))
                return result;
            try
            {
                DataSet ds = ChatDLL.GetCustomerActiveLeads(CustomerID);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    result.Data.CustomerID = Convert.ToInt64(CustomerID);
                    result.Data.Leads = new();
                    result.Message = "Success";
                    result.Status = true;

                    foreach (DataRow item in ds.Tables[0].Rows)
                    {
                        Leadlist leadlist = new()
                        {
                            LeadID = item["ParentID"] != DBNull.Value ? Convert.ToInt64(item["ParentID"]) : default,
                            ProductID = item["ProductID"] != DBNull.Value ? Convert.ToInt16(item["ProductID"]) : default,
                            ProductName = item["ProductName"] != DBNull.Value ? Convert.ToString(item["ProductName"]) : default,
                            AssignedAgentID = item["EmployeeId"] != DBNull.Value ? Convert.ToString(item["EmployeeId"]) : default,
                            AssignedUserID = item["Userid"] != DBNull.Value ? Convert.ToInt64(item["Userid"]) : default,
                            AssignedUserGroupID = item["AssignToGroupId"] != DBNull.Value ? Convert.ToInt16(item["AssignToGroupId"]) : default,
                            EmployeeName = item["EmployeeName"] != DBNull.Value ? Convert.ToString(item["EmployeeName"]) : default,
                            TalkTime = item["talktime"] != DBNull.Value ? Convert.ToInt64(item["talktime"]) : default,
                            IsRenewal = item["IsRenewal"] != DBNull.Value && Convert.ToBoolean(item["IsRenewal"]),
                            IsUHNIAgent = item["IsUHNIAgent"] != DBNull.Value ? Convert.ToInt16(item["IsUHNIAgent"]) : default,
                        };
                        result.Data.Leads.Add(leadlist);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CustomerID, Convert.ToInt64(CustomerID), ex.ToString(), "GetCustomerActiveLeads", "ChatBLL", "MatrixCore", string.Empty, string.Empty, request, DateTime.Now);
            }
            return result;

        }
        public ResponseData<LeadTT> GetLeadTT(string LeadID)
        {
            ResponseData<LeadTT> result = new() { Status = false, Message = "Invalid Inputs", Data = new() };
            DateTime request = DateTime.Now;
            if (Convert.ToInt64(LeadID) == Convert.ToInt64(0))
                return result;
            try
            {

                WebSiteServiceBLL objWebSiteServiceBLL = new();
                DataSet ds = ChatDLL.GetLeadTT(LeadID);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    result = new()
                    {
                        Message = "Success",
                        Status = true,
                        Data = new()
                        {
                            Leadslist = new()
                            {
                                LeadID = ds.Tables[0].Rows[0]["ParentID"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["ParentID"]) : default,
                                ProductID = ds.Tables[0].Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["ProductID"]) : default,
                                ProductName = ds.Tables[0].Rows[0]["ProductName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["ProductName"]) : default,
                                AssignedAgentID = ds.Tables[0].Rows[0]["EmployeeId"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]) : default,
                                AssignedUserID = ds.Tables[0].Rows[0]["Userid"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["Userid"]) : default,
                                AssignedUserGroupID = ds.Tables[0].Rows[0]["AssignToGroupId"] != DBNull.Value ? Convert.ToInt16(ds.Tables[0].Rows[0]["AssignToGroupId"]) : default,
                                EmployeeName = ds.Tables[0].Rows[0]["EmployeeName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["EmployeeName"]) : default,
                                TalkTime = ds.Tables[0].Rows[0]["talktime"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["talktime"]) : default,
                                CustName = ds.Tables[0].Rows[0]["CustName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["CustName"]) : default
                            },
                            ProcessType = ds.Tables[0].Rows[0]["FLoorProcess"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["FLoorProcess"]) : default,
                            leaddetails = objWebSiteServiceBLL.GetAllBasicLeadDetails(LeadID, "matrix", null, null),
                            leadagentdetails = LeadPrioritizationDLL.GetAssignedAgentData(Convert.ToInt64(LeadID))
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadID, Convert.ToInt64(LeadID), ex.ToString(), "GetLeadTT", "ChatBLL", "MatrixCore", string.Empty, string.Empty, request, DateTime.Now);
            }
            return result;

        }

        public ResponseData<AdditionalInfo> GetAdditionalInfo(long leadId, int productId)
        {
            ResponseData<AdditionalInfo> result = new ResponseData<AdditionalInfo>();
            result.Status = false;
            DateTime reqTime = DateTime.Now;

            try
            {
                if (leadId > 0)
                {
                    DataSet ds = ChatDLL.GetAdditionalInfo(leadId, productId);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        result.Message = "Success";
                        result.Status = true;
                        switch (productId)
                        {
                            case 7:
                                result.Data = new()
                                {
                                    SmokerStatus = ds.Tables[0].Rows[0]["SmokerStatus"] != DBNull.Value ? Convert.ToBoolean(ds.Tables[0].Rows[0]["SmokerStatus"]) : false,
                                    Education = ds.Tables[0].Rows[0]["Education"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Education"]) : string.Empty,
                                    DOB = ds.Tables[0].Rows[0]["DOB"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["DOB"]) : string.Empty,
                                    Occupation = ds.Tables[0].Rows[0]["Occupation"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Occupation"]) : string.Empty,
                                    AnnualIncome = ds.Tables[0].Rows[0]["AnnualIncome"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["AnnualIncome"]) : string.Empty,
                                    Status = ds.Tables[0].Rows[0]["Status"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["Status"]) : string.Empty
                                };
                                break;
                        }
                        
                    }
                    else
                    {
                        result.Message = "No data Found amongst the lead.";
                    }
                }
                else
                {
                    result.Message = "Invalid LeadId";
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(leadId), leadId, ex.ToString(), "GetAdditionalInfo", "ChatBLL", "MatrixCore", string.Empty, string.Empty, reqTime, DateTime.Now);
                result.Message = ex.ToString();
            }
            return result;
        }

        public ChatFilterData ChatfilterData(long LeadID, string mobileno)
        {
            ChatFilterData result = new ChatFilterData();
            DateTime reqTime = DateTime.Now;
            long MobileNo = 0;

            try
            {
                if(LeadID > 0 || (!string.IsNullOrEmpty(mobileno) && long.TryParse(mobileno,out MobileNo)))
                {
                    DataSet ds = LeadPrioritizationDLL.getParentID(LeadID);

                    if(ds!= null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0 && ds.Tables[0].Rows[0]["ParentID"] != null)
                    {
                        var ParentId = Convert.ToInt64(ds.Tables[0].Rows[0]["ParentID"]);

                        if(LeadID > ParentId)
                        {
                            result.childLeadExists = true;
                        }
                    }

                    result.previousBookingExist = ChatDLL.IsBookedCustomer(mobileno, LeadID);

                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(LeadID), LeadID, ex.ToString(), "ChatfilterData", "ChatBLL", "MatrixCore", string.Empty, string.Empty, reqTime, DateTime.Now);
            }
            return result;
        }
        public bool SetCustInteraction(InteractionModel data)
        {
            bool result = false;
            DateTime reqTime = DateTime.Now;

            try
            {
                if(data.LeadId > 0)
                    result = ChatDLL.SetCustInteraction(data) > 0 ? true : false;
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(data.LeadId), 0, ex.ToString(), "ChatfilterData", "ChatBLL", "MatrixCore", JsonConvert.SerializeObject(data), string.Empty, reqTime, DateTime.Now);
            }
            return result;
        }

        public ResponseData<EmployeeDetails> GetLastActiveAgentDetails(long leadId)
        {
            ResponseData<EmployeeDetails> result = new ResponseData<EmployeeDetails>()
            {
                Status = false,
                Message = string.Empty,
                Data = new EmployeeDetails()
            };
            DateTime reqTime = DateTime.Now;

            try
            {
                if (leadId > 0)
                {
                    DataSet ds = ChatDLL.GetLastActiveAgentDetails(leadId);
                    if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                    {
                        result.Data.EmployeeId = ds.Tables[0].Rows[0]["EmployeeId"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["EmployeeId"]) : String.Empty;
                        result.Data.UserId = ds.Tables[0].Rows[0]["AssignedToUserID"] != DBNull.Value ? Convert.ToInt64(ds.Tables[0].Rows[0]["AssignedToUserID"]) : 0;
                        result.Data.UserName = ds.Tables[0].Rows[0]["UserName"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["UserName"]) : String.Empty;

                        result.Status = true;
                    }
                    else
                    {
                        result.Message = "No Data Found on Lead!";
                    }
                }
                else
                {
                    result.Message = "Invalid Leadid!";
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(leadId), 0, ex.ToString(), "GetLastActiveAgentDetails", "ChatBLL", "MatrixCore", JsonConvert.SerializeObject(result), string.Empty, reqTime, DateTime.Now);
                result.Message = "Invalid Response";
            }
            return result;
        }

        public ResponseAPI SetCustomerNITrigger(long customerId, int type)
        {
            ResponseAPI result = new ResponseAPI()
            {
                status = false,
                message = string.Empty
            };
            DateTime reqTime = DateTime.Now;

            try
            {
                if(customerId > 0)
                {
                    bool response = ChatDLL.SetCustomerNITrigger(customerId, type); ;
                    if(response == true)
                    {
                        result.status = true;
                        result.message = "Successfullt added the CustomerId!";
                    }
                    else
                    {
                        result.message = "Data is not updated!";
                    }
                }
                else
                {
                    result.message = "Invalid CustomerId!";
                }
            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(customerId), 0, ex.ToString(), "SetCustomerNITrigger", "ChatBLL", "MatrixCore", Convert.ToString(type), string.Empty, reqTime, DateTime.Now);
                result.message = "Invalid Response";
            }
            return result;
        }

        public ResponseData<LeadData> GetleadInfo(long LeadID, int productId)
        {
            LeadDetailsBLL objLeaddetailsBLL = new LeadDetailsBLL();
            string strException = string.Empty;
            var indianCode = ",0,91,+91,INDIA,392,OTHERS,India";
            AllocationLeadData LeadAllocationData = null;
            ResponseData<LeadData> LeadResponse = new ResponseData<LeadData>();
            try
            {

                var invalidMobileNos = MasterData.InvalidMobileList();
                List<int> TamilRegional = new List<int>() { 30, 26, 17, 16, 1, 37 };
                List<int> TamilExcludeCity = new List<int>() { 6, 837, 853, 18, 843, 571, 845 };


                LeadDetailResponse _LeadDetailResponse = ChatDLL.getLeadDetailsforChat(LeadID, productId);
                string utmSource = "";
                if (_LeadDetailResponse == null || _LeadDetailResponse.LeadData == null)
                { 
                    LeadResponse.Status = false;
                    LeadResponse.Message = "Data Not found";
                    return LeadResponse;
                }



                _LeadDetailResponse.LeadData.CountryCode = (_LeadDetailResponse.LeadData.CountryCode == 0 ? 91 : _LeadDetailResponse.LeadData.CountryCode);


                if (_LeadDetailResponse != null && _LeadDetailResponse.LeadData != null)
                {

                    if (_LeadDetailResponse.LeadData.ProductName.ToUpper() == "NEWCAR")
                    {
                        var ParentLeadSource = string.Empty;
                        if (_LeadDetailResponse.LeadData.ParentLeadID == _LeadDetailResponse.LeadData.LeadID || _LeadDetailResponse.LeadData.LeadSource.ToUpper() == "RENEWAL")
                        {
                            ParentLeadSource = _LeadDetailResponse.LeadData.LeadSource;
                            utmSource = _LeadDetailResponse.LeadData.UtmSource;
                        }
                        else
                        {

                            var dssource = ChatDLL.GetParentLeadSource(_LeadDetailResponse.LeadData.ParentLeadID);
                            if (dssource != null && dssource.Tables.Count > 0 && dssource.Tables[0].Rows.Count > 0)
                            {
                                ParentLeadSource = Convert.ToString(dssource.Tables[0].Rows[0]["Leadsource"]);
                                if (dssource.Tables[0].Rows[0]["Leadsource"] != null && dssource.Tables[0].Rows[0]["Leadsource"] != DBNull.Value)
                                {
                                    utmSource = Convert.ToString(dssource.Tables[0].Rows[0]["Utm_source"]);
                                }
                            }
                        }
                        bool isexpired = false;
                        bool NewCar = false;
                        if (ParentLeadSource.ToUpper() == "RENEWAL")
                            _LeadDetailResponse.LeadData.ProductName = "NewCar_Renewal";
                        //else if (ParentLeadSource.ToUpper() == "REOPEN" || (!string.IsNullOrEmpty(utmSource) && ("CRMSMS,CRMPMAILER".Contains(utmSource.ToUpper()))))
                        else if (_LeadDetailResponse.LeadData.LeadSource.ToUpper() == "REOPEN")
                            _LeadDetailResponse.LeadData.ProductName = "Retainers";
                        else if ("carmumbaigroups".AppSettings().Contains("," + ChatDLL.GetAssignedGroup(_LeadDetailResponse.LeadData.ParentLeadID) + ","))
                        {
                            _LeadDetailResponse.LeadData.GroupType = "mumbai";
                        }
                        else
                        {
                            isexpired = ChatDLL.IsExpiry(_LeadDetailResponse.LeadData.ParentLeadID, ref NewCar);
                            _LeadDetailResponse.LeadData.newcar = NewCar;
                        }

                        //if (NewCar==false && TamilRegional.Contains(Convert.ToInt32(_LeadDetailResponse.LeadData.State)) && !TamilExcludeCity.Contains(Convert.ToInt32(_LeadDetailResponse.LeadData.City)))
                        if (NewCar == false && TamilRegional.Contains(Convert.ToInt32(_LeadDetailResponse.LeadData.State)))
                        {
                            Int32 state = Convert.ToInt32(_LeadDetailResponse.LeadData.State);
                            List<Int32> tamil = new List<Int32>() { 30, 26 };
                            List<Int32> telgu = new List<Int32>() { 1, 37 };
                            List<Int32> kanad = new List<Int32>() { 16 };
                            List<Int32> malyalam = new List<Int32>() { 17 };
                            if (tamil.Contains(state))
                            {
                                _LeadDetailResponse.LeadData.GroupType = "tamil";
                            }
                            else if (telgu.Contains(state))
                            {
                                _LeadDetailResponse.LeadData.GroupType = "telgu";
                            }
                            else if (kanad.Contains(state))
                            {
                                _LeadDetailResponse.LeadData.GroupType = "kanad";
                            }
                            else if (malyalam.Contains(state))
                            {
                                _LeadDetailResponse.LeadData.GroupType = "malyalam";
                            }

                        }
                        /*stopped on 01-aug-2022*/
                        //else
                        //{
                        //    _LeadDetailResponse.LeadData.isexpired = isexpired;
                        //}
                    }
                    else if (_LeadDetailResponse.LeadData.ProductName.ToUpper() == "HEALTH")
                    {
                        var ParentLeadSource = string.Empty;
                        if (_LeadDetailResponse.LeadData.ParentLeadID == _LeadDetailResponse.LeadData.LeadID || _LeadDetailResponse.LeadData.LeadSource.ToUpper() == "RENEWAL")
                        {
                            ParentLeadSource = _LeadDetailResponse.LeadData.LeadSource;
                            utmSource = _LeadDetailResponse.LeadData.UtmSource;
                        }
                        else
                        {

                            var dssource = ChatDLL.GetParentLeadSource(_LeadDetailResponse.LeadData.ParentLeadID);
                            if (dssource != null && dssource.Tables.Count > 0 && dssource.Tables[0].Rows.Count > 0)
                            {
                                ParentLeadSource = Convert.ToString(dssource.Tables[0].Rows[0]["Leadsource"]);
                                if (dssource.Tables[0].Rows[0]["Leadsource"] != null && dssource.Tables[0].Rows[0]["Leadsource"] != DBNull.Value)
                                {
                                    utmSource = Convert.ToString(dssource.Tables[0].Rows[0]["Utm_source"]);
                                }
                            }
                        }

                        if (ParentLeadSource.ToUpper() == "RENEWAL")
                            _LeadDetailResponse.LeadData.ProductName = "HealthRenewal";
                        else if (!string.IsNullOrEmpty(_LeadDetailResponse.LeadData.UtmSource) && "ChatRetainersUtmSource".AppSettings().Contains("," + _LeadDetailResponse.LeadData.UtmSource.ToUpper() + ","))
                        {
                            _LeadDetailResponse.LeadData.GroupType = "Health_Retainers";
                        }
                        else if (!string.IsNullOrEmpty(_LeadDetailResponse.LeadData.UtmSource) && _LeadDetailResponse.LeadData.UtmSource.ToUpper() == "H_WHATSAPP" && _LeadDetailResponse.LeadData.LeadSource.ToUpper() == "WHATSAPP")
                        {
                            _LeadDetailResponse.LeadData.ProductName = "Health_WA_Facebook";
                        }
                        else if (!indianCode.Contains("," + _LeadDetailResponse.LeadData.Country + ","))
                        {
                            _LeadDetailResponse.LeadData.ProductName = "Health_NRI";
                        }
                        //else if("healthretentiongroups".AppSettings().Contains("," + ChatData.GetAssignedGroup(_LeadDetailResponse.LeadData.ParentLeadID) + ","))
                        //{
                        //    _LeadDetailResponse.LeadData.ProductName = "Health_Retention"; 
                        //}
                        //else if(",1,37,30,17,".Contains("," + Convert.ToString(_LeadDetailResponse.LeadData.State) + ","))
                        //{
                        //    _LeadDetailResponse.LeadData.ProductName = "Health_Regional"; 
                        //}
                        //else
                        //{
                        //    _LeadDetailResponse.LeadData.isexpired = ChatData.IsExpiry(_LeadDetailResponse.LeadData.ParentLeadID);
                        //}
                    }
                    //if (isservice == 1)
                    //{
                    //    _LeadDetailResponse.LeadData.ProductName = _LeadDetailResponse.LeadData.ProductName + "_Service";
                    //    _LeadDetailResponse.LeadData.ParentLeadID = _LeadDetailResponse.LeadData.LeadID;
                    //}

                    _LeadDetailResponse.LeadData.ChatDepartmentID = ChatDLL.getDepartmentID(_LeadDetailResponse.LeadData.ProductName);
                    _LeadDetailResponse.LeadData.LeadID = _LeadDetailResponse.LeadData.ParentLeadID;

                    if (_LeadDetailResponse.LeadData.ProductID == 115)
                    {
                        if (!indianCode.Contains("," + _LeadDetailResponse.LeadData.Country + ","))
                        {
                            _LeadDetailResponse.LeadData.Country = "NRI";
                            _LeadDetailResponse.LeadData.ProductName = "Investments_Nri";
                        }
                        else
                            _LeadDetailResponse.LeadData.Country = "India";
                    }
                    if (_LeadDetailResponse.LeadData.ProductID == 7)
                    {
                        if (!indianCode.Contains("," + _LeadDetailResponse.LeadData.Country + ","))
                        {
                            _LeadDetailResponse.LeadData.Country = "NRI";
                            _LeadDetailResponse.LeadData.ProductName = "TermLife_Nri";
                        }
                        else
                            _LeadDetailResponse.LeadData.Country = "India";

                        if (_LeadDetailResponse.LeadData.DOB != DateTime.MinValue)
                        {
                            int days = DateTime.Now.Day;
                            if (days > 28 && DateTime.Now.Month == 2)
                                days = 28;
                            DateTime currentdate = new DateTime(_LeadDetailResponse.LeadData.DOB.Year, DateTime.Now.Month, days);
                            //DateTime currentdate = DateTime.Now;
                            var daysdiff = (_LeadDetailResponse.LeadData.DOB - currentdate).Days;
                            if ((daysdiff >= 0 && daysdiff < 45) || (daysdiff <= -320 && daysdiff > -365))
                                _LeadDetailResponse.LeadData.welcomemessage = ChatDLL.getWelcomeMessage(_LeadDetailResponse.LeadData.ProductName + "_DOB");
                        }
                    }
                    if (_LeadDetailResponse.LeadData.ProductID == 155 || _LeadDetailResponse.LeadData.ProductID == 156)
                    {
                        _LeadDetailResponse.LeadData.ProductName = "InternationalLife";
                    }
                    if (_LeadDetailResponse.LeadData.ProductID == 160 || _LeadDetailResponse.LeadData.ProductID == 159 || _LeadDetailResponse.LeadData.ProductID == 158)
                    {
                        _LeadDetailResponse.LeadData.ProductName = "InternationalBanking";
                    }
                    if (_LeadDetailResponse.LeadData.ProductID == 191)
                    {
                        _LeadDetailResponse.LeadData.ProductName = "InternationalWarranty";
                    }
                    _LeadDetailResponse.LeadData.ChatDepartmentID = ChatDLL.getDepartmentID(_LeadDetailResponse.LeadData.ProductName);
                    if (invalidMobileNos != null && invalidMobileNos.ContainsKey(_LeadDetailResponse.LeadData.MobileNo))
                    {
                        _LeadDetailResponse.LeadData.invflag = 1;
                    }

                    #region HEALTH LEADRANK FROM LEADSCORE
                    try
                    {
                        string LRError = string.Empty;
                        if ("stopGetLeadAllocationDataAPI".AppSettings() != "true"
                                && _LeadDetailResponse != null
                                && _LeadDetailResponse.LeadData != null
                                && _LeadDetailResponse.LeadData.ProductName.ToUpper() == "HEALTH"
                                && _LeadDetailResponse.LeadData.LeadRank != 110
                            )
                        {
                            try
                            {

                                // update KFA
                                ChatDLL.UpdateKFAforLead(_LeadDetailResponse.LeadData.ParentLeadID, _LeadDetailResponse.LeadData.CustID, _LeadDetailResponse.LeadData.ProductID, "CHAT");


                                // Get Ranks
                                var response = GetLeadAllocationData(_LeadDetailResponse.LeadData.ParentLeadID, "CHAT");
                                var _allocateLeadResponse = JsonConvert.DeserializeObject<AllocateLeadResponse>(response);
                                if (_allocateLeadResponse != null && _allocateLeadResponse.LeadDetails != null && _allocateLeadResponse.LeadDetails.Count > 0)
                                {
                                    LeadAllocationData = _allocateLeadResponse.LeadDetails[0];
                                }


                            }
                            catch (Exception ex)
                            {
                                LRError = ex.ToString();
                            }
                            finally
                            {
                                var chatLeadRank = 0;
                                if (LeadAllocationData != null && LeadAllocationData.ChatLeadRank > 0)
                                {
                                    chatLeadRank = LeadAllocationData.ChatLeadRank;
                                }
                                else
                                {
                                    // get KFA data, if not available
                                    LeadAllocationData = new();
                                    var KFAdataset = ChatDLL.GetLeadAllocationKeyFactors(_LeadDetailResponse.LeadData.ParentLeadID);

                                    if (KFAdataset != null)
                                    {
                                        var leadKFA = KFAdataset.Tables[0].Rows[0];
                                        LeadAllocationData.PreviousBooking = (leadKFA["PreviousBooking"] == null || leadKFA["PreviousBooking"] == DBNull.Value) ? String.Empty : Convert.ToString(Convert.ToInt16(leadKFA["PreviousBooking"]));
                                        LeadAllocationData.RepeatCustomer = (leadKFA["RepeatCustomer"] == null || leadKFA["RepeatCustomer"] == DBNull.Value) ? String.Empty : Convert.ToString(Convert.ToInt16(leadKFA["RepeatCustomer"]));
                                        LeadAllocationData.InsurerID = (leadKFA["InsurerID"] == null || leadKFA["InsurerID"] == DBNull.Value) ? 0 : (Int32)leadKFA["InsurerID"];
                                    }
                                    else
                                    {
                                        LeadAllocationData.PreviousBooking = "0";
                                        LeadAllocationData.RepeatCustomer = "0";
                                        LeadAllocationData.InsurerID = 0;
                                    }
                                }

                                if (chatLeadRank > 0) { }
                                else if (LeadAllocationData != null
                                    && (
                                        LeadAllocationData.InsurerID > 0
                                        || (CoreCommonMethods.IsValidInteger(LeadAllocationData.RepeatCustomer) > 0)
                                    )
                                )
                                {
                                    chatLeadRank = LeadID % 2 == 0 ? 81 : 82;
                                }
                                else
                                {
                                    chatLeadRank = LeadID % 2 == 0 ? 83 : 84;
                                }
                                var leadid = _LeadDetailResponse.LeadData.ParentLeadID != 0 ? _LeadDetailResponse.LeadData.ParentLeadID : LeadID;

                                // Update leadrank
                                _LeadDetailResponse.LeadData.LeadRank = chatLeadRank;

                                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(leadid), LRError, "GetLeadAllocationData", "MatrixCoreAPI", "ChatBll", leadid.ToString(), chatLeadRank.ToString(), DateTime.Now, DateTime.Now);
                            }
                        }
                    }
                    catch { }
                    #endregion

                    //empty mobile No
                    _LeadDetailResponse.LeadData.MobileNo = "";
                    LeadResponse.Data = new LeadData();
                    LeadResponse.Status = true;
                    LeadResponse.Data = _LeadDetailResponse.LeadData;
                    return LeadResponse;
                }
                else
                {
                    LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, strException, "GetcustInfo-Chat-NoData", "communication", string.Empty, string.Empty, "Lead Data Not Found", DateTime.Now, DateTime.Now);
                    return null;
                }

            }
            catch (Exception ex)
            {
                strException = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, strException, "GetleadInfo-Chat", "communication", string.Empty, string.Empty, string.Empty, DateTime.Now, DateTime.Now);
                return null;
                //throw ex;
            }
        }

        public LeadAgentDetailsCHAT GetAssignedLeadAgentDetails(long leadid, short productId)
        {
            LeadAgentDetailsCHAT obj = new();
            string error = string.Empty;
            DateTime reqTime = DateTime.Now;
            try
            {
                obj.AgentDetails = LeadPrioritizationDLL.GetAssignedAgentData(leadid);
                if (productId == 2)
                {
                    var response = GetLeadAllocationData(leadid, "CHAT", false, true);
                    if (response != null)
                    {
                        var _allocateLeadResponse = JsonConvert.DeserializeObject<AllocateLeadResponse>(response);
                        if (_allocateLeadResponse != null && _allocateLeadResponse.LeadDetails != null && _allocateLeadResponse.LeadDetails.Count > 0)
                        {
                            obj.NewLeadRank = _allocateLeadResponse.LeadDetails[0].ChatLeadRank;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadid, error, "GetAssignedLeadAgentDetails", "MatrixCore", "ChatBLL", string.Empty, JsonConvert.SerializeObject(obj), reqTime, DateTime.Now);
            }
            
            return obj;
        }

    }
}
