using Confluent.Kafka;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using PropertyLayers;
using System;
using System.Threading.Tasks;

public static class KafkaWrapper
{
    private static KafkaConfig? _kafkaConfig;
    

    public static async Task PushToKafka<T>(T request,string Topic= "bms-logger-topic")
    {
        string serializedData = string.Empty;
        DateTime reqtime = DateTime.Now;
        try
        {
            serializedData = JsonConvert.SerializeObject(request);

            _kafkaConfig = ReadKafkaConfig();
           
            if (_kafkaConfig != null)
            {
                var producerConfig = new ProducerConfig
                {
                    BootstrapServers = _kafkaConfig.BootstrapServers,
                    RequestTimeoutMs = _kafkaConfig.Timeout,
                    SocketTimeoutMs = _kafkaConfig.Timeout,
                    MessageSendMaxRetries = 2,
                    SaslUsername = _kafkaConfig.UserName,
                    SaslMechanism = SaslMechanism.Plain,
                    SecurityProtocol = SecurityProtocol.SaslPlaintext,
                    SaslPassword = _kafkaConfig.Password,
                    Acks = Acks.All
                };


                using (var producer = new ProducerBuilder<Null, string>(producerConfig).Build())
                {
                    var response = await producer.ProduceAsync(Topic, new Message<Null, string> { Value = serializedData });
                    producer.Flush(TimeSpan.FromSeconds(2));
                    if (response.Status != PersistenceStatus.Persisted)
                    {
                        Console.WriteLine($"PushToKafka . Request Time: {reqtime}, Response Timeout: {DateTime.Now}, Error: {"Message not persisted"}, Data: {serializedData}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine(string.Format("Method PushToKafka>Reuqest Time>{0}>Response Timeout>{1}> Error>{2}> data>{3}", reqtime, DateTime.Now, ex.ToString(), serializedData));
        }


    }
    private static KafkaConfig ReadKafkaConfig()
    {
        if (_kafkaConfig != null)
            return _kafkaConfig;
        else
        {
            string Enviornment = string.Empty;
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            Enviornment = Environment.GetEnvironmentVariable("MATRIX_ASPNETCORE_ENVIRONMENT");

            if (String.IsNullOrEmpty(Enviornment) || Enviornment.ToLower() == "production")
                Enviornment = con.GetSection("Communication").GetSection("Environment").Value.ToString().ToUpper();
            
            _kafkaConfig = new KafkaConfig()
            {
                BootstrapServers = con.GetSection("Communication").GetSection("KafkaBootstrapServers").GetSection(Enviornment).Value.ToString(),
                UserName = con.GetSection("Communication").GetSection("KafkaUserName").GetSection(Enviornment).Value.ToString(),
                Password = con.GetSection("Communication").GetSection("KafkaPassword").GetSection(Enviornment).Value.ToString(),
                //Topic = Topic,
                Timeout = 5000
            };
            return _kafkaConfig;
        }
    }


}

