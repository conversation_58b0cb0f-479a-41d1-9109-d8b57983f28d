using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Helper;
using System.Runtime.Caching;
using Newtonsoft.Json;
using System.Net;
using System.Text;

namespace Redis
{
    public class MultiProdRedisHelper
    {
        static readonly object _obj = new();
        static readonly object Identity = new();
        private static readonly List<ConnectionMultiplexer> _multiProdRedisList = new();
        private static readonly int poolSize = 10;

        static IDatabase GetMultiProdDatabase()
        {
            int index = new Random().Next(0, poolSize);
            ConnectionMultiplexer connection = _multiProdRedisList.ElementAtOrDefault(index);

            if (_multiProdRedisList == null || _multiProdRedisList.Count == 0 || connection == null || !connection.IsConnected || !connection.GetDatabase().IsConnected(default))
            {
                lock (_obj)
                {
                    if (connection == null || !connection.IsConnected || !connection.GetDatabase().IsConnected(default))
                    {
                        if (connection != null)
                            _multiProdRedisList.RemoveAt(index);

                        IConfiguration con = Custom.ConfigurationManager.AppSetting;

                        string Enviornment = CoreCommonMethods.GetEnvironmentVar().ToUpper();
                        List<string> nodes = con.GetSection("Communication").GetSection("MultiProdRedisConnection").GetSection(Enviornment).Value.Split(',').ToList();
                        string AWSSecretEnv = con.GetSection("Communication").GetSection("MultiProdAWSSecretEnvironment").GetSection(Enviornment).Value.ToString();
                        string RedisPass = GetSecretKeyFromAWS("MultiProdAssigRedisPass", AWSSecretEnv.ToString());
                       
                        ConfigurationOptions option = new()
                        {
                            AbortOnConnectFail = false,
                            ConnectTimeout = 1000,
                            SyncTimeout = 2000,
                            ConnectRetry = 1,
                            Password = RedisPass,
                            Ssl = true
                        };

                        foreach (var node in nodes)
                        {
                            option.EndPoints.Add(node);
                        }

                        connection = ConnectionMultiplexer.Connect(option);
                        _multiProdRedisList.Add(connection);
                    }
                }
            }

            return connection.GetDatabase();
        }

        public static string GetSecretKeyFromAWS(string Key, string SecretEnvironment)
        {
            string EnvObj = string.Empty;
            string Value = string.Empty;
            StringBuilder sb = new StringBuilder();
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string Enviornment = CoreCommonMethods.GetEnvironmentVar().ToUpper();

            try
            {
                string IsMultiProdAWSSceretEnabled = con.GetSection("Communication").GetSection("IsMultiProdAWSSceretEnabled").GetSection(Enviornment).Value.ToString();
                if (CoreCommonMethods.IsValidString(IsMultiProdAWSSceretEnabled) && Convert.ToBoolean(IsMultiProdAWSSceretEnabled))
                {
                    if (MemoryCache.Default[SecretEnvironment] != null)
                    {
                        EnvObj = Convert.ToString(MemoryCache.Default[SecretEnvironment]);
                    }
                    else
                    {
                        lock (Identity)
                        {
                            if (MemoryCache.Default[SecretEnvironment] != null)
                            {
                                EnvObj = Convert.ToString(MemoryCache.Default[SecretEnvironment]);
                            }
                            else
                            {
                                sb.Append(" ==== read secret === ");
                                sb.Append(" ==== IPAddress === " + GetIPAddress());
                                sb.Append(" ,SecretEnvironment" + SecretEnvironment);

                                if (!string.IsNullOrEmpty(SecretEnvironment))
                                { EnvObj = AmazonSecret.GetSecret(SecretEnvironment); }

                                if (CoreCommonMethods.IsValidString(EnvObj))
                                {
                                    sb.Append(" ====  Insert Cache=== " + EnvObj);
                                    CommonCache.GetOrInsertIntoCache(EnvObj, SecretEnvironment, 12 * 60);
                                }
                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(EnvObj))
                    {
                        sb.Append("=========Enter ConnectionObj  is not empty===========");
                        dynamic obj = JsonConvert.DeserializeObject(EnvObj);
                        if (obj != null)
                        {
                            if (Key.ToLower() == "multiprodassigredispass")
                                Value = obj.Password.Value;

                        }
                    }
                    if (string.IsNullOrEmpty(Value))
                    { 
                        Value = con.GetSection("Communication").GetSection("MultiProdAssigRedisPass").GetSection(Enviornment).Value.ToString();
                    }
                }
                else
                {
                    Value = con.GetSection("Communication").GetSection("MultiProdAssigRedisPass").GetSection(Enviornment).Value.ToString();
                }
            }
            catch (Exception ex)
            {
                Value = con.GetSection("Communication").GetSection("MultiProdAssigRedisPass").GetSection(Enviornment).Value.ToString();
            }

            return Value.ToString();

        }

        public static string GetIPAddress()
        {
            string ipaddress = string.Empty;
            try
            {
                string strHostName = System.Net.Dns.GetHostName();
                IPHostEntry ipHostInfo = Dns.GetHostEntry(strHostName);
                IPAddress ipAddress = ipHostInfo.AddressList[0];
                ipaddress = ipAddress.ToString();
            }
            catch (Exception ex)
            {

            }
            return ipaddress;
        }

        public static string GetMultiProdRedisData(string Key)
        {
            IDatabase db = GetMultiProdDatabase();
            string data = db.StringGet(Key, CommandFlags.PreferReplica);
            return data;
        }

        public static void SetMultiProdRedisData(string Key, string Value, TimeSpan timeSpan)
        {
            IDatabase db = GetMultiProdDatabase();
            db.StringSet(Key, Value);
            db.KeyExpire(Key, timeSpan);
        }

        public static void SetMultiProdRedisData(string Key, string Value)
        {
            IDatabase db = GetMultiProdDatabase();
            db.StringSet(Key, Value);
        }

        public static void ClearMultiProdRedisData(string Key)
        {
            IDatabase db = GetMultiProdDatabase();
            db.KeyDelete(Key);
        }

        public static void MSetMultiProdRedisData(Dictionary<string, string> KeyValueList, TimeSpan? timeSpan)
        {
            IDatabase db = GetMultiProdDatabase();
            if (KeyValueList.Any())
            {
                int size = KeyValueList.Count;
                var set = KeyValueList.Select(item => 
                            new KeyValuePair<RedisKey, RedisValue>(item.Key, item.Value)).ToArray();
                if (set.Any())
                {
                    foreach (var item in set)
                    {
                        db.StringSet(item.Key, item.Value, timeSpan);
                    }
                }
            }
        }
    }    
    
}