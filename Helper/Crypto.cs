﻿using System;
using System.Text;
using System.Security.Cryptography;
using System.IO;
using System.Text.RegularExpressions;
using System.Web;
using Microsoft.Extensions.Configuration;


namespace Helper
{
    public static class Crypto
    {

        public static string MaskMobileNo(this string mobileNo)
        {
            int show = 2;
            if (string.IsNullOrEmpty(mobileNo) || mobileNo.Length <= show)
            {
                return mobileNo;
            }
            StringBuilder new_string = new StringBuilder();
            for (var i = 0; i < mobileNo.Length - show; i++)
            {
                new_string.Append("X");
            }
            new_string.Append(mobileNo.Substring(mobileNo.Length - show));

            return new_string.ToString();
        }
        #region Encrypt Message

        public static string EncryptString(string plainMessage)
        {
            string strmsg = string.Empty;
            try
            {
                byte[] encode = new byte[plainMessage.Length];
                encode = Encoding.UTF8.GetBytes(plainMessage);
                strmsg = Convert.ToBase64String(encode);
                encode = null;
            }
            catch (Exception ex)
            {
            }
            return strmsg;
        }

        public static String encrypt_AES(String Input, int KeySize, int BlockSize, string encKey, string IVKey)
        {
            var aes = new RijndaelManaged();
            aes.KeySize = KeySize;
            aes.BlockSize = BlockSize;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String(encKey);
            aes.IV = Convert.FromBase64String(IVKey);
            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Encoding.UTF8.GetBytes(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = ms.ToArray();
            }
            String Output = Convert.ToBase64String(xBuff);
            //var bytes = Encoding.UTF8.GetBytes(Output);
            //Output = Convert.ToBase64String(bytes);

            return Output;
        }

        public static string decrypt_AES(string Input, int KeySize, int BlockSize, string encKey, string IVKey)
        {
            RijndaelManaged aes = new RijndaelManaged();
            aes.KeySize = KeySize;
            aes.BlockSize = BlockSize;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Convert.FromBase64String(encKey);
            aes.IV = Convert.FromBase64String(IVKey);
            var decrypt = aes.CreateDecryptor();
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = ms.ToArray();
            }
            String Output = Encoding.UTF8.GetString(xBuff);
            return Output;
        }

        public static string EncryptStringAES(string plainMessage)
        {
            string strmsg = string.Empty;
            try
            {
                byte[] encode = new byte[plainMessage.Length];
                encode = Encoding.UTF8.GetBytes(plainMessage);
                strmsg = Convert.ToBase64String(encode);
                encode = null;
            }
            catch (Exception ex)
            {
            }
            return strmsg;
        }

        public static String Encrypt_AES256(String Input)
        {
            String enryptStringCrypto = "";
            string Inputkey = "AKSA18CDY3464CG0A2E8O71F9B6B9EA9";
            var result = EncryptRijndael(Input, ref enryptStringCrypto, Inputkey);
            return enryptStringCrypto;
        }
        public static String Decrypt_AES256(String Input)
        {
            string decryptCrypto = "";
            string Inputkey = "AKSA18CDY3464CG0A2E8O71F9B6B9EA9";
            var result = DecryptRijndael(Input, ref decryptCrypto, Inputkey);
            return decryptCrypto;
        }


        #endregion

        #region Decrypt Message

        public static string DecryptString(string EncryptMessage)
        {
            string decryptpwd = string.Empty;
            try
            {
                UTF8Encoding encodepwd = new UTF8Encoding();
                Decoder Decode = encodepwd.GetDecoder();
                byte[] todecode_byte = Convert.FromBase64String(EncryptMessage);
                int charCount = Decode.GetCharCount(todecode_byte, 0, todecode_byte.Length);
                char[] decoded_char = new char[charCount];
                Decode.GetChars(todecode_byte, 0, todecode_byte.Length, decoded_char, 0);
                decryptpwd = new String(decoded_char);
                todecode_byte = null;
            }
            catch
            {

            }
            return decryptpwd;
        }

        public static bool EncryptRijndael(string plainString, ref string encString, String Key)
        {
            try
            {
                if (string.IsNullOrEmpty(plainString))
                    throw new ArgumentNullException("Not a valid text");

                var aesAlg = NewRijndaelManaged(Key);

                var encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);
                var msEncrypt = new MemoryStream();
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                using (var swEncrypt = new StreamWriter(csEncrypt))
                {
                    swEncrypt.Write(plainString);
                }
                var s = msEncrypt.ToArray();
                encString = Convert.ToBase64String(msEncrypt.ToArray());
                return true;
            }
            catch (Exception ex)
            {
                encString = ex.Message;
                return false;
            }
        }

        public static bool DecryptRijndael(string cipherText, ref string decString, string key)
        {
            try
            {
                if (string.IsNullOrEmpty(cipherText))
                    throw new ArgumentNullException("cipherText");

                if (!IsBase64String(cipherText))
                    throw new Exception("The cipherText input parameter is not base64 encoded");

                var aesAlg = NewRijndaelManaged(key);
                var decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);
                var cipher = Convert.FromBase64String(cipherText);

                using (var msDecrypt = new MemoryStream(cipher))
                {
                    using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                    {
                        using (var srDecrypt = new StreamReader(csDecrypt))
                        {
                            decString = srDecrypt.ReadToEnd();
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                decString = ex.Message;
                return false;
            }
        }

        private static bool IsBase64String(string base64String)
        {
            base64String = base64String.Trim();
            return (base64String.Length % 4 == 0) &&
                   Regex.IsMatch(base64String, @"^[a-zA-Z0-9\+/]*={0,3}$", RegexOptions.None);

        }

        private static RijndaelManaged NewRijndaelManaged(string Inputkey)
        {
            var aesAlg = new RijndaelManaged();
            aesAlg.Key = Encoding.ASCII.GetBytes(Inputkey);
            aesAlg.IV = Encoding.ASCII.GetBytes(Inputkey.Substring(0, 16));
            return aesAlg;
        }

        #endregion

        public static String Encrypt(this String Input)
        {
            IConfiguration con = Custom.ConfigurationManager.AppSetting;
            string _saltKey = con.GetSection("Communication").GetSection("SaltKey").Value.ToString();
            string _initializationVector = con.GetSection("Communication").GetSection("InitializationVectorKey").Value.ToString();

            var aes = new RijndaelManaged
            {
                KeySize = 128,
                BlockSize = 128,
                Padding = PaddingMode.PKCS7,
                Key = Convert.FromBase64String(_saltKey),
                IV = Convert.FromBase64String(_initializationVector)
            };

            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Encoding.UTF8.GetBytes(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }

                xBuff = ms.ToArray();
            }

            String Output = Convert.ToBase64String(xBuff);
            return Output;
        }

        public static String Decrypt(this String Input)
        {
            try
            {
                IConfiguration con = Custom.ConfigurationManager.AppSetting;
                string _saltKey = con.GetSection("Communication").GetSection("SaltKey").Value.ToString();
                string _initializationVector = con.GetSection("Communication").GetSection("InitializationVectorKey").Value.ToString();


                Input = Input.Replace(" ", "+").Replace("\\", "");
                RijndaelManaged aes = new RijndaelManaged();
                aes.KeySize = 128;
                aes.BlockSize = 128;
                aes.Mode = CipherMode.CBC;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = Convert.FromBase64String(_saltKey);
                aes.IV = Convert.FromBase64String(_initializationVector);

                var decrypt = aes.CreateDecryptor();
                byte[] xBuff = null;
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                    {
                        byte[] xXml = Convert.FromBase64String(Input);
                        cs.Write(xXml, 0, xXml.Length);
                    }

                    xBuff = ms.ToArray();
                }

                String Output = Encoding.UTF8.GetString(xBuff);
                return Output;
            }
            catch (CryptographicException ex)
            {
                return Input;
            }
        }

        public static String encrypt_AES(String Input, string Key, string IV, int KeySize = 256, int BlockSize = 256)
        {
            var aes = new RijndaelManaged
            {
                KeySize = KeySize,
                BlockSize = BlockSize,
                Padding = PaddingMode.PKCS7,
                Key = Convert.FromBase64String(Key),
                IV = Convert.FromBase64String(IV)
            };
            var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Encoding.UTF8.GetBytes(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }
                xBuff = ms.ToArray();
            }
            String Output = Convert.ToBase64String(xBuff);
            //var bytes = Encoding.UTF8.GetBytes(Output);
            //Output = Convert.ToBase64String(bytes);

            return Output;
        }

        public static string GetValidationKey(long leadId, string Key, string IV)
        {
            string encryptedLeadId = encrypt_AES(leadId.ToString(), Key, IV);
            string encryptedEncodedLeadId = EncryptString(encryptedLeadId);
            return UrlEncode(encryptedEncodedLeadId);

        }

        public static string UrlEncode(string input)
        {
            return HttpUtility.UrlEncode(input);
        }
        public static string UrlDeEncode(string input)
        {
            return HttpUtility.UrlDecode(input);
        }
        public static String CommonEncrytion(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey)
        {
            if (string.IsNullOrWhiteSpace(Input))
            {
                return string.Empty;
            }
            try { 
                var aes = new RijndaelManaged();
                UnicodeEncoding UE = new UnicodeEncoding();
                aes.KeySize = KeySize;
                aes.BlockSize = BlockSize;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = Convert.FromBase64String(encKey);
                aes.IV = Convert.FromBase64String(IVKey);
                var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
                byte[] xBuff = null;
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                    {
                        byte[] xXml = Encoding.UTF8.GetBytes(Input);
                        cs.Write(xXml, 0, xXml.Length);
                    }

                    xBuff = ms.ToArray();
                }
                String Output = Convert.ToBase64String(xBuff);
                byte[] bytes = Encoding.GetEncoding(28591).GetBytes(Output);
                string baseKey = Convert.ToBase64String(bytes);
                return HttpUtility.UrlEncode(baseKey);
            }
            catch
            {
                return Input;
            }
        }


        public static String Encrytion_Payment_AES(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey, bool IsUrlEncoded = true)
        {
            String Output = string.Empty;
            try
            {
                var aes = new RijndaelManaged();
                aes.KeySize = KeySize;
                aes.BlockSize = BlockSize;
                aes.Padding = PaddingMode.PKCS7;
                aes.Key = Encoding.UTF8.GetBytes(encKey);
                aes.IV = Encoding.UTF8.GetBytes(IVKey);
                var encrypt = aes.CreateEncryptor(aes.Key, aes.IV);
                byte[] xBuff = null;
                using (var ms = new MemoryStream())
                {
                    using (var cs = new CryptoStream(ms, encrypt, CryptoStreamMode.Write))
                    {
                        byte[] xXml = Encoding.UTF8.GetBytes(Input);
                        cs.Write(xXml, 0, xXml.Length);
                    }

                    xBuff = ms.ToArray();
                }
                Output = Convert.ToBase64String(xBuff);
                if (IsUrlEncoded)
                    Output = UrlEncode(Output);
            }
            catch (Exception ex)
            {

            }

            return Output;
        }
        

        public static String Decrytion_Payment_AES(String Input, string encSource, int KeySize, int BlockSize, string encKey, string IVKey, bool IsUrlEncoded = false)
        {
            String Output = string.Empty;
            if(IsUrlEncoded)
            {
                Input = UrlDeEncode(Input);
            }
            Input = Input.Replace(" ", "+").Replace("\\", "");
            RijndaelManaged aes = new RijndaelManaged();
            aes.KeySize = KeySize;
            aes.BlockSize = BlockSize;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;
            aes.Key = Encoding.UTF8.GetBytes(encKey);
            aes.IV = Encoding.UTF8.GetBytes(IVKey);

            var decrypt = aes.CreateDecryptor();
            byte[] xBuff = null;
            using (var ms = new MemoryStream())
            {
                using (var cs = new CryptoStream(ms, decrypt, CryptoStreamMode.Write))
                {
                    byte[] xXml = Convert.FromBase64String(Input);
                    cs.Write(xXml, 0, xXml.Length);
                }

                xBuff = ms.ToArray();
            }

            Output = Encoding.UTF8.GetString(xBuff);

            return Output;
        }

        public static string MaskEmailID(string EmailId)
        {
            if (!string.IsNullOrEmpty(EmailId))
            {
                string pattern = @"(?<=[\w]{1})[\w-\._\+%]*(?=[\w]{1}@)";
                EmailId = Regex.Replace(EmailId, pattern, m => new string('*', m.Length));
            }
            return EmailId;
        }

        public static string EncryptString(string textToEncrypt, string aesKey, string aesIV, int KeySize, int BlockSize)
        {
            try
            {
                CryptoStream cryptoStream = null;
                RijndaelManaged rijndaelManaged = null;
                ICryptoTransform encrypt = null;
                MemoryStream memoryStream = new MemoryStream();
                if (!string.IsNullOrEmpty(textToEncrypt))
                {
                    // Create crypto objects  
                    rijndaelManaged = new RijndaelManaged();
                    rijndaelManaged.BlockSize = BlockSize;
                    rijndaelManaged.KeySize = KeySize;
                    rijndaelManaged.Padding = PaddingMode.PKCS7;
                    rijndaelManaged.Key = Convert.FromBase64String(aesKey);
                    rijndaelManaged.IV = Convert.FromBase64String(aesIV);
                    encrypt = rijndaelManaged.CreateEncryptor();
                    cryptoStream = new CryptoStream(memoryStream, encrypt, CryptoStreamMode.Write);
                    // Write encrypted value into memory  
                    byte[] input = Encoding.UTF8.GetBytes(textToEncrypt);
                    cryptoStream.Write(input, 0, input.Length);
                    cryptoStream.FlushFinalBlock();
                    var output = Convert.ToBase64String(memoryStream.ToArray());
                    string base64Encoded = Convert.ToBase64String(Encoding.UTF8.GetBytes(output));
                    string urlEncoded = HttpUtility.UrlEncode(base64Encoded);
                    // Retrieve the encrypted value to return  
                    return urlEncoded;
                }
                else
                    return textToEncrypt;
            }
            catch(Exception ex)
            {
                return textToEncrypt;
            }
            
        }

        public static string EncryptBase64(string Input)
        {
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(Input);
            return System.Convert.ToBase64String(plainTextBytes);
        }

        public static string AESencrypt_Communication(string plainText)
        {
            byte[] key = Encoding.UTF8.GetBytes("NjU1YjIwZjg2NWE1ZDEwMDAxM2QxYTk0");
            using (Aes aesAlg = Aes.Create())
            {
                aesAlg.Key = key;
                aesAlg.IV = Convert.FromBase64String("S0d4bUhSaXZqeXNvSG1aUQ==");
                aesAlg.Mode = CipherMode.CBC;

                ICryptoTransform encryptor = aesAlg.CreateEncryptor(aesAlg.Key, aesAlg.IV);

                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        using (StreamWriter swEncrypt = new StreamWriter(csEncrypt))
                        {
                            swEncrypt.Write(plainText);
                        }
                    }
                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }

    }
}
