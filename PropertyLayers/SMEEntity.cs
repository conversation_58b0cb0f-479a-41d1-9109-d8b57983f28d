﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace PropertyLayers
{
    public class MyLeadData
    {
        public long LeadId { get; set; }
        public long CustomerId { get; set; }
        public string LeadSource { get; set; }
        public DateTime CreatedOn { get; set; }
        public long ParentId { get; set; }
        public short SubProductId { get; set; }
        public string CompanyName { get; set; }
        public decimal Premium { get; set; }
        public string CIN { get; set; }
        public short StatusId { get; set; }
        public string StatusName { get; set; }
        public string SubProductName { get; set; }
        public short RFQStatusId { get; set; }
        public string RFQStatus { get; set; }
        public long QuoteAgentId { get; set; }
        public string QuoteAgentName { get; set; }
        public long AssignedAgentId { get; set; }
        public string AssignedAgentName { get; set; }
        public long RenewalLeadId { get; set; }
        public string ExpiryDate { get; set; }
        public string PolicyNo { get; set; }
        public string LastYearInsurer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short LinkedinConnection { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LinkedinLink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ClientCityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long PolicyStartDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short PolicyType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int IndustryTypeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ClaimHistory { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ExistingBroker { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ExistingInsurer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ExistingTPA { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int Probability { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long SumInsured { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int NoOfLives { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UtmSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CrossSellSubProductIds { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string WinningStrategy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PolicyTypeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DecisionMakerCityId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ExecutiveRole { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ParentCompany { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UtmMedium { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OtherExistingBroker { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OtherExistingInsurer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OtherExistingTPA { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SubCIN { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<AltContactInformationDetails> AltContactInformation { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string PolicyDate { get; set; }

    }
    public class MOMData
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime MeetingDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime FollowUpDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MeetingType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CityId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int MeetingStatus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ClientName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ClientDesignation { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MeetingAgenda { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PbAttendees { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CreatedOn { get; set; }
        [DataMember(EmitDefaultValue =false)]
        public string LOB { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallPriority { get; set; }

    }

    public class FeedbackData
    {
        [DataMember(EmitDefaultValue = false)]
        public string Feedback { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserId { get; set; }

    }
    public class LeadDetailsResponse
    {
        public string AgentName { get; set; }
        public string SubProductName { get; set; }
        public string CustomerName { get; set; }
        public string PolicyNumber { get; set; }
        public string ProductAbbreviation { get; set; }
        public short SubproductId { get; set; }
        public string AgentEcode { get; set; }
    }

    public class GeneratePerLifeRateRequest
    {
        public long LeadId { get; set; }
        public long CustomerId { get; set; }
        public string Token { get; set; }
    }

    public class AltContactInformationDetails
    {
        public long LeadId { get; set; }
        public long PrimaryId { get; set; }
        public string AltContactPersonName { get; set; }
        public string AltMobileNo { get; set; }
        public string AltEmailId { get; set; }
    }
}
