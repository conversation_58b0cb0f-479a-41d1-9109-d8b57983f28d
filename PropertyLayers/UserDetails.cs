﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
namespace PropertyLayers
{

    [DataContract]
    public class GroupDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 GroupId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string GroupName
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsBMSGroup
        { get; set; }
    }

    [DataContract]
    public class UserProductMapping
    {
        [DataMember(EmitDefaultValue = false)]
        public List<GroupDetails> Groups
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductName
        { get; set; }
    }

    [DataContract]
    public class UserDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Grade
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsActive
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<UserProductMapping> Products
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string DomainUserName
        { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public bool IsFosAgent
        { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int UserProductId
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ManagerId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public List<string> EmployeeIds { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AssignToGroupId { get; set; }
    }

    [DataContract]
    public class UserList
    {
        [DataMember(EmitDefaultValue = false)]
        public List<UserDetails> Data
        { get; set; }
    }

    [DataContract]
    public class UserGroupMappingList
    {
        [DataMember(EmitDefaultValue = false)]
        public UserList UserListResult
        { get; set; }
    }

    [DataContract]
    public class CTCUserInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId
        { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int32 ManagerId
        { get; set; }

        [DataMember]
        public Int32 TotalCTCCalls
        { get; set; }

        [DataMember]
        public Int32 TotalCTCAttempts
        { get; set; }

        [DataMember]
        public Int32 Answerd
        { get; set; }

        [DataMember]
        public Int32 Unanswered
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 UserId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 RoleId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ManagerName
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ManagerEmpId
        { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int16 ManagerRoleId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 level
        { get; set; }

    }

    [DataContract]
    public class UserCertificationDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ExamMode
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CertificationDate
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Category
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CertificationEndDate
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string WorkLocation
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CertificateType
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int IsAvCertified
        { get; set; }
    }
    [DataContract]
    public class UserDetailsResponse
    {
        [DataMember]
        public bool status { get; set; }
        [DataMember]
        public string message { get; set; }
    }

    [DataContract]
    public class SalesPartnerAndSalesSpecialist
    {
        [DataMember(EmitDefaultValue = false)]
        public List<UserDetails> SalesPartners { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Message { get; set; }
    }

    public class User
    {
        public string UserId { get; set; }
        public string AsteriskToken { get; set; }
        public string EmployeeId { get; set; }
        public int GroupId { get; set; }
    }

    public class CustomerContactDetails
    {
        public string MobileNo { get; set; }
        public bool IsValidCustomer { get; set; }
    }


    public class UserCookie
    {
        public string UserName { get; set; }
        public string EmployeeId { get; set; }
        public string Email { get; set; }
        public int GroupId { get; set; }
        public bool IsQueueAgent { get; set; }
        public string UserId { get; set; }
        public string RoleId { get; set; }
        public bool IsAsterickDialer { get; set; }
        public bool IsCreateLead { get; set; }
        public bool IsProgressive { get; set; }
        public string Asterisk_IP { get; set; }
        public string Asterisk_Url { get; set; }
        public string DialerPWD { get; set; }
        public string CallingCompany { get; set; }
        public string DIDNo { get; set; }
        public object Context { get; set; }
        public object Queue { get; set; }
        public int AvgRating { get; set; }
        public int ResRating { get; set; }
        public int NorRating { get; set; }
        public int QualityScore { get; set; }
        public int IssuanceScore { get; set; }
        public bool IsSOSAgent { get; set; }
        public bool IsNewSV { get; set; }
        public bool IsEnableVC { get; set; }
        public bool IsEnableChat { get; set; }
        public bool IsWebphone { get; set; }
        public bool IsSOSGroup { get; set; }
        public string ManagerName { get; set; }
        public string ManagerEmployeeId { get; set; }
        public int ManagerId { get; set; }
        public bool IsWFH { get; set; }
        public string UserBand { get; set; }
        public DateTime MinVoucherVal { get; set; }
        public DateTime MaxVoucherVal { get; set; }
        public bool IsVoucherEnable { get; set; }
        public int Grade { get; set; }
        public bool IsPODUser { get; set; }
        public string PODManagerId { get; set; }
        public string processId { get; set; }
        public string GroupProcessId { get; set; }
        public string LanguageID { get; set; }
        public bool IsOneLead { get; set; }
        public string UserQueue { get; set; }
        public string Token { get; set; }
        public object mToken { get; set; }
        public Userbumapping[] UserBUMapping { get; set; }
        public int ViewGrpCount { get; set; }
        public string[] viewPrd { get; set; }
        public int IsRenewal { get; set; }
        public int IsChat { get; set; }
        public int IsInbound { get; set; }
    }

    public class Userbumapping
    {
        public int ProductId { get; set; }
        public bool IsRenewal { get; set; }
    }


}
