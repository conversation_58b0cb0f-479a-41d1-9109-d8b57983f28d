﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using MongoDB.Bson;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    public class WhatsAppMessage
    {
        //[BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        //[BsonIgnoreIfDefault]
        public ObjectId id { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo;
        [DataMember(EmitDefaultValue = false)]
        public string name;
        [DataMember(EmitDefaultValue = false)]
        public string source;
        [DataMember(EmitDefaultValue = false)]
        public string emailid;
        [DataMember(EmitDefaultValue = false)]
        public string department;
        [DataMember(EmitDefaultValue = false)]
        public long CustID;
        [DataMember(EmitDefaultValue = false)]
        public string departmentid;
        [DataMember(EmitDefaultValue = false)]
        public string Body;
        [DataMember(EmitDefaultValue = false)]
        public string Message { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RoomID;
        [DataMember(EmitDefaultValue = false)]
        public string UserID;
        [DataMember(EmitDefaultValue = false)]
        public List<Attachments> attachments { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string waid;
        [DataMember(EmitDefaultValue = false)]
        public string read { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool verify { get; set; }
    }
    [DataContract]
    [Serializable]
    public class Attachments
    {
        [DataMember(EmitDefaultValue = false)]
        public string title { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string title_link { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool title_link_download { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string image_url { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string image_type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int image_size { get; set; }
    }




    public class WAResponse
    {
        public WAResult result { get; set; }
    }
    public class WAResult
    {
        public bool IsOptIn { get; set; }
        public bool IsWhatsappAvailable { get; set; }
    }

    public class sendcommunicationResponse
    {
        public string TriggerName { get; set; }
        public long LeadId { get; set; }
        public int CommunicationType { get; set; }
        public long MobileNo { get; set; }
        public int CountryCode { get; set; }
        public int ProductId { get; set; }
        public Inputdata InputData { get; set; }
        public string[] To { get; set; }
        public string[] CC { get; set; }
        public string[] BCC { get; set; }
        public bool IsSalesWA { get; set; }
        public string ProductDisplayName { get; set; }
        public string AgentEcode { get; set; }
        public string AgentName { get; set; }
        public string SMSURL { get; set; }
        public string WhatsappURL { get; set; }
        public string DummyRMName { get; set; }
        public string DummyRMEmail { get; set; }
        public string CustomerName { get; set; }
        public Int16 GroupId { get; set; }
        public long CustomerId { get; set; }
    }

    public class Inputdata
    {
        public string ModelName { get; set; }
        public string MakeName { get; set; }

        public string CustomerName { get; set; }
        public string InsurerName { get; set; }
        public string TollFreeNo { get; set; }
        public string ProductDisplayName { get; set; }
        public string ContinueLink { get; set; }
        public List<Bookingdetail> BookingDetails { get; set; }
        public string OTP { get; set; }
        public string LeadID { get; set; }
        public string Source { get; set; }
        public string ToMobileNo { get; set; }
        public string ProductName { get; set; }
        public string AgentName { get; set; }
        public string AgentEcode { get; set; }
        public string CallTypeId { get; set; }
        public string CallUId { get; set; }
        public string ScheduleDate { get; set; }
        public string ScheduleDay { get; set; }
        public int ProductId { get; set; }
        public long LeadId { get; set; }
        public string ScheduleTime { get; set; }
        public string CustomerAddress { get; set; }
        public string AppointmentDateTime { get; set; }
        public string FOSLink { get; set; }
        public string AppointmentId { get; set; }
        public string AppointmentUID { get; set; }
        public string AppointmentSubStatusID { get; set; }
        public string DynamicURL { get; set; }
        public string DummyRMName { get; set; }
        public string DummyRMEmail { get; set; }
        public string Subject { get; set; }
        public string Content { get; set; }
        public string RenewalLink { get; set; }
        public decimal TotalAmount { get; set; }
        public string MobileNo { get; set; }

        public string SecondaryNumber { get; set; }
        public string VerificationLink { get; set; }
        public Int32 SecondaryCountryCode { get; set; }

        public string SecondaryEmailID { get; set; }
        public Int32 CountryCode { get; set; }

        public string Link { get; set; }
        public string PolicyNo { get; set; }
        public string DueDate { get; set; }
        public decimal Amount { get; set; }
        public decimal SumInsured { get; set; }
        public string PaymentLink { get; set; }
        public string DynamicUrlData { get; set; }
        public string EmployeeName { get; set; }
        public string ECode { get; set; }
        public string AppLink { get; set; }
        public string WhatsAppOptinLink { get; set; }
        public string ApplicationNo { get; set; }
        public long TollFreeNumber { get; set; }
        public string EncryptedEnquiryId { get; set; }
    }

    public class Bookingdetail
    {
        public string InsurerName { get; set; }
        public DateTime PolicyEndDate { get; set; }

        public string PolicyNo { get; set; }
        public string DueDate { get; set; }
        public string PGPaymentLink { get; set; }
        public string PaymentLink { get; set; }
        public string GraceDate { get; set; }
        public string PlanName { get; set; }
        public long FinalPremium { get; set; }
        public string PaymentPeriodicity { get; set; }
        public string SupplierLogo { get; set; }
        public string ProductDisplayName { get; set; }
        public string PgRegistrationLink { get; set; }

    }

    public class ShortLinkResponse
    {
        public bool status { get; set; }
        public string url { get; set; }
    }




}
