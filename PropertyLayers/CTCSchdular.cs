﻿using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;

namespace PropertyLayers
{
    [DataContract]
    public class CTCSchdular
    {
        [BsonIgnoreIfDefault]
        [DataMember]
        public long id
        {
            get;
            set;
        }
                
        [BsonIgnoreIfDefault]
        [DataMember]
        public long LeadID
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public long CTCLeadID
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string GroupID
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string LeadSource
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string ScheduleTime
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string CreatedOn
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string SourceIP
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public bool CallNow
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public int Issue
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public int SubIssue
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string Comments
        {
            get;
            set;
        }        
        [BsonIgnoreIfDefault]
        [DataMember]
        public byte IsGoogleInvite
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public long MobileNO
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public byte IsPicked
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string NotPickedReason
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string ProcessName
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string IssueCode
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string SubIssueCode
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string encryptMobileNo
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public int ProductId
        {
            get;
            set;
        }
    }

    [DataContract]
    public class BMSCTCSchdularModel
    {
        [BsonIgnoreIfDefault]
        [DataMember]
        public long LeadId
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string GroupCode
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string LeadSource
        {
            get;
            set;
        }

        [BsonIgnoreIfDefault]
        [DataMember]
        public string CallBackDate
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public int Issue
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public int SubIssue
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public int UserId
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public int RequestType
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public int CreatedBy
        {
            get;
            set;
        }
        [BsonIgnoreIfDefault]
        [DataMember]
        public string Remarks
        {
            get;
            set;
        }

        
        public string logurl
        {
            get;
            set;
        }
    }
}
