﻿using System;

namespace PropertyLayers
{
    public class UploadRenewalLeadsDetails
    {
        public string FilePath { get; set; }
        public int ProcessType { get; set; }
        public string UniqueId { get; set; }
        public string UploadedBy { get; set; }
        public string ProductId { get; set; }
        public string AssignedGroupId { get; set; }
        public string AssignedUserId { get; set; }
        public string ProcessId { get; set; }
    }
    public class FileExceptionLog
    {
        public string TrackingId { get; set; }
        public string Exception { get; set; }
        public string UploadedBy { get; set; }
        public DateTime Createon { get; set; }
        public string PrimaryColumn { get; set; }
    }

    public enum ProcessTypes
    {
        SmeBulkLeadUpload = 3,
        PaymentLinkUploads = 4,
        UpsellLeadCreate = 5,
        SmeBulkBooking = 6,
        LastYearLostCases = 7,
        RenewalMissingLeads = 8,
        CrossSmeUpload = 9,
        SmeFosBulkUpload=10,
    }
}
