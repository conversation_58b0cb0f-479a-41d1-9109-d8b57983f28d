﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    public class BookingPaymentModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long? LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public BookingModel BookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public BookingInfo BookingInfo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public MotorBookingDetails MotorBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public PaymentDetails PaymentDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public ProposerDetails ProposerDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<Nominee> Nominee { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public SmeBookingDetails SmeBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public HealthBookingDetails HealthBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public TravelBookingDetails TravelBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public LifeBookingDetails LifeBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public HomeBookingDetails HomeBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public TwBookingDetails TwBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DocumentDetails DocumentDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public CommunicationDetails CommunicationDetails { get; set; }
    }

    [DataContract]
    [Serializable]
    public class SmeBookingDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public decimal Brokerage { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int TotalNoOfLives { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int TotalNoOfEmployees { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int OccupancyId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CorpOrgId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short? MarineCoverType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<InstallmentDetails> InstallmentsData { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string QuoteId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<PremiumListM> PremiumList { get; set; }
    }
    public class PremiumListM
    {
        public decimal? Premium { get; set; }
        public int PremiumTypeId { get; set; }
    }
    [DataContract]
    [Serializable]
    public class HomeBookingDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public byte CoverageType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal BuildingValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal ContentValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte PropertyType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte Purpose { get; set; }
    }

    [DataContract]
    [Serializable]
    public class HealthBookingDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public short Portability { get; set; }



        [DataMember(EmitDefaultValue = false)]
        public short TermTenure { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal TermSI { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string InsurerPolicyNo { get; set; }

    }

    [DataContract]
    [Serializable]
    public class BookingModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long CustomerID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string InsuredName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PolicyType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PolicyTerm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PlanId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? SupplierId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal SumInsured { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? BasicPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal TotalPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? ServiceTax { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? IsSTP { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OfferCreatedON { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 InstallmentsPaid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? PaymentPeriodicity { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PayTerm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CreatedBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BookingType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsEMI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TransRefNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short PartnerId { get; set; }
    }


    [DataContract]
    [Serializable]
    public class BookingInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public string ApplicationNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProposalNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object PolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object PreviousBookingNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object PreviousPolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PrevPolicyExpiryDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int PreviousInsurerID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyDocUploadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int KYCStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int KYCTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string KYCRemarks { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public DateTime? PolicyStartDate { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public DateTime? PolicyEndDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Rider { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? RiderSI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short Portability { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsBookingValidated { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool InCreditPendingPool { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? CreditReceived { get; set; }
    }

    [DataContract]
    [Serializable]
    public class AdditionalVehicleDetail
    {
        public int custAddVehicleId { get; set; }
        public int custVehicleId { get; set; }
        public object registeredOwnerAddress { get; set; }
        public object registrationPostCode { get; set; }
        public object registrationContactNo { get; set; }
        public int previousInsurerId { get; set; }
        public object vehicleOwnedBy { get; set; }
        public object organizationName { get; set; }
        public object contactPersonInOrganization { get; set; }
        public bool vehicleRegisteredOwner { get; set; }
        public bool vehicleBoughtWithinLast12Months { get; set; }
        public bool rcEndorsedInCurrentOwnerName { get; set; }
        public string rcOwnerName { get; set; }
        public object carUsedFor { get; set; }
        public bool claimsMadeInPreviousPolicy { get; set; }
        public int notClaimedSince { get; set; }
        public object registeredOwnerDOB { get; set; }
        public int voluntaryExcess { get; set; }
        public object professionId { get; set; }
        public bool automobileAssociationMember { get; set; }
        public string automobileAssociationName { get; set; }
        public string automobileAssociationMembershipNo { get; set; }
        public string automobileAssociationMembershipExpiryDate { get; set; }
        public bool antiTheftDeviceApprovedByARAI { get; set; }
        public bool availAgeDiscount { get; set; }
        public string loanType { get; set; }
        public string financialInstitutionName { get; set; }
        public object financialInstitutionCode { get; set; }
        public object financialInstitutionAddress { get; set; }
        public string financialInstitutionCity { get; set; }
        public object vehicleDrivenBy { get; set; }
        public object driverFullName { get; set; }
        public object drivingExperience { get; set; }
        public object driverDateOfBirth { get; set; }
        public object driverAge { get; set; }
        public object totalDrivers { get; set; }
        public object driverGender { get; set; }
        public object parkingType { get; set; }
        public object driverMaritalStatus { get; set; }
        public object annualKilometersRuns { get; set; }
        public object totalNumberOfClaimsMade { get; set; }
        public object ncbDocumentHolds { get; set; }
        public object licenseType { get; set; }
        public object licenseAge { get; set; }
        public bool effectiveDL { get; set; }
        public bool underLoan { get; set; }
        public object vehicleMostlyUsed { get; set; }
        public object licenseNumber { get; set; }
        public object licenseIssueDate { get; set; }
        public object licenseExpDate { get; set; }
        public object licenseIssuingAuthority { get; set; }
        public object vehicleColor { get; set; }
        public bool electricalAccessories { get; set; }
        public object electricalAccessoriesInvoiceDate { get; set; }
        public bool nonElectricalAccessories { get; set; }
        public object nonElectricalAccessoriesInvoiceDate { get; set; }
        public bool cngFitted { get; set; }
        public object typeOfCNGKit { get; set; }
        public object cngInvoiceDate { get; set; }
        public object paCoverForUnnamedPassengers { get; set; }
        public bool insurePaidDriver { get; set; }
        public int paidDriverLiabilityAmount { get; set; }
        public object claimAmount { get; set; }
        public bool prePolicyHaveZeroDep { get; set; }
        public object valueOfElectricalAccessories { get; set; }
        public object valueOfNonElectricalAccessories { get; set; }
        public object cngAmount { get; set; }
        public object title { get; set; }
        public object paCoverForOwnerDriver { get; set; }
        public object bodyTypeId { get; set; }
        public object actualCC { get; set; }
        public bool isTransferPreviousNCB { get; set; }
        public bool isLLTPDiscountOpted { get; set; }
        public bool isPolicyBreakCase { get; set; }
        public bool isPolicyHolder { get; set; }
        public bool isVehicleSecondHand { get; set; }
        public bool isExistingOwnerChange { get; set; }
        public bool isCommercialVehicle { get; set; }
        public bool isOwnerDriverCoverOpted { get; set; }
        public bool isCreditGST { get; set; }
        public bool isInspectionRaised { get; set; }
        public int transferNCBPercentage { get; set; }
        public int policyType { get; set; }
        public int exShowRoomPrice { get; set; }
        public string previousPolicyExpiryDate { get; set; }
        public int policySubType { get; set; }
        public int previousPolicyType { get; set; }
        public int previousPolicyCategory { get; set; }
        public int renewalIDV { get; set; }
        public int inspectionLocationCode { get; set; }
        public string inspectionLocation { get; set; }
        public int inspectionCity { get; set; }
        public string actualInspectionCity { get; set; }
        public string gsttype { get; set; }
        public int lastYearLeadID { get; set; }
        public int previousTPInsurerId { get; set; }
        public string previousTPPolicyNumber { get; set; }
        public int supplierIdv { get; set; }
        public int serviceTax { get; set; }
        public int finalPremium { get; set; }
        public string inspectionStatus { get; set; }
        public string inspectionDate { get; set; }
        public string preferredInspectionDate { get; set; }
        public string discountType { get; set; }
        public int isPrevInsurerZeroDep { get; set; }
        public int biFuelKitId { get; set; }
        public bool isUsageBasedPlan { get; set; }
        public int optedKms { get; set; }
        public int odometerReading { get; set; }
        public bool isShortTermSOAD { get; set; }
        public string payAsDrivePremium { get; set; }
    }

    [DataContract]
    [Serializable]
    public class TwBookingDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? ODPremium { get; set; }
    }

    [DataContract]
    [Serializable]
    public class CommunicationDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public byte? BlockAutoCommunication { get; set; }        
    }

    [DataContract]
    [Serializable]
    public class DocumentDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public byte? DocumentRequired { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int? Medical_or_InspectionRequired { get; set; }
    }

    [DataContract]
    [Serializable]
    public class MotorBookingDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public bool IsPAYD { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int PAYDPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? ODPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? TPPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int OdoReading { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int OptedKM { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ErrorCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AgentNote { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int PostIssuanceInspectionTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TPStartDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TPEndDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int InspectionType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }
    }

    [DataContract]
    [Serializable]
    public class Nominee
    {
        [DataMember(EmitDefaultValue = false)]
        public int NomineeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DOB { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int Gender { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ContactNumber { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RelTypeWithProposer { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int PercentageShare { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AppointeeName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AppointeeDOB { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AppointeeRelWithNominee { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PaymentDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string PaymentMode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ItemId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OrderNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PaymentStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PaymentSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime PaymentDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PaymentSubStatus { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PolicyMotorQuoteBreakUpDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public int custMotorQutbrkId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int custVehicleID { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int basicPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int electricalAccessoriesPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int insurerDiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int nonElectricalAccessoriesPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int biFuelKitPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int totalOwnDamagePremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int antiTheftDiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int voluntaryDiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int automobileMembershipDiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int finalTotalDiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int professionDiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int ageDiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int finalOwnDamagePremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int biFuelKitLiabilityPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int totalLiability { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int compulsaryPACoverForOwnerDriverPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int legalLiabilityToPaidDriverPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int paidDriverPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int totalPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int finalTotalLiabilityPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int driveAssurePackagePremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int emergencyCoverPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int packagePremium { get; set; }
        public bool isOnline { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string isIndividualCase { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int keyReplacement { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int quoteReferenceNumber { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int zeroDepPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int totalAddOnPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int dailyACPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int quoteSource { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int windShieldPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool isCorporate { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int discountRate { get; set; }



        [DataMember(EmitDefaultValue = false)]
        public int rimPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool isCorPlan { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object odOnly { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object odOnlyNew { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool isSpecialPrice { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object fireAndTheftPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string createdOn { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int createdBy { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int ncbdiscount { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int tppdliabilityPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int paforUnnamedPassengerPremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int rsapremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int inpcpremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int ncbprotection { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int eppremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int lpbpremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int cocpremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int thepremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int papremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int tcpremium { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int mdpremium { get; set; }

    }

    [DataContract]
    [Serializable]
    public class ProposerDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string ProposerName { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int CountryCode { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string DOB { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int Gender { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string ApplicationNumber { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int MaritalStatus { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string Occupation { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string AnnualIncome { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string PermanentAddress { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string CommunicationAddress { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int CommunicationCityId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int CommunicationStateId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int CustomerID { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int CommunicationPinCode { get; set; }

    }



    [DataContract]
    [Serializable]
    public class VehicalInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public VehicleDetailsModel vehicleDetails { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public AdditionalVehicleDetail additionalVehicleDetail { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public PolicyMotorQuoteBreakUpDetails policyMotorQuoteBreakUpDetails { get; set; }
    }


    [Serializable]
    public class VehicleDetailsModel
    {
        [DataMember(EmitDefaultValue = false)]
        public int custVehicleId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object productId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object vehicleCode { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string chassisNo { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string engineNo { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string registrationNo { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object expiryDate { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object currentInsurerId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string manufacturingDate { get; set; }



        [DataMember(EmitDefaultValue = false)]
        public int customerId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int variantId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int modelId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int makeId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object customerAddressId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int custPolicyId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object vehilceTypeId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object vehicleType { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string registrationDate { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int fuelTypeId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object fuelTypeName { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int cubicCapacityId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int seatingCapacity { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int vehicleIDV { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int electricalAccessoriesIDV { get; set; }


        [DataMember(EmitDefaultValue = false)]

        public int nonElectricalAccessoriesIDV { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int cngLPGUnitIDV { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public int totalIDV { get; set; }



        [DataMember(EmitDefaultValue = false)]
        public object regAddress { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object registeredStateId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object registeredCityId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object registrationCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object registrationRTOCode { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object registrationPostOfficeVORef { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object registrationPostCodeLocality { get; set; }


        [DataMember(EmitDefaultValue = false)]

        public object previousInsurerAddress { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object ccValue { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object vehicleAge { get; set; }


        public object carrierType { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public object vehicleBodyPrice { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object builtType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool imtIncluded { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object busType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object gvw { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object custVehicleIdRef { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object addOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object planType { get; set; }
    }

    public enum PaymentPeriodicityEnum
    {
        Monthly = 1,
        Quaterly = 3,
        HalfYearly = 6,
        Yearly = 12,
        SinglePremium = 13
    }

    [DataContract]
    [Serializable]
    public class TravelBookingDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string DestinationCountry { get; set; }
    }
    [DataContract]
    [Serializable]
    public class LifeBookingDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public bool IsPasa { get; set; }

    }
    public enum PremiumTypeMasters
    {
        Fire = 1,
        Burglary = 2,
        Terrorism = 3
    }

}
