﻿using System;
using System.Runtime.Serialization;
using MongoDB.Bson.Serialization.Attributes;

namespace PropertyLayers
{
    [DataContract]
    public class LeadInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ApplicationNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string InsurerName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int InsurerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PlanName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Campaign { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TransferType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte ServiceAgentGrade { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LeadStatus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int StatusId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int AssignToGroupId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string error { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SearchedLeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 ParentLeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 AssignedToUserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string message { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime? LastUpdatedOn { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public AppointmentsDataModel appDataModel { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AssignUser { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ProductName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsFresh { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public String CustomerName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNumber { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int Make { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime RegistrationDate { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string LeadSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MakeName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ModelName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool RejectAll { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RejectionReason { get; set; }
        [DataMember(EmitDefaultValue =false)]
        public BasicLeadDetails BasicLeadDetail { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int CountryId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CompanyName { get; set; }
    }

    [DataContract]
    public class LeadModel
    {
        [DataMember(EmitDefaultValue = false)]
        public string CustId { get; set; }
    }


    [DataContract]
    public class BasicLeadDetails
    {
        [DataMember(EmitDefaultValue = true)]
        public Int64 LeadID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Name { get; set; }
       [DataMember(EmitDefaultValue = true)]
        public string Gender { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string DOB { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string EmailID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Address { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int CityID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int StateID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string PostCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Country { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string MaritalStatus { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string AnnualIncome { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string LeadSource { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Utm_source { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string UTM_Medium { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Utm_term { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Utm_campaign { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public Int64 ParentID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public Int64 ReferralID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ProductID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Source { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public DateTime CreatedON { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string ExitPointURL { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public bool IsParent { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int CustomerID { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public bool? NoCostEMI { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string MonthlyMode { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string ACH { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public short IsSmartCollect { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public int AssignToGroupId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public DateTime UpdatedOn { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int IsActive { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime LastVisitedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PolicyType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int TalkTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CallScheduleDetails { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RollOver { get; set; }
    }

    [DataContract]
    public class LeadRfqData
    {
        [DataMember]
        public long LeadId { get; set; }
        [DataMember]
        public long CustomerId { get; set; }
        [DataMember]
        public short RFQId { get; set; }
        [DataMember]
        public string Token { get; set; }
        [DataMember]
        public short PolicyTypeId { get; set; }
        [DataMember]
        public string PolicyTypeName { get; set; }
        [DataMember]
        public short PolicySubTypeId { get; set; }
        [DataMember]
        public string PolicySubTypeName { get; set; }
    }

    [DataContract]
    public class AgentCallData
    {
        [DataMember]
        public string? pbcentrallogin { get; set; }
        [DataMember]
        public string? MobileNo { get; set; }

    }

    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class CustomerNANC
    {
        public long CustID { get; set; }
        public short TodayNANCCount { get; set; }
        private DateTime _callDate;

        [DataMember(EmitDefaultValue = false)]
        public DateTime CallDate
        {
            get
            {
                if (_callDate.Kind == DateTimeKind.Utc)
                {
                    _callDate = _callDate.ToLocalTime();
                }
                return _callDate;
            }
            set { _callDate = value; }
        }

        private DateTime _updatedOn;

        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn
        {
            get
            {
                if (_updatedOn.Kind == DateTimeKind.Utc)
                {
                    _updatedOn = _updatedOn.ToLocalTime();
                }
                return _updatedOn;
            }
            set { _updatedOn = value; }
        }
    }
}