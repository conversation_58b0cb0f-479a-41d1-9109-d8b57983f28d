﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;

namespace PropertyLayers
{

    [DataContract]
    public class BookingDetailsModel
    {
        [DataMember(EmitDefaultValue = false)]
        public string OfferNumber { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime? OfferCreatedON { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal TotalPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal SumInsured { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long MatrixLeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public SupplierModelDTO Supplier { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TransRefNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BankNameBranch { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ChequeNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte PolicyTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyTypeName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PaymentStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PaymentSubStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public PlanModelDTO Plan { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PaymentPeriodicity { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BookingType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsEMI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 PartnerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ApplicationNo { get; set; }
        public int? SalesAgent { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CreditReceived { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? ODPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? Medical_or_InspectionRequired { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? DateOfInspection { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string InspectionStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short? Portability { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? ItemId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? NeedId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? PaymentDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PaymentAttempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PaymentMode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? EnquiryId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? PeriodicityAmount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OrderNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long OrderCreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OrderStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? OrderStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public ProductModel Product { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PaymentLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string VoucherNumber { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? ProductTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? ItemStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ItemStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProposalNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? DocumentsRequired { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int MedicalRequired { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 InstallmentPaid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? BookingTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte? PaymentPeriodicityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public UserTimeStamp UserTimeStamp { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? InvestmentTypeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PolicyTerm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? PayTerm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string InsuredName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? RiderSI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Rider { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PreviousBookingNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public SubProduct SubProduct { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PaymentSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte LiveFlag { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Comments { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool ShowBookingDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool UpdatePaymentdetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ReferenceNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? Brokerage { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? IsRSA { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? IsTP { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int Offer { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CompanyName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int NoOfLives { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int NoOfEmployees { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? OccupancyId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsDocReceived { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal LoadingAmount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PrevPolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal PaidPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? PolicyStartDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long? PolicyEndDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TransitType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal TermSI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 TermTenure { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BranchName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OtherOccupany { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CityID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int StateID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long IssuanceDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SubStatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }

        [DataMember(EmitDefaultValue = false)]

        public byte CoverageTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte PropertyTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte PurposeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal InsurerFee { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal PBDiscount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? TPPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? ServiceTax { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? AddonPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmailID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal BuildingSI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal ContentSI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? PremiumExclusion { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? RevenueAddition { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ExpiringInsurer { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyDocName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DocumentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<SalesPartner> SalesPartners { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<SalesPartner> SalesSpecialists { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PrimaryAgentSharePercentage { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public FamilyTypeModel FamilyType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public SumInsuredTypeModel SumInsuredType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<GradeDetailsModel> Grades { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime RegDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool Saod { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal CC { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FuelType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal? Premium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ChassisNumber { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EngineNumber { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 MakeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FuleType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime RegistrationDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int TotalNoOfEmployees { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int TotalNoOfLives { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short BookingProcess { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsConfirmPayment { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public BookingProcess Process { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ShipmentType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? IsSTP { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BookingSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PAN { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string GST { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CIN { get; set; }

        [DataMember(EmitDefaultValue = false)]

        public string? MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UpsellOffer { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OfferDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal UpsellPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UpsellReasonID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UpsellReasonID1 { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long OrgId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long AgentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short? MarineCoverType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SlotSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RenewedChanges { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string InsurerPolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsPasa { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int NCB { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<InstallmentDetails> InstallmentsData { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public LeadDocument LeadDocument { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short? ShopTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool CoInsurance { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short LeadersPercentage { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<FollowerSupplierEntity> FollowerSuppliers { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DestinationCountry { get; set; }

        [DataMember(EmitDefaultValue = false)]

        public int? TransitFromCityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? TransitToCityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OccupationType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ManufacturerTraderName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ManufacturerTraderContactNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ConstitutionOfBusiness { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool InCreditPendingPool { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsBookingShown { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte BlockAutoCommunication { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsRetargeting { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<short> Inclusion { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BulkBookingProcess { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsBookingValidated { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? MedicalExtension { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<WorkerTypeEntity>  WorkerTypes { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<RiskLocation> RiskLocations { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? ChildOccupancyId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal StockSI { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long? AssociationId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int? Loading{ get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int? Discounting { get; set; }
        [DataMember(EmitDefaultValue =false)]
        public string BookingFrom { get; set; }
        [DataMember(EmitDefaultValue =false)]
        public string Source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PolicyCategory { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ChildOccupancies { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string QuoteId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public CityModal CityState { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal? TerrorismPremium { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal? BurglaryPremium { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal? FirePremium { get; set; }
    }

    public class WorkerTypeEntity
    {
        public string WorkerType { get; set; }
        public int NoOfWorker { get; set; }
        public int Salary { get; set; }
    }

    public class RiskLocation
    {
        public string RiskAddress { get; set; }
        public short CityId { get; set; }
        public string PinCode { get; set; }
    }

    public class FollowerSupplierEntity
    {
        public short FollowerPercentage { get; set; }
        public int SupplierId { get; set; }
    }

    public class InstallmentDetails
    {
        public DateTime Date { get; set; }
        public decimal Amount { get; set; }
    }

    public class LeadDocument
    {
        public string DocId { get; set; }
        public short DocTypeId { get; set; }
        public string FileName { get; set; }
    }

    public class UserTimeStamp
    {
        public long CreatedById { get; set; }
        public string CreatedBy { get; set; }
        public long CreatedOn { get; set; }
        public long UpdatedById { get; set; }
        public string UpdatedBy { get; set; }
        public long UpdatedOn { get; set; }
    }

    [DataContract]
    [Serializable]
    public class SumInsuredTypeModel
    {
        [DataMember(EmitDefaultValue = false)]
        public int Id { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }
    }

    public class ProductModel
    {
        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductName { get; set; }

        public List<GroupModel> Groups { get; set; }
    }

    [DataContract]
    [Serializable]
    public class SalesPartner
    {
        [DataMember(EmitDefaultValue = false)]
        public string SharePercentage { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserId { get; set; }
    }

    [DataContract]
    [Serializable]
    public class FamilyTypeModel
    {
        [DataMember(EmitDefaultValue = false)]
        public int Id { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }
    }

    [DataContract]
    [Serializable]
    public class GradeDetailsModel
    {
        [DataMember(EmitDefaultValue = false)]
        public int Id { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal SumInsured { get; set; }
    }


    [DataContract]
    [Serializable]
    public class GroupModel
    {
        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string GroupName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsBMSGroup { get; set; }
    }

    [DataContract]
    [Serializable]
    public class SupplierModelDTO
    {
        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SupplierId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SupplierName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? OldSupplierId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SupplierDisplayName { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PlanModelDTO : SupplierModelDTO
    {
        [DataMember(EmitDefaultValue = false)]
        public int PlanId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PlanName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PlanDisplayName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? OldPlanId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DocumentModelDTO Document { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<DocumentModelDTO> DocumentList { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Brokerage { get; set; }
    }

    [DataContract]
    public class DocumentModelDTO
    {
        [DataMember(EmitDefaultValue = false)]
        public string DocType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DocUrl { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DocName { get; set; }
    }

    [DataContract]
    public class SubProduct
    {
        [DataMember(EmitDefaultValue = false)]
        public int ID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }
    }

    [DataContract]
    public class BmsResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public string TrackingID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool Status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<Dictionary<string, string>> ErrorMessage { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<Dictionary<string, string>> WarningMessage { get; set; }
    }

    public static class PaymentPeriodicity
    {
        public static string GetPaymentPeriodicity(short periodicityId)
        {
            return PaymentPeriodicityDictionary
                .Where(PP => PP.Key == periodicityId)
                .Select(PP => PP.Value).FirstOrDefault();
        }

        private static readonly Dictionary<short, string> PaymentPeriodicityDictionary = new()
        {
            { 1, "Monthly" },
            { 3, "Quarterly" },
            { 6, "Half Yearly" },
            { 12, "Yearly" },
            { 13, "Single Premium" },
            { 14, "Flexi" }
        };
    }

    public enum BookingProcess
    {
        BulkBooking = 1,
        ManualBooking = 2
    }

    public enum PolicyType
    {
        New = 0,
        Renewal = 1
    }

    public enum BooKingType
    {
        Direct = 1,
        Offline = 2
    }

    public class SmeResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public long Data { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ErrorCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsSuccess { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int TotalRecords { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<Dictionary<string, string>> Errors { get; set; }
    }

    [DataContract]
    public class LeadDetailsEntity
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustomerID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DOB { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmailID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Address { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int StateId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long PostCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long ParentID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long StatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long SubStatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string StatusName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SubStatusName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PreviousPolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int PlanID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SupplierID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public double Amount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long EnquiryID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyExpiryDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int RenewalSupplierID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int RenewalPlanID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte InvestmentTypeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int NoOfLives { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int NoOfEmployees { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public byte PolicyTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte CoverTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int OccupancyId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SubProductName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyTypeName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OccupancyName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CompanyName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string InsuredName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string FamilyType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AssociationId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ShipmentType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TransitType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SumInsuredType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int MedicalExtension { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long SA {  get; set; }
    }

    public class KnowYoutAdvisorModel
    {
        [DataMember(EmitDefaultValue = false)]
        public object _id { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductName { get; set; }

        public List<GroupModel> Groups { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string encryptCustId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CustId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 Count { get; set; }
    }

    [DataContract]
    public enum EnumVerifyAdvisor
    {
        Agent_NotFound=1,
        Customer_NotFound=2,
        Attempt_Exceeded=3,
        SomeErrorOccuerd=4
    }
}
