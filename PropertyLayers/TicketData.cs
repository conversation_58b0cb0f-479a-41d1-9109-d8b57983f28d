﻿using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class TicketData
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TicketId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Comment { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CommentBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ActionType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TicketdetailId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SubSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmployeeID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeName { get; set; }



    }
    public class TicketURLData
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 Uid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ECode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 Type { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long LId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 TId { get; set; }


    }

    public class PolicyData
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 CommunicationType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductId { get; set; }

    }
    public class CommBoxModal
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AgentName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 TypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<long> GroupIds { get; set; }
    }
    public class PaymentFailedTicketResponse
    {
        [DataMember(EmitDefaultValue = true)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long ParentId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long TicketID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string TicketDisplayID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string PayId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string OrderId { get; set; }

    }
    public class FeedbackTicket
    {
        [DataMember(EmitDefaultValue = false)]
        public long[] ParentIds { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int IssueId { get; set; }

    }

    public class TicketDataResponse
    {
        public List<TicketDetailsData> Data { get; set; }
        public object Message { get; set; }
        public int ErrorCode { get; set; }
        public object Response { get; set; }
    }

    public class TicketDetailsData
    {
        public long TicketId { get; set; }
        public string TicketDetailsID { get; set; }
        public string AssignToUserName { get; set; }
        public string FollowUpOn { get; set; }
        public string RefObjectId { get; set; }
        public string ProductName { get; set; }
        public int ProductId { get; set; }
        public string SupplierName { get; set; }
        public int StatusID { get; set; }
        public string Status { get; set; }
        public string SubStatusName { get; set; }
        public int LeadID { get; set; }
        public int IssueID { get; set; }
        public string IssueName { get; set; }
        public int SubIssueID { get; set; }
        public string SubIssueName { get; set; }
        public string TicketViewPage { get; set; }
        public int LeadStatusID { get; set; }
        public string LeadStatus { get; set; }
        public string LeadStatusUpdatedOn { get; set; }
        public string UpdatedOn { get; set; }
    }

    public class MyTicketReq
    {
        [DataMember(EmitDefaultValue = false)]
        public long StartDate { get; set; }
        
        [DataMember(EmitDefaultValue = true)]
        public long EndDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short Type { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmployeeID { get; set; }
    }

    public class IssueTypeByLeadIdResp 
    {
        [DataMember]
        public bool status { get; set; }

        [DataMember]
        public string data { get; set; }
    }
}