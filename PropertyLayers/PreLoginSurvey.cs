﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace PropertyLayers
{
    [DataContract]
    public class PreLoginSurvey
    {
        [DataMember(EmitDefaultValue = false)]
        public int SurveyId { get; set; }
        [DataMember(EmitDefaultValue = false)]

        public string SurveyName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SurveyHtmlUrl { get; set; }
        public string ButtonText { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte ContentType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Link { get; set; }
    }

    public class PreLoginSurveyResult
    {
        [DataMember(EmitDefaultValue = false)]
        public int UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Response { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int SurveyId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
    }
}
