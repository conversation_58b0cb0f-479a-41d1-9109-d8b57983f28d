﻿using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class SendMailResponse
    {
        public int value { get; set; }
        public string deliveryResponse { get; set; }
        public string commProvider { get; set; }

        public SendMailResponse()
        {
            value = 0;
            deliveryResponse = "";
            commProvider = "";
        }
    }

    public class MailboxResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public string InboxURL { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public MailBoxUserData Data { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsSuccess { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Message { get; set; }
    }

    public class MailBoxUserData
    {
        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsPersonalAdmin { get; set; }
    }
}