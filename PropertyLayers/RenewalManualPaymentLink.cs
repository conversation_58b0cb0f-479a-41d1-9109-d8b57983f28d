﻿using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class RenewalManualPaymentLink
    {

        [DataMember]
        public long LeadId { get; set; }
        
        [DataMember]
        public int ID { get; set; }

        [DataMember]
        public string emailId { get; set; }

        [DataMember]
        public bool IsEmailchanged { get; set; }

        [DataMember]
        public short txTypeId { get; set; }

        [DataMember]
        public bool NSTP { get; set; }

        [DataMember]
        public bool HoldPayment { get; set; }

        [DataMember]
        public bool Emandate { get; set; }

        [DataMember]
        public string proposalNo { get; set; }

        [DataMember]
        public int PlanID { get; set; }

        [DataMember]
        public long SumInsured { get; set; }
    }
}
