﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class CommunicationLogsResponse
    {
        public List<CommunicationLog> Data { get; set; }
    }

    public class CommunicationLog
    {
        public long UserId { get; set; }
        public DateTime CommunicationDate { get; set; }
        public string TriggerName { get; set; }
        public string CommType { get; set; }
        public string CommId { get; set; }
        public string EmployeeId { get; set; }
        public string UserName { get; set; }
        public string ConversationType { get; set; }
        public long LeadId { get; set; }
        public bool IsBooking { get; set; }
    }

    [DataContract]
    public class CommHistoryResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public string InteractionId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TriggerName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public EnumCommTypes CommType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CommTypeValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ConversationType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ConversationTypeValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AgentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AgentName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CallDuration { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CommunicationDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<Disposition> DispositionDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CallID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncrptyCallID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncrptAgentID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CallDataID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptCallDataID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MaskMobileNo { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string Context { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int64 CallingNo { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string CallType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int CallTalktime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Disposition { get; set; }
        [DataMember]
        public int IsBMS { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TranferType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallDisconnected { get; set; }
    }

    [DataContract]
    public class AgentCommLogs
    {
        [DataMember(EmitDefaultValue = true)]
        public long LeadId { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public long CustomerId { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string CompanyName { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public DateTime TimeStamp { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string Comments { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public int Talktime { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string CommType { get; set; }
    }

    [DataContract]
    public enum EnumCommTypes
    {
        Email = 1,
        SMS = 2,
        OB = 3,
        Chat = 4,
        OTP = 5,
        IB = 6,
        C2C = 7,
        WA = 8,
        VIDEOMEET = 9,
        SCREENSHARE = 10,
        TRANSFERCALL = 21,
        FOSVisit  = 22
    }
}
