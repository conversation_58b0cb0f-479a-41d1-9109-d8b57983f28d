﻿using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
namespace PropertyLayers
{
    [DataContract]  
    public class LeadDetailResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public LeadData LeadData
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public ClaimData ClaimData
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public PolicyDetails PolicyDetails
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public HealthRenewalBenefit HealthRenewalBenefit
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public BookingPolicyDetails BookingPolicyDetails
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public List<AppointmentDetail> AppointmentDetails
        {
            get;
            set;
        }
        [DataMember]
        public CompanyContact CompanyContactInfo
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public TicketDetail TicketDetail { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public SupplierDocUploadInfo SupplierDocUploadInfo
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public HEXSelectionDetails HEXSelectionDetails
        {
            get;
            set;
        }
        
    }
    [DataContract]
    public class KVPair
    {
        [DataMember(EmitDefaultValue = false)]
        public string Key { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Value { get; set; }
    }
    [DataContract]
    public class ClaimData
    {
        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }
 
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }
        
        [DataMember(EmitDefaultValue = false)]
        public string ContactNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<KVPair> AttachmentsLinks { get; set; }
    }
    [DataContract]
    public class LeadData
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long ParentLeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long EnquiryId
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string EncrypEnquiryId
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public int CoreLeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int NeedID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int EnquiryID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public long CustID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int GroupID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedDateTime
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string ProductName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int32 LeadCreationDays
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string LeadSource
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string AgentID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string AgentName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string TLName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string TLID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int16 LeadStatusID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SubStatusID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string TriggerName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string LeadStatusName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public DateTime DOB
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string Gender
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string Address
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PostCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AltPhoneNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MaritalStatus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ContinueJourneyLink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallBackDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallBackTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ShortMobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Remarks { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MFlink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string GM_CompanyName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ChatDepartmentID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 CustomerAge { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string InvestmentType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte invflag { get; set; }
        public string AnnualIncome
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string welcomemessage { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte cb { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccBaseLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string HotLine { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptLeadId
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptMobileNo
        { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Footer { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LogoUrl { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccShortLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TicketID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccDocShortLink { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MyAccEndoShortLink { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool isexpired { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UtmSource
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public bool newcar { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte SubProductId
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string SubProductName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string GroupType
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string Make { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Model { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LastYearPolicyNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LastYearPolicyExpiryDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 LeadRank { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string VariantName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string GroupName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MyAccEncData { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CountryCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UtmContent { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Language { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public bool RecFound { get; set; }
    }

    [DataContract]
    public class PolicyDetails:commonPolicyDetails
    {        
        [DataMember]
        public string PlanSelectionDate
        {
            get;
            set;
        }
        [DataMember]
        public string PolicyExpiredDate
        {
            get;
            set;
        }
        [DataMember]
        public Int32 PolicyExpiryDays
        {
            get;
            set;
        }        
        [DataMember]
        public string PlanReviewURL { get; set; }
        [DataMember]
        public string PaymentLink { get; set; }
        [DataMember]
        public string FamilyType { get; set; }
        [DataMember]
        public string TwoYearPremium { get; set; }
    }

    [DataContract]
    public class CompanyContact
    {
        [DataMember]
        public string WebSiteLink
        {
            get;
            set;
        }
        [DataMember]
        public string TelPhoneNo
        {
            get;
            set;
        }
        [DataMember]
        public string TollFreeNo
        {
            get;
            set;
        }
        [DataMember]
        public string EmailTo
        {
            get;
            set;
        }
        [DataMember]
        public string Fax
        {
            get;
            set;
        }
        [DataMember]
        public string CustCareEmailTo { get; set; }
        [DataMember]
        public string MobileWebSiteLink { get; set; }
        [DataMember]
        public string ImageLogoURL { get; set; }
        [DataMember]
        public string ContactEmailTo { get; set; }
        [DataMember]
        public string TicketUrl { get; set; }
        [DataMember]
        public string WhatsApNo { get; set; }
        [DataMember]
        public string FooterEmail { get; set; }
        [DataMember]
        public string FooterContactNo { get; set; }

        [DataMember]
        public string Address { get; set; }

        [DataMember]
        public string ClaimTollFreeNo { get; set; }
    }

    [DataContract]
    public class BookingPolicyDetails : commonPolicyDetails
    {
        //[DataMember]
        //public string SumInsured { get; set; }
        [DataMember]
        public DateTime BookingDate { get; set; }
        [DataMember]
        public string PolicyTypeName { get; set; }
        [DataMember]
        public string ApplicationNo { get; set; }
        [DataMember]
        public string PolicyType { get; set; }
        [DataMember]
        public string UploadDocLink { get; set; }
        //[DataMember]
        //public string RegistrationNo { get; set; }
        [DataMember]
        public string MedicalScheduleLink { get; set; }
        [DataMember]
        public string SurveyURL { get; set; }
        [DataMember]
        public string SurveyLink { get; set; }
        [DataMember]
        public string DiscountVoucher { get; set; }
        [DataMember]
        public string BookingType { get; set; }
        [DataMember]
        public List<MailAttachments> MailAttachmentList { get; set; }
        [DataMember]
        public string CourierName { get; set; }
        [DataMember]
        public string TrackingNumber { get; set; }
        [DataMember]
        public string RefundCourierName { get; set; }
        [DataMember]
        public string RefundTrackingNumber { get; set; }
        [DataMember]
        public string MyAccLink
        { get; set; }
        [DataMember]
        public string MyAccLinkForDocUpload
        { get; set; }
        [DataMember]
        public string MyAccLinkForTracking
        { get; set; }
        [DataMember]
        public string RefundAmount
        { get; set; }
        [DataMember]
        public string RefundMode
        { get; set; }
        [DataMember]
        public short RefundModeKey { get; set; }

        [DataMember]
        public List<Document> DocumentDetails
        {
            get;
            set;
        }
        [DataMember]
        public bool IsMedRequired
        {
            get;
            set;
        }
        [DataMember]
        public string IHOCourierName { get; set; }
        [DataMember]
        public string IHOTrackingNumber { get; set; }
        [DataMember]
        public string SmsShortUrl { get; set; }
      
    } 

    [DataContract]
    public class AppointmentDetail
    {
        [DataMember]
        public string SupplierName
        {
            get;
            set;
        }
        [DataMember]       
        public Int64 SupplierId
        {
            get;
            set;
        }        
        [DataMember]
        public string SupplierShortName
        {
            get;
            set;
        }        
        [DataMember]
        public string AppointmentDate
        {
            get;
            set;
        }
        [DataMember]
        public string AppointmentTime
        {
            get;
            set;
        }
        [DataMember]
        public string CreatedBy
        {
            get;
            set;
        }
        [DataMember]
        public string AddressOption { get; set; }
        [DataMember]
        public int StatusID { get; set; }
        [DataMember]
        public string CityName { get; set; }
        [DataMember]
        public string Address { get; set; }
        [DataMember]
        public string AddressLine2 { get; set; }
        [DataMember]
        public string AddressLine3 { get; set; }
        [DataMember]
        public string Pincode { get; set; }
    }

    [DataContract]
    public class commonPolicyDetails
    {
        [DataMember]
        public string SupplierName
        {
            get;
            set;
        }
        [DataMember]
        public string PlanName
        {
            get;
            set;
        }
        [DataMember]
        public Int64 SupplierId
        {
            get;
            set;
        }
        [DataMember]
        public Int64 PlanId
        {
            get;
            set;
        }
        [DataMember]
        public string SupplierShortName
        {
            get;
            set;
        }
        [DataMember]
        public string PlanShortName
        {
            get;
            set;
        }
        [DataMember]
        public string Premium { get; set; }
        [DataMember]
        public string PolicyNo { get; set; }
        [DataMember]
        public string SumInsured { get; set; }
        [DataMember]
        public string Make { get; set; }
        [DataMember]
        public string Model { get; set; }
        [DataMember]
        public string RegistrationNo { get; set; }

    }

    [DataContract]
    public class SupplierDocUploadInfo
    {
        [DataMember]
        public string SupplierDocUpLoadLink
        {
            get;
            set;
        }
        [DataMember]
        public string SupplierEmailId
        {
            get;
            set;
        }
        [DataMember]
        public byte SupplierDocSize
        {
            get;
            set;
        }
    }


    [DataContract]
    public class HEXSelectionDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string InsurerName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int InsurerID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public int PlanID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptPlanID
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string PlanName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsDiseaseExlusion
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsDiseaseExlusionPerm
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string DiseaseExclusionPerm
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsDiseaseExlusionTemp
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string DiseaseExclusionTemp
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsLoadPremium
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string FinalPremium
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public bool IsCoPay
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string CoPay
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string SumInsured
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public bool IsMemberExclusion
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public List<MemberExclusion> Members
        {
            get;
            set;
        }
       
    }

    [DataContract]
    public class MemberExclusion
    {
        [DataMember(EmitDefaultValue = false)]
        public string MemberName
        {
            get;
            set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string ExclusionRemarks
        {
            get;
            set;
        }
    }
    [DataContract]
    public class HealthRenewalBenefit
    {
        [DataMember(EmitDefaultValue = false)]
        public string PEDCoverage{get;set;}
        [DataMember(EmitDefaultValue = false)]
        public string HealthCheckUP{get;set;}
        [DataMember(EmitDefaultValue = false)]
        public string SpecificDisease{get;set;}
        [DataMember(EmitDefaultValue = false)]
        public string Bonus{get;set;}
        [DataMember(EmitDefaultValue = false)]
        public string RewardLoyaltyPoints{get;set;}
        [DataMember(EmitDefaultValue = false)]
        public string AdditionalBenefits { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Others { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string NetworkHospitalsCovered { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string HospitalRoomEligibility { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PreHospitalization { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PostHospitalization { get; set; }

    }

    [DataContract]
    [Serializable]
    public class AllocationLeadData
    {
        [DataMember]
        public Int64 LeadId { get; set; }

        [DataMember]
        public Int32 Age { get; set; }

        [DataMember]
        public Int32 InsurerID { get; set; }

        [DataMember]
        public String PreviousBooking { get; set; }

        [DataMember]
        public String RepeatCustomer { get; set; }

        [DataMember]
        public Int64 AnnualIncome { get; set; }

        [DataMember]
        public Int16 CityID { get; set; }

        [DataMember]
        public String LeadSource { get; set; }

        [DataMember]
        public String Utm_source { get; set; }

        [DataMember]
        public String UTM_Medium { get; set; }

        [DataMember]
        public Int16 Covercount { get; set; }

        [DataMember]
        public Byte CMDOB { get; set; }

        [DataMember]
        public String Utm_campaign { get; set; }

        [DataMember]
        public Byte StateID { get; set; }

        [DataMember]
        public Boolean IsPED { get; set; }

        [DataMember]
        public String PEDTypes { get; set; }

        [DataMember]
        public String source { get; set; }

        [DataMember]
        public String Utm_term { get; set; }

        [DataMember]
        public String IsUnAssisted { get; set; }

        [DataMember]
        public Byte IsMarkExchange { get; set; }

        [DataMember]
        public short ChildCount { get; set; }

        [DataMember]
        public short AdultCount { get; set; }

        [DataMember]
        public string Country { get; set; }

        [DataMember]
        public DateTime CreatedOn { get; set; }

        [DataMember]
        public short LeadRank { get; set; }

        [DataMember]
        public byte ChatStatus { get; set; }

        [DataMember]
        public byte CommunicationId { get; set; }

        [DataMember]
        public String GroupCode { get; set; }

        [DataMember]
        public decimal LeadScore { get; set; }

        [DataMember]
        public short SpecialLeadRank { get; set; }

        [DataMember]
        public long AssigntoUserID { get; set; }

        [DataMember]
        public long AssignbyUserID { get; set; }

        [DataMember]
        public short JobID { get; set; }

        [DataMember]
        public short LastAssignedGroupID { get; set; }

        [DataMember]
        public short GroupID { get; set; }

        [DataMember]
        public short ProductID { get; set; }

        [DataMember]
        public byte AllocationTrackingEntryFlag { get; set; }

        [DataMember]
        public short AgentGrade { get; set; }

        [DataMember]
        public string AssignedToEmpId { get; set; }

        [DataMember]
        public short LeadGrade { get; set; }

        [DataMember]
        public short LastLeadRank { get; set; }

        [DataMember]
        public byte StatusId { get; set; }

        [DataMember]
        public short SubStatusId { get; set; }

        [DataMember]
        public long CustomerId { get; set; }

        [DataMember]
        public short SelectionCount { get; set; }

        [DataMember]
        public string FraudStatus { get; set; }

        [DataMember]
        public byte AssignedToGroup { get; set; }

        [DataMember]
        public short OutLeadRank { get; set; }

        [DataMember]
        public DateTime? CTCScheduleTime { get; set; }

        [DataMember]
        public bool IsAlreadyBooked { get; set; }

        public string MobileNo { get; set; }

        [DataMember]
        public short PayULeadRank { get; set; }

        [DataMember]
        public string EmailId { get; set; }

        [DataMember]
        public string Language { get; set; }

        [DataMember]
        public string ProcessName { get; set; }
        [DataMember]
        public bool IsNRI { get; set; }
        [DataMember]
        public long MobileSeries { get; set; }
        [DataMember]
        public long ClusterId { get; set; }

        [DataMember]
        public short IPCountryId { get; set; }

        [DataMember]
        public long Lowconversionvalue { get; set; }

        [DataMember]
        public string CustSource { get; set; }

        [DataMember]
        public short ChatLeadRank { get; set; }
        [DataMember]
        public string assignedToEcode { get; set; }
        [DataMember]
        public string assignedToAgentName { get; set; }
        [DataMember]
        public int groupID { get; set; }

    }


    [DataContract]
    [Serializable]
    public class AllocateLeadResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public List<AllocationLeadData> LeadDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Error { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsSuccess { get; set; }
        public Dictionary<long, string> LeadErrors { get; set; }
    }

    [DataContract]
    public class CreditChangeResponse
    {
        [DataMember(EmitDefaultValue = true)]
        public int RequestID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long BookingId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long CustomerID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ProductId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int AgentTypeID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string AgentType { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ReasonID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Reason { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long RequestedByUserID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string RequestedByEmpCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string RequestedByUserName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long OldAdvisorUserID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string OldAdvisorEmpCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string OldAdvisorUserName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long NewAdvisorUserID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string NewAdvisorEmpCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string NewAdvisorUserName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long FirstLevelApproverUserID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long ApprovedByUserID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string RequestorComment { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string FirstLevelComment { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string SecondLevelComment { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int StatusID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Status { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public DateTime Createdon { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public DateTime UpdatedOn { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public DateTime BookingDate { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string FirstLevelApproverEmpCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string FirstLevelApproverUserName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string ApprovedByUserName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string ApprovedByEmpCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long ReferenceId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long ReferenceCustId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ReferenceProdId { get; set; }
    }

    [DataContract]
    public class CreditChangeReasonMaster
    {
        [DataMember(EmitDefaultValue = true)]
        public int AgentTypeID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ReasonID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Reason { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ProductId { get; set; }
    }
    [DataContract]
    public class CreditChangeRequest
    {
        [DataMember(EmitDefaultValue = true)]
        public int RequestID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long RequestorUserID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long BookingID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long NewAgentID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int AgentTypeID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ReasonID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string RequestorRemarks { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int Action { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string ReferenceId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int CurrentStatus { get; set; }

    }
    [DataContract]
    public class CreditChangeLogs
    {
        [DataMember(EmitDefaultValue =true)]
        public long BookingID { get; set; }
        [DataMember(EmitDefaultValue =true)]
        public int AgentTypeID { get; set; }
        [DataMember(EmitDefaultValue =true)]
        public int StatusID { get; set; }
        [DataMember(EmitDefaultValue =true)]
        public long ActionByUserID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string UserName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string EmployeeCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public DateTime Createdon { get; set; }
    }
    [DataContract]
    public class CreditChangeRequestsBUList
    {
        [DataMember(EmitDefaultValue = true)]
        public string BUEmpCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string BUUserName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string BUEmail { get; set; }
    }

    [DataContract]
    public class PauseSelfCallingResponse
    {
        [DataMember(EmitDefaultValue = true)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string CustomerName { get; set; }
    }

    public class BulkUploadResponseList
    {
        [DataMember(EmitDefaultValue = true)]
        public long BookingId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string NewEmployeeCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string OldEmployeeCode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int Reason { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string UploadedBy { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public ResponseAPI Response { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public DateTime CreatedOn { get; set; }
    }

    public class VirtualNumberModel
    {
        [DataMember(EmitDefaultValue = true)]
        public long VirtualNumber { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long Id { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public bool UnansweredAttempt { get; set; }
        
    }


    [DataContract]
    public class FollowUpComment
    {
        [DataMember]
        public long leadID { get; set; }
        [DataMember]
        public long userID { get; set; }

        [DataMember]
        public string comment { get; set; }

        [DataMember]
        public int eventtype { get; set; }
    }
}
