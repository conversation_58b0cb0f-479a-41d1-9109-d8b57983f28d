﻿using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class AllocationDetails
    {        
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte ProductID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 InsurerID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 PlanID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LeadGroup
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadDate
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte LeadRank
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte ModelGrade
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupID
        { get; set; }
        public Decimal LeadPPLAmount
        { get; set; }
        public Int64 AssigntoUserID
        { get; set; }
        public string AssignedToEmpId
        { get; set; }
        public Int64 AssignbyUserID
        { get; set; }
        public byte AllocationTrackingEntryFlag
        { get; set; }
        public Int16 JobID
        {
            get;
            set;
        }
        public Int16 SelectionCount
        { get; set; }
        public byte StatusID
        { get; set; }
        public Int16 SubStatusID
        { get; set; }
        public Int16 AgentGrade 
        { get; set; }
        public Int16 LeadScore
        { get; set; }
        public string Utm_source
        { get; set; }
        public string Utm_term
        { get; set; }
        public string UTM_Medium
        { get; set; }
        public int SubProductTypeId
        { get; set; }
        public byte InvTypeID
        { get; set; }          
        public Byte Age
        { get; set; }
        public Int64 AnnualIncome
        { get; set; }
        public string RegNo
        { get; set; }
        public DateTime RegDate
        { get; set; }
        public string CityID
        { get; set; }
        public int TotalPayout
        { get; set; }
        public bool IsPrevBooking
        { get; set; }
        public bool IsRepeatCustomer
        { get; set; }
        public string LeadSource
        { get; set; }
        public DateTime ? PolicyExpDate
        { get; set; }
        public int premium { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte PolicySubType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 payTerm { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Country
        { get; set; }
        public byte LastLeadRank { get; set; }
        public string GroupCode { get; set; }
        public Int16 LastAssignedGroupID
        { get; set; }
        public byte AILeadRank { get; set; }
        public byte SpecialLeadRank { get; set; }
    }
    public class SelectionLead
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 InsurerID
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 PlanID
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string SourcePage
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool OnLeadCreation
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool Isrevisit
        { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public byte LeadRank { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool ChatReassignment { get; set;}
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool UnAssigned { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CBsource { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsTP { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long ParentLeadID { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string GroupCode { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsCompare { get; set; }

    }
    public class inProcessLeads
    {
        public long LeadID 
        {get;set;}
        public bool IsSelection
        {get;set;}
        public DateTime ts
        {get;set;}
        public bool UnAssigned
        { get; set; }
    }
    public class customerRevisitinprocess
    {
        public long ParentLeadID
        { get; set; }        
        public DateTime ts
        { get; set; }
        public string pageName
        { get; set; }
        public string status
        { get; set; }
    }
    public class customerRevisitData
    {
        public long LeadID { get; set; }
        public int enquiryID { get; set; }
        public string IP { get; set; }
        public Int16 productID { get; set; }
        public DateTime ts { get; set; }
        public string PageName { get; set; }
        public string status { get; set; }
        public bool realtime { get; set; }
        public string RoomCode { get; set; }
        public bool IsActive { get; set; }
    }
    public class CTCRevistleadRankMapping
    {
        [BsonElement("_id")]
        public Int16 productID { get; set; }  
        public List<RankMapping> MapRank{get;set;}
    }
    
    public class RankMapping
    {
        public byte A_Rank { get; set; }
        public byte M_Rank { get; set; }
    }

    [DataContract]
    public class LeadAgentDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmpCode
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmpName
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsParent
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime CreatedOn
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string GroupCode
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonConverter(typeof(CustomDateTimeConverter))]
        public DateTime CBDate
        { get; set; }   
        [DataMember(EmitDefaultValue = false)]
        public byte LeadStatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LeadStatusName { get; set; }
    }
    [DataContract]
    public class SpecialGroup
    {
        [DataMember(EmitDefaultValue = false)]
        public Byte ProductId
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string GroupCode
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsAgent
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsGroup
        { get; set; }
    }
    [DataContract]
    public class MobileCustomerMapping
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 MobileNumber
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Response
        {
            get;set;
        }
    }
    [DataContract]
    public class MobileCustomerMappingRequest
    {
        [DataMember(EmitDefaultValue = false)]        
        public List<Int64> Customerlst
        { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Process
        {
            get; set;
        }
        [DataMember(EmitDefaultValue = false)]
        public string tokenValue
        {
            get; set;
        }
    }
    public class CreateLeadDetails
    {
        public Int64 CustomerID { get; set; }
        public string Name { get; set; }
        public string AltEmailID { get; set; }
        public int GenderID { get; set; }
        public String GenderName { get; set; }
        public string Gen { get; set; }
        public String DOB { get; set; }
        public string DateOfBirth { get; set; }
        public string AltPhoneNo { get; set; }
        public string MobileNo { get; set; }
        public string EmailID { get; set; }
        public string CustAddress { get; set; }
        public short StateID { get; set; }
        public string StateName { get; set; }
        public short CityID { get; set; }
        public String CityName { get; set; }
        public int AltCityID { get; set; }
        public string AltCity { get; set; }
        public string AltAddress { get; set; }
        public string PinCode { get; set; }
        public string AltPinCode { get; set; }
        public string MaritalStatus { get; set; }
        public int AnnualIncomeID { get; set; }
        public string AnnualIncomeRange { get; set; }
        public string Profession { get; set; }
        public string PassportNo { get; set; }
        public string Nominee { get; set; }
        public string Relationship { get; set; }
        public string LeadSource { get; set; }
        public string ReferredBy { get; set; }
        public string OfferNumber { get; set; }
        public int CountryID { get; set; }
        public int AltCountryID { get; set; }
        public int ProfessionID { get; set; }
        public int StatusID { get; set; }
        public int SubstatusID { get; set; }
        public int ProductID { get; set; }
        public string Premium { get; set; }
        public string SA { get; set; }
        public Int64 LeadID { get; set; }
        public Int64 ParentID { get; set; }
        public string AgentId { get; set; }
        public string ContactNumber { get; set; }
        public string comments { get; set; }
        public string eventType { get; set; }
        public string ReminderText { get; set; }
        public string ReminderContent { get; set; }
        public DateTime ReminderDate { get; set; }
        public string RemarkId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string EventTypeId { get; set; }
        public DateTime EventDate { get; set; }
        public string UserId { get; set; }
        public string IsAlarm { get; set; }
        public int SnoozeTime { get; set; }
        public int PayMode { get; set; }
        public int Supplier { get; set; }
        public string Product { get; set; }
        public string BankName { get; set; }
        public string ChequeNo { get; set; }
        public string PlanName { get; set; }
        public string Filter { get; set; }
        public int GroupId { get; set; }
        public int SmsId { get; set; }
        public int PolicyTypeId { get; set; }
        public string PolicyTypeName { get; set; }
        public int PolicyTerm { get; set; }
        public Boolean IsTobaccoUser { get; set; }
        public string BuyerName { get; set; }
        public string BuyerId { get; set; }
        public int ProductTypeID { get; set; }
        public String CallTime { get; set; }
        public String AssignedToAgentId { get; set; }
        public String AssignedToAgentName { get; set; }
        public String EmployeeID { get; set; }
        public String GroupName { get; set; }
        public DateTime LeadDate { get; set; }
        public bool IsCTC { get; set; }

        //new auto lead creation for request with other UTM_source
        public String SessionID { get; set; }
        public String UTM_Source { get; set; }
        public String Utm_Medium { get; set; }
        public String Utm_Campaign { get; set; }
        public String Utm_Term { get; set; }
        //new auto lead creation for request with other UTM_source
        /// <summary>
        /// investment product is further devided in sub categories
        /// (1 = Save Tax & Grow Money,2 = Secure your future (Retirement),3 = Secure Child's future)
        /// </summary>
        public Int32 InvestmentTypeID { get; set; }
        public String Source { get; set; }
        public int SubProductID { get; set; }
        public long ReferralLead { get; set; }
        public long AnnualIncome { get; set; }
        public string CompanyName { get; set; }
        public short OccupationId { get; set; }
        public string AssignLead { get; set; }
        public Int64 AssignTogroupID { get; set; }
        public Int64 AssignToUserId { get; set; }
        public string SubProductIdList { get; set; }
        public long AssociationId { get; set; }
        public short MaxStatusID { get; set; }

    }

    #region customer
    public class Rootobject
    {
        public Createcustomerresult CreateCustomerResult { get; set; }
    }

    public class Createcustomerresult
    {
        public Datum[] Data { get; set; }
        public object Error { get; set; }
        public int ErrorCode { get; set; }
    }

    public class Datum
    {
        public int CustomerId { get; set; }
        public long MobileNo { get; set; }
    }

    public class CoreCustomerRegistration
    {
        public long[] MobileNo { get; set; }
        public string Name { get; set; }
        public int CountryID { get; set; }
    }

    public class UTMLeadDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public byte Gender { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DOB { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Category { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsDNC { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CityID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 StateID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PostCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Utm_source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UTM_Medium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Utm_term { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Utm_campaign { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RegNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SupplierId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int PlanId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal TotalPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal SumInsured { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CountryCode { get; set; }
        public short SubProductId { get; set; }
        public long CustomerID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreationDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Comments { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long ReferralLead { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        public long AnnualIncome { get; set; }
        public string CompanyName { get; set; }
        public short OccupationId { get; set; }
        public string AssignLead { get; set; }
        public string CreatedByEmpId { get; set; }
        public string CreatedByEmpName { get; set; }
        public long AssignToUserId { get; set; }
        public long AssignTogroupID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AssociationId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsSmeFosCreateLead {get; set; }
        public short MaxStatusID { get; set; }
        public bool ISHNICustomer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ApiSource { get; set; }
    }

    [DataContract]
    public class SaveInfo<T>
    {
        [DataMember]
        public bool IsSaved { get; set; }

        [DataMember]
        public string Message { get; set; }
        [DataMember]
        public Int32 StatusCode { get; set; }

        [DataMember]
        public T Output { get; set; }

        [DataMember]
        public long CustomerId { get; set; }
        [DataMember]
        public string AssignedToEcode { get; set; }
        [DataMember]
        public string AssignedToAgentName { get; set; }
        [DataMember]
        public int AssignedToGroupID { get; set; }
    }

    [DataContract]
    public class LeadAgentDetailsCHAT
    {
        [DataMember(EmitDefaultValue = false)]
        public LeadAgentDetails AgentDetails { get; set; }

        [DataMember]
        public short NewLeadRank { get; set; }
    }


    #endregion
    public class CustomDateTimeConverter : JsonConverter<DateTime>
    {
        public override void WriteJson(JsonWriter writer, DateTime value, JsonSerializer serializer)
        {
            // Calculate milliseconds since Unix epoch
            long milliseconds = (long)(value.ToUniversalTime() - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds;

            // Get time zone offset in "+hhmm" format
            TimeSpan offset = TimeZoneInfo.Local.GetUtcOffset(value);
            string offsetString = $"{(offset >= TimeSpan.Zero ? "+" : "-")}{offset.Hours:00}{offset.Minutes:00}";

            // Write the JSON representation
            writer.WriteRawValue($@"""/Date({milliseconds}{offsetString})/""");
        }

        public override DateTime ReadJson(JsonReader reader, Type objectType, DateTime existingValue, bool hasExistingValue, JsonSerializer serializer)
        {
            throw new NotImplementedException();
        }
    }
}
