﻿using System;
using System.Collections.Generic;
using System.Numerics;
using System.Runtime.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace PropertyLayers
{
    [DataContract]
    [Serializable]
    [BsonIgnoreExtraElements]
    public class PriorityModel
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 LeadPoints { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<long> ActiveLeadSet { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        private DateTime leadDate;

        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedOn
        {
            get
            {
                if (leadDate != null && leadDate.Kind == DateTimeKind.Utc)
                {
                    leadDate = leadDate.ToLocalTime();
                }
                return leadDate;
            }
            set
            {
                this.leadDate = value;
            }
        }

        [DataMember(EmitDefaultValue = false)]
        public long CustID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 LeadRank { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public SupplierData Supplier { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public LeadStatusData LeadStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public userData User { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public RevisitData Revisit { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallBackData CallBack { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CallData Call { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public EventTypeEnum EventType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public LeadCategoryEnum LeadCategory { get; set; }


        [DataMember(EmitDefaultValue = false)]// Reason for Lead Coming in Priority
        public string Reason { get; set; }

        private DateTime Skiptime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime SkippingTime
        {
            get
            {
                if (Skiptime != null && Skiptime.Kind == DateTimeKind.Utc)
                {
                    Skiptime = Skiptime.ToLocalTime();
                }
                return Skiptime;
            }
            set
            {
                this.Skiptime = value;
            }
        }

        [DataMember(EmitDefaultValue = false)]// EmailRevert
        public bool IsEmailRevert { get; set; }

        [DataMember(EmitDefaultValue = false)]// Inbound flag
        public bool IsInbound { get; set; }

        [DataMember(EmitDefaultValue = false)]// selection flag
        public bool IsSelection { get; set; }

        [DataMember(EmitDefaultValue = false)]// Referral flag
        public bool IsReferral { get; set; }

        [DataMember(EmitDefaultValue = false)]// NRI flag
        public bool IsNRI { get; set; }

        private DateTime pointDeductionTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime PointDeductTime
        {
            get
            {
                if (pointDeductionTime != null && pointDeductionTime.Kind == DateTimeKind.Utc)
                {
                    pointDeductionTime = pointDeductionTime.ToLocalTime();
                }
                return pointDeductionTime;
            }
            set { pointDeductionTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]// Flag if The lst bucket was Rest
        public bool IsLastBucketRest { get; set; }

        private DateTime PolicyExpDate;
        [DataMember(EmitDefaultValue = false)]// Mainly for Motor
        public DateTime PrevPolicyExpDate
        {
            get
            {
                if (PolicyExpDate != null && PolicyExpDate.Kind == DateTimeKind.Utc)
                {
                    PolicyExpDate = PolicyExpDate.ToLocalTime();
                }
                return PolicyExpDate;
            }
            set { PolicyExpDate = value; }
        }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public bool MongoOperation { get; set; }

        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public bool LeadPointOperation { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public byte CallReleaseCount { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 SkipDurationHrs { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 ReleaseStatus { get; set; }

        private DateTime expectedAppearTime;
        [DataMember(EmitDefaultValue = false)]

        public DateTime ExpectedAppearingTime
        {
            get
            {
                if (expectedAppearTime != null && expectedAppearTime.Kind == DateTimeKind.Utc)
                {
                    expectedAppearTime = expectedAppearTime.ToLocalTime();
                }
                return expectedAppearTime;
            }
            set { expectedAppearTime = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public byte star { get; set; }


        private DateTime callReleasets;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CallReleaseTime
        {
            get
            {
                if (callReleasets != null && callReleasets.Kind == DateTimeKind.Utc)
                {
                    callReleasets = callReleasets.ToLocalTime();
                }
                return callReleasets;
            }
            set { callReleasets = value; }
        }


        private DateTime PaymentAttemptTs;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime PaymentAttemptTime
        {
            get
            {
                if (PaymentAttemptTs != null && PaymentAttemptTs.Kind == DateTimeKind.Utc)
                {
                    PaymentAttemptTs = PaymentAttemptTs.ToLocalTime();
                }
                return PaymentAttemptTs;
            }
            set { PaymentAttemptTs = value; }
        }


        private DateTime _PaymentFailureTime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime PaymentFailureTime
        {
            get
            {
                if (_PaymentFailureTime != null && _PaymentFailureTime.Kind == DateTimeKind.Utc)
                {
                    _PaymentFailureTime = _PaymentFailureTime.ToLocalTime();
                }
                return _PaymentFailureTime;
            }
            set { _PaymentFailureTime = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string PageName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 Country { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime DOB { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool EmailOnly { get; set; }

        private DateTime _TripStart;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime TripStart
        {
            get
            {
                if (_TripStart != null && _TripStart.Kind == DateTimeKind.Utc)
                {
                    _TripStart = _TripStart.ToLocalTime();
                }
                return _TripStart;
            }

            set { _TripStart = value; }
        }

        private DateTime _TripEnd;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime TripEnd
        {
            get
            {
                if (_TripEnd != null && _TripEnd.Kind == DateTimeKind.Utc)
                {
                    _TripEnd = _TripEnd.ToLocalTime();
                }
                return _TripEnd;
            }

            set { _TripEnd = value; }
        }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 SumInsured { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 BookedId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool IsBooked { get; set; }

        private DateTime _BookedTime;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime BookedTime
        {
            get
            {
                if (_BookedTime != null && _BookedTime.Kind == DateTimeKind.Utc)
                {
                    _BookedTime = _BookedTime.ToLocalTime();
                }
                return _BookedTime;
            }

            set { _BookedTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public TicketObj Ticket { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public Int16 Counter { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public Int16 CustomerNANCAttempt { get; set; }

        private DateTime _CustomerCalltime;
        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public DateTime CustomerCalltime
        {
            get
            {
                if (_CustomerCalltime != null && _CustomerCalltime.Kind == DateTimeKind.Utc)
                {
                    _CustomerCalltime = _CustomerCalltime.ToLocalTime();
                }
                return _CustomerCalltime;
            }

            set { _CustomerCalltime = value; }
        }

        [DataMember(EmitDefaultValue = false)]   // Customer level Unasnwered attempt in a day
        public Int16 CustomerTodayNANC { get; set; }

        private DateTime _ProposalError;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime ProposalError
        {
            get
            {
                if (_ProposalError != null && _ProposalError.Kind == DateTimeKind.Utc)
                {
                    _ProposalError = _ProposalError.ToLocalTime();
                }
                return _ProposalError;
            }
            set { _ProposalError = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 NRIAreaCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int64 RevisitCount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Decimal LeadPriorityScore { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public short InvestmentTypeId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public DateTime? GraceEndDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime? EmiDueDate;


        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime PFFilledTime
        {
            get
            {
                if (_PFFilledTime != null && _PFFilledTime.Kind == DateTimeKind.Utc)
                {
                    _PFFilledTime = _PFFilledTime.ToLocalTime();
                }
                return _PFFilledTime;
            }
            set { _PFFilledTime = value; }
        }
        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DateTime RMCSTime
        {
            get
            {
                if (_RMCSTime != null && _RMCSTime.Kind == DateTimeKind.Utc)
                {
                    _RMCSTime = _RMCSTime.ToLocalTime();
                }
                return _RMCSTime;
            }
            set { _RMCSTime = value; }
        }

        private DateTime _PFFilledTime;
        private DateTime _RMCSTime;

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public Int16 ScoreModel { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsAppointed { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public DncData DNC { get; set; }

        private DateTime _AppointmentTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentTime
        {
            get
            {
                if (_AppointmentTime != null && _AppointmentTime.Kind == DateTimeKind.Utc)
                {
                    _AppointmentTime = _AppointmentTime.ToLocalTime();
                }
                return _AppointmentTime;
            }
            set { _AppointmentTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public AppointmentData Appointment { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnore]
        public AppointmentTypeEnum AppointmentTypeEnum { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 PinCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [BsonIgnoreIfDefault]
        public CustEmergencyNo CustEmergencyNo { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class DncData
    {
        private DateTime DncTime;
        private DateTime IbCallTime;
        [DataMember(EmitDefaultValue = false)]
        public Int16 CoolingPeriod { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Score { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (DncTime != null && DncTime.Kind == DateTimeKind.Utc)
                {
                    DncTime = DncTime.ToLocalTime();
                }
                return DncTime;
            }
            set { this.DncTime = value; }
        }

        [BsonElement("ib_ts")]
        [DataMember(EmitDefaultValue = false)]
        public DateTime LastIbCallTime
        {
            get
            {
                if (IbCallTime.Kind == DateTimeKind.Utc)
                {
                    IbCallTime = IbCallTime.ToLocalTime();
                }
                return IbCallTime;
            }
            set { this.IbCallTime = value; }
        }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class LeadStatusData
    {
        [BsonIgnore]
        [DataMember(EmitDefaultValue = false)]
        public List<long> Leads { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public byte StatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SubStatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime Statustime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Status { get; set; }
    }
    [DataContract]
    [BsonIgnoreExtraElements]
    public class SupplierData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 PlanID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SupplierID { get; set; }
    }
    [DataContract]
    [BsonIgnoreExtraElements]
    public class userData
    {
        [BsonIgnore]
        public Int32 AssignedID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        private DateTime _AssignedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AssignedOn
        {
            get
            {
                if (_AssignedOn != null && _AssignedOn.Kind == DateTimeKind.Utc)
                {
                    _AssignedOn = _AssignedOn.ToLocalTime();
                }
                return _AssignedOn;
            }
            set { _AssignedOn = value; }
        }

        private DateTime _FirstAssignedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime FirstAssignedOn
        {
            get
            {
                if (_FirstAssignedOn == DateTime.MinValue)
                    _FirstAssignedOn = AssignedOn.ToLocalTime().Date;
                else if (_FirstAssignedOn != null && _FirstAssignedOn.Kind == DateTimeKind.Utc)
                {
                    _FirstAssignedOn = _FirstAssignedOn.ToLocalTime();
                }

                return _FirstAssignedOn;
            }
            set { _FirstAssignedOn = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public byte Grade { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public bool Reassigned { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public short JobID { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class RevisitData
    {
        private DateTime RevisitTime;

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (RevisitTime != null && RevisitTime.Kind == DateTimeKind.Utc)
                {
                    RevisitTime = RevisitTime.ToLocalTime();
                }
                return RevisitTime;
            }
            set { this.RevisitTime = value; }
        }



        [DataMember(EmitDefaultValue = false)]
        public RevisitType RevisitType { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class CallBackData
    {
        private DateTime cbTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CBtime
        {
            get
            {
                if (cbTime != null && cbTime.Kind == DateTimeKind.Utc)
                {
                    cbTime = cbTime.ToLocalTime();
                }
                return cbTime;
            }
            set { cbTime = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public byte Duration { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public CallBackTypeEnum CallBackType { get; set; }

        private DateTime CBCreatedTime;
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts
        {
            get
            {
                if (CBCreatedTime != null && CBCreatedTime.Kind == DateTimeKind.Utc)
                {
                    CBCreatedTime = CBCreatedTime.ToLocalTime();
                }
                return CBCreatedTime;
            }
            set { this.CBCreatedTime = value; ; }
        }// callBack created Time

        [DataMember(EmitDefaultValue = false)]
        public bool IsPaymentCB { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class CallData
    {
        private DateTime callDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime calltime
        {
            get
            {
                if (callDate != null && callDate.Kind == DateTimeKind.Utc)
                {
                    callDate = callDate.ToLocalTime();
                }
                return callDate;
            }
            set { callDate = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Duration { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 TalkTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CallType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Disposition { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 lastNCallTT { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 TotalTT { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CallAttempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 NANC_Attempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<string> Shifts { get; set; }// This will contain  M A E for morning ,afternoon and Evening

        [DataMember(EmitDefaultValue = false)]   // flag if true Need to deduct point 
        public bool DeductPoint { get; set; }

        [DataMember(EmitDefaultValue = false)]   // flag if true Need to deduct point 
        public Int16 TodaysAttempt { get; set; }

        [DataMember(EmitDefaultValue = false)]   // NOT PICKING IN priority
        public Int16 TodaysNANCAttempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Week_Attempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Current_Week { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 uid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 TodayAnswered { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Boolean IsProcessed { get; set; }
    }

    public enum EventTypeEnum
    {
        Default = 0,
        selection = 1,
        Assignment = 2,
        Call = 3,
        CallBack = 4,
        Revisit = 5,
        statusupdate = 6,
        Reject = 7,
        Book = 8,
        NewLead = 9,
        SingleNA1Hr = 10,
        ReleaseLeads = 11,
        SkipLead = 12,// Not in Use Right Now,
        OneTimeDataPreparation = 13
    }

    public enum RevisitType
    {
        Default = 0,
        Inbound = 1,
        Email = 2,
        WebVisit = 3,
        Ctc = 4,
        BajajCustomer = 5,
        QuoteShared = 6,
        RevisionShared = 7
    }

    public enum CallBackTypeEnum
    {
        Default = 0,
        CustRequested = 1,
        CustAgreed = 2,
        AgentBestGuess = 3,
        SystemSetCB = 4

    }



    public class LeadPointsDTO
    {
        public Int64 LeadId;
        public Int16 Points;
        public byte PointType;
        public byte PointSubType;
        public bool IsReset;
        public bool IsPaymentCB;
        public string Remarks;
        public bool IsPointDeducted;
        public Int16 CurrentWeek;
        public Int16 WeekPoints;
        public DateTime FirstAssignedOn;
    }

    public enum LeadCategoryEnum
    {
        Default = 0,
        PaymentCB = 1,
        ActiveRevisit = 2,
        ActiveNew = 3,
        ActiveCB = 4,
        PassiveRevisit = 5,
        PassiveNew = 6,
        PassiveCB = 7,
        SecondAttemptPCB = 8,
        SecondAttemptActvRevisit = 9,
        SecondAttemptActvNew = 10,
        SecondAttemptActvCB = 11,
        SecondAttemptPasvRevisit = 12,
        SecondAttemptPasvNew = 13,
        SecondAttemptPasvCB = 14,
        UnansweredLeads = 15,
        RestLeads_1 = 16,
        SecondAttemptRestLeads = 17,
        RestLeads_2 = 18,
        CallReleasedLeads = 19,
        RecentExpiry = 20,
        SkippedLeads = 21,
        FutureCallBackLeads = 22,
        BdayLeads = 23,
        UnansweredRecentLeads = 24,
        EmailRevisit = 25,
        SecondAttemptEmailRevisit = 26,
        PaymentFailure = 27,
        SecondAttemptPaymentFailure = 28,
        CTCRevisit = 29,
        BookedLead = 30,
        RevisitAddLead = 31,
        allLeadpopup = 32,
        TopAddLead = 33,
        CTCLead = 33,
        Search = 34,
        CTC = 35,
        SecondAttemptRevisitCTC = 36,
        PredictiveAdd = 37,
        BajajCustomerRevisit = 38,
        MissedCB = 39,
        TicketUpdate = 40,
        StatusChange = 41,
        BookedCB = 42,
        NoLeadPopup = 43,
        ProposalError = 44,
        QuoteShared = 45,
        RestPriorityLeads = 46,
        RevisionShared = 47,
        TodayExpiry = 48,
        SecondAttemptTodayExpiry = 49,
        PFFilled = 50,
        SecondAttemptPFFilled = 51,
        RMLeads = 52,
        CancelBookedLead = 53,
        SOSDocTickets = 54,
        SOSRest = 55
    }


    [DataContract]
    public class CallDataHistory
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CallDate;
        [DataMember(EmitDefaultValue = false)]
        public Int32 TalkTime;
        [DataMember(EmitDefaultValue = false)]
        public Int16 Duration;
        [DataMember(EmitDefaultValue = false)]
        public string CallType;
        [DataMember(EmitDefaultValue = false)]
        public string Status;
        [DataMember(EmitDefaultValue = false)]
        public string CallId;
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId;
        [DataMember(EmitDefaultValue = false)]
        public Int64 CallDataId;
    }
    public class LeadCallDetailsDTO
    {
        public long ParentId;
        public int Totaltalktime;
        public int lastNtalktime;
        public int productId;
        public Int64 userID;
        public string LeadSource;
        public int TotalDuration;
        public MobileStatusEnum MobileStatus;
    }

    public class InternalIps
    {
        public ObjectId _id { get; set; }
        public string IP { get; set; }
    }

    public class SortingPriorityModel
    {
        public PriorityModel PriorityModel { get; set; }
        public DateTime SortingDate { get; set; }
    }

    [DataContract]
    public class Response
    {
        [DataMember]
        public bool status { get; set; }
        [DataMember]
        public string message { get; set; }
        [DataMember]
        public string LeadId { get; set; }
    }

    [DataContract]
    public class ReleasePointData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmployeeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public byte Status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime RequestDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ExpiryDate { get; set; }

    }

    [DataContract]
    public class ReleaseLeadRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public List<Int64> LeadIds { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Status { get; set; }
    }

    [DataContract]
    public class RejectLeads
    {
        [DataMember(EmitDefaultValue = false)]
        public List<Int64> LeadIds { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }
    }

    public enum Actions
    {
        Default = 0,
        LeadOpened = 1,
        CallInitiated = 2,
        CallEnded = 3,
        DoneClicked = 4,
        NoLeadPopup = 5
    }

    [DataContract]
    [Serializable]
    public class ReassignedLead
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReAssignedBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Status { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReAssignedTo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReAssignedToGroup { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ReAssignedDate { get; set; }
    }

    public class PODUserDetails
    {

        public string EmployeeId;
        public Int64 UserID;

    }

    public class OnlineCustomerInfo
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        public string Page { get; set; }
        public string CustName { get; set; }
        private DateTime _ts;
        public DateTime ts
        {
            get
            {
                if (_ts != null && _ts.Kind == DateTimeKind.Utc)
                {
                    _ts = _ts.ToLocalTime();
                }
                return _ts;
            }
            set { _ts = value; }
        }
        public Int64 AgentId { get; set; }
        public string RoomCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long visitLead { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long visitEnquiryID { get; set; }
    }

    public class OnlineCustomerTracking
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        public string Page { get; set; }
        public string CustName { get; set; }
        public DateTime ts { get; set; }
        public Int16 AgentId { get; set; }
        public string status { get; set; }
    }


    [DataContract]
    [Serializable]
    public class UserLeadDistribution
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public ObjectId _id { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> AgentAllLeads;
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsPointGTZero; // out of AgentAllLeads
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsNotCalledIn4Days; // out of LeadsPointGTZero
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsWithNoFCB;// out of LeadsNotCalledIn4Days
        [DataMember(EmitDefaultValue = false)]
        public List<PriorityModel> LeadsNotSkipped;// out of LeadsWithNoFCB
        [DataMember(EmitDefaultValue = false)]
        public int TotalTalkTime;
        [DataMember(EmitDefaultValue = false)]
        public byte DaysPresent;
        [DataMember(EmitDefaultValue = false)]
        public UserManagerMapping oUserManagerMapping;
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts;
    }

    public class UserManagerMapping
    {
        public Int64 UserId;
        public string Username;
        public string UserEmpId;
        public Int64 MgrId;
        public string MgrName;
        public string MgrEmpID;

    }

    public class Next5WidgetLead
    {
        public long LeadId;
        public string Name;
        public DateTime ts;
        public string Reason;
        public byte Priority;
        public long CustomerId;
        public short ProductId;
        [BsonIgnoreIfDefault]
        public string CallStatus;
        public short ReasonId;
        public bool EmailOnly;
        public bool IsReligare;
        public Int16 Counter;
        public string ProposalNo;
        public long BookingId;
        public string EncryptedLeadId;
        public DateTime CustConnectTime;
        public int IsAddLeadtoQueue;
    }

    [DataContract]
    public class CustJourneyTrackingInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId;
        [DataMember(EmitDefaultValue = false)]
        public string Name;
        [DataMember(EmitDefaultValue = false)]
        public DateTime LeadCreatedOn;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CBTime;
        [DataMember(EmitDefaultValue = false)]
        public bool IsPaymentAttempted;
        [DataMember(EmitDefaultValue = false)]
        public string CurrentPage;
        [DataMember(EmitDefaultValue = false)]
        public String EligibleNCB;
        [DataMember(EmitDefaultValue = false)]
        public String PreviousNCB;
        [DataMember(EmitDefaultValue = false)]
        public Int16 VoluntaryExcess;
        [DataMember(EmitDefaultValue = false)]
        public bool IsCNG;
    }

    public class UserNext5Leads
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long UserId;
        public List<Next5WidgetLead> Leads;

        [BsonIgnoreIfDefault]
        public List<Next5WidgetLead> BookedLeads;
        [BsonIgnoreIfDefault]
        public bool IsAuto;

        [DataMember(EmitDefaultValue = false)]
        public string BMSUserToken { get; set; }

    }

    public class CountryTimeZone
    {
        public short CountryId { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public short AreaCode { get; set; }
        public short CountryCode { get; set; }
        public TimeSpan CountryStartTime { get; set; }
        public TimeSpan CountryEndTime { get; set; }
        public string TimeDiffFromIST { get; set; }
        public string City { get; set; }
        public string CityTZ { get; set; }
    }
    public class NriCityTimeZone
    {
        public string NriCity { get; set; }
        public TimeSpan StartTime { get; set; }
        public TimeSpan EndTime { get; set; }
        public string TimeZone { get; set; }
        public string TimeDiffFromIST { get; set; }
    }

    public class AgentActivityTrackingModel

    {

        public List<DailerData> lstDialerDispDetails { get; set; }
        public Dictionary<Int64, List<DailerData>> UserBookingTimes { get; set; }
        //  public UserLoginDetails oUserLoginDetails { get; set; }
    }

    //public class UserLoginDetails
    //{

    //}
    [DataContract]
    public class DailerData
    {

        [DataMember(EmitDefaultValue = false)]
        public long ClaimID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 Duration { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 talktime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime callDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 AgentId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AgentName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime BookingDateTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime AgentLoginTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string process { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsConclave { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PbmeetV1AgentUrl { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string VcMeetingId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeName { get; set; }
    }

    [DataContract]
    [Serializable]
    public class ResetLPData
    {
        [BsonElement("_id")]
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class TicketObj
    {
        [DataMember(EmitDefaultValue = false)]
        public String ReqBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
    }

    [DataContract]
    public class CustomerCallBack
    {
        [DataMember(EmitDefaultValue = false)]
        public String Type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ProductID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmpID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string serverIP { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AgentPhone { get; set; }
    }

    [DataContract]
    public class UnAnsweredSummary
    {
        [DataMember(EmitDefaultValue = true)]
        public Int32 WeekAttempts { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CurrentWeek { get; set; }

        private DateTime _AttemptsTillDate;
        [DataMember(EmitDefaultValue = false)]
        public DateTime AttemptsTillDate
        {
            get
            {
                if (_AttemptsTillDate == DateTime.MinValue)
                {
                    _AttemptsTillDate = DateTime.Now;
                }
                else if (_AttemptsTillDate != null && _AttemptsTillDate.Kind == DateTimeKind.Utc)
                {
                    _AttemptsTillDate = _AttemptsTillDate.ToLocalTime();
                }
                return _AttemptsTillDate;
            }
            set { _AttemptsTillDate = value; }
        }

        [DataMember(EmitDefaultValue = true)]
        public Int32 TodayNANC { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 MaxAttempts { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CustomerNANC { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public Int32 CustomerTodayNANC { get; set; }
    }

    [DataContract]
    public class NotContactedLeads
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CustID { get; set; }
    }
    [DataContract]
    public class QuoteSharedTracking
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime ts { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 AgentId { get; set; }
    }
    [DataContract]
    public class OneLeadScoreMapping
    {
        public string Type { get; set; }
        public Int16 MinValue { get; set; }
        public Int16 MaxValue { get; set; }
        public Decimal Score { get; set; }
        public Int16 ProductID { get; set; }
    }

    public enum MobileStatusEnum
    {
        Default = 0,
        Valid = 1,
        Suspicious = 2,
        InValid = 3
    }
    [DataContract]
    public class CustomerProfileData
    {
        [DataMember(EmitDefaultValue = false)]
        public string Memberage { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Gender { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? Birthday { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AnnualIncome { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int? InvestmentTypeID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 IsPED { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string PlanType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string utmSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 MemberCount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string selectedInsurer { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string ApplicationNumber { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FV { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NML { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NML20x { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NFL { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string PlanName { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string TermStartDate { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string KYCRemark { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string LeadSocre { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string DOB { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FUPDate { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string TotalPremium { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string AnnualisedPremium { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string FullTermPremium { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string BookingFrequency { get; set; }



        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string BajaAnnualIncome { get; set; }

    }

    [DataContract]
    public class claimcomments
    {
        [DataMember(EmitDefaultValue = false)]
        public List<commentslist> Data { get; set; }
    }

    [DataContract]
    public class commentslist
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ClaimId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 StatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SubStatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<CommentsData> Comments { get; set; }

    }

    [DataContract]
    public class CommentsData
    {
        [DataMember(EmitDefaultValue = false)]

        public string Comments { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CommentDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }

    }

    [DataContract]
    public class CustContactInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadId { get; set; }

        [DataMember]
        public Int32 ProductId { get; set; }

        [DataMember]
        public long CustomerId { get; set; }


        [DataMember]
        public string Name { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CountryID { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int32 UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }
        [DataMember]
        public string Type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsPrimary { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string value { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 CustMobId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string mobNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 countryId { get; set; }
        [DataMember]
        public string OTP
        {
            get;
            set;
        }
        [DataMember]
        public bool IsOTPVerified
        {
            get;
            set;
        }

        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 CountryCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 SecondaryCountryCode { get; set; }
    }
    public struct ClickToLeadResponse
    {
        public int EnquiryID { get; set; }
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public string RedirectionURL { get; set; }
    }
    public class SOSBookingModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long leadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptedLeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]

        public string BMSUserToken { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime Createdon { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string link { get; set; }

    }

    public class VCLogModel
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long leadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Comments { get; set; }

    }
    public class WhatsAPPModal
    {
        [DataMember(EmitDefaultValue = false)]
        public string isSuccess { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string url { get; set; }



    }



    public class IsOnCall
    {
        public bool onCall { get; set; }
    }

    public class OnCallModal
    {
        public bool IsCustOnCall { get; set; }
        public string message { get; set; }
        public int status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public IsOnCall data { get; set; }
    }
    public class OnCallCustomerModal
    {
        public bool IsCustOnCall { get; set; }
        public string message { get; set; }
        public int status { get; set; }


    }

    public class CityMasterModal
    {
        public Int32 CountryId { get; set; }
        public string Country { get; set; }
        public Int32 StateId { get; set; }

        public Int32 CityId { get; set; }
        public Int32 ZoneId { get; set; }
        public Int32 AppointmentTypeId { get; set; }
        public Int32 AssigntmentId { get; set; }
        public string City { get; set; }
        public string Zone { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Lat { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Long { get; set; }


    }

    public class AppointmentTypeModal
    {
        public Int32 AppointmentId { get; set; }
        public Int32 ProcessId { get; set; }
        public string value { get; set; }


    }
    public class FOSAssignmentModal
    {
        public Int32 AssignmentId { get; set; }
        public Int32 AppointmentId { get; set; }
        public string AssignmentType { get; set; }

    }
    public class FOSModal
    {
        public List<AppointmentTypeModal> fosAppointmentList { get; set; }
        public List<FOSAssignmentModal> fosAssignmentList { get; set; }

    }
    public class InsurerQuotePrice
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 SupplierId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Price { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int OTC { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UpdatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 FamilyId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string FilePath { get; set; }
        public string DocId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 QuoteNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long SMEQuoteId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ControlId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CreatedBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UploadedBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime? UpdatedDate { get; set; }

    }

    public class CustomerBookingDocumentDetails
    {
        public string DocumentURL { get; set; }
        public string ShortDocUrl { get; set; }
        public string error { get; set; }
        public string LeadId { get; set; }

    }

    public class BasicPolicyInfo
    {
        public long CustomerId { get; set; }
        public string DocUploadId { get; set; }
        public int PaymentStatus { get; set; }
        public int StatusId { get; set; }
        public DateTime BookingDate { get; set; }
        public int PolicyType { get; set; }
        public string PolicyNo { get; set; }
        public DateTime PolicyStartDate { get; set; }
        public DateTime PolicyEndDate { get; set; }
        public string RegistrationNo { get; set; }
        public decimal IDV { get; set; }
    }


    public class BmsLoginUrl
    {
        [DataMember(EmitDefaultValue = false)]
        public bool isSuccess { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string message { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RedirectUrl { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BMSAuthToken { get; set; }

    }
    [DataContract]
    public class ResponseAPI
    {
        [DataMember]
        public bool status { get; set; }


        [DataMember]
        public string message { get; set; }


    }
    [DataContract]
    public class CityModal
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 CityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string city { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DisplayName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 StateId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }
    }

    public class SubStatusModal
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SubStatusName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ShortSubStatus { get; set; }

    }


    [DataContract]
    public class SaveInfo
    {
        [DataMember]
        public bool IsSaved { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public string Warnings { get; set; }

        [DataMember]
        public Int32 StatusCode { get; set; }

        [DataMember]
        public string ReferralId { get; set; }

        [DataMember]
        public AppResponse data { get; set; }
    }

    public class AppResponse
    {
        [DataMember]
        public string AppointmentId { get; set; }

    }

    [DataContract]
    public class SMEQuoteModal
    {
        [DataMember]
        public long LeadID { get; set; }

        [DataMember]
        public int ControlId { get; set; }

        [DataMember]
        public string Path { get; set; }

        [DataMember]
        public long UserId { get; set; }
        [DataMember]
        public Int16 typeid { get; set; }
        [DataMember]
        public Int16 QuotesStatusId { get; set; }

        [DataMember]
        public string SMEQuote { get; set; }

        [DataMember]
        public string fileName { get; set; }

        [DataMember]
        public int roleId { get; set; }
        [DataMember]
        public string comment { get; set; }
        [DataMember]
        public string ReasonId { get; set; }

        [DataMember]
        public int SubStatusId { get; set; }

        [DataMember]
        public int productId { get; set; }

        [DataMember]
        public string DocumentId { get; set; }

        [DataMember]
        public string QuoteSource { get; set; }
    }

    [DataContract]
    public class SMELeadQuote
    {
        [DataMember(EmitDefaultValue = false)]
        public long RowId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ControlId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Path { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UploadedBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime UploadedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public QuotesStatusMaster QuotesStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsUploadedByAgent { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FileName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<InsurerQuotePrice> QuotePriceList { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ReasonName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ReasonID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long parentRowId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 QuoteType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DocumentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string QuoteSource { get; set; }
    }
    [DataContract]
    public class QuotesStatusMaster
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 QuotesStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusId { get; set; }
    }

    [DataContract]
    public class LeadStatusDetails
    {
        public string LeadStatus { get; set; }
        public int StatusId { get; set; }
        public int AssignToGroupId { get; set; }
        public string LeadId { get; set; }
        public string error { get; set; }
    }

    [DataContract]
    public class SMEQuoteDetailModel
    {
        [DataMember(EmitDefaultValue = false)]
        public int SMEQuotesId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 roleId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ControlId { get; set; }
    }

    [DataContract]
    public class SMEAdditionalFileModal
    {
        [DataMember]
        public long LeadID { get; set; }

        [DataMember]
        public int ControlId { get; set; }

        [DataMember]
        public string filePath { get; set; }

        [DataMember]
        public long UserId { get; set; }


        [DataMember]
        public string FileName { get; set; }

        [DataMember]
        public int roleId { get; set; }
        [DataMember]
        public long parentRowId { get; set; }
        [DataMember]
        public bool IsDelete { get; set; }
        [DataMember]
        public int RowId { get; set; }

        [DataMember]
        public string DocumentId { get; set; }

        [DataMember]
        public string QuoteSource { get; set; }
    }

    [DataContract]
    public class CustAddModal
    {

        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }
        [DataMember]
        public string AddressType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 AddressTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CityId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CustAddrId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string House { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsActive { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsPrimary { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Landmark { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Locality { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 PinCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 StateId { get; set; }
        //[DataMember(EmitDefaultValue = false)]
        //public long UserId { get; set; }

    }

    [DataContract]
    public class SubProductListModal
    {
        [DataMember(EmitDefaultValue = false)]
        public long SubProductId { get; set; }
        [DataMember]
        public string SubProductName { get; set; }
        [DataMember]
        public string GroupName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string QueueName { get; set; }
    }
    [DataContract]
    public class AssignLeadModal
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember]
        public long AssignTo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long AssignBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 JobId { get; set; }
    }
    [DataContract]
    public class TTModal
    {

        [DataMember(EmitDefaultValue = false)]
        public string HistoryTalktime;
        [DataMember(EmitDefaultValue = false)]
        public string TodayTalkTime;



    }

    public class CustomerSelection
    {
        [DataMember]
        public Int64 LeadID { get; set; }
        [DataMember]
        public DateTime QuoteCreatedOn { get; set; }
        public int PlanID { get; set; }
        public string PlanName { get; set; }
    }



    [DataContract]
    public class bookingModel
    {
        [DataMember(EmitDefaultValue = false)]
        public Int16 statusCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 AssigmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string BookingType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int LanguageId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 SalesAgentGroupId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SalesAgentGroupName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SalesAgentLocation { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsCrossBUSale { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<LeadProduct> LeadProductData { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long SalesAgentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 AppointmentUserProcessId { get; set; }
    }
    [DataContract]
    public class LeadProduct
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Gender { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DOB { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AltPhoneNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmailID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Address { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CityID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 StateID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PostCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Country { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MaritalStatus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AnnualIncome { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ExitPointURL { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Utm_source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UTM_Medium { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Utm_term { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Utm_campaign { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedON { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedON { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 ReferralID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 AddOnParentID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime DateOfBirth { get; set; } //date

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadCreationSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string utm_content { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 EnquiryId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptionText { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptedLeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 TenantId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 BusinessPartnerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProfessionId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 ParentID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 Term { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TypeOfPolicy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 InvestmentTypeID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 Noofmembers { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 PayTerm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PlanFeatureType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CompanyName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TransitType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Nationality { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string SalesAgent { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string SecondarySalesAgent { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string InternalResponse { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SlotSource { get; set; }
    }
    [DataContract]
    public class LeadFlags
    {
        [DataMember]
        public Int16 AttributeID { get; set; }

        [DataMember]
        public Int16 Value { get; set; }

        [DataMember]
        public Int64 LeadId { get; set; }
        [DataMember]
        public Int64 UserId { get; set; }


    }
    [DataContract]
    public class NewSVURLModel
    {
        [DataMember]
        public Int16 StatusCode { get; set; }
        [DataMember]
        public string StatusMessage { get; set; }
        [DataMember]
        public string URL { get; set; }

    }

    [DataContract]
    public class UserInfoModel
    {
        [DataMember]
        public Int16 StatusCode { get; set; }
        [DataMember]
        public string Status { get; set; }
        [DataMember]
        public int Ok { get; set; }
        [DataMember]
        public List<long> Leads { get; set; }
    }

    [DataContract]
    public class UserInfo
    {
        [DataMember]
        public long Mobile { get; set; }
        [DataMember]
        public long CustId { get; set; }
        [DataMember]
        public int NoOfProfile { get; set; }
        [DataMember]
        public string Name { get; set; }
        [DataMember]
        public string Source { get; set; }
    }

    [DataContract]
    public class SelectedQuote
    {
        [DataMember]
        public long ID { get; set; }
        [DataMember]
        public int PlanID { get; set; }
        [DataMember]
        public short ProductID { get; set; }
        [DataMember]
        public decimal Premium { get; set; }
        [DataMember]
        public long LeadID { get; set; }
        [DataMember]
        public int SelectedBy { get; set; }
        [DataMember]
        public string IPAddress { get; set; }
        [DataMember]
        public int NeedId { get; set; }
        [DataMember]
        public int EnquiryId { get; set; }
        [DataMember]
        public int SupplierId { get; set; }
        [DataMember]
        public string SourcePage { get; set; }

        [DataMember]
        public string PlanFeature { get; set; }
        [DataMember]
        public int AddonComboId { get; set; }
        [DataMember]
        public short SpecialPlanType { get; set; }

    }

    [DataContract]
    public class LeadDeatils
    {
        [DataMember]
        public string LeadSource { get; set; }
        [DataMember]
        public string UTMSource { get; set; }
        [DataMember]
        public string UTMTerm { get; set; }
        [DataMember]
        public string UTMMedium { get; set; }
        [DataMember]
        public string UTMCampaign { get; set; }
        [DataMember]
        public string Name { get; set; }
        [DataMember]
        public string DOB { get; set; }
        [DataMember]
        public string MobileNo { get; set; }
        [DataMember]
        public string EmailId { get; set; }
        [DataMember]
        public int CityID { get; set; }
        [DataMember]
        public int StateID { get; set; }
        [DataMember]
        public int ProductID { get; set; }
        [DataMember]
        public int SupplierId { get; set; }
        [DataMember]
        public int PlanId { get; set; }
        [DataMember]
        public string PlanName { get; set; }
        [DataMember]
        public string SupplierName { get; set; }
        [DataMember]
        public long CustomerID { get; set; }
        [DataMember]
        public string PolicyNo { get; set; }
        [DataMember]
        public string ParentLeadSource { get; set; }
        [DataMember]
        public long ParentLeadId { get; set; }
        [DataMember]
        public Int16 Gender { get; set; }
        [DataMember]
        public DateTime LeadCreatedOn { get; set; }
        [DataMember]
        public bool IsReferral { get; set; }

        [DataMember]
        public bool IsFos { get; set; }

        [DataMember]
        public short StatusId { get; set; }

        [DataMember]
        public string StatusName { get; set; }

        [DataMember]
        public Int16 BookingFraud{ get; set; }

        [DataMember]
        public bool IsAssigned { get; set; }
        [DataMember]
        public DateTime PolicyExpiryDate { get; set; }
        [DataMember]
        public long LeadId { get; set; }
        [DataMember]
        public long AssignedAgentId { get; set; }
    }

    [DataContract]
    public class LeadRequest
    {
        [DataMember]
        public string Name { get; set; }
        [DataMember]
        public string DOB { get; set; }
        [DataMember]
        public string MobileNo { get; set; }
        [DataMember]
        public string EmailId { get; set; }
        [DataMember]
        public int CityID { get; set; }
        [DataMember]
        public int StateID { get; set; }
        [DataMember]
        public int ProductID { get; set; }
        [DataMember]
        public int SupplierId { get; set; }
        [DataMember]
        public int PlanId { get; set; }
        [DataMember]
        public long ReferralID { get; set; }
        [DataMember]
        public string PlanName { get; set; }
        [DataMember]
        public string SupplierName { get; set; }
        [DataMember]
        public int CustomerID { get; set; }
        [DataMember]
        public string LastYearPolicyNo { get; set; }
        [DataMember]
        public long CustPolicyID { get; set; }
        [DataMember]
        public string PolicyExpiryDate { get; set; }
        [DataMember]
        public short IsActualEndDate { get; set; }
    }

    [DataContract]
    public class LeadResponse
    {
        [DataMember]
        public Int64 LeadID { get; set; }
        [DataMember]
        public string Error { get; set; }
    }

    [DataContract]
    public class Remarks
    {
        [DataMember]
        public List<string> RemarksList { get; set; }
    }

    [DataContract]
    public class GetLeadDetails
    {
        [DataMember]
        public string LeadId { get; set; }

        [DataMember]
        public bool IsBooking { get; set; }
    }

    [DataContract]
    public class HealthRenewalRemarks
    {
        [DataMember]
        public string Remarks { get; set; }
    }

    [DataContract]
    public class AgentSupervisor
    {
        [DataMember]
        public string EmployeeId { get; set; }

        [DataMember]
        public string EmailID { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public List<Supervisor> Supervisors { get; set; }
    }

    [DataContract]
    public class Supervisor
    {
        [DataMember]
        public int Level { get; set; }

        [DataMember]
        public string EmployeeId { get; set; }

        [DataMember]
        public string EmailID { get; set; }

        [DataMember]
        public string UserName { get; set; }
    }

    [DataContract]
    public class CustomerIntent
    {
        [DataMember]
        public long LeadID { get; set; }

        [DataMember]
        public string Remarks { get; set; }

        [DataMember]
        public string KeyName { get; set; }
    }

    [DataContract]
    public class LeadAction
    {
        [DataMember]
        public long LeadID { get; set; }

        [DataMember]
        public string Action { get; set; }

        [DataMember]
        public string CreatedOn { get; set; }
    }

    [DataContract]
    public class GenerateUrlModel
    {
        [DataMember]
        public long LeadId { get; set; }

        [DataMember]
        public long UserId { get; set; }
        [DataMember]
        public short TypeId { get; set; }
    }

    [DataContract]
    public class LeadDataModel
    {
        [DataMember]
        public long LeadId { get; set; }

        [DataMember]
        public long CustomerId { get; set; }

        [DataMember]
        public short roleId { get; set; }

        [DataMember]
        public long UserId { get; set; }

        [DataMember]
        public short ProductId { get; set; }

        [DataMember]
        public int NeedId { get; set; }

        [DataMember]
        public long MatrixLeadId { get; set; }

        [DataMember]
        public string EmployeeID { get; set; }

    }

    [DataContract]
    public class LeadAudit
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ActionOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long AgentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SectionName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Field { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string OldValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string NewValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Status { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AgentName { get; set; }
    }

    [DataContract]
    public class UpsellStatus
    {
        [DataMember]
        public List<SVField> LeadWiseChanges { get; set; }
    }

    [DataContract]
    public class SVField
    {
        [DataMember]
        public long LeadId { get; set; }

        [DataMember]
        public List<KeyValue> upsellFields { get; set; }
    }

    [DataContract]
    public class KeyValue
    {
        [DataMember]
        public string key { get; set; }

        [DataMember]
        public string value { get; set; }
    }

    public class LeadDetailsForCustId
    {
        public long LeadId { get; set; }
        public long RenewalLeadId { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public DateTime CreatedON { get; set; }
        public int StatusID { get; set; }
        public string StatusName { get; set; }
        public string RegistrationNo { get; set; }
    }

    public class LeadDetailsForCustIdResponse
    {
        public List<LeadDetailsForCustId> LeadDetails { get; set; }
        public string Message { get; set; }
        public bool Status { get; set; }
    }

    public class CallDataResponse
    {
        public List<CallDetail> CallDetails { get; set; }
        public string Message { get; set; }
        public bool Status { get; set; }
    }

    public class CallDetail
    {
        public long LeadId { get; set; }
        public int Duration { get; set; }
        public int Talktime { get; set; }
        public long UserId { get; set; }
        public string CallDate { get; set; }
    }

    public class CallBack
    {
        public Int64 LeadId { get; set; }
        public string CallBacktime { get; set; }
        public string ProcessName { get; set; }
        public Int64 AgentId { get; set; }
        public Int64 AdminId { get; set; }
        public Int16 CallBackTypeId { get; set; }
        public string Subject { get; set; }
        public Int64 ServiceLeadId { get; set; }
    }

    public class CJUrlModel
    {
        public string token { get; set; }
        public string redirectionUrl { get; set; }
        public bool isMobile { get; set; }
        public string mobileUrl { get; set; }
        public string remarks { get; set; }
        public string message { get; set; }
        public string matrixLeadID { get; set; }
        public string enquiryID { get; set; }
        public string encEnquiryId { get; set; }
        public bool isSuccess { get; set; }
        public string loginStatus { get; set; }
        public List<int> suppliers { get; set; }
        public int leadCount { get; set; }
        public int createdDays { get; set; }
        public string redirectionNotInCJURL { get; set; }
        public string ExitPointURL { get; set; }
        public string redirectURL { get; set; }

    }

    [DataContract]
    public class GetBasicLeadDetails
    {
        [DataMember]
        public string LeadId { get; set; }
    }

    [DataContract]
    public class EncryptionKeyDetails
    {
        [DataMember]
        public string Source { get; set; }
        [DataMember]
        public string EncKey { get; set; }
        [DataMember]
        public string EncIV { get; set; }
    }

    [DataContract]
    public class PGViewModel
    {
        [DataMember]
        public string LeadId { get; set; }
        [DataMember]
        public string Insurer { get; set; }
        [DataMember]
        public Int16 Paymentstatus { get; set; }
        [DataMember]
        public decimal Amount { get; set; }
        [DataMember]
        public string OrderNo { get; set; }
        [DataMember]
        public string AttemptID { get; set; }
        [DataMember]
        public string PaymentMode { get; set; }
        [DataMember]
        public string FailureReason { get; set; }
        [DataMember]
        public DateTime AttemptTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Product;
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn;
        [DataMember(EmitDefaultValue = false)]
        public Int16 AttemptCount;
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId;
        [DataMember]
        public string CustomerName;


    }

    public class RenewalBookingData
    {
        public string ReferralId { get; set; }
        public int ProductId { get; set; }
        public string CustomerId { get; set; }
    }

    [DataContract]
    public class TicketModel
    {
        [DataMember]
        public Int64 CreatedBy { get; set; }
        [DataMember]
        public Int16 SourceID { get; set; }
        [DataMember]
        public Int16 IssueID { get; set; }
        [DataMember]
        public string Title { get; set; }
        [DataMember]
        public string Comments { get; set; }
        [DataMember]
        public Int16 ProductID { get; set; }
        [DataMember]
        public string FileURL { get; set; }
        [DataMember]
        public string FileName { get; set; }

    }

    public class ExclusiveBenifit
    {
        public Int64 LeadId { get; set; }
        public Int64 customerID { get; set; }
        public Int16 isExclusiveBenifit { get; set; }
    }

    [DataContract]
    [Serializable]
    public class UrlResponse
    {
        [DataMember(EmitDefaultValue = true)]
        public string StatusMessage { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public string ExitPointURL { get; set; }
    }
    [DataContract]
    [Serializable]
    public class AgentProfileData
    {
        [DataMember(EmitDefaultValue = true)]
        public string AgentName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string AgentEmployeeId { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string AgentProfileImage { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string PreferredLanguage { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int TotalPoliciesSold { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int Tenure { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Rewards { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string AgentStatus { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int IsAVCertified { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int IsFOS { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public List<AssignmentDetails> AssignmentDetailsData { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public List<CallDetails> CallDetailsData { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public bool Status { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string Message { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public decimal TotalSumInsured { get; set; }
        [DataMember(EmitDefaultValue = true)]    
        public string Gender { get; set; }
    }
    [DataContract]
    [Serializable]
    public class AssignmentDetails
    {
        [DataMember]
        public long LeadID { get; set; }
        [DataMember]
        public string AgentName { get; set; }
        [DataMember]
        public long AgentUserId { get; set; }
        [DataMember]
        public string AgentEmpId { get; set; }
        [DataMember]
        public DateTime AssignmentTime { get; set; }
        [DataMember]
        public bool IsLastAssigned { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string AgentProfileImage { get; set; }
    }
    [DataContract]
    [Serializable]
    public class CallDetails
    {
        [DataMember]
        public long LeadID { get; set; }
        [DataMember]
        public long AgentUserId { get; set; }
        [DataMember]
        public DateTime CallDateTime { get; set; }
        [DataMember]
        public int CallDuration { get; set; }
        [DataMember]
        public int Talktime { get; set; }
    }

    public class EmployeeDetails
    {
        public string EmployeeId { get; set; }
        public string EmailId { get; set; }
        public string UserName { get; set; }
        public string ContactNo { get; set; }
        public long UserId { get; set; }
    }
    [DataContract]
    [Serializable]
    public class StoreDetails
    {
        [DataMember(EmitDefaultValue = true)]
        public int CityID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string StoreName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string StoreAddress { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public double Latitude { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public double Longitude { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long InboundNumber { get; set; }

        [DataMember]
        public Int32 StoreId { get; set; }

        [DataMember]
        public string CityName { get; set; }

    }

    [DataContract]
    public class CreateLeadRequest
    {
        [DataMember(EmitDefaultValue = false)]
        public bool IsDialer { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DialerUniqueId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string IvrOptions { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Hasbooking { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UtmSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UtmMedium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UtmCampaign { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UtmTerm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SubProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SupplierId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CountryId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Email { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Comment { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LeadCreationDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ReferralLead { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string VirtualNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Age { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ApiSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int Gender { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string DateofBirth { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public Int16 StateID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public Int16 CityID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long AnnualIncome { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string CompanyName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short OccupationId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AssignLead { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CallTransferType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CreatedByEmpId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CreatedByEmpName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AssignedUser { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AssignedGroupId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SubProductIdList { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AssociationId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Remarks { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsSmeFosCreateLead { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ParentCompany { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int LinkedinConnection { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LinkedinLink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string DecisionMakerCityId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ExecutiveRole { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int IndustryTypeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CINNumber { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long Premium { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short PolicyType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long PolicyStartDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ClaimHistory { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ExistingBroker { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ExistingInsurer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ExistingTPA { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int Probability { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CrossSellSubProductIds { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string WinningStrategy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long SumInsured { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int NoOfLives { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RegistrationNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short MaxStatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string QueueName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsHNICustomer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OtherExistingBroker { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OtherExistingInsurer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OtherExisitngTPA { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long RefLeadIdSme { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SubCIN { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<AltContactInformationDetails> AltContactInformation { get; set; }
    }

    public class CreateLeadResponse
    {
        public bool IsLeadCreated { get; set; }
        public long LeadId { get; set; }
        public long CustId { get; set; }
        public string Message { get; set; }
        public int SubProductId { get; set; }
        public VirtualNoAgentDetails AgentDetails { get; set; }
    }

    public class VirtualNoAgentDetails
    {
        public string DIDNo { get; set; }
        public bool IsWFH { get; set; }
        public string EmployeeId { get; set; }
        public string EmployeeName { get; set; }
        public int GroupID  { get; set; }
        public bool IsLoggedIn { get; set; }
        public string Queue { get; set; }
        public string OtherLanguaQueue { get; set; }
        public string AgentType { get; set; }
    }

    [DataContract]
    [Serializable]
    public class UTMCampaignDetails
    {
        [DataMember(EmitDefaultValue = false)]
        public string UtmCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UtmCampaign { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string link { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UtmSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UtmMedium { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }
    }


    public class CustomerLeadsRequest
    {
        public string SearchInput { get; set; }
        public short SearchType { get; set; }
        public long UserId { get; set; }
    }

    public class CustomerLeads
    {
        public long LeadId { get; set; }
        public string Name { get; set; }
        public string MobileNo { get; set; }
        public long CustomerId { get; set; }
        public string EmailId { get; set; }
        public long ParentId { get; set; }
        public string ProductName { get; set; }
        public string CreatedOn { get; set; }
        public bool IsReleased { get; set; }
        public bool IsDNC { get; set; }
        public string LeadSource { get; set; }
        public string UtmSource { get; set; }
        public short CityId { get; set; }
        public string ReturnsType { get; set; }
        public string CustomerStatus { get; set; }
        public string SalesAgentName { get; set; }
        public short SubStatusId { get; set; }
        public string DateofCertification { get; set; }
        public string City { get; set; }
        public string SubProductName { get; set; }
        public string StatusName { get; set; }
        public int StatusId { get; set; }
        public string StatusSubStatus { get; set; }
        public string AgentName { get; set; }
        public string GroupName { get; set; }
        public short GroupId { get; set; }
        public string StatusDate { get; set; }
        public string AssignedOn { get; set; }
        public string OfferNumber { get; set; }
        public bool IsNDNC { get; set; }
        public bool IsPaymentCall { get; set; }
        public string InvalidMobile { get; set; }
        public string PropFormFilled { get; set; }
        public string IsCertified { get; set; }

        public short ProductID { get; set; }

        public string ExitPointURL { get; set; }
    }

    public class LinkAppointmentRequest
    {
        public long LeadId { get; set; }
        public long ApptId { get; set; }
        public bool Type { get; set; }
    }

    public class GenericAPIResponse
    {
        public string message { get; set; }
        public int status { get; set; }
    }

    public class CallTransferRequest
    {
        public string transfer_agents { get; set; }
        public string campaign { get; set; }
        public string bookingid { get; set; }
        public string action { get; set; }
        public string transfer_type { get; set; }
        public string agent { get; set; }
    }

    public class MobileAppInstallationStatus
    {
        [DataMember(EmitDefaultValue = false)]
        public bool appCust { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string appVersion { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string lastSeen { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string os { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool success { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int status { get; set; }

    }

    public class InvestmentQuesURL
    {
        [DataMember(EmitDefaultValue = false)]
        public bool HasError { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReturnValue { get; set; }
    }

    public class CustomerInvestResponse
    {
        public bool HasError { get; set; }
        public Returnvalue ReturnValue { get; set; }
        public string StatusMessage { get; set; }
    }

    public class Returnvalue
    {
        public string NewLeadID { get; set; }
        public string NewEnquiryID { get; set; }
        public string LeadID { get; set; }
        public string CustId { get; set; }
        public string MobileNo { get; set; }
        public string MobileKey { get; set; }
        public string EnquiryKey { get; set; }
        public string CustKey { get; set; }
        public string GenderID { get; set; }
        public string Gender { get; set; }
        public string AnnualInvestment { get; set; }
        public string Frequency { get; set; }
        public string CustomerName { get; set; }
        public string Age { get; set; }
        public string CityID { get; set; }
        public string CityName { get; set; }
        public string StateID { get; set; }
        public string StateName { get; set; }
        public string CountryID { get; set; }
        public string CountryName { get; set; }
        public string FamilyMemberCount { get; set; }
        public string IsMarried { get; set; }
        public string ChildCount { get; set; }
        public string Income { get; set; }
        public string Expense { get; set; }
        public string EMI { get; set; }
        public string GoalTypeID { get; set; }
        public string GoalTypeName { get; set; }
        public string LastStepNumber { get; set; }
        public string ChildName { get; set; }
        public string ChildAge { get; set; }
        public string RetirementAge { get; set; }
        public object Field1 { get; set; }
        public object Field2 { get; set; }
        public object Field3 { get; set; }
        public object Field4 { get; set; }
        public string DataExists { get; set; }
        public Investmentdetail[] InvestmentDetails { get; set; }
    }

    public class Investmentdetail
    {
        public string CustomerInvestmentTypeID { get; set; }
        public string CustomerInvestmentKey { get; set; }
        public string Amount { get; set; }
        public string CustID { get; set; }
    }

    public class ShopType
    {
        public short Id { get; set; }
        public string ShopTypeName { get; set; }
    }
    [Serializable]
    public class CjReferralResponseModel
    {
        public bool HasError { get; set; }
        public string ReturnValue { get; set; }
    }

    public class EmailSaveInfo
    {
        public string encEmail { get; set; }
        public string statusMsg { get; set; }
        public string custId { get; set; }
        public Int32 statusCode { get; set; }

    }

    public class SendTemplateRequest
    {
        public string CustId { get; set; }
        public string URL { get; set; }
        public string LeadId { get; set; }
        public string productId { get; set; }
        public string process { get; set; }
    }

    [DataContract]
    public class ParentChildLeadData
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember]
        public bool IsParent { get; set; }
    }

    [DataContract]
    public class UserProductList
    {
        [DataMember(EmitDefaultValue = false)]
        public List<Product> Products { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<int> RoleSuperIds { get; set; }

        [DataMember(EmitDefaultValue = true)]
        public bool IsAgent { get; set; }
    }


    public class Product
    {
        [DataMember(EmitDefaultValue = false)]
        public int ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ProductName { get; set; }
    }
    [DataContract]
    public class CouponRedeemModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmpCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ReleasedBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ParentId { get; set; }
    }

    public class CallInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string pbcentrallogin { get; set; }
        
        public string Source { get; set; }
    }

    public class ClaimCallData
    {
        public string CallDateTime { get; set; }
        public int Duration { get; set; }
        public int Talktime { get; set; }
        public string CallingNo { get; set; }
        public string AgentName { get; set; }
        public string EmployeeId { get; set; }
    }


    public class ClaimCallDataRoot
    {
        public List<ClaimCallData> Data { get; set; }
        public object Error { get; set; }
        public int ErrorCode { get; set; }
    }




   
    [DataContract]
    public class CallIntentData
    {
        [DataMember(EmitDefaultValue = false)]
        public long Calldataid;
        [DataMember(EmitDefaultValue = false)]
        public long LeadId;
        [DataMember(EmitDefaultValue = false)]
        public long UserId;
        [DataMember(EmitDefaultValue = false)]
        public string Intent;
        [DataMember(EmitDefaultValue = false)]
        public string Predicted_label;
        [DataMember(EmitDefaultValue = false)]
        public decimal? Predicted_Scores;
        [DataMember(EmitDefaultValue = false)]
        public string Source;
    }
    
    [DataContract]
    public class HWData
    {
        [DataMember]
        public long BookingID { get; set; }
        [DataMember]
        public DateTime BookingDate { get; set; }
        [DataMember]
        public long CustomerId { get; set; }
        [DataMember]
        public string CustomerName { get; set; }
        [DataMember]
        public string Status { get; set; }
        [DataMember]
        public int ProductID { get; set; }
        [DataMember]
        public int IsSpouseWorking { get; set; }
        [DataMember]
        public int IsHWPitched { get; set; }
        [DataMember]
        public int IsSpousePlanBooked { get; set; }
        [DataMember]
        public string AgentName { get; set; }
        [DataMember]
        public string EmployeeId { get; set; }
    }

    [DataContract]
    public class VCPitchData 
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CallDataID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short ProductID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short Talktime { get; set; }
    }

    [DataContract]
    public class FeatureData
    {
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Feature { get; set; }
    }

    [DataContract]
    public class IncomeFromPayUData
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; } 

        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }
        
        [DataMember(EmitDefaultValue = false )]
        public string MobileNoHash { get; set; }
    }
    
    [DataContract]
    public class AssistanceData
    {
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string NeedAssistance { get; set; }
    }
    [DataContract]
    public class SmeIndustryType
    {
        [DataMember]
        public long Id { get; set; }

        [DataMember]
        public string Name { get; set; }
    }

    [DataContract]
    public class QuickSightUrl
    {
        [DataMember]
        public string Url { get; set; }
    }

    [DataContract]
    public class Master
    {
        [DataMember]
        public int Id { get; set; }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public Decimal Value { get; set; }
    }
    [DataContract]
    public class SMEMasterList
    {
        [DataMember(EmitDefaultValue = false)]
        public List<Master> TPA { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<Master> Insurer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<Master> Broker { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class CustEmergencyNo
    {
        private DateTime _ValidTill;
        
        [DataMember(EmitDefaultValue = false)]
        public int CountryCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncNumber { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ValidTill
        {
            get
            {
                if (_ValidTill != null && _ValidTill.Kind == DateTimeKind.Utc)
                {
                    _ValidTill = _ValidTill.ToLocalTime();
                }
                return _ValidTill;
            }
            set { this._ValidTill = value; }
        }

        [DataMember(EmitDefaultValue = false)]
        public long CustMobId { get; set; }
    }

    [DataContract]
    public class BHRResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public string Percentage { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string BGColorCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string BoxColorCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserSuperGroup { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TooltipParagraph { get; set; }
    }

    public class BHRColorPalette
    {
        public Dictionary<string, double[]> Ranges { get; set; }
    }

    public class ResponseUltraHNICust
    {
        [DataMember]
        public bool status { get; set; }

        [DataMember]
        public string message { get; set; }

        [DataMember]
        public bool IsUltraHNICustomer { get; set; }

        [DataMember]
        public PayuScores PayuScores { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PayuScoresResponse
    {
        [DataMember]
        public string message { get; set; }
        [DataMember]
        public PayuScores data { get; set; }
        [DataMember]
        public string id { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PayuScores
    {
        [DataMember]
        public int affluence_score { get; set; }
        [DataMember]
        public int pbhealth_gi_propensity_tier_calibrated { get; set; }
    }
    
    public class ScheduledCallbackData
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime EventDate { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public short IsGoogleInvite { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }    
    }
    public class AssignedLeadData
    {
        public long LeadId { get; set; }
        public long AssignedAgent { get; set; }
        public long ParentId { get; set; }
    }
    [DataContract]
    public class LeadAssignResp
    {
        [DataMember(EmitDefaultValue = true)]
        public string AssignedAgentECode { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string AssignToAgentName { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public long LeadID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int GroupID { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public int ISUHNIAgent { get; set; }

    }

    [DataContract]
    public class HealthPersistencyResponse
    {
        [DataMember(EmitDefaultValue = true)] 
        public string data { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public bool isSuccess { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string errorMessage { get; set; }
        [DataMember(EmitDefaultValue = true)]
        public string errorCode { get; set; }
    }

    public class EndorsementData
    {
        public string EndorsementDate { get; set; }
        public string EndorsementSection { get; set; }
        public string EndorsementField { get; set; }
        public string OldValue { get; set; }
        public string NewValue { get; set; }
        public string CurrentStatus { get; set; }
    }

    public class EndorsementDetailsResponse
    {
        public List<EndorsementData> Data { get; set; }
        public string Status { get; set; }
        public int ErrorCode { get; set; }
        public string ErrorMsg { get; set; }
        public bool IsSuccess { get; set; }
        public string ErrorTitle { get; set; }
        public string ErrorCTA { get; set; }
    }

    public class ClaimCallTransferAgentDetailsRequest
    {
        public long LeadId { get; set; }
        public int ProductId { get; set; }
        public string Source { get; set; }
        public bool IsC2C { get; set; }
        public long FollowUpDateUnix { get; set; }
        public string Remarks { get; set; }
        public string Action { get; set; }
    }

    public class CallTransferData
    {
        public int ClaimId { get; set; }
        public string QueueName { get; set; }
        public int AgentId { get; set; }
        public string EmployeeId { get; set; }
        public bool IsSuccessFUP { get; set; }
    }

    public class ClaimCallTransferAgentDetailsResponse
    {
        public CallTransferData Data { get; set; }
        public string Error { get; set; }
        public int ErrorCode { get; set; }
    }

    public class CustomerBookingsRequest
    {
        public int CustomerId { get; set; }
        public int ProductId { get; set; }
    }

    public class CustomerBookingDetails
    {
        public long LeadId { get; set; }
        public int? PlanId { get; set; }
        public int? SupplierId { get; set; }
        public int ProductId { get; set; }
        public string SupplierShortName { get; set; }
    }

    public class CustomerBookingsResponse
    {
        public List<CustomerBookingDetails> Bookings { get; set; }
        public string Error { get; set; }
        public int ErrorCode { get; set; }

        public CustomerBookingsResponse()
        {
            Bookings = new List<CustomerBookingDetails>();
            Error = string.Empty;
            ErrorCode = 0;
        }
    }

    public class ServiceCallTransferInfoRequest
    {
        public long LeadId { get; set; }
        public string CallType { get; set; }
        public string Source { get; set; }
    }

    public class ServiceCallTransferInfoResponse
    {
        public ServiceBookingInfo Data { get; set; }
        public string Error { get; set; }
        public int ErrorCode { get; set; }
    }

    public class ServiceBookingInfo
    {
        public string LeadId { get; set; }
        public string QueueName { get; set; }
        public string AssignedEmployeeID { get; set; }
    }

    public class AssignedAgentDetailRequest
    {
        public int CustomerId { get; set; }
        public int ProductId { get; set; }
    }

    public class AssignedAgentDetails
    {
        public long LeadId { get; set; }
        public string EmployeeId { get; set; }
        public string Error { get; set; }
        public int ErrorCode { get; set; }
    }
}