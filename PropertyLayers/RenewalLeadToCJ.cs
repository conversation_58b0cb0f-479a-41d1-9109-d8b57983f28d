﻿using System;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class RenewalLeadToCJ
    {

        [DataMember(EmitDefaultValue = false)]
        public string AddOnBasePremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AddOnProduct { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AddOnUPSellPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BaseSI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BaseSIPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string BatchID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustomerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Email { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FamilyType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PlanID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PlanName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PlanTerm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyEndDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Product { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProposerDOB { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ProposerName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object OneYearPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object OneYearQuote { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object TwoYearPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object TwoYearQuote { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object ThreeYearPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object ThreeYearQuote { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UPSellSI { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string UPSellSIPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long MatrixLeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object UpsellOneYearPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object UpsellOneYearQuote { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object UpsellTwoYearPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object UpsellTwoYearQuote { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object UpsellThreeYearPremium { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object UpsellThreeYearQuote { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductCode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MasterPolicyNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FreeHealthCheckup { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal NoClaimBonus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int RenewalYear { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsPreviousClaimsTaken { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int SpecificDiseaseWaitingPeriod { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AdditionalBenefits { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int PreExistingDiseaseWaitingPeriod { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object Wellnesspoint { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal PremiumInflation { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public object OldBookingId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MonthlyMode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ACH { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyInceptionDate { get; set; }
        public string Message { get; set; }
        public bool IsSuccess { get; set; }

        public string SupplierID { get; set; }
        public string Remarks { get; set; }
        public string Utmsource { get; set; }
        public int CityID { get; set; }
        public int ZipCode { get; set; }
        public string PremiumInflationReason { get; set; }
        public Int16 PremiumWaivedOff { get; set; }
        public Int16 LoadingPercentage { get; set; }
        public bool IsLoading { get; set; }
        public decimal LoadingPremium { get; set; }
        public string LoadingReason { get; set; }
        public bool WasPort { get; set; }
        public bool IsHealthRenewalFOS { get; set; }
        public string EnrollmentDate { get; set; }
        public string SubStatusCode { get; set; }
        public int Country { get; set; }
        public string PEDInfo { get; set; }
        public string STPStatus { get; set; }
        public string NSTPReason { get; set; }
        public bool UpsellOffer { get; set; }
        public string OfferDetails { get; set; }
        public bool DirectLastYear { get; set; }
    }




    public class GetRenewalLead {
        [DataMember(EmitDefaultValue = false)]
        public string CustomerId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PolicyNo { get; set; }
    }

    public class GetTicketServiceURL
    {
        [DataMember(EmitDefaultValue = false)]
        public string UId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ECode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Type { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Lid { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string TId { get; set; }
    }
}
