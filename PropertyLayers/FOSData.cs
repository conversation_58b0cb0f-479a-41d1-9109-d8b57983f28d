﻿using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class FOSLoginData
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Password { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }


    }

    public class APIResponse
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 statusCode { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string AgentType { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string Msg { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool Result { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string URL { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime? SelfieExpiryDate { get; set; }
    }

    public class LeadsSubStatusModel
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 StatusId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 ParentID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsLogSubStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Latitude { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Longitude { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 IsAppDone { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CancelReasonId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long CreatedBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Address { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptLeadId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string EncryptedCustomerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentUID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Address1 { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Address2 { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Pincode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Landmark { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Comments { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 EventId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ReasonText { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 IsActive { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 Gender { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string CustomerInstructions { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public PlaceLatLongModel AppointmentLatLong { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ProductId { get; set; }

        [DataMember]
        public bool SendRescheduleTrigger { get; set; } = true;
    }

    public class AppointmentsDataModel
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public object _id { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Status { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string SubStatus { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int64 AppointmentId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Int32 StatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PivcStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string VerificationStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ApplicationNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string PivcLink { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 Type { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long CustomerId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long ParentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Address { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Pincode { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 CityId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 AppointmentType { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 OfflineCityId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 ZoneId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Address1 { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Landmark { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Comments { get; set; }


        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public List<Recommendation> PlanList;
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 AssignmentId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Reason { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 ReasonId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 subStatusId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Source { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 ProductId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public DateTime? AppointmentStart { get; set; }


        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 IncomeDocsId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 EducationId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 IncomeId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 BookingId { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 SlotId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public PlaceLatLongModel location { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string NearBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Email { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime CbTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string EmployeeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string AgentName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal StartDistance { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal EndDistance { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string AssignmentVal { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string AppointmentVal { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public int ActionDoneBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string DisplayAppSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string QRCode { get; set; }



        [DataMember(EmitDefaultValue = false)]
        public string EventType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 EventTypeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 ReferralID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string LeadSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime OldAppointmentDateTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AssignTo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsCustReschedule { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AppCreatedSource { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 IsRenewal { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 Gender { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool flag { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 FirstReporting { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 SecondReporting { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 ThirdReporting { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ProductName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string FirstReportingValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string SecondReportingValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ThirdReportingValue { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsReScheduled { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public String City { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long AppCreatedBy { get; set; }


        [DataMember(EmitDefaultValue = true)]
        public long LinkedLeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string GenderName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string DisplayAssignmentType { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsCustConfirmed { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsCustLocationConfirmed { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsNewTrigger { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public List<AIAudioModel> AudioFiles { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 CountryCode { get; set; }

        public bool canCreateMotorAppt { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 TTAttempts { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 TTTalkTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime LastAttempt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 OTPVerified { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 RealTimeStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]

        public string UserName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RealTimeStatus { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public long ManagerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal IdleTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal distanceFromCustomer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime StatusChangedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal overallDistance { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal distance { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool Islogout { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 AgentCurrentStatus { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptedLeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptedCustomerId { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string place_id { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 CancelReasonId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 VirtualProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 IsDropLocationConfirm { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string InvestmentType { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int16 InvestmentTypeId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AppVersion { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OS { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string OSVersion { get; set; }
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public PlaceLatLongModel AgentBaseLocation { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 RescheduleCount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? Portability { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime? PolicyExpiryDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public  LatLongModel PincodeLatLong { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustResponse { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool AffluencePayU { get; set; }
    }
    public enum AppointmentTypeEnum
    {
        Default = 0,
        setAppointment = 1,

    }

    public class CustInfoModel
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID { get; set; }

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Email { get; set; }



        [DataMember(EmitDefaultValue = false)]
        public string Address { get; set; }


        //[DataMember(EmitDefaultValue = false)]
        //public Int16 cityId { get; set; }


        //[DataMember(EmitDefaultValue = false)]
        //public Int16 stateId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string Address1 { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Pincode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Landmark { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string State { get; set; }



    }

    public class ActvityHistoryModel
    {



        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string SubStatusName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }
    }

    public class LogInDTO
    {
        public string EmployeeId { get; set; }
        public string Password { get; set; }
        public string IPAddress { get; set; }
        public int LogInType { get; set; }
        public int LogOutType { get; set; }
        public int LogInBy { get; set; }
        public int LogOutBy { get; set; }
        public bool IsActive { get; set; }
        public long UserId { get; set; }
        public string URL { get; set; }
        public string SessionId { get; set; }
        public int LoginButtonId { get; set; }
        public string Device { get; set; }
        public string Resolution { get; set; }
        public Decimal Latitude { get; set; }
        public Decimal Longitude { get; set; }
        public bool IsLogout { get; set; }

        public string Process { get; set; }
    }

    public class CommentModal
    {

        [DataMember(EmitDefaultValue = false)]
        public Int64 CustomerID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64? ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Comment { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16? EventTypeId { get; set; }

    }

    public class Data
    {

        [DataMember(EmitDefaultValue = false)]
        public Int16 statusCode { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string AgentType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool Result { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 PassExpDaysLeft { get; set; }
        

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public data data { get; set; }
    }
    public class data
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string mail { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]

        public string givenName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string displayName { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string description { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProcessId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<GroupDetails> Groups { get; set; }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string VirtualNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public UserProfileData UserProfileData { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsRM { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DIDNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsSkipCustOtp { get; set; }
    }

    public class LdapModal
    {
        public bool error { get; set; }
        public object data { get; set; }
        public string message { get; set; }
        public object token { get; set; }
        public bool authenticated { get; set; }
        public LdapUserModel user { get; set; }
    }
    public class LdapUserModel
    {
        public string dn { get; set; }
        public string userPrincipalName { get; set; }
        public string sAMAccountName { get; set; }
        public string mail { get; set; }
        public string lockoutTime { get; set; }
        public string whenCreated { get; set; }
        public string pwdLastSet { get; set; }
        public string userAccountControl { get; set; }
        public string sn { get; set; }
        public string givenName { get; set; }
        public string cn { get; set; }
        public string displayName { get; set; }
        public string description { get; set; }
    }
    public class EmpData
    {

        public string empId { get; set; }
        public string password { get; set; }

    }
    [JsonObject(ItemNullValueHandling = NullValueHandling.Ignore)]
    public class ReminderModal
    {
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public long LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public long AppointmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime ReminderDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Description { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string ReminderTopic { get; set; }

        [DataMember(EmitDefaultValue = false)]

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? startDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime? EndDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public DateTime UpdatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public long UserId { get; set; }

        public long FosReminderId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Message { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public bool IsMarked { get; set; }



    }

    public class ReminderUpdateModal
    {
        [DataMember(EmitDefaultValue = false)]
        public long FosReminderId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsDelete { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsMarked { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public long UserId { get; set; }
    }
    [DataContract]
    public class CustomerCommentModal
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Comments { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CreatedBy { get; set; }
    }

    public class AppointmentLocation
    {
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 SubStatusId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Lat { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Long { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Distance { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string DeviceId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public long UserId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public Int32 Type { get; set; }
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        [DataMember(EmitDefaultValue = false)]
        public string source { get; set; }


    }

    public class AssignmentCityMaster
    {
        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public Int32 CityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AssignmentType { get; set; }




    }
    public class LeadAttributesModel
    {

        [BsonIgnoreIfDefault]
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public Dictionary<string, string> data { get; set; }

    }
    public class AppLocationsModal
    {
        [DataMember(EmitDefaultValue = false)]
        public decimal Latitude { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Longitude { get; set; }
    }




    public class PIVCData
    {

        public int ParentLeadId { get; set; }
        public int LeadId { get; set; }
        public string PivcLink { get; set; }
        public string PivcStatus { get; set; }
        public string VerificationStatus { get; set; }
        public string ApplicationNo { get; set; }
        public int ProductId { get; set; }
        public int SupplierId { get; set; }
        public string AppointmentTypeId { get; set; }
    }

    public class ReasonCancelModal
    {
        [DataMember(EmitDefaultValue = false)]
        public Int16 ReasonId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Reason { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 subStatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 Type { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsShow { get; set; }
    }



    public class FOSPitchedModel
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public bool Response { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Type { get; set; }
    }

    public class PolicyStatusByLeadIDModel
    {
        public int LeadID { get; set; }
        public string PolicyNo { get; set; }
        public string StatusName { get; set; }
        public string ApplicationNo { get; set; }
        public long BookingDate { get; set; }
        public string IsIssued { get; set; }
        public int ProductID { get; set; }
        public string RegistrationNo { get; set; }
        public int ItemID { get; set; }
        public string OrderNo { get; set; }
        public DateTime? LeadCreationDate { get; set; }
        public string IsBooked { get; set; }
        public string AssignedAgent { get; set; }
        public object AssignedDate { get; set; }
        public object IssuanceDate { get; set; }
        public bool IsNRI { get; set; }
        public string SubStatusName { get; set; }
        public int StatusID { get; set; }
        public int SubStatusID { get; set; }
        public string SupplierName { get; set; }
        public string PlanName { get; set; }
        public double Premium { get; set; }
        public int CustomerId { get; set; }
        public DateTime? PolicyStartDate { get; set; }
        public DateTime? PolicyEndDate { get; set; }
        public double SumInsured { get; set; }
        public string IsRejected { get; set; }
        public int PlanId { get; set; }
        public int SupplierId { get; set; }
        public string DOB { get; set; }
        public string IsSTP { get; set; }
        public string SalesAgent { get; set; }
        public int PolicyTerm { get; set; }
        public int PolicyTermInDays { get; set; }
        public string PaymentPeriodicity { get; set; }
        public DateTime? OfferCreatedON { get; set; }
        public int InstallmentsPaid { get; set; }
        public string PolicyLink { get; set; }
        public bool ProposalFillFlag { get; set; }
        public bool PaymentE2EFlag { get; set; }
        public DateTime? LastStatusChange { get; set; }
        public string ExitPointURL { get; set; }
        public string ARNNo { get; set; }
        public string LeadSource { get; set; }
        public string PIWCStatus { get; set; }
        public string PIWCLink { get; set; }
        public bool InternalPIVCAvailable { get; set; }
        public object PIWCStatusUpdatedOn { get; set; }
        public string InsuredName { get; set; }
        public int PayTerm { get; set; }
        public string ProposerName { get; set; }
        public object ProposerDateofBirth { get; set; }
        public int CurrencyId { get; set; }
        public string CurrencyName { get; set; }
        public int CurrencyDisplayValue { get; set; }
        public string CurrencySymbol { get; set; }
        public int InspectionType { get; set; }
        public string InspectionTypeName { get; set; }
        public int NoOfEMIPaid { get; set; }
        public object IsPolicyLapsed { get; set; }
        public object CancellationDate { get; set; }
        public string WellnessProductStatus { get; set; }
        public object PFFillDate { get; set; }
        public string EducationValue { get; set; }
        public string Occupation { get; set; }
        public string AnnualIncome { get; set; }
        public double AnnualIncomeValue { get; set; }
        public string InsurerLeadID { get; set; }
        public string DocUploadId { get; set; }
        public int PaymentStatus { get; set; }
        public string AppointmentType { get; set; }
        public string CoverType { get; set; }
        public object CoverTypeId { get; set; }
        public string RejectionReason { get; set; }
        public string VerificationStatus { get; set; }
    }

    public class GetPolicyStatusByLeadIDResult
    {
        public PolicyStatusByLeadIDModel Data { get; set; }
        public object Error { get; set; }
        public int ErrorCode { get; set; }
    }

    [DataContract]
    public class PlaceLatLongModel
    {
        [DataMember]
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]

        public string place_id { get; set; }

        [DataMember]
        public decimal Lat { get; set; }
        [DataMember]
        public decimal Long { get; set; }

    }




    public class GoogleDetailModel
    {
        public object[] html_attributions { get; set; }
        public GoogleDetailModelResult result { get; set; }
        public string status { get; set; }
    }

    public class GoogleDetailModelResult
    {
        public Address_Components[] address_components { get; set; }
        public string adr_address { get; set; }
        public string business_status { get; set; }
        public string formatted_address { get; set; }
        public string formatted_phone_number { get; set; }
        public Geometry geometry { get; set; }
        public string icon { get; set; }
        public string icon_background_color { get; set; }
        public string icon_mask_base_uri { get; set; }
        public string international_phone_number { get; set; }
        public string name { get; set; }
        public Opening_Hours opening_hours { get; set; }
        public Photo[] photos { get; set; }
        public string place_id { get; set; }
        public Plus_Code plus_code { get; set; }
        public float rating { get; set; }
        public string reference { get; set; }
        public Review[] reviews { get; set; }
        public string[] types { get; set; }
        public string url { get; set; }
        public int user_ratings_total { get; set; }
        public int utc_offset { get; set; }
        public string vicinity { get; set; }
        public string website { get; set; }
    }

    public class Geometry
    {
        public Location location { get; set; }
        public Viewport viewport { get; set; }
    }

    public class Location
    {
        public float lat { get; set; }
        public float lng { get; set; }
    }

    public class Viewport
    {
        public Northeast northeast { get; set; }
        public Southwest southwest { get; set; }
    }

    public class Northeast
    {
        public float lat { get; set; }
        public float lng { get; set; }
    }

    public class Southwest
    {
        public float lat { get; set; }
        public float lng { get; set; }
    }

    public class Opening_Hours
    {
        public bool open_now { get; set; }
        public Period[] periods { get; set; }
        public string[] weekday_text { get; set; }
    }

    public class Period
    {
        public Close close { get; set; }
        public Open open { get; set; }
    }

    public class Close
    {
        public int day { get; set; }
        public string time { get; set; }
    }

    public class Open
    {
        public int day { get; set; }
        public string time { get; set; }
    }

    public class Plus_Code
    {
        public string compound_code { get; set; }
        public string global_code { get; set; }
    }

    public class Address_Components
    {
        public string long_name { get; set; }
        public string short_name { get; set; }
        public string[] types { get; set; }
    }

    public class Photo
    {
        public int height { get; set; }
        public string[] html_attributions { get; set; }
        public string photo_reference { get; set; }
        public int width { get; set; }
    }

    public class Review
    {
        public string author_name { get; set; }
        public string author_url { get; set; }
        public string language { get; set; }
        public string profile_photo_url { get; set; }
        public int rating { get; set; }
        public string relative_time_description { get; set; }
        public string text { get; set; }
        public int time { get; set; }
    }

    public class AppLocation
    {
        public decimal Lat { get; set; }
        public decimal Long { get; set; }
    }

    [DataContract]
    [BsonIgnoreExtraElements]
    public class AppointmentData
    {
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime ScheduledOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 StatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public AppointmentSourceEnum Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 PCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Lat { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Long { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime LastVisited { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 RescheduleCount { get; set; }

    }

    public enum AppointmentSourceEnum
    {
        Default = 0,
        Customer = 1
    }
    [DataContract]
    public class AppointmentMongoModel
    {

        [DataMember(EmitDefaultValue = false)]
        public List<AppointmentData> AppointmentData { get; set; }

    }


    [DataContract]
    public class CustLeadInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public string EmployeeId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AgentName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustomerName { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }
    }



    [DataContract]
    public class CustomerAuthenticateData
    {

        [DataMember]
        public string encryptLeadId { get; set; }

        [DataMember]
        public long Token { get; set; }
        [DataMember]
        public DateTime ts { get; set; }
        [DataMember]
        public string Url { get; set; }
        [DataMember]
        public string LeadId { get; set; }

    }

    public enum EnumAppSusbtatus
    {
        Booked = 2002,
        Completed = 2003,
        Cancelled = 2004,
        ReScheduled = 2005,
        Confirmed = 2088,
        start = 2124
    }

    public class SurveyInfo
    {
        public Int64 SurveyId { get; set; }
        public bool Status { get; set; }
    }

    public class CheckSurveyAgentResponse
    {
        public Int64 SurveyId { get; set; }
        public bool Eligible { get; set; }
        public bool IsComplete { get; set; }
    }

    public enum EnumAppEvents
    {
        Status_Update = 49,
        Address_Change = 50,
        CustCommunication = 51,
        Call = 52,
        Start_Progressive = 53,
        End_Progressive = 54,
        KillApp = 55,
        Lead_Assigned = 56,
        Communication_Trigger = 57,
        New_Appointment = 58,
        FOS_Appointment_Complete=71,
        CustRecordingConsent = 73


    }

    [DataContract]
    public class QRCodeModel
    {
        [DataMember]
        public string QRCode { get; set; }

        [DataMember]
        public Int16 PendingTime { get; set; }
        [DataMember]
        public string message { get; set; }
        [DataMember]
        public bool isSuccess { get; set; }
    }

    public enum EnumAppLogs
    {

        ApphistoryLogs = 1,
        LeadAssign = 2,
        CommunincationTrigger = 3,
        Call = 4,
    }
    public static class dictionaries
    {
        public static readonly Dictionary<Int32, string> QRErrorCodeMapping = new Dictionary<int, string>
        {
            {-1, "It seems like appointment has been already started from this QR" },
        };
    }

    public enum EnumFOSUserProcessId
    {
        store = 7,
        Fos = 6,
        Self = 8,
        CallingAgent = 0,
        AdminOrTL = 0
    }
    public enum EnumAssignmentType
    {
        StoreFOS = 1,
        DedicatedFOS = 2,
        Self = 3,
        HealthDedicatedFOS = 4,
        PSOP = 5,
        defaultVal = 0

    }
    public enum EnumGender
    {
        Male = 1,
        Female = 2,
        Uknown = 0
    }

    [DataContract]
    public class StoreAndAdvisorInfoModel
    {

        [DataMember]
        public List<StoreDetails> StoreInfo { get; set; }
        [DataMember]
        public List<CityGroupMapping> AdvisorInfo { get; set; }



    }
    [DataContract]
    public class CityGroupMapping
    {

        [DataMember]
        public Int32 CityId { get; set; }

        [DataMember]
        public Int32 GroupId { get; set; }

        [DataMember]
        public decimal Latitude { get; set; }
        [DataMember]
        public decimal Longitude { get; set; }

        [DataMember]
        public long UserID { get; set; }
        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string JoiningMonth { get; set; }

        [DataMember]
        public Int32 JoiningYear { get; set; }
        [DataMember]
        public string language { get; set; }

        [DataMember]
        public Int32 StoreId { get; set; }

        [DataMember]
        public string CityName { get; set; }

        [DataMember]
        public long InboundNumber { get; set; }

        [DataMember]
        public long ProductName { get; set; }
        [DataMember]
        public long bkgsCount { get; set; }
        [DataMember]
        public string Product { get; set; }
        [DataMember]
        public List<string> ProductMapping { get; set; }
        [DataMember]
        public List<string> LanguageMapping { get; set; }


    }
    public class LanguageModel
    {
        [DataMember(EmitDefaultValue = false)]
        public int LanguageId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Language { get; set; }


    }

    public enum EnumProducts
    {
        Health = 2,
        Term = 7,
        Investment = 115
    }

    [DataContract]
    public class FOSCityMaster
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 CityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsFosConfigured { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ProductId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsStoreAvl { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsDedicatedAvl { get; set; }

    }
    public enum EnumAppUserID
    {
        Customer = 4020,

    }

    [DataContract]
    public class CSATModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsFos { get; set; }
    }

    [DataContract]
    public class AIAudioModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long CallDataID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<string> AudioClipsPath { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string audioClip { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime audioCreatedOn { get; set; }

    }
    [DataContract]
    public class UserProfileData
    {
        [DataMember(EmitDefaultValue = false)]
        public string URL { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime? SelfieExpiryDate { get; set; }

    }

    [DataContract]
    public class FeedBackData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 QuesId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Question { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Answer { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int32 AnswerId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Comments { get; set; }
        
    }


    [DataContract]
    public class FOSAppCompeleModel
    {
        [DataMember(EmitDefaultValue = false)]
        public List<FeedBackData> lstFeedBackData { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long AppId { get; set; }
    }
    public class QuestionModel
    {
        public int QuesId { get; set; }
        public string Question { get; set; }
        public List<AnswerModel> Answers { get; set; }
    }
    public class AnswerModel
    {
        public string Answer { get; set; }
        public int AnswerId { get; set; }
    }




    [DataContract]
    public class TeamMaster
    {
        [DataMember(EmitDefaultValue = false)]
        public int TeamID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TeamName { get; set; }

    }
    [DataContract]
    public class ApplicationMaster
    {
        [DataMember(EmitDefaultValue = false)]
        public int TeamID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public int ApplicationID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ApplicationName { get; set; }

    }
    [DataContract]
    public class IssueCategoryMaster
    {
        [DataMember(EmitDefaultValue = false)]
        public int IssueCategoryID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string IssueCategoryName { get; set; }

    }
    [DataContract]
    public class ApplicationMonitorMaster
    {
        [DataMember(EmitDefaultValue = false)]
        public List<TeamMaster> TeamMasterList { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<ApplicationMaster> ApplicationMasterList { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public List<IssueCategoryMaster> IssueCategoryMasterList { get; set; }

    }
    [DataContract]
    public class MemberDetailsModel
    {
        [DataMember(EmitDefaultValue = false)]
        public string Name { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long MobileNo { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Level { get; set; }
    }
    [DataContract]
    [Serializable]
    public class AppMonitorReqModel
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 ID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UniqueID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 TeamID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 ApplicationID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 IssueCategoryID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string AlertMessage { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public bool IsRingAllForSameLevel { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<MemberDetailsModel> MemberDetails { get; set; }

    }
    public class AppMonitorData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int32 ID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UniqueID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 TeamID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string TeamName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ApplicationID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string ApplicationName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MemberName { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MobileNo { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string Level { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string IssueCategoryID { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string IssueCategory { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PlugInURL { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string MessageDesc { get; set; }


    }

    [DataContract]
    public class CustomerLocationModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int CityId { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public long CountryId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string PlaceId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Landmark { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Address { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int Pincode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Source { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ResponseSource { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CreatedBy { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Lat { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public decimal Long { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EncryptedLeadId { get; set; }

        public Int16 Gender { get; set; }
    }
    public class SelfieModel
    {
        public long AppointmentId { get; set; }
        public Int16 AccuracyFlag { get; set; }
        public decimal AccuracyPct { get; set; }
    }

    public class DateAvailableModel
    {
        public Int32 AppointmentDay { get; set; }
        public DateTime AppointmentDate { get; set; }

        public string WeekDays { get; set; }

        public Int32 TotalAppointments { get; set; }

        public Int16 AvailableZone { get; set; }


    }

    [DataContract]
    public class AppRealTimeStatus
    {
        [DataMember(EmitDefaultValue = false)]
        public Int16 AppStatusID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 RealTimeStatusId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string RealTimeStatus { get; set; }

    }
    public class LatLongModel
    {
        [DataMember]
        public decimal Lat { get; set; }
        [DataMember]
        public decimal Long { get; set; }

    }
    public class GeoCodeData
    {
        [DataMember]
        public string formatted_address { get; set; }

        [DataMember]
        public string place_id { get; set; }
        [DataMember]
        public Int32 Pincode { get; set; }
        [DataMember]
        public List<Int32> CityList { get; set; }
        [DataMember]
        public bool IsLeadAssigned { get; set; }
    }

    public class LeadLatLong
    {
        [DataMember]
        public string LeadId { get; set; }

        [DataMember]
        public LatLongModel latlong { get; set; }

        [DataMember]
        public string Address { get; set; }
  


    }

    public enum RealTimeAppointmentstatusEnum
    {
        Travelling = 1,
        Meeting = 2,
        Calling = 3,
        Idle = 4,
        Logout = 5
    }
    public enum EnumAppValidation
    {
        None = 0,
        CITYMISMATCH=1,
        SOMETHINGWRONG=2
    }

    public class FOSCoreAddressModel
    {
        [DataMember]
        public string LeadId { get; set; }

        [DataMember]
        public bool PreviousAddressClicked { get; set; }

        [DataMember]
        public bool IsAddressUsed { get; set; }




    }
    public class CarDataModel
    {
        [DataMember]
        public long LeadId { get; set; }

        [DataMember]
        public string PolicyType { get; set; }

        [DataMember]
        public DateTime PreviousPolicyExpiryDate { get; set; }

        [DataMember]
        public DateTime ExpectedDeliveryDate { get; set; }

    }
    public class AppointmentURLData
    {
        [DataMember]
        public string AppointmentURL { get; set; }

        [DataMember]
        public bool IsAppointmentCreated { get; set; }

    }

    public class LeadIdModel
    {
        [DataMember]
        public long LeadId { get; set; }

        [DataMember]
        public string EncryptedLeadId { get; set; }
    }

    public class AppointmentDataModelAI
    {
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short ProductId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }


        [DataMember(EmitDefaultValue = false)]
        public String Address { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 Pincode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short OfflineCityId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CreatedOn { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public DateTime UpdatedOn { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Boolean IsActive { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 AppointmentType { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Landmark { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 AssignmentId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public Int16 ProcessId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public String NearBy { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CallDate { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long CallDataID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short talktime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime CallCreatedOn { get; set; }
    }

    public class FOSApptCallDataAI
    {
        [DataMember(EmitDefaultValue = false)]
        public long AppointmentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long  CallDataId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? HomeVisitPitch { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? CustomerAgree { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? CustomerRefusal { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MeetingTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string MeetingAddress { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? InvPlanDiscussed { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustomerWantsNxt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string ReasonForAppt { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string City { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public long Pincode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string CustSentiment { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string InterestLevel { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string PurposeOfVisit { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool? ThirdPartyReceiver { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public int ModelGrade { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Remarks { get; set; }
    }

    public class CommPreferenceData {
        public long CustomerId { get; set; }
        public short CommTypeId { get; set; }
        public long LeadId { get; set; }
        public long UserId { get; set; }
        public string Source { get; set; }
    }
    
    public class SendWAOptInData {
        public long LeadId { get; set; }
        public long UserId { get; set; }
        public string Source { get; set; }
    }

    public class RescheduleApptData
    {
        [DataMember(EmitDefaultValue = false)]
        public long LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public short SlotId { get; set; }
    }

    public class CallIdModel
    {
        [DataMember(EmitDefaultValue = false)]
        public long ParentID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int32 ProductID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string EmpCode { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string UserId { get; set; }


    }
}