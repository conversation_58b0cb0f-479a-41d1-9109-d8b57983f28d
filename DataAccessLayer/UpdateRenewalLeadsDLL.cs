﻿using System;
using DataAccessLibrary;
using DataHelper;
using PropertyLayers;
using System.Data;
using System.Data.SqlClient;

namespace DataAccessLayer
{
    public class UpdateRenewalLeadsDLL
    {
        public static bool ProcessPolicyNoForSmeUploads(string policyNo, string renewalLink, string uploadedBy, string trackingId, int toUpdate, string processType, string triggerName)
        {
            var response = false;
            var sqlParam = new SqlParameter[8];
            sqlParam[0] = new SqlParameter("@PolicyNo", policyNo);
            sqlParam[1] = new SqlParameter("@RenewalLink", string.IsNullOrEmpty(renewalLink) ? DBNull.Value : renewalLink);
            sqlParam[2] = new SqlParameter("@ProcessType", processType);
            sqlParam[3] = new SqlParameter("@UploadedBy", uploadedBy);
            sqlParam[4] = new SqlParameter("@TrackingId", trackingId);
            sqlParam[5] = new SqlParameter("@ToUpdate", toUpdate);
            sqlParam[6] = new SqlParameter("@Source", "SmeUpload");
            sqlParam[7] = new SqlParameter("@TriggerName", triggerName);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(),
                                                 CommandType.StoredProcedure,
                                                "[MTX].[ProcessPolicyNoForSmeUploads]", sqlParam);
            if (result != null)
                response = Convert.ToBoolean(result);

            return response;
        }

        public static bool ProcessUpsellSmeUploads(string existingLeadId, string leadSource, string utmSource, string empId, string uploadedBy, string trackingId)
        {
            var sqlParam = new SqlParameter[8];
            sqlParam[0] = new SqlParameter("@ExistingLeadId", existingLeadId);
            sqlParam[1] = new SqlParameter("@LeadSource", leadSource);
            sqlParam[2] = new SqlParameter("@UtmSource", utmSource);
            sqlParam[3] = new SqlParameter("@EmployeeCode", empId);
            sqlParam[4] = new SqlParameter("@ProcessType", ProcessTypes.UpsellLeadCreate.ToString());
            sqlParam[5] = new SqlParameter("@UploadedBy", uploadedBy);
            sqlParam[6] = new SqlParameter("@TrackingId", trackingId);
            sqlParam[7] = new SqlParameter("@Source", "SmeUpload");

            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                             CommandType.StoredProcedure,
                                             "[MTX].[ProcessUpsellSmeUploads]", sqlParam) > 0;
        }

        public static void ProcessSmeBulkBookingUploads(DataRow row, UploadRenewalLeadsDetails requestData)
        {
            var sqlParam = new SqlParameter[23];
            sqlParam[0] = new SqlParameter("@ProcessType", ProcessTypes.SmeBulkBooking.ToString());

            if (row["LeadId"] != null && long.TryParse(row["LeadId"].ToString().Trim(), out long leadId))
            {
                sqlParam[1] = new SqlParameter("@LeadId", leadId);
            }
            if (row["SubProductId"] != null && int.TryParse(row["SubProductId"].ToString().Trim(), out int Id))
            {
                sqlParam[2] = new SqlParameter("@SubProductId", Id);
            }
            if (row["InsurerId"] != null && int.TryParse(row["InsurerId"].ToString().Trim(), out Id))
            {
                sqlParam[3] = new SqlParameter("@InsurerId", Id);
            }
            if (row["PlanId"] != null && int.TryParse(row["PlanId"].ToString().Trim(), out Id))
            {
                sqlParam[4] = new SqlParameter("@PlanId", Id);
            }
            if (row["PolicyType"] != null && !string.IsNullOrEmpty(row["PolicyType"].ToString()))
            {
                sqlParam[5] = new SqlParameter("@PolicyType", row["PolicyType"].ToString().Trim());
            }
            if (row["InsuredName"] != null && !string.IsNullOrEmpty(row["InsuredName"].ToString()))
            {
                sqlParam[6] = new SqlParameter("@InsuredName", row["InsuredName"].ToString().Trim());
            }
            if (row["CompanyName"] != null && !string.IsNullOrEmpty(row["CompanyName"].ToString()))
            {
                sqlParam[7] = new SqlParameter("@CompanyName", row["CompanyName"].ToString().Trim());
            }
            if (row["City"] != null && !string.IsNullOrEmpty(row["City"].ToString()))
            {
                sqlParam[8] = new SqlParameter("@City", row["City"].ToString().Trim());
            }
            if (row["Occupancy"] != null && !string.IsNullOrEmpty(row["Occupancy"].ToString()))
            {
                sqlParam[9] = new SqlParameter("@Occupancy", row["Occupancy"].ToString().Trim());
            }
            if (row["PolicyTerm"] != null && int.TryParse(row["PolicyTerm"].ToString().Trim(), out Id))
            {
                sqlParam[10] = new SqlParameter("@PolicyTerm", Id);
            }
            if (row["PayTerm"] != null && int.TryParse(row["PayTerm"].ToString().Trim(), out Id))
            {
                sqlParam[11] = new SqlParameter("@PayTerm", Id);
            }
            if (row["PolicyNumber"] != null && !string.IsNullOrEmpty(row["PolicyNumber"].ToString()))
            {
                sqlParam[12] = new SqlParameter("@PolicyNumber", row["PolicyNumber"].ToString().Trim());
            }
            if (row["SumInsured"] != null && decimal.TryParse(row["SumInsured"].ToString().Trim(), out decimal val))
            {
                sqlParam[13] = new SqlParameter("@SumInsured", val);
            }
            if (row["Premium"] != null && int.TryParse(row["Premium"].ToString().Trim(), out int sum))
            {
                sqlParam[14] = new SqlParameter("@Premium", sum);
            }
            if (row["PolicyStartDate(MM/DD/YYYY)"] != null && DateTime.TryParse(row["PolicyStartDate(MM/DD/YYYY)"].ToString().Trim(), out DateTime dt))
            {
                sqlParam[15] = new SqlParameter("@PolicyStartDate", dt);
            }
            if (row["PolicyEndDate(MM/DD/YYYY)"] != null && DateTime.TryParse(row["PolicyEndDate(MM/DD/YYYY)"].ToString().Trim(), out dt))
            {
                sqlParam[16] = new SqlParameter("@PolicyEndDate", dt);
            }
            if (row["PaymentMode"] != null && !string.IsNullOrEmpty(row["PaymentMode"].ToString()))
            {
                sqlParam[17] = new SqlParameter("@PaymentMode", row["PaymentMode"].ToString().Trim());
            }
            if (row["PaymentFrequency"] != null && !string.IsNullOrEmpty(row["PaymentFrequency"].ToString()))
            {
                sqlParam[18] = new SqlParameter("@PaymentFrequency", row["PaymentFrequency"].ToString().Trim());
            }
            if (row["BankName"] != null && !string.IsNullOrEmpty(row["BankName"].ToString()))
            {
                sqlParam[19] = new SqlParameter("@BankName", row["BankName"].ToString().Trim());
            }
            if (row["SalesAgent"] != null && !string.IsNullOrEmpty(row["SalesAgent"].ToString()))
            {
                sqlParam[20] = new SqlParameter("@SalesAgent", row["SalesAgent"].ToString().Trim());
            }
            sqlParam[21] = new SqlParameter("@UploadedBy", requestData.UploadedBy);
            sqlParam[22] = new SqlParameter("@TrackingId", requestData.UniqueId);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                      CommandType.StoredProcedure,
                                      "[MTX].[ProcessSmeBulkBookingUploads]", sqlParam);
        }

        public static int ProcessLastYearLostCases(DataRow row, UploadRenewalLeadsDetails requestData)
        {
            var sqlParam = new SqlParameter[17];
            sqlParam[0] = new SqlParameter("@ProcessType", ProcessTypes.LastYearLostCases.ToString());
            sqlParam[1] = new SqlParameter("@UploadedBy", requestData.UploadedBy);
            sqlParam[2] = new SqlParameter("@TrackingId", requestData.UniqueId);
            sqlParam[3] = new SqlParameter("@ToUpdate", 3);
            if (row["LastYearLeadId"] != null && long.TryParse(row["LastYearLeadId"].ToString().Trim(), out long leadId))
            {
                sqlParam[4] = new SqlParameter("@LastYearLeadId", leadId);
            }
            if (row["CompanyName"] != null && !string.IsNullOrEmpty(row["CompanyName"].ToString()))
            {
                sqlParam[5] = new SqlParameter("@CompanyName", row["CompanyName"].ToString());
            }
            if (row["Name"] != null && !string.IsNullOrEmpty(row["Name"].ToString()))
            {
                sqlParam[6] = new SqlParameter("@Name", row["Name"].ToString());
            }
            if (row["NumberOfEmployees"] != null && long.TryParse(row["NumberOfEmployees"].ToString().Trim(), out long noOfEmp))
            {
                sqlParam[7] = new SqlParameter("@NumberOfEmployees", noOfEmp);
            }
            if (row["Email"] != null && !string.IsNullOrEmpty(row["Email"].ToString()))
            {
                sqlParam[8] = new SqlParameter("@Email", row["Email"].ToString());
            }
            if (row["SubProduct"] != null && !string.IsNullOrEmpty(row["SubProduct"].ToString()))
            {
                sqlParam[9] = new SqlParameter("@SubProduct", row["SubProduct"].ToString());
            }
            if (row["CityName"] != null && !string.IsNullOrEmpty(row["CityName"].ToString()))
            {
                sqlParam[10] = new SqlParameter("@CityName", row["CityName"].ToString());
            }
            if (row["LeadSource"] != null && !string.IsNullOrEmpty(row["LeadSource"].ToString()))
            {
                sqlParam[11] = new SqlParameter("@LeadSource", row["LeadSource"].ToString());
            }
            if (row["UTM_Medium"] != null && !string.IsNullOrEmpty(row["UTM_Medium"].ToString()))
            {
                sqlParam[12] = new SqlParameter("@UTM_Medium", row["UTM_Medium"].ToString());
            }
            if (row["PolicyExpiryDate"] != null && DateTime.TryParse(row["PolicyExpiryDate"].ToString().Trim(), out DateTime dt))
            {
                sqlParam[13] = new SqlParameter("@PolicyExpiryDate", dt);
            }
            if (row["Utm_Campaign"] != null && !string.IsNullOrEmpty(row["Utm_Campaign"].ToString()))
            {
                sqlParam[14] = new SqlParameter("@Utm_Campaign", row["Utm_Campaign"].ToString());
            }
            if (row["Utm_Source"] != null && !string.IsNullOrEmpty(row["Utm_Source"].ToString()))
            {
                sqlParam[15] = new SqlParameter("@Utm_Source", row["Utm_Source"].ToString());
            }
            if (row["AgentId"] != null && !string.IsNullOrEmpty(row["AgentId"].ToString()))
            {
                sqlParam[16] = new SqlParameter("@AgentId", row["AgentId"].ToString());
            }

            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                             CommandType.StoredProcedure,
                                             "[MTX].[ProcessLastYearLostCases]", sqlParam);
        }

        public static int ProcessSmeBulkLeadUpload(DataRow row, UploadRenewalLeadsDetails requestData)
        {
            var sqlParam = new SqlParameter[13];
            sqlParam[0] = new SqlParameter("@ProcessType", ProcessTypes.SmeBulkLeadUpload.ToString());
            sqlParam[1] = new SqlParameter("@UploadedBy", requestData.UploadedBy);
            sqlParam[2] = new SqlParameter("@TrackingId", requestData.UniqueId);
            sqlParam[3] = new SqlParameter("@ToUpdate", 1);
            if (row["ContactPersonName"] != null && !string.IsNullOrEmpty(row["ContactPersonName"].ToString()))
            {
                sqlParam[4] = new SqlParameter("@Name", row["ContactPersonName"].ToString());
            }
            if (row["Email"] != null && !string.IsNullOrEmpty(row["Email"].ToString()))
            {
                sqlParam[5] = new SqlParameter("@Email", row["Email"].ToString());
            }
            if (row["MobileNo"] != null && !string.IsNullOrEmpty(row["MobileNo"].ToString()))
            {
                sqlParam[6] = new SqlParameter("@MobileNo", row["MobileNo"].ToString());
            }
            if (row["City"] != null && !string.IsNullOrEmpty(row["City"].ToString()))
            {
                sqlParam[7] = new SqlParameter("@CityName", row["City"].ToString());
            }
            if (row["CompanyName"] != null && !string.IsNullOrEmpty(row["CompanyName"].ToString()))
            {
                sqlParam[8] = new SqlParameter("@CompanyName", row["CompanyName"].ToString());
            }
            if (row["SubProductId"] != null && !string.IsNullOrEmpty(row["SubProductId"].ToString()))
            {
                sqlParam[9] = new SqlParameter("@SubProductId", row["SubProductId"].ToString());
            }
            if (row["LeadSource"] != null && !string.IsNullOrEmpty(row["LeadSource"].ToString()))
            {
                sqlParam[10] = new SqlParameter("@LeadSource", row["LeadSource"].ToString());
            }
            if (row["Utm_source"] != null && !string.IsNullOrEmpty(row["Utm_source"].ToString()))
            {
                sqlParam[11] = new SqlParameter("@Utm_source", row["Utm_source"].ToString());
            }
            if (row["EmployeeCode"] != null && !string.IsNullOrEmpty(row["EmployeeCode"].ToString()))
            {
                sqlParam[12] = new SqlParameter("@EmployeeCode", row["EmployeeCode"].ToString());
            }
            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                             CommandType.StoredProcedure,
                                             "[MTX].[ProcessSmeBulkLeadUpload]", sqlParam);
        }

        public static int ProcessRenewalMissingLeads(DataRow row, UploadRenewalLeadsDetails requestData)
        {
            var sqlParam = new SqlParameter[7];
            sqlParam[0] = new SqlParameter("@ProcessType", ProcessTypes.RenewalMissingLeads.ToString());
            sqlParam[1] = new SqlParameter("@UploadedBy", requestData.UploadedBy);
            sqlParam[2] = new SqlParameter("@TrackingId", requestData.UniqueId);
            sqlParam[3] = new SqlParameter("@ToUpdate", 2);
            if (row["BookingId"] != null && long.TryParse(row["BookingId"].ToString(), out long leadId))
            {
                sqlParam[4] = new SqlParameter("@BookingId", leadId);
            }
            if (row["MobileNo"] != null && !string.IsNullOrEmpty(row["MobileNo"].ToString()))
            {
                sqlParam[5] = new SqlParameter("@MobileNo", row["MobileNo"].ToString());
            }
            if (row["MobileNo"] != null && long.TryParse(row["MobileNo"].ToString(), out long mobileNo))
            {
                sqlParam[6] = new SqlParameter("@IsValidMobileNo", mobileNo > 0);
            }

            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                             CommandType.StoredProcedure,
                                             "[MTX].[ProcessSmeRenewalMissingLeads]", sqlParam);
        }

        public static int ProcessBulkCrossSmeLeads(DataRow row, UploadRenewalLeadsDetails requestData)
        {
            var sqlParam = new SqlParameter[12];
            sqlParam[0] = new SqlParameter("@ProcessType", ProcessTypes.CrossSmeUpload.ToString());
            sqlParam[1] = new SqlParameter("@UploadedBy", requestData.UploadedBy);
            sqlParam[2] = new SqlParameter("@TrackingId", requestData.UniqueId);
            sqlParam[3] = new SqlParameter("@ToUpdate", 1);
            if (row["ContactPersonName"] != null && !string.IsNullOrEmpty(row["ContactPersonName"].ToString()))
            {
                sqlParam[4] = new SqlParameter("@Name", row["ContactPersonName"].ToString());
            }
            if (row["MobileNo"] != null && !string.IsNullOrEmpty(row["MobileNo"].ToString()))
            {
                sqlParam[5] = new SqlParameter("@MobileNo", row["MobileNo"].ToString());
            }
            if (row["Email"] != null && !string.IsNullOrEmpty(row["Email"].ToString()))
            {
                sqlParam[6] = new SqlParameter("@Email", row["Email"].ToString());
            }
            if (row["LeadSource"] != null && !string.IsNullOrEmpty(row["LeadSource"].ToString()))
            {
                sqlParam[7] = new SqlParameter("@LeadSource", row["LeadSource"].ToString());
            }
            if (row["Utm_source"] != null && !string.IsNullOrEmpty(row["Utm_source"].ToString()))
            {
                sqlParam[8] = new SqlParameter("@Utm_source", row["Utm_source"].ToString());
            }
            if (row["Utm_Medium"] != null && !string.IsNullOrEmpty(row["Utm_Medium"].ToString()))
            {
                sqlParam[9] = new SqlParameter("@Utm_Medium", row["Utm_Medium"].ToString());
            }
            if (row["EmployeeCode"] != null && !string.IsNullOrEmpty(row["EmployeeCode"].ToString()))
            {
                sqlParam[10] = new SqlParameter("@EmployeeCode", row["EmployeeCode"].ToString());
            }
            if (row["ProductId"] != null && !string.IsNullOrEmpty(row["ProductId"].ToString()))
            {
                sqlParam[11] = new SqlParameter("@ProductId",Convert.ToInt32(row["ProductId"]));
            }
            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                             CommandType.StoredProcedure,
                                             "[MTX].[ProcessCrossSmeBulkLeadUpload]", sqlParam);
        }

        public static string FetchRenewalBulkUploadFile(string UniqueId, string ProductId)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@UniqueId", Convert.ToString(UniqueId));
            SqlParam[1] = new SqlParameter("@ProductId", Convert.ToString(ProductId));
            var res = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure,
                                            "[MTX].[FetchRenewalBulkUploadFile]", SqlParam);

            if (res != null)
                return Convert.ToString(res);
            else
                return string.Empty;
        }

        public static int ProcessSmeFosBulkUpload(DataRow row, UploadRenewalLeadsDetails requestData)
        {
            var sqlParam = new SqlParameter[31];
            if (row["CIN"] != null && !string.IsNullOrEmpty(row["CIN"].ToString()))
            {
                sqlParam[0] = new SqlParameter("@CIN", row["CIN"].ToString());
            }

            if (row["CompanyName"] != null && !string.IsNullOrEmpty(row["CompanyName"].ToString()))
            {
                sqlParam[1] = new SqlParameter("@CompanyName", row["CompanyName"].ToString());
            }

            if (row["ClientCityName"] != null && !string.IsNullOrEmpty(row["ClientCityName"].ToString()))
            {
                sqlParam[2] = new SqlParameter("@ClientCityName", row["ClientCityName"].ToString());
            }

            if (row["ParentCompany"] != null && !string.IsNullOrEmpty(row["ParentCompany"].ToString()))
            {
                sqlParam[3] = new SqlParameter("@ParentCompany", row["ParentCompany"].ToString());
            }

            if (row["Name"] != null && !string.IsNullOrEmpty(row["Name"].ToString()))
            {
                sqlParam[4] = new SqlParameter("@Name", row["Name"].ToString());
            }

            if (row["MobileNo"] != null && !string.IsNullOrEmpty(row["MobileNo"].ToString()))
            {
                sqlParam[5] = new SqlParameter("@MobileNo", row["MobileNo"].ToString());
            }

            if (row["EmailId"] != null && !string.IsNullOrEmpty(row["EmailId"].ToString()))
            {
                sqlParam[6] = new SqlParameter("@EmailId", row["EmailId"].ToString());
            }

            if (row["ExecutiveRole"] != null && !string.IsNullOrEmpty(row["ExecutiveRole"].ToString()))
            {
                sqlParam[7] = new SqlParameter("@ExecutiveRole", row["ExecutiveRole"].ToString());
            }

            if (row["IndustryType"] != null && !string.IsNullOrEmpty(row["IndustryType"].ToString()))
            {
                sqlParam[8] = new SqlParameter("@IndustryType", row["IndustryType"].ToString());
            }

            if (row["Utm_source"] != null && !string.IsNullOrEmpty(row["Utm_source"].ToString()))
            {
                sqlParam[9] = new SqlParameter("@Utm_source", row["Utm_source"].ToString());
            }

            if (row["SubProductId"] != null && !string.IsNullOrEmpty(row["SubProductId"].ToString()))
            {
                sqlParam[10] = new SqlParameter("@SubProductId", row["SubProductId"]);
            }

            if (row["PolicyType"] != null && !string.IsNullOrEmpty(row["PolicyType"].ToString()))
            {
                sqlParam[11] = new SqlParameter("@PolicyType", row["PolicyType"]);
            }

            if (row["DecisionMakerCityName"] != null && !string.IsNullOrEmpty(row["DecisionMakerCityName"].ToString()))
            {
                sqlParam[12] = new SqlParameter("@DecisionMakerCityName", row["DecisionMakerCityName"].ToString());
            }

            if (row["LinkedinLink"] != null && !string.IsNullOrEmpty(row["LinkedinLink"].ToString()))
            {
                sqlParam[13] = new SqlParameter("@LinkedinLink", row["LinkedinLink"].ToString());
            }

            if (row["AltMobileNo"] != null && !string.IsNullOrEmpty(row["AltMobileNo"].ToString()))
            {
                sqlParam[14] = new SqlParameter("@AltMobileNo", row["AltMobileNo"].ToString());
            }

            if (row["AltEmailId"] != null && !string.IsNullOrEmpty(row["AltEmailId"].ToString()))
            {
                sqlParam[15] = new SqlParameter("@AltEmailId", row["AltEmailId"].ToString());
            }

            if (row["Probability"] != null && !string.IsNullOrEmpty(row["Probability"].ToString()))
            {
                sqlParam[16] = new SqlParameter("@Probability", row["Probability"]);
            }

            if (row["Utm_medium"] != null && !string.IsNullOrEmpty(row["Utm_medium"].ToString()))
            {
                sqlParam[17] = new SqlParameter("@Utm_medium", row["Utm_medium"].ToString());
            }

            if (row["CrossSellSubProductIds"] != null && !string.IsNullOrEmpty(row["CrossSellSubProductIds"].ToString()))
            {
                sqlParam[18] = new SqlParameter("@CrossSellSubProductIds", row["CrossSellSubProductIds"].ToString());
            }

            if (row["Discussion"] != null && !string.IsNullOrEmpty(row["Discussion"].ToString()))
            {
                sqlParam[19] = new SqlParameter("@Discussion", row["Discussion"].ToString());
            }

            if (row["ClaimHistory"] != null && !string.IsNullOrEmpty(row["ClaimHistory"].ToString()))
            {
                sqlParam[20] = new SqlParameter("@ClaimHistory", row["ClaimHistory"].ToString());
            }

            if (row["ExistingBrokerName"] != null && !string.IsNullOrEmpty(row["ExistingBrokerName"].ToString()))
            {
                sqlParam[21] = new SqlParameter("@ExistingBrokerName", row["ExistingBrokerName"].ToString());
            }

            if (row["ExistingInsurerName"] != null && !string.IsNullOrEmpty(row["ExistingInsurerName"].ToString()))
            {
                sqlParam[22] = new SqlParameter("@ExistingInsurerName", row["ExistingInsurerName"].ToString());
            }

            if (row["ExistingTPAName"] != null && !string.IsNullOrEmpty(row["ExistingTPAName"].ToString()))
            {
                sqlParam[23] = new SqlParameter("@ExistingTPAName", row["ExistingTPAName"].ToString());
            }

            if (row["SumInsured"] != null && !string.IsNullOrEmpty(row["SumInsured"].ToString()))
            {
                sqlParam[24] = new SqlParameter("@SumInsured", row["SumInsured"]);
            }

            if (row["PremiumAtInception"] != null && !string.IsNullOrEmpty(row["PremiumAtInception"].ToString()))
            {
                sqlParam[25] = new SqlParameter("@PremiumAtInception", row["PremiumAtInception"]);
            }
            if (row["PolicyStartDate (MM/DD/YYYY)"] != null && DateTime.TryParse(row["PolicyStartDate (MM/DD/YYYY)"].ToString().Trim(), out DateTime dt))
            {
                sqlParam[26] = new SqlParameter("@PolicyStartDate", dt);
            }
            sqlParam[27] = new SqlParameter("@ProcessType", ProcessTypes.SmeFosBulkUpload.ToString());
            sqlParam[28] = new SqlParameter("@UploadedBy", requestData.UploadedBy);
            sqlParam[29] = new SqlParameter("@TrackingId", requestData.UniqueId);
            if (row["EmployeeCode"] != null && !string.IsNullOrEmpty(row["EmployeeCode"].ToString()))
            {
                sqlParam[30] = new SqlParameter("@EmployeeCode", row["EmployeeCode"].ToString());
            }
            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                             CommandType.StoredProcedure,
                                             "[MTX].[ProcessSmeFosBulkUpload]", sqlParam);
        }

    }
}
