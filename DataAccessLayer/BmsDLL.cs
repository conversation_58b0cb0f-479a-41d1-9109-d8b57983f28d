﻿using DataHelper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using PropertyLayers;
using Helper;

namespace DataAccessLibrary
{
    public class BmsDLL
    {

        public static bool UpdateLeadAttribute(LeadFlags oLeadFlags)
        {
            try
            {
                string connection = ConnectionClass.LivesqlConnection();
                SqlParameter[] sqlparm = new SqlParameter[3];
                sqlparm[0] = new SqlParameter("@LeadId", oLeadFlags.LeadId);
                sqlparm[1] = new SqlParameter("@AttributeID", oLeadFlags.AttributeID);
                sqlparm[2] = new SqlParameter("@Value", oLeadFlags.Value);
                //sqlparm[3] = new SqlParameter("@UserId", oLeadFlags.UserId);
                var result = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, "[FOS].[UpdateLeadAttribute]", sqlparm);
                return true;
            }
            catch (Exception ex)
            {
                return false;

            }



        }

        public static DataSet GetNewSVURL(long LeadId, string Source)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@LeadId", Convert.ToInt64(LeadId));
                sqlParam[1] = new SqlParameter("@Source", Source);
                
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetActiveRenewalLead]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet UpdateNoCostEMI(long LeadId, long UserId, bool NoCostEMI)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@LeadId", Convert.ToInt64(LeadId));
                sqlParam[1] = new SqlParameter("@UserId", Convert.ToInt64(UserId));
                sqlParam[2] = new SqlParameter("@NoCostEMI", Convert.ToBoolean(NoCostEMI));
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateNoCostEMI]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetAgentSupervisor(int UserId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", Convert.ToInt64(UserId));
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentAndSuperiorDetails]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetNoCostEMI(long ParentId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@ParentId", ParentId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetNoCostEMI]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public static DataSet GetOldBookingIds(string LeadIds)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadIds", LeadIds);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetOldBookingIds]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadIds, 0, ex.ToString(), "GetOldBookingIds", "MatrixCore", null, LeadIds.ToString(), ex.ToString(), DateTime.Now, DateTime.Now);
                return null;
            }
        }

        #region CreateBooking

        public static short ValidateBookingDetails(long leadId, int productId, int? investmentTypeId, int? occId, long userId)
        {
            var sqlparam = new Dictionary<object, object>
            {
                {"LeadId", leadId},
                {"ProductId", productId},
                {"InvestmentTypeId", investmentTypeId != null ? investmentTypeId : 0},
                {"OccId", occId != null ? occId : 0},
                {"AgentId", userId }
            };
            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[MTX].[ValidateBookingDetails]",
                                                 SetParamterFromDictionary(sqlparam));
            if (result != null)
                return Convert.ToInt16(result);
            else
                return 0;
        }

        public static void UpdatePayment(BookingDetailsModel bookingDetails)
        {
            var sqlparam = new Dictionary<object, object>
            {
                {"Result", 0},
                {"MatrixLeadID", bookingDetails.MatrixLeadId > 0 ? bookingDetails.MatrixLeadId : bookingDetails.LeadId},
                {"PaymentStatus", bookingDetails.PaymentStatus},
                {"PaymentSubStatus", bookingDetails.PaymentSubStatus},
                {"ProductId", bookingDetails.Supplier.ProductId},
                {"UserID", bookingDetails.UserId},
                {"Comments", bookingDetails.Comments},
                {"LiveFlag", bookingDetails.LiveFlag},
                {"SubStatusID", bookingDetails.SubStatusID}
            };
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                      CommandType.StoredProcedure,
                                      "[MTX].[ConfirmLeadPayment_V2]",
                                      SetParamterFromDictionary(sqlparam));
        }

        public static int InsertUpdateBookingAndPaymentDetails(BookingDetailsModel bookingDetails, string source, out string message)
        {
            var sqlparam = new Dictionary<object, object> {
                {"Result", 0},
                {"ErrorMessage", string.Empty},
                {"LeadId", bookingDetails.MatrixLeadId},
                { "ProductId", bookingDetails.Supplier !=null && bookingDetails.Supplier.ProductId > 0  ? bookingDetails.Supplier.ProductId : 0},
                { "SumInsured", bookingDetails.SumInsured},
                { "Premium", bookingDetails.TotalPremium},
                { "BookingType", bookingDetails.BookingType},
                { "ApplicationNumber", bookingDetails.ApplicationNo},
                {"PolicyNo",bookingDetails.PolicyNo},
                { "DocumentsRequired", bookingDetails.DocumentsRequired},
                { "MedicalsRequired", bookingDetails.Medical_or_InspectionRequired},
                { "PolicyTypeId", bookingDetails.PolicyTypeId},
                { "PolicyTypeName", bookingDetails.PolicyTypeName},
                { "SupplierId", bookingDetails.Supplier != null && bookingDetails.Supplier.OldSupplierId > 0  ? bookingDetails.Supplier.OldSupplierId : 0},
                { "SupplierName", bookingDetails.Supplier != null && !string.IsNullOrEmpty(bookingDetails.Supplier.SupplierDisplayName) ? bookingDetails.Supplier.SupplierDisplayName : string.Empty},
                { "PlanId", bookingDetails.Plan!= null  && bookingDetails.Plan.OldPlanId > 0 ? bookingDetails.Plan.OldPlanId : 0},
                { "PlanName", bookingDetails.Plan != null && !string.IsNullOrEmpty(bookingDetails.Plan.PlanDisplayName) ? bookingDetails.Plan.PlanDisplayName : string.Empty},
                { "PaymentStatus", bookingDetails.PaymentStatus},
                { "PaymentSubStatus", bookingDetails.PaymentSubStatus},
                { "PaymentPeriodicity", bookingDetails.PaymentPeriodicity},
                { "ChequeDDNumber", bookingDetails.ChequeNo},
                { "InstallmentsPaid", bookingDetails.InstallmentPaid},
                { "Portability", bookingDetails.Portability},
                { "PaymentTransactionNumber", bookingDetails.TransRefNo},
                { "ProductTypeId", bookingDetails.InvestmentTypeID},
                { "PolicyTerm", bookingDetails.PolicyTerm},
                { "PayTerm", bookingDetails.PayTerm},
                { "InsuredName", bookingDetails.InsuredName},
                { "RiderSI", bookingDetails.RiderSI},
                { "Rider", bookingDetails.Rider},
                { "PreviousBookingNo", bookingDetails.PreviousBookingNo},
                { "PaymentSource", bookingDetails.PaymentSource},
                { "IsEMI", bookingDetails.IsEMI},
                { "Bank", bookingDetails.BankNameBranch},
                { "LiveFlag", bookingDetails.LiveFlag},
                {"updatepaymentdetails",bookingDetails.UpdatePaymentdetails},
                {"InspectionStatus",bookingDetails.InspectionStatus},
                {"ReferenceNo",bookingDetails.ReferenceNo},
                {"ODPremium",bookingDetails.ODPremium},
                {"Brokerage", bookingDetails.Brokerage},
                {"CompanyName", bookingDetails.CompanyName},
                {"NoOfLives", bookingDetails.NoOfLives},
                {"OccupancyId", bookingDetails.OccupancyId},
                {"NoOfEmployees", bookingDetails.NoOfEmployees},
                {"IsDocReceived", bookingDetails.IsDocReceived},
                {"LoadingAmount", bookingDetails.LoadingAmount},
                {"IsRSA", bookingDetails.IsRSA},
                {"PrevPolicyNo", bookingDetails.PrevPolicyNo},
                {"PeriodicityAmount", bookingDetails.PaidPremium},
                {"TransitType", bookingDetails.TransitType},
                {"TermTenure", bookingDetails.TermTenure},
                {"TermSI", bookingDetails.TermSI},
                {"BranchName", bookingDetails.BranchName},
                {"RegistrationNo", bookingDetails.RegistrationNo},
                {"CoverTypeId", bookingDetails.CoverageTypeId},
                {"PropertyTypeId", bookingDetails.PropertyTypeId},
                {"PurposeId", bookingDetails.PurposeId},
                {"InsurerFee", bookingDetails.InsurerFee},
                {"PBDiscount", bookingDetails.PBDiscount},
                {"OtherOccupany", bookingDetails.OtherOccupany},
                {"BuildingSI", bookingDetails.BuildingSI},
                {"ContentSI", bookingDetails.ContentSI},
                {"UserId", bookingDetails.UserId},
                {"IsTP", bookingDetails.IsTP },
                {"TransitFromCityId", bookingDetails.TransitFromCityId != null ? (short)bookingDetails.TransitFromCityId : DBNull.Value},
                {"TransitToCityId", bookingDetails.TransitToCityId != null ? (short)bookingDetails.TransitToCityId : DBNull.Value},
                {"OccupationType", string.IsNullOrEmpty(bookingDetails.OccupationType) ? DBNull.Value : bookingDetails.OccupationType},
                {"ManufacturerTraderName", string.IsNullOrEmpty(bookingDetails.ManufacturerTraderName) ? DBNull.Value : bookingDetails.ManufacturerTraderName},
                {"ManufacturerTraderContactNo", string.IsNullOrEmpty(bookingDetails.ManufacturerTraderContactNo) ? DBNull.Value : bookingDetails.ManufacturerTraderContactNo},
                {"ConstitutionOfBusiness", string.IsNullOrEmpty(bookingDetails.ConstitutionOfBusiness) ? DBNull.Value : bookingDetails.ConstitutionOfBusiness}
            };

            if (bookingDetails.TPPremium != null)
                sqlparam.Add("TPPremium", bookingDetails.TPPremium);

            if (!string.IsNullOrEmpty(bookingDetails.EmailID))
                sqlparam.Add("EmailID", bookingDetails.EmailID);

            if (bookingDetails.ServiceTax != null)
                sqlparam.Add("ServiceTax", bookingDetails.ServiceTax);

            if (bookingDetails.AddonPremium != null)
                sqlparam.Add("AddonPremium", bookingDetails.AddonPremium);

            if (bookingDetails.CityID > 0)
            {
                sqlparam.Add("cityId", bookingDetails.CityID);
                sqlparam.Add("stateId", bookingDetails.StateID);
            }

            if (bookingDetails.PaymentDate!=null && bookingDetails.PaymentDate > 0 && DateTime.TryParse(Convert.ToInt64(bookingDetails.PaymentDate).ToDateTime().ToString(), out DateTime dt))
                sqlparam.Add("PaymentDate", dt);

            if (bookingDetails.PolicyStartDate != null &&  bookingDetails.PolicyStartDate > 0 && DateTime.TryParse(Convert.ToInt64(bookingDetails.PolicyStartDate).ToDateTime().ToString(), out dt))
                sqlparam.Add("PolicyStartDate", dt);

            if (bookingDetails.PolicyEndDate != null &&  bookingDetails.PolicyEndDate > 0 && DateTime.TryParse(Convert.ToInt64(bookingDetails.PolicyEndDate).ToDateTime().ToString(), out dt))
                sqlparam.Add("PolicyEndDate", dt);

            if (bookingDetails.DateOfInspection != null &&  bookingDetails.DateOfInspection > 0 && DateTime.TryParse(Convert.ToInt64(bookingDetails.DateOfInspection).ToDateTime().ToString(), out dt))
                sqlparam.Add("DateOfInspection", dt);

            if (bookingDetails.IssuanceDate > 0 && DateTime.TryParse(bookingDetails.IssuanceDate.ToDateTime().ToString(), out dt))
                sqlparam.Add("IssuanceDate", dt);

            if (bookingDetails.PremiumExclusion != null)
                sqlparam.Add("PremiumExclusion", bookingDetails.PremiumExclusion);

            if (bookingDetails.RevenueAddition != null)
                sqlparam.Add("RevenueAddition", bookingDetails.RevenueAddition);

            if (bookingDetails.ProposalNo != null)
                sqlparam.Add("ProposalNo", bookingDetails.ProposalNo);

            if (bookingDetails.ExpiringInsurer != 0)
                sqlparam.Add("ExpiringInsurerId", bookingDetails.ExpiringInsurer);

            if (bookingDetails.SalesPartners != null && bookingDetails.SalesPartners.Count > 0)
            {
                var salesPartners = CoreCommonMethods.SerializeToXml(bookingDetails.SalesPartners);
                if (!string.IsNullOrEmpty(salesPartners))
                    sqlparam.Add("SalesPartners", salesPartners);
            }

            if (bookingDetails.SalesSpecialists != null && bookingDetails.SalesSpecialists.Count > 0)
            {
                var salesSpecialists = CoreCommonMethods.SerializeToXml(bookingDetails.SalesSpecialists);
                if (!string.IsNullOrEmpty(salesSpecialists))
                    sqlparam.Add("SalesSpecialists", salesSpecialists);
            }

            if (!string.IsNullOrEmpty(bookingDetails.PrimaryAgentSharePercentage))
                sqlparam.Add("PrimaryAgentSharePercentage", bookingDetails.PrimaryAgentSharePercentage);

            if (bookingDetails.FamilyType != null && !string.IsNullOrEmpty(bookingDetails.FamilyType.Name))
                sqlparam.Add("FamilyType", bookingDetails.FamilyType.Name);

            if (bookingDetails.SumInsuredType != null && !string.IsNullOrEmpty(bookingDetails.SumInsuredType.Name))
                sqlparam.Add("SumInsuredType", bookingDetails.SumInsuredType.Name);

            if (bookingDetails.Grades != null && bookingDetails.Grades.Count > 0)
            {
                var grades = CoreCommonMethods.SerializeToXml(bookingDetails.Grades);
                if (!string.IsNullOrEmpty(grades))
                    sqlparam.Add("Grades", grades);
            }

            if (!string.IsNullOrEmpty(bookingDetails.ShipmentType))
                sqlparam.Add("ShipmentType", bookingDetails.ShipmentType);

            if (!string.IsNullOrEmpty(bookingDetails.PAN))
                sqlparam.Add("PAN", bookingDetails.PAN.ToUpper());

            if (!string.IsNullOrEmpty(bookingDetails.GST))
                sqlparam.Add("GST", bookingDetails.GST.ToUpper());

            if (!string.IsNullOrEmpty(bookingDetails.CIN))
                sqlparam.Add("CIN", bookingDetails.CIN.ToUpper());

            if (!string.IsNullOrEmpty(bookingDetails.Name))
                sqlparam.Add("Name", bookingDetails.Name);

            if (bookingDetails.MarineCoverType != null && bookingDetails.MarineCoverType > 0)
                sqlparam.Add("MarineCoverType", bookingDetails.MarineCoverType);

            if (bookingDetails.InstallmentsData != null && bookingDetails.InstallmentsData.Count > 0)
            {
                var installments = CoreCommonMethods.SerializeToXml(bookingDetails.InstallmentsData);
                if (!string.IsNullOrEmpty(installments))
                    sqlparam.Add("InstallmentsData", installments);
            }            
            if (bookingDetails.LeadDocument != null && !string.IsNullOrEmpty(bookingDetails.LeadDocument.DocId))
            {
                sqlparam.Add("DocumentId", bookingDetails.LeadDocument.DocId);
                sqlparam.Add("DocTypeId", bookingDetails.LeadDocument.DocTypeId);
                sqlparam.Add("DocName", bookingDetails.LeadDocument.FileName);
            }
            if (bookingDetails.ShopTypeId != null && bookingDetails.ShopTypeId > 0)
            {
                sqlparam.Add("ShopTypeId", bookingDetails.ShopTypeId);
            }
            if (bookingDetails.CoInsurance)
            {
                sqlparam.Add("CoInsurance", bookingDetails.CoInsurance);
                sqlparam.Add("LeadersPercentage", bookingDetails.LeadersPercentage);

                if (bookingDetails.FollowerSuppliers != null && bookingDetails.FollowerSuppliers.Count > 0)
                {
                    var followers = CoreCommonMethods.SerializeToXml(bookingDetails.FollowerSuppliers);
                    if (!string.IsNullOrEmpty(followers))
                        sqlparam.Add("FollowerSuppliers", followers);
                }
            }
            if (bookingDetails.IsSTP != null)
            {
                sqlparam.Add("IsSTP", bookingDetails.IsSTP);
            }
            if (bookingDetails.Inclusion != null && bookingDetails.Inclusion.Count > 0)
            {
                string inclusions = string.Join(',', bookingDetails.Inclusion);
                sqlparam.Add("Inclusions", inclusions);
            }
            if (bookingDetails.MedicalExtension != null && bookingDetails.MedicalExtension > 0)
            {
                sqlparam.Add("MedicalExtension", (int)bookingDetails.MedicalExtension);
            }
            if (bookingDetails.WorkerTypes != null && bookingDetails.WorkerTypes.Count > 0)
            {
                var workerTypes = CoreCommonMethods.SerializeToXml(bookingDetails.WorkerTypes);
                if (!string.IsNullOrEmpty(workerTypes))
                    sqlparam.Add("WorkerTypes", workerTypes);
            }
            if (bookingDetails.RiskLocations != null && bookingDetails.RiskLocations.Count > 0)
            {
                var riskLocations = CoreCommonMethods.SerializeToXml(bookingDetails.RiskLocations);
                if (!string.IsNullOrEmpty(riskLocations))
                    sqlparam.Add("RiskLocations", riskLocations);
            }
            if (bookingDetails.ChildOccupancyId != null && bookingDetails.ChildOccupancyId > 0)
            {
                sqlparam.Add("ChildOccupancyId", (int)bookingDetails.ChildOccupancyId);
            }
            if (!string.IsNullOrEmpty(bookingDetails.ChildOccupancies))
            {
                sqlparam.Add("ChildOccupancies", bookingDetails.ChildOccupancies);
            }
            if (bookingDetails.StockSI > 0)
            {
                sqlparam.Add("StockSI", bookingDetails.StockSI);
            }
            if (bookingDetails.AssociationId != null && bookingDetails.AssociationId > 0)
            {
                sqlparam.Add("AssociationId", bookingDetails.AssociationId);
            }
            if (bookingDetails.Loading != null && bookingDetails.Loading > 0)
            {
                sqlparam.Add("Loading", bookingDetails.Loading);
            }
            if(bookingDetails.Discounting != null && bookingDetails.Discounting > 0)
            {
                sqlparam.Add("Discounting", bookingDetails.Discounting);
            }
            if(!string.IsNullOrEmpty(bookingDetails.BookingFrom))
            {
                sqlparam.Add("BookingFrom", bookingDetails.BookingFrom);
            }
            if(!string.IsNullOrEmpty(bookingDetails.PolicyCategory))
            {
                sqlparam.Add("PolicyCategory", bookingDetails.PolicyCategory);
            }
            if(!string.IsNullOrEmpty(bookingDetails.QuoteId))
            {
                sqlparam.Add("QuoteId", bookingDetails.QuoteId);
            }
            if (bookingDetails.TerrorismPremium != null)
            {
                sqlparam.Add("TerrorismPremium", bookingDetails.TerrorismPremium);
            }
            if (bookingDetails.BurglaryPremium != null)
            {
                sqlparam.Add("BurglaryPremium", bookingDetails.BurglaryPremium);
            }
            if (bookingDetails.FirePremium != null)
            {
                sqlparam.Add("FirePremium", bookingDetails.FirePremium);
            }



            SqlParameter[] parameters = SetParamterFromDictionary(sqlparam);
            parameters[0].Direction = ParameterDirection.Output;
            parameters[1].Direction = ParameterDirection.Output;
            parameters[1].DbType = DbType.String;
            parameters[1].Size = 500;

            var procName = "[MTX].[InsertUpdateBookingAndPaymentDetails_V2]";
            if (!string.IsNullOrEmpty(source) && source.ToLower().Equals("pbcroma"))
                procName = "[MTX].[InsertUpdatePreBookingDetails]";

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, procName, parameters);
            message = Convert.ToString(parameters[1].Value);
            return Convert.ToInt32(parameters[0].Value);
        }

        private static SqlParameter[] SetParamterFromDictionary(Dictionary<object, object> param)
        {
            if (param.Count <= 0)
                return Array.Empty<SqlParameter>();
            return param.Select(s => new SqlParameter("@" + s.Key, s.Value)).ToArray();
        }

        public static void UpdateBookingDetails(BookingDetailsModel bookingDetails)
        {
            var sqlparam = new Dictionary<object, object>
            {
                {"MatrixLeadID", bookingDetails.MatrixLeadId > 0 ? bookingDetails.MatrixLeadId : bookingDetails.LeadId},
                {"RegDate", bookingDetails.RegDate == DateTime.MinValue ? null : bookingDetails.RegDate},
                {"FuelType", bookingDetails.FuelType},
                {"CC", bookingDetails.CC},
                {"UserID", bookingDetails.UserId},
                {"Saod", bookingDetails.Saod},
                {"ProductId", bookingDetails.Supplier != null ? bookingDetails.Supplier.ProductId : 0},
                {"ODPremium", bookingDetails.ODPremium},
                {"TPPremium", bookingDetails.TPPremium},
                {"RegNo", string.IsNullOrEmpty(bookingDetails.RegistrationNo) ? null : bookingDetails.RegistrationNo}
            };

            if (bookingDetails.CityID > 0)
                sqlparam.Add("CityId", bookingDetails.CityID);

            if (bookingDetails.InvestmentTypeID > 0)
                sqlparam.Add("SubProductId", bookingDetails.InvestmentTypeID);

            if (!string.IsNullOrEmpty(bookingDetails.CompanyName))
                sqlparam.Add("CompanyName", bookingDetails.CompanyName);

            if (bookingDetails.PolicyTerm > 0)
                sqlparam.Add("PolicyTerm", bookingDetails.PolicyTerm);

            if (bookingDetails.PayTerm > 0)
                sqlparam.Add("PayTerm", bookingDetails.PayTerm);

            if (bookingDetails.AgentId > 0 && bookingDetails.BookingSource == "BulkBooking")
                sqlparam.Add("AgentId", bookingDetails.AgentId);

            if (bookingDetails.NCB > 0)
                sqlparam.Add("NCB", bookingDetails.NCB);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                          CommandType.StoredProcedure,
                                          "[MTX].[UpdateAdditionalBookingDetails]",
                                          SetParamterFromDictionary(sqlparam));
        }

        public static string GetAgentNameAndEmpId(long AgentId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", 0);
            sqlParam[1] = new SqlParameter("@AgentId", AgentId);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),
                                                 "[MTX].[GetPrimaryAgent]",
                                                 sqlParam);
            if (result != null)
                return result.ToString();
            else
                return string.Empty;
        }

        public static DataSet GetAgentData(long? leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            "[MTX].[GetLeadBasicInfo]",
                                            sqlParam);
        }

        public static int UpdateLeadDetails(LeadDetailsEntity reqData)
        {
            var sqlParams = new Dictionary<object, object>
            {
                { "LeadID", reqData.LeadID },
                { "Name", reqData.Name },
                { "CityId", reqData.CityId },
                { "StateId", reqData.StateId },
                { "SubProductTypeId", reqData.InvestmentTypeID },
                { "PolicyTypeId", reqData.PolicyTypeId },
                { "NoOfLives", reqData.NoOfLives },
                { "NoOfEmployees", reqData.NoOfEmployees },
                { "CoverTypeId", reqData.CoverTypeId },
                { "OccupancyId", reqData.OccupancyId },
                { "CompanyName", reqData.CompanyName },
                { "InsuredName", reqData.InsuredName}
            };
            var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[MTX].[UpdateLeadDetails_V2]",
                                                 SetParamterFromDictionary(sqlParams));
            if (result != null)
                return Convert.ToInt16(result);
            else
                return 0;
        }

        public static DataTable GetLeadDetailsByLeadId(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[GetLeadDetailsByProduct_V2]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }
        public static DataTable GetAdditionalLeadDetailsByLeadID(long leadId, string Source)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[GetAdditionalLeadBoookingDetails]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }
        
        public static DataSet GetRenewalDetails(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@RenewalLead", leadId);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[RenewDetails_v2]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data;
            else
                return null;
        }

        public static void InsertUpdateLeadStatus(StampingRequestModel stampingRequestModel)
        {
                SqlParameter[] SqlParam = new SqlParameter[8];
                SqlParam[0] = new SqlParameter("@LeadID", stampingRequestModel.LeadId);
                SqlParam[1] = new SqlParameter("@UserID", stampingRequestModel.UserId);
                SqlParam[2] = new SqlParameter("@StatusId", stampingRequestModel.StatusId);
                SqlParam[3] = new SqlParameter("@SubstatusId", stampingRequestModel.SubStatusID);
                SqlParam[4] = new SqlParameter("@EventType", 23);
                SqlParam[5] = new SqlParameter("@StatusCreatedOn", stampingRequestModel.StatusCreatedOn);
                SqlParam[6] = new SqlParameter("@IslastStatus", stampingRequestModel.IsLaststatus);
                SqlParam[7] = new SqlParameter("@StatusUpdatedOn", stampingRequestModel.StatusUpdatedOn);

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertUpdateLeadStatus]", SqlParam);
        }

        #endregion

        public static DataTable GetLeadHistory(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[GetLeadHistory]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }

        public static DataSet GetLeadLogs(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetLeadLogs]",
                                            sqlParam);
        }

        public static DataTable GetBookingDetailsByAgentId(long userId, DateTime FromDate, DateTime ToDate)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@UserId", userId);
            sqlParam[1] = new SqlParameter("@FromDate", FromDate);
            sqlParam[2] = new SqlParameter("@ToDate", ToDate);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[GetLeadsByAgentId]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }
        public static DataSet GetBookedAndRenewalLeadData(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@leadId", leadId);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[GetBookedandRenewalLeads]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data;
            else
                return null;
        }

        public static DataSet GetAgentDetails(long userId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@userId", Convert.ToInt64(userId));
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentDetailsById]", sqlParam);
        }

    }
}