﻿using DataHelper;
using System;
using System.Data;
using System.Data.SqlClient;
using PropertyLayers;
using System.Globalization;
using MongoDB.Driver;
using MongoDB.Driver.Builders;

namespace DataAccessLibrary
{
    public class ChatDLL
    {
        public static bool IsRoomExists(string RID)
        {            
            string Connectionstring = string.Empty;
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@RID", RID);
            Connectionstring = ConnectionClass.LivesqlConnection();
            var recieved = SqlHelper.ExecuteScalar(Connectionstring, CommandType.StoredProcedure, "[MTX].[IsChatRoomExist]", SqlParam);
            if (Convert.ToInt32(recieved) > 0)
                return true;
            else
                return false;       
        }
        public static bool InsertChatData(ChatDataModel _ChatDataModel)
        {            
            string connection = ConnectionClass.LivesqlConnection();                
            SqlParameter[] SqlParam = new SqlParameter[24];
            SqlParam[0] = new SqlParameter("@LeadId", _ChatDataModel.LeadId);
            SqlParam[0].SqlDbType = SqlDbType.BigInt;
            SqlParam[1] = new SqlParameter("@EmployeeId", _ChatDataModel.EmpId);
            SqlParam[1].SqlDbType = SqlDbType.VarChar;
            SqlParam[2] = new SqlParameter("@ReceivedOn", _ChatDataModel.RecievedOn);
            SqlParam[2].SqlDbType = SqlDbType.DateTime;
            SqlParam[3] = new SqlParameter("@ResponseTime", _ChatDataModel.ResponseTime);
            SqlParam[3].SqlDbType = SqlDbType.Int;
            SqlParam[4] = new SqlParameter("@LastMessage", _ChatDataModel.LastMsg);
            SqlParam[4].SqlDbType = SqlDbType.DateTime;
            SqlParam[5] = new SqlParameter("@MessageCount", _ChatDataModel.MessageCount);
            SqlParam[5].SqlDbType = SqlDbType.SmallInt;
            SqlParam[6] = new SqlParameter("@Status", _ChatDataModel.Status);
            SqlParam[6].SqlDbType = SqlDbType.VarChar;
            SqlParam[7] = new SqlParameter("@ClosedOn", _ChatDataModel.ClosedOn);
            SqlParam[7].SqlDbType = SqlDbType.DateTime;
            SqlParam[8] = new SqlParameter("@ClosedBy", _ChatDataModel.clsd);
            SqlParam[8].SqlDbType = SqlDbType.VarChar;
            SqlParam[9] = new SqlParameter("@RID", _ChatDataModel.rid);
            SqlParam[9].SqlDbType = SqlDbType.VarChar;
            SqlParam[10] = new SqlParameter("@ProductName", _ChatDataModel.prdct);
            SqlParam[10].SqlDbType = SqlDbType.VarChar;
            SqlParam[11] = new SqlParameter("@cb", _ChatDataModel.cb);
            SqlParam[11].SqlDbType = SqlDbType.Bit;
            SqlParam[12] = new SqlParameter("@AgentMsg", _ChatDataModel.AgentMsg);
            SqlParam[12].SqlDbType = SqlDbType.SmallInt;
            SqlParam[13] = new SqlParameter("@chatleadID", _ChatDataModel.chatleadID);
            SqlParam[13].SqlDbType = SqlDbType.BigInt;
            SqlParam[14] = new SqlParameter("@Source", _ChatDataModel.Source);
            SqlParam[14].SqlDbType = SqlDbType.VarChar;
            SqlParam[15] = new SqlParameter("@BotMsg", _ChatDataModel.BotMsg);
            SqlParam[15].SqlDbType = SqlDbType.SmallInt;
            SqlParam[16] = new SqlParameter("@EnquiryId", _ChatDataModel.EnquiryId);
            SqlParam[16].SqlDbType = SqlDbType.BigInt;
            SqlParam[17] = new SqlParameter("@chatby", _ChatDataModel.chatby);
            SqlParam[17].SqlDbType = SqlDbType.VarChar;            
            SqlParam[18] = new SqlParameter("@label", _ChatDataModel.label);
            SqlParam[18].SqlDbType = SqlDbType.VarChar;
            SqlParam[19] = new SqlParameter("@transferred_room_id", _ChatDataModel.transferred_room_id);
            SqlParam[19].SqlDbType = SqlDbType.VarChar;
            SqlParam[20] = new SqlParameter("@TransferedFrom", _ChatDataModel.TransferedFrom);
            SqlParam[20].SqlDbType = SqlDbType.VarChar;
            SqlParam[21] = new SqlParameter("@transferred_date", _ChatDataModel.transferred_date);
            SqlParam[21].SqlDbType = SqlDbType.DateTime;
            SqlParam[22] = new SqlParameter("@code", _ChatDataModel.code);
            SqlParam[22].SqlDbType = SqlDbType.BigInt;
            SqlParam[23] = new SqlParameter("@OtpAuthUnverified", _ChatDataModel.OtpAuthUnverified);
            SqlParam[23].SqlDbType = SqlDbType.Bit;
            string query1 = "insert into MTX.ChatData(LeadId,EmployeeId,ReceivedOn,ResponseTime,LastMessage,MessageCount,Status,ClosedOn,ClosedBy,RID,ProductName,CB,AgentMsgCount,ChatLeadID,Source,BotMessageCount,EnquiryId,ChatBy,label,TransferredRID,TransferredFrom,transferred_date,code,OtpAuthUnverified) values(@LeadId,@EmployeeId,@ReceivedOn,@ResponseTime,@LastMessage,@MessageCount,@Status,@ClosedOn,@ClosedBy,@RID,@ProductName,@cb,@AgentMsg,@chatleadID,@Source,@BotMsg,@EnquiryId,@chatby,@label,@transferred_room_id,@TransferedFrom,@transferred_date,@code,@OtpAuthUnverified)";
            SqlHelper.ExecuteNonQuery(connection, CommandType.Text, query1, SqlParam);                
            return true;                       
        }
        public static bool UpdateChatData(ChatDataModel _ChatDataModel)
        {            
            string connection = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[24];
            SqlParam[0] = new SqlParameter("@LeadId", _ChatDataModel.LeadId);
            SqlParam[0].SqlDbType = SqlDbType.BigInt;
            SqlParam[1] = new SqlParameter("@EmployeeId", _ChatDataModel.EmpId);
            SqlParam[1].SqlDbType = SqlDbType.VarChar;
            SqlParam[2] = new SqlParameter("@ReceivedOn", _ChatDataModel.RecievedOn);
            SqlParam[2].SqlDbType = SqlDbType.DateTime;
            SqlParam[3] = new SqlParameter("@ResponseTime", _ChatDataModel.ResponseTime);
            SqlParam[3].SqlDbType = SqlDbType.Int;
            SqlParam[4] = new SqlParameter("@LastMessage", _ChatDataModel.LastMsg);
            SqlParam[4].SqlDbType = SqlDbType.DateTime;
            SqlParam[5] = new SqlParameter("@MessageCount", _ChatDataModel.MessageCount);
            SqlParam[5].SqlDbType = SqlDbType.SmallInt;
            SqlParam[6] = new SqlParameter("@Status", _ChatDataModel.Status);
            SqlParam[6].SqlDbType = SqlDbType.VarChar;
            SqlParam[7] = new SqlParameter("@ClosedOn", _ChatDataModel.ClosedOn);
            SqlParam[7].SqlDbType = SqlDbType.DateTime;
            SqlParam[8] = new SqlParameter("@ClosedBy", _ChatDataModel.clsd);
            SqlParam[8].SqlDbType = SqlDbType.VarChar;
            SqlParam[9] = new SqlParameter("@RID", _ChatDataModel.rid);
            SqlParam[9].SqlDbType = SqlDbType.VarChar;
            SqlParam[10] = new SqlParameter("@ProductName", _ChatDataModel.prdct);
            SqlParam[10].SqlDbType = SqlDbType.VarChar;
            SqlParam[11] = new SqlParameter("@cb", _ChatDataModel.cb);
            SqlParam[11].SqlDbType = SqlDbType.Bit;
            SqlParam[12] = new SqlParameter("@AgentMsg", _ChatDataModel.AgentMsg);
            SqlParam[12].SqlDbType = SqlDbType.SmallInt;
            SqlParam[13] = new SqlParameter("@chatleadID", _ChatDataModel.chatleadID);
            SqlParam[13].SqlDbType = SqlDbType.BigInt;
            SqlParam[14] = new SqlParameter("@Source", _ChatDataModel.Source);
            SqlParam[14].SqlDbType = SqlDbType.VarChar;
            SqlParam[15] = new SqlParameter("@BotMsg", _ChatDataModel.BotMsg);
            SqlParam[15].SqlDbType = SqlDbType.SmallInt;
            SqlParam[16] = new SqlParameter("@EnquiryId", _ChatDataModel.EnquiryId);
            SqlParam[16].SqlDbType = SqlDbType.BigInt;
            SqlParam[17] = new SqlParameter("@chatby", _ChatDataModel.chatby);
            SqlParam[17].SqlDbType = SqlDbType.VarChar;
            SqlParam[18] = new SqlParameter("@label", _ChatDataModel.label);
            SqlParam[18].SqlDbType = SqlDbType.VarChar;
            SqlParam[19] = new SqlParameter("@transferred_room_id", _ChatDataModel.transferred_room_id);
            SqlParam[19].SqlDbType = SqlDbType.VarChar;
            SqlParam[20] = new SqlParameter("@TransferedFrom", _ChatDataModel.TransferedFrom);
            SqlParam[20].SqlDbType = SqlDbType.VarChar;
            SqlParam[21] = new SqlParameter("@transferred_date", _ChatDataModel.transferred_date);
            SqlParam[21].SqlDbType = SqlDbType.DateTime;
            SqlParam[22] = new SqlParameter("@code", _ChatDataModel.code);
            SqlParam[22].SqlDbType = SqlDbType.BigInt;
            SqlParam[23] = new SqlParameter("@OtpAuthUnverified", _ChatDataModel.OtpAuthUnverified);
            SqlParam[23].SqlDbType = SqlDbType.Bit;
            string query1 = "UPDATE MTX.ChatData set LeadId=@LeadId,EmployeeId=@EmployeeId,ReceivedOn=@ReceivedOn,ResponseTime=@ResponseTime,LastMessage=@LastMessage,MessageCount=@MessageCount,Status=@Status,ClosedOn=@ClosedOn,ClosedBy=@ClosedBy,ProductName=@ProductName,cb=@cb,AgentMsgCount=@AgentMsg,chatleadID=@chatleadID,Source=@Source,BotMessageCount=@BotMsg,EnquiryId=@EnquiryId,ChatBy=@chatby,label=@label,TransferredRID=@transferred_room_id,TransferredFrom=@TransferedFrom,transferred_date=@transferred_date,code=@code,OtpAuthUnverified=@OtpAuthUnverified WHERE rid=@RID";
            SqlHelper.ExecuteNonQuery(connection, CommandType.Text, query1, SqlParam);
            return true;            
        }
        public static DataSet getCustinfobyMobileNo(string MobileNo, string ProductName, Int64 LeadID, bool isservice = false)
        {
            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@MobileNo", MobileNo);
            sqlParam[1] = new SqlParameter("@ProductName", ProductName);
            sqlParam[2] = new SqlParameter("@IsService", isservice);
            sqlParam[3] = new SqlParameter("@LeadID", LeadID);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetCustinfoByMobileNo", sqlParam);
        }
        public static LeadDetailResponse getLeadDetailsforChat(long LeadID,int ProductId)
        {
            SqlParameter[] sqlparm = new SqlParameter[2];
            sqlparm[0] = new SqlParameter("@LeadID", LeadID);
            sqlparm[1] = new SqlParameter("@ProductId", ProductId);
            var ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[getLeadDetails_Chat]", sqlparm);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                LeadData objLeadData = new LeadData();
                objLeadData.LeadID = long.Parse(Convert.ToString(ds.Tables[0].Rows[0]["W_LeadID"]));
                objLeadData.ParentLeadID = long.Parse(Convert.ToString(ds.Tables[0].Rows[0]["LeadID"])); // Parent Lead                                
                if (!string.IsNullOrEmpty(Convert.ToString(ds.Tables[0].Rows[0]["CustomerID"])))
                {
                    objLeadData.CustID = Convert.ToInt64(Convert.ToString(ds.Tables[0].Rows[0]["CustomerID"]));
                }
                objLeadData.CustomerName = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(Convert.ToString(ds.Tables[0].Rows[0]["CustomerName"]).ToLower());
                objLeadData.MobileNo = Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]);
                objLeadData.EmailId = Convert.ToString(ds.Tables[0].Rows[0]["EmailId"]);
                objLeadData.ProductID = Convert.ToInt16(ds.Tables[0].Rows[0]["ProductID"]);
                objLeadData.ProductName = Convert.ToString(ds.Tables[0].Rows[0]["Product"]);
                objLeadData.LeadSource = Convert.ToString(ds.Tables[0].Rows[0]["LeadSource"]);
                if (ds.Tables[0].Rows[0]["Utm_source"] != null && ds.Tables[0].Rows[0]["Utm_source"] != DBNull.Value)
                    objLeadData.UtmSource = Convert.ToString(ds.Tables[0].Rows[0]["Utm_source"]);
                if (ds.Tables[0].Rows[0]["utm_content"] != null && ds.Tables[0].Rows[0]["utm_content"] != DBNull.Value)
                    objLeadData.UtmContent = Convert.ToString(ds.Tables[0].Rows[0]["utm_content"]);
                if (ds.Tables[0].Rows[0]["Language"] != null && ds.Tables[0].Rows[0]["Language"] != DBNull.Value)
                    objLeadData.Language = Convert.ToString(ds.Tables[0].Rows[0]["Language"]);
                objLeadData.LeadCreatedDateTime = Convert.ToDateTime(ds.Tables[0].Rows[0]["LeadDate"]);
                if (objLeadData.ProductID == 115)
                {
                    string InvestTypeVar = Convert.ToString(ds.Tables[0].Rows[0]["InvestmentType"]);
                    objLeadData.InvestmentType = (InvestTypeVar == null || InvestTypeVar == "") ? "INVESTMENT" : InvestTypeVar;
                }

                if (ds.Tables[0].Rows[0]["Country"] != null && ds.Tables[0].Rows[0]["Country"] != DBNull.Value)
                    objLeadData.Country = Convert.ToString(ds.Tables[0].Rows[0]["Country"]);

                objLeadData.State = (ds.Tables[0].Rows[0]["StateID"] != null && ds.Tables[0].Rows[0]["StateID"] != DBNull.Value) ? Convert.ToString(ds.Tables[0].Rows[0]["StateID"]) : null;

                objLeadData.AnnualIncome = Convert.ToString(ds.Tables[0].Rows[0]["AnnualIncome"]);
                try
                {
                    if (ds.Tables[0].Rows[0]["DOB"] != null && ds.Tables[0].Rows[0]["DOB"] != DBNull.Value)
                    {
                        string[] arr = Convert.ToString(ds.Tables[0].Rows[0]["DOB"]).Split('-');
                        if (arr.Length > 0)
                            objLeadData.DOB = new DateTime(Convert.ToInt16(arr[2]), Convert.ToInt16(arr[1]), Convert.ToInt16(arr[0]));
                        else
                            objLeadData.DOB = Convert.ToDateTime(ds.Tables[0].Rows[0]["DOB"].ToString());
                    }
                }
                catch (Exception ex)
                {

                }

                objLeadData.LeadRank = (ds.Tables[0].Rows[0]["LeadRank"] != null && ds.Tables[0].Rows[0]["LeadRank"] != DBNull.Value) ? Convert.ToInt32(ds.Tables[0].Rows[0]["LeadRank"]) : 0;
                objLeadData.City = ds.Tables[0].Rows[0]["CityID"] != null && ds.Tables[0].Rows[0]["CityID"] != DBNull.Value ? Convert.ToString(ds.Tables[0].Rows[0]["CityID"]) : null;
                objLeadData.CountryCode = Convert.ToInt16(ds.Tables[0].Rows[0]["countryCode"]);
                objLeadData.RecFound = Convert.ToBoolean(ds.Tables[0].Rows[0]["RecFound"]);                
                return new LeadDetailResponse() { LeadData = objLeadData };
            }
            else
                return null;
        }
        public static DataSet GetParentLeadSource(long leadid)
        {
            try
            {
                string Connectionstring = string.Empty;                
                Connectionstring = ConnectionClass.ReplicasqlConnection();                
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@leadid", leadid);
                string strQuery = "select Leadsource,Utm_source from [Matrix].[CRM].[Leaddetails] (nolock) where leadid=@leadid";
                return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public static bool IsExpiry(long leadid, ref bool NewCar)
        {
            try
            {
                bool isExpiry = false;
                string Connectionstring = string.Empty;                
                Connectionstring = ConnectionClass.ReplicasqlConnection();                
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@leadid", leadid);
                DataSet ds = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[CheckIsExpiry]", SqlParam);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    isExpiry = Convert.ToBoolean(ds.Tables[0].Rows[0]["IsExpiry"]);
                    NewCar = Convert.ToBoolean(ds.Tables[0].Rows[0]["NewCar"]);
                    return isExpiry;
                }
                else
                    return false;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public static string getDepartmentID(string ProdName)
        {
            //var ParentID = GetParentID(LeadID);
            MongoHelper objCommDB = new MongoHelper(SingletonClass.ChatDB());
            IMongoQuery varquery = null;
            if (!string.IsNullOrEmpty(ProdName))
                varquery = Query.EQ("name", ProdName);

            IMongoFields SelectFields = Fields.Include("id");
            return objCommDB.GetSclarField(varquery, MongoCollection.ChatDepartmentCollection(), "_id");
        }
        public static Int16 GetAssignedGroup(long leadid)
        {
            try
            {
                string Connectionstring = string.Empty;                

                Connectionstring = ConnectionClass.ReplicasqlConnection();                

                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@leadid", leadid);
                string strQuery = "SELECT ISNULL(AssignToGroupId,0) FROM Matrix.CRM.LeadAssignDetails (NOLOCK) WHERE LeadID=@leadid and IsLastAssigned=1";
                var _obj = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.Text, strQuery, SqlParam);
                if (_obj != null)
                    return Convert.ToInt16(_obj);
                else
                    return 0;

            }
            catch (Exception ex)
            {
                return 0;
            }
        }
        public static string getWelcomeMessage(string Key)
        {
            //var ParentID = GetParentID(LeadID);
            MongoHelper objCommDB = new MongoHelper(SingletonClass.ChatDB());
            IMongoQuery varquery = null;
            if (!string.IsNullOrEmpty(Key))
                varquery = Query.EQ("_id", Key);

            return objCommDB.GetSclarField(varquery, MongoCollection.ChatWelcomeMessageCollection(), "message");
        }
        public static DataSet getCarInfo(Int64 LeadID)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadID);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetvehicleDetail_Chat]", sqlParam);
            return ds;
        }

        public static DataSet getHealthInfo(Int64 LeadID)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadID);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetHealthDetail_Chat]", sqlParam);
            return ds;

        }

        public static DataSet IsUser_WAEligible(string UserID,string LeadID)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserID", UserID);
            sqlParam[1] = new SqlParameter("@LeadId", LeadID);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[IsUser_WAEligible]", sqlParam);
            return ds;

        }


        public static DataSet ChkWhatsAppAllowed(Int64 LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@leadid", LeadID);
            var LoginDataDS = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[IsWhatsAPPAllowed_test]", SqlParam);
            return LoginDataDS;
        }
        public static DataSet GetCustomerActiveLeads(string CustomerID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@CustomerID", CustomerID);
            var result = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerActiveLeads_WA]", SqlParam);
            return result;
        }
        public static DataSet GetLeadTT(string LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            var result = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadTT]", SqlParam);
            return result;
        }

        public static void LogWAEligibleTime(Int64 LeadID)
        {
            DateTime dtReq = DateTime.Now;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@LeadId", LeadID);
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[LogWAEligibleTime]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadID.ToString(), LeadID, ex.ToString(), "LogWAEligibleTime",
                                                          "MRSCore", "", "", "", dtReq, DateTime.Now);
            }
        }
        public static DataSet GetAssignDataforchat(long leadId)
        {
            try
            {
                string connectionstring = ConnectionClass.LivesqlConnection();

                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", leadId);
                DataSet ds = SqlHelper.ExecuteDataset(connectionstring, CommandType.StoredProcedure, "[MTX].[GetProgLeadAssignDetails]", sqlParam);
                return ds;
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public static DataSet UpdateKFAforLead(long parentId, long customerId, short productId,string source)
        {
            try
            {
                string connectionstring = ConnectionClass.LivesqlConnection();

                SqlParameter[] sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@LeadID", parentId);
                sqlParam[1] = new SqlParameter("@CustID", customerId);
                sqlParam[2] = new SqlParameter("@ProductID", productId);
                sqlParam[3] = new SqlParameter("@Source", source);

                DataSet ds = SqlHelper.ExecuteDataset(connectionstring, CommandType.StoredProcedure, "[MTX].[CalculateLeadRank_Allocation_NewApp]", sqlParam);
                return ds;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetLeadAllocationKeyFactors(Int64 leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);


            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAllocationKeyFactors]", SqlParam);

            if (ds == null || ds.Tables.Count < 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }


        public static DataSet GetAdditionalInfo(long leadId, int productId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            if(productId == 7)
            {
                sqlParam[1] = new SqlParameter("@Type", "Term");
            }

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetAdditionalInfo", sqlParam);
        }

        public static bool IsBookedCustomer(string mobileno, long leadID)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@MobNO", SqlDbType.VarChar, 12);
            SqlParam[0].Value = mobileno;
            SqlParam[1] = new SqlParameter("@LeadID", leadID);
            var BookingCount = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "MTX.CustBookingCount", SqlParam);
            if (Convert.ToInt16(BookingCount) > 0)
                return true;
            else
                return false;
        }

        public static int SetCustInteraction(InteractionModel data)
        {
            SqlParameter[] SqlParam = new SqlParameter[5];
            SqlParam[0] = new SqlParameter("@LeadId", data.LeadId);
            SqlParam[1] = new SqlParameter("@CustID", data.CustId);
            SqlParam[2] = new SqlParameter("@StartDate", data.StartDate == null ? null : (DateTime?)Convert.ToDateTime(data.StartDate));
            SqlParam[3] = new SqlParameter("@EndDate", data.EndDate == null ? null : (DateTime?)Convert.ToDateTime(data.EndDate));
            SqlParam[4] = new SqlParameter("@Type", data.IntractionType);

            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertCustomerInteraction]", SqlParam);
        }

        public static DataSet GetLastActiveAgentDetails(long leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLastActiveAgentDetails]", SqlParam);
        }

        public static bool SetCustomerNITrigger(long customerId, int type)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@CustomerId", customerId);
            SqlParam[1] = new SqlParameter("@TriggerId", type);

            var response = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SetCustomerNITrigger]", SqlParam);
            if (Convert.ToInt16(response) > 0)
                return true;
            
            else
                return false;
            
        }
    }
}
