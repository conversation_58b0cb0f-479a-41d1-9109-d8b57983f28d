﻿using DataHelper;
using System;
using System.Data;
using System.Data.SqlClient;
using PropertyLayers;
using MongoDB.Driver.Builders;
using Helper;

namespace DataAccessLibrary
{
    public class SalesViewDLL


    {
        public static DataSet GetUserAssignedLeads(Int64 UserID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserID);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUserAssignedLeads]", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }
        public static DataSet GetReassignedLeads(Int64 UserID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserID);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetUserReassignedLeads", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }

        public static Int64 IsShowMobileNo(Int64 UserID, Int64 LeadID)
        {
            Int64 result = 0;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@UserID", UserID);
                sqlParam[1] = new SqlParameter("@LeadId", LeadID);
                var obj = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[IsShowMobileNo]", sqlParam);

                if (Convert.ToInt64(obj) > 0)
                    result = Convert.ToInt64(obj);
            }
            catch (Exception ex)
            {
                result = 0;
            }
            return result;
        }

        public static int ReAssignLead(AllocationDetails _AllocationDetails)
        {
            _AllocationDetails.AllocationTrackingEntryFlag = 1;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[10];
            SqlParam[0] = new SqlParameter("@AssignedTo_AgentId", _AllocationDetails.AssigntoUserID);
            SqlParam[1] = new SqlParameter("@AssignedBy_AgentId", _AllocationDetails.AssignbyUserID);
            SqlParam[2] = new SqlParameter("@ProductId", _AllocationDetails.ProductID);
            SqlParam[3] = new SqlParameter("@LeadId", _AllocationDetails.LeadID);
            SqlParam[4] = new SqlParameter("@GroupId", _AllocationDetails.GroupID);
            SqlParam[5] = new SqlParameter("@Flag", _AllocationDetails.AllocationTrackingEntryFlag);
            SqlParam[6] = new SqlParameter("@JobId", _AllocationDetails.JobID);
            SqlParam[7] = new SqlParameter("@FirstSelectedPlanId", _AllocationDetails.InsurerID);
            SqlParam[8] = new SqlParameter("@SelectionCount", _AllocationDetails.SelectionCount);
            SqlParam[9] = new SqlParameter("@LeadRank", _AllocationDetails.LeadRank);
            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[CRM].[Insert_AssignedToAgent]", SqlParam);
        }

        public static bool UnAssignLead(long leadId, Int16 grouPID, Int64 AssignedTo = 0)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var query = (Query<PriorityModel>.EQ(p => p.LeadID, leadId));
            UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(x => x.User.AssignedOn, DateTime.Now).Set(x => x.User.UserID, AssignedTo).Set(x => x.User.GroupId, grouPID);
            _CommDB.UpdateDocument(query, update, MongoCollection.LPDataCollection());
            return true;
        }
        public static Int64 SetAppointmentData(AppointmentsDataModel objAppointmentData)
        {
            Int64 appointmentId = 0;
            try
            {
                string query = string.Empty;
                string connection = ConnectionClass.LivesqlConnection();
                SqlParameter[] sqlparm = new SqlParameter[26];
                sqlparm[0] = new SqlParameter("@LeadId", objAppointmentData.ParentId);
                sqlparm[1] = new SqlParameter("@CustomerId", objAppointmentData.CustomerId);
                sqlparm[2] = new SqlParameter("@AppointmentDateTime", objAppointmentData.AppointmentDateTime);
                sqlparm[3] = new SqlParameter("@Address ", objAppointmentData.Address);
                sqlparm[4] = new SqlParameter("@Pincode", objAppointmentData.Pincode);
                sqlparm[5] = new SqlParameter("@CityId ", objAppointmentData.CityId);
                sqlparm[6] = new SqlParameter("@CreatedBy", objAppointmentData.UserId);
                sqlparm[7] = new SqlParameter("@AppointmentType", objAppointmentData.AppointmentType);
                sqlparm[8] = new SqlParameter("@Landmark", objAppointmentData.Landmark);
                sqlparm[9] = new SqlParameter("@Address1", objAppointmentData.Address1);
                sqlparm[10] = new SqlParameter("@OfflineCityId ", objAppointmentData.OfflineCityId);
                sqlparm[11] = new SqlParameter("@Comments ", objAppointmentData.Comments);
                sqlparm[12] = new SqlParameter("@ZoneId ", objAppointmentData.ZoneId);

                sqlparm[13] = new SqlParameter("@AssignmentId ", objAppointmentData.AssignmentId);
                sqlparm[14] = new SqlParameter("@SubStatusId ", objAppointmentData.subStatusId);
                sqlparm[15] = new SqlParameter("@IncomeId", objAppointmentData.IncomeId);
                sqlparm[16] = new SqlParameter("@IncomeDocsId", objAppointmentData.IncomeDocsId);
                sqlparm[17] = new SqlParameter("@EducationId", objAppointmentData.EducationId);
                sqlparm[18] = new SqlParameter("@ProductId", objAppointmentData.ProductId);

                sqlparm[19] = new SqlParameter("@Source", objAppointmentData.Source.ToLower());
                sqlparm[20] = new SqlParameter("@SlotId", objAppointmentData.SlotId);
                sqlparm[21] = new SqlParameter("@PlaceId", objAppointmentData.place_id);
                sqlparm[22] = new SqlParameter("@NearBy", objAppointmentData.NearBy);
                sqlparm[23] = new SqlParameter("@Type", 1);
                sqlparm[24] = new SqlParameter("@Gender", objAppointmentData.Gender);
                sqlparm[25] = new SqlParameter("@InvestmentTypeId", objAppointmentData.InvestmentTypeId);

                var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SetFOSAppointmentData]", sqlparm);

                if (Convert.ToInt64(result) > 0 && Convert.ToInt64(result) > 1)
                    appointmentId = Convert.ToInt64(result);
            }
            catch (Exception ex)
            {
                return appointmentId;

            }
            return appointmentId;
        }

        public static void SetHealthAppointmentData(AppointmentsDataModel objAppointmentData) {
            
            SqlParameter[] sqlParams = new SqlParameter[]
            {
                new("@LeadId", objAppointmentData.ParentId),
                new("@AppointmentId", objAppointmentData.AppointmentId),
                new("@Portability", objAppointmentData.Portability),
                new("@PolicyExpiryDate", objAppointmentData.PolicyExpiryDate),
            };

            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertHealthAppointmentData]", sqlParams);
        }

        public static DataSet GetAppointmentData(long CustomerId, long ParentId)
        {

            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@CustomerId ", CustomerId);
            sqlParam[1] = new SqlParameter("@LeadId", ParentId);
            sqlParam[2] = new SqlParameter("@Type", 0);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetFOSAppointmentDetails]", sqlParam);

        }

        public static DataSet GetCustDetails(string CustId, string LeadId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@CustId", Convert.ToInt64(CustId));
                sqlParam[1] = new SqlParameter("@LeadId", Convert.ToInt64(LeadId));
                string query = "SELECT TOP 1 ProductID,Name,MobileNo,EmailID FROM MATRIX.CRM.Leaddetails (NOLOCK) WHERE CustomerID=@CustId and LeadId=@LeadId";
                ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.Text, query, sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }

        public static bool SetAppointmentPlans(Int64 supplierId, Int64 planId, Int64 appointmentId, Int64 UserId)
        {
            try
            {
                string query = string.Empty;
                string connection = ConnectionClass.LivesqlConnection();
                SqlParameter[] sqlparm = new SqlParameter[5];
                sqlparm[0] = new SqlParameter("@appointmentId", appointmentId);
                sqlparm[1] = new SqlParameter("@supplierId", supplierId);
                sqlparm[2] = new SqlParameter("@planId", planId);
                sqlparm[3] = new SqlParameter("@UserId", UserId);
                sqlparm[4] = new SqlParameter("@Type", 1);

                var result = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, "[MTX].[GetSetAppointmentPlans]", sqlparm);

                return true;
            }
            catch (Exception ex)
            {
                return false;

            }



        }

        public static DataSet GetOfflineCities(int Type, int ProductId, long UserId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@ProductId", ProductId);
                sqlParam[1] = new SqlParameter("@UserId", UserId);

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetOfflineCities]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }

        public static DataSet GetAppointmentTypeList(int ProductId, long UserId, int ProcessId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@ProductId", ProductId);
                sqlParam[1] = new SqlParameter("@UserId", UserId);
                sqlParam[2] = new SqlParameter("@ProcessId", ProcessId);

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAppointmentTypeList_V2]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }
        public static DataSet GetSMEQuotesReport(long LeadId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", LeadId);


                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSMEQuotesLogs]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }
        public static DataSet GetReasonMaster(int ProductId, int SubStatusId, string Source)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@ProductId", ProductId);
                sqlParam[1] = new SqlParameter("@SubStatusId", SubStatusId);
                if (!string.IsNullOrEmpty(Source) && Source == "SMEFosCreateLeadPanel")
                {
                    sqlParam[2] = new SqlParameter("@Source", Source);
                }

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetReasonMasterData]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }


        public static bool SaveAppCancelReason(AppCancelReasonModel oAppCancelReasonModel)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[7];
                SqlParam[0] = new SqlParameter("@Leads", oAppCancelReasonModel.Leads);
                SqlParam[1] = new SqlParameter("@PropertyId", oAppCancelReasonModel.PropertyId);
                SqlParam[2] = new SqlParameter("@Name", oAppCancelReasonModel.Name);
                SqlParam[3] = new SqlParameter("@textValue", oAppCancelReasonModel.textValue);
                SqlParam[4] = new SqlParameter("@value", oAppCancelReasonModel.value);
                SqlParam[5] = new SqlParameter("@UserID", oAppCancelReasonModel.UserID);
                SqlParam[6] = new SqlParameter("@SubStatusId", oAppCancelReasonModel.SubStatusId);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdateLeadSubStatusProperty]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static bool GetAdvisorInfo(string EmpCode)
        {
            bool response = false;
            try
            {
                string query = string.Empty;
                string connection = ConnectionClass.LivesqlConnection();
                SqlParameter[] sqlparm = new SqlParameter[1];
                sqlparm[0] = new SqlParameter("@EmpCode", EmpCode.Trim());

                var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAdvisorInfo]", sqlparm);

                if (Convert.ToInt64(result) == 1)
                    response = true;
            }
            catch (Exception ex)
            {
                return response;

            }
            return response;
        }

        #region SME

        public static int AddUpdateSMEQuotes(SMEQuoteModal oSMEQuoteModal)
        {
            int controlId = 0;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[10];
                SqlParam[0] = new SqlParameter("@LeadID", oSMEQuoteModal.LeadID);
                SqlParam[1] = new SqlParameter("@ControlId", oSMEQuoteModal.ControlId);
                SqlParam[2] = new SqlParameter("@QuotePath", oSMEQuoteModal.Path);
                SqlParam[3] = new SqlParameter("@CreatedBy", oSMEQuoteModal.UserId);
                SqlParam[4] = new SqlParameter("@typeid", oSMEQuoteModal.typeid);
                SqlParam[5] = new SqlParameter("@FileName", oSMEQuoteModal.fileName);
                SqlParam[6] = new SqlParameter("@roleId", oSMEQuoteModal.roleId);
                SqlParam[7] = new SqlParameter("@reasonId", oSMEQuoteModal.ReasonId);
                SqlParam[8] = new SqlParameter("@DocumentId", oSMEQuoteModal.DocumentId);
                SqlParam[9] = new SqlParameter("@QuoteSource", oSMEQuoteModal.QuoteSource);

                var controlIdOutput = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[AddUpdateSMEQuoteUploadsV2]", SqlParam);
                controlId = Convert.ToInt32(controlIdOutput);
            }
            catch
            {
                //Do nothing
            }
            return controlId;
        }

        public static DataSet GetSMEQuotes(long LeadId)
        {
            DataSet dt = new DataSet();
            try
            {
                string Connectionstring = string.Empty;
                Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@LeadId", LeadId);


                dt = SqlHelper.ExecuteDataset(Connectionstring, CommandType.StoredProcedure, "[MTX].[GetSMEQuoteUploadsList]", SqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return dt;
        }

        public static bool DeleteSMEQuoteData(SMEQuoteDetailModel oSMEQuoteDetailModel)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[4];
                SqlParam[0] = new SqlParameter("@LeadID", oSMEQuoteDetailModel.LeadID);
                SqlParam[1] = new SqlParameter("@SMEQuotesId", oSMEQuoteDetailModel.SMEQuotesId);
                SqlParam[2] = new SqlParameter("@CreatedBy", oSMEQuoteDetailModel.UserId);
                SqlParam[3] = new SqlParameter("@ControlId", oSMEQuoteDetailModel.ControlId);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[DeleteSMEQuoteData]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static bool UpdateSMEQuoteList(SMEQuoteModal oSMEQuoteModal)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[8];
                SqlParam[0] = new SqlParameter("@LeadID", oSMEQuoteModal.LeadID);
                SqlParam[1] = new SqlParameter("@comment", oSMEQuoteModal.comment);
                SqlParam[2] = new SqlParameter("@CreatedBy", oSMEQuoteModal.UserId);
                SqlParam[3] = new SqlParameter("@QuotesStatusId", oSMEQuoteModal.QuotesStatusId);
                SqlParam[4] = new SqlParameter("@SMEQuote", oSMEQuoteModal.SMEQuote);
                SqlParam[5] = new SqlParameter("@roleId", oSMEQuoteModal.roleId);
                SqlParam[6] = new SqlParameter("@SubStatusId", 0);
                SqlParam[7] = new SqlParameter("@ControlId", oSMEQuoteModal.ControlId);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdateSMEQuoteList]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static bool AddSMEQuotesList(SMEQuoteModal oSMEQuoteModal)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[6];
                SqlParam[0] = new SqlParameter("@LeadID", oSMEQuoteModal.LeadID);
                SqlParam[1] = new SqlParameter("@comment", oSMEQuoteModal.comment);
                SqlParam[2] = new SqlParameter("@CreatedBy", oSMEQuoteModal.UserId);
                //SqlParam[3] = new SqlParameter("@QuotesStatusId", oSMEQuoteModal.QuotesStatusId);
                SqlParam[3] = new SqlParameter("@SMEQuote", oSMEQuoteModal.SMEQuote);
                SqlParam[4] = new SqlParameter("@roleId", oSMEQuoteModal.roleId);
                //SqlParam[5] = new SqlParameter("@SubStatusId", 0);
                SqlParam[5] = new SqlParameter("@ControlId", oSMEQuoteModal.ControlId);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[AddSMEQuoteList]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in AddSMEQuoteList in DLL." + ex.ToString());
                return false;
            }
        }

        public static bool UpdateSMEQuotesStatusV2(SMEQuoteModal oSMEQuoteModal)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[6];
                SqlParam[0] = new SqlParameter("@LeadID", oSMEQuoteModal.LeadID);
                SqlParam[1] = new SqlParameter("@comment", oSMEQuoteModal.comment);
                SqlParam[2] = new SqlParameter("@CreatedBy", oSMEQuoteModal.UserId);
                SqlParam[3] = new SqlParameter("@QuotesStatusId", oSMEQuoteModal.QuotesStatusId);
                //SqlParam[4] = new SqlParameter("@SMEQuote", oSMEQuoteModal.SMEQuote);
                SqlParam[4] = new SqlParameter("@roleId", oSMEQuoteModal.roleId);
                SqlParam[5] = new SqlParameter("@SubStatusId", oSMEQuoteModal.SubStatusId);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdateSMEQuoteStatusV2]", SqlParam);
                //SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[AddSMEQuoteList]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in UpdateSMEQuotesStatusV2 in DLL." + ex.ToString());
                return false;
            }

        }

        public static bool SMEQuotesAdditionalUploadV2(SMEAdditionalFileModal oSMEAdditionalFileModal)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[11];
                SqlParam[0] = new SqlParameter("@LeadID", oSMEAdditionalFileModal.LeadID);
                SqlParam[1] = new SqlParameter("@FilePath", oSMEAdditionalFileModal.filePath);
                SqlParam[2] = new SqlParameter("@UserId", oSMEAdditionalFileModal.UserId);
                SqlParam[3] = new SqlParameter("@FileName", oSMEAdditionalFileModal.FileName);
                SqlParam[4] = new SqlParameter("@roleId", oSMEAdditionalFileModal.roleId);
                SqlParam[5] = new SqlParameter("@parentRowId", oSMEAdditionalFileModal.parentRowId);
                SqlParam[6] = new SqlParameter("@ControlId", oSMEAdditionalFileModal.ControlId);
                SqlParam[7] = new SqlParameter("@IsDelete", oSMEAdditionalFileModal.IsDelete);
                SqlParam[8] = new SqlParameter("@RowId", oSMEAdditionalFileModal.RowId);
                SqlParam[9] = new SqlParameter("@DocumentId", oSMEAdditionalFileModal.DocumentId);
                SqlParam[10] = new SqlParameter("@QuoteSource", oSMEAdditionalFileModal.QuoteSource);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[SMEQuotesAdditionalUploadV2]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in SMEQuotesAdditionalUploadV2 in DLL." + ex.ToString());
                return false;
            }
        }
        #endregion
        public static DataSet GetAgentStory(long userId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@agentId", userId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetAgentStory", sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static bool UpdateAgentStoryResponse(AgentStory storyResponse)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[8];
                SqlParam[0] = new SqlParameter("@userId", storyResponse.AgentId);
                SqlParam[1] = new SqlParameter("@storyId", storyResponse.StoryId);
                SqlParam[2] = new SqlParameter("@logtype", storyResponse.Logtype);
                SqlParam[3] = new SqlParameter("@reaction", storyResponse.Reaction);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdateAgentStoryResponse]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public static DataSet GetCallDetails(long LeadId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.ReplicasqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId ", Convert.ToInt64(LeadId));
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadCallLogs]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }

        public static DataTable GetLeadDetails(long LeadId)
        {
            DataTable dt = new DataTable();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId ", Convert.ToInt64(LeadId));
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadDetails]", sqlParam);
                if (ds.Tables.Count == 0)
                    return null;
                else
                    dt = ds.Tables[0];
            }
            catch (Exception ex)
            {
                return null;
            }
            return dt;
        }

        public static bool IsCreateTicketAllowed(string LeadId)
        {
            bool result = false;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", Convert.ToInt64(LeadId));

                var obj = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[IsCreateTicketAllowed]", sqlParam);

                if (Convert.ToInt64(obj) == 1)
                    result = true;
            }
            catch (Exception ex)
            {
                return result;
            }
            return result;
        }

        public static DataSet GetCityIdByParentId(long ParentId, int ProductId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.ReplicasqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@LeadId ", Convert.ToInt64(ParentId));
                sqlParam[1] = new SqlParameter("@ProductId ", Convert.ToInt32(ProductId));
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetDataForCityWidget]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }

        public static DataSet GetAssignmentCityMppping(Int32 ProductId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.ReplicasqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@ProductId ", Convert.ToInt32(ProductId));
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAssignmentCityMapping]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }
        public static DataSet GetAgentTypeByBooking(long AgentId, long BookingId)
        {

            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@AgentId ", Convert.ToInt64(AgentId));
                sqlParam[1] = new SqlParameter("@BookingId ", Convert.ToInt64(BookingId));
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAgentTypeByBooking]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetCityIdByParentId_Term(long ParentId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId ", ParentId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetCityByPincode]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }
        public static DataSet GetAppointmentStatus(long ParentId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@ParentId ", ParentId);
                string query = "SELECT TOP 1 Id as AppointmentId FROM mtx.appointmentdata (NOLOCK) WHERE  LeadId= @ParentId";
                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.Text, query, sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }

        public static bool SaveFosPitchedData(FOSPitchedModel oFOSPitchedModel)
        {
            try
            {
                string query = string.Empty;
                string connection = ConnectionClass.LivesqlConnection();
                SqlParameter[] sqlparm = new SqlParameter[4];
                sqlparm[0] = new SqlParameter("@LeadId", oFOSPitchedModel.LeadId);
                sqlparm[1] = new SqlParameter("@Response", oFOSPitchedModel.Response);
                sqlparm[2] = new SqlParameter("@UserId", oFOSPitchedModel.UserId);
                sqlparm[3] = new SqlParameter("@Type", oFOSPitchedModel.Type);
                var result = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, "[MTX].[SaveFosPitchedData]", sqlparm);

                return true;
            }
            catch (Exception ex)
            {
                return false;

            }



        }
        public static DataSet GetUserObj(long UserId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId ", UserId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[CRM].[GetUserInfo]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }
        public static DataSet GetAgentContextQueue(long UserId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId ", UserId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentContextQueue]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }
        public static DataSet GetCustomerFollowupPermissions(long UserId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId ", UserId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerFollowupPermissionsByUserId]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }

        public static DataSet GetAgentTypeByAgentId(long AgentId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@AgentId ", Convert.ToInt64(AgentId));
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAgentTypeByAgentId]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public static DataSet GetChurnLogicMsg(long LeadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId ", LeadId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetChurnLogicMsg]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static bool ReAssignChurnLead(long LeadId, long UserId)
        {
            DateTime dt = DateTime.Now;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@LeadId", LeadId);
                SqlParam[1] = new SqlParameter("@UserId", UserId);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[FOS].[InsertChurnLeads]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "ReAssignChurnLead", "MatrixCore", "FOSDLL", UserId.ToString(), "", dt, DateTime.Now);
                return false;
            }

        }

        public static DataSet GetLeadDetailsByCustIDAndProdID(long CustomerID, long LeadID)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@CustomerID ", CustomerID);
            sqlParam[1] = new SqlParameter("@LeadID ", LeadID);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadDetailsByCustIDAndProdID_V2]", sqlParam);
        }

        public static DataSet GetAppointmentHistory(long LeadID)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId ", LeadID);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[AppointmentHistory]", sqlParam);
        }

        public static bool IsAppMarkCancel(long LeadId)
        {
            bool result = false;
            string output = string.Empty;

            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", LeadId);

            var DS = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[IsAppMarkedCancel]", SqlParam);

            if (Convert.ToInt64(DS) > 0)
                result = true;

            return result;
        }
        public static string GetBizHealthRatingPercentage(string AgentId, string ProductID)
        {
            DateTime dt = DateTime.Now;
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            string Percentage = string.Empty;
            try
            {
                ProductID = string.IsNullOrEmpty(ProductID) ? "115" : ProductID;
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@AgentId", Convert.ToInt64(AgentId));
                SqlParam[1] = new SqlParameter("@ProductID", Convert.ToInt32(ProductID));

                DataSet ds = SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetAgentInforcementRating]", SqlParam);
                if(ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    Percentage = Convert.ToString(ds.Tables[0].Rows[0]["HealthPercentage"] != null && ds.Tables[0].Rows[0]["HealthPercentage"] != DBNull.Value ? ds.Tables[0].Rows[0]["HealthPercentage"] : 0);
                }
                return Percentage;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AgentId, Convert.ToInt32(ProductID), ex.ToString(), "GetBizHealthRatingPercentage", "MatrixCore", "SalesViewDLL", AgentId.ToString(), ex.ToString(), dt, DateTime.Now);
                return Percentage;
            }

        }
        public static DataSet GetConsolidatedPolicyInforcementData(string AgentId, string ProductId)
        {
            DateTime dt = DateTime.Now;
            DataSet ds = new DataSet();

            try
            {
                ProductId = string.IsNullOrEmpty(ProductId) ? "115" : ProductId;
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@AgentId", Convert.ToInt64(AgentId));
                SqlParam[1] = new SqlParameter("@ProductID", Convert.ToInt32(ProductId));

                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetEnforcementData]", SqlParam);
                return ds;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AgentId, Convert.ToInt32(ProductId), ex.ToString(), "GetConsolidatedPolicyInforcementData", "MatrixCore", "SalesViewDLL", AgentId.ToString(), ex.ToString(), dt, DateTime.Now);
                return ds;
            }

        }

        public static string GetBHRDeduction(double BHR, double ShareMonthlyMode)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            string Deduction = string.Empty;

            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@BHR", BHR);
            SqlParam[1] = new SqlParameter("@ShareMonthlyMode", ShareMonthlyMode);

            Deduction = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetBHRDeductionData]", SqlParam).ToString();

            return Deduction;
        }
        public static string GetUserSuperGroup(string AgentId, string ProductID)
        {
            DateTime dt = DateTime.Now;
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            string UserSuperGroup = string.Empty;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@AgentId", Convert.ToInt64(AgentId));
                SqlParam[1] = new SqlParameter("@ProductID", Convert.ToInt32(ProductID));

                UserSuperGroup = SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetUserSuperGroup]", SqlParam).ToString();
                return UserSuperGroup;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AgentId, Convert.ToInt32(ProductID), ex.ToString(), "GetUserSuperGroup", "MatrixCore", "SalesViewDLL", AgentId.ToString(), ex.ToString(), dt, DateTime.Now);
                return UserSuperGroup;
            }


        }

        public static DataSet GetUserGroup(string ProductID, string SourcePage, string AgentId)
        {
            DateTime dt = DateTime.Now;
            DataSet ds = new DataSet();
            string UserGroup = string.Empty;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@UserId", Convert.ToInt64(AgentId));
                SqlParam[1] = new SqlParameter("@ProductID", Convert.ToInt32(ProductID));
                SqlParam[2] = new SqlParameter("@SourcePage", Convert.ToString(SourcePage));

                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUserGroupsByProductID]", SqlParam);
                return ds;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AgentId, Convert.ToInt32(ProductID), ex.ToString(), "GetUserGroupsByProductID", "MatrixCore", "SalesViewDLL", AgentId.ToString(), ex.ToString(), dt, DateTime.Now);
                return new DataSet();
            }


        }
        public static DataTable GetPaymentFailedFeedbackTickets(DataTable dt, long AgentId, int IssueId)
        {
            DateTime startdt = DateTime.Now;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@ParentIDList", dt);
                SqlParam[1] = new SqlParameter("@AgentId", AgentId);
                SqlParam[2] = new SqlParameter("@IssueId", IssueId);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetPaymentFailedFeedbackTickets]", 20, SqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AgentId.ToString(), 0, ex.ToString(), "GetPaymentFailedFeedbackTickets", "MatrixCore", "SalesViewDLL", AgentId.ToString(), ex.ToString(), startdt, DateTime.Now);
                return new DataTable();
            }

        }
        public static DataTable GetPaymentFailedCasesCount(long UserId)
        {
            DataTable oDataTable = null;
            string strException = "";
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                oDataTable = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetPaymentFailedCasesCount]", sqlParam).Tables[0];
            }
            catch (Exception ex)
            {
                strException = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), 0, ex.ToString(), "GetPaymentFailedCasesCount", "SalesViewDLL", "124", UserId.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return null;
            }
            //finally
            //{
            //    LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), 0, strException.ToString(), "GetPaymentFailedCasesCount", "SalesViewDLL", "124", UserId.ToString(), strException.ToString(), RequestDatetime, DateTime.Now);
            //}

            return oDataTable;
        }
        public static DataTable GetSelfEnforcementRatingDataDLL(long UserId)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSelfInforcementRatingData]", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), 0, ex.ToString(), "GetSelfEnforcementRatingDataDLL", "SalesViewDLL", "124", UserId.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataTable();
            }
        }
        public static DataSet GetInforcementRatingHierarchialDataDLL(long UserId, string Role, string ProductID)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                ProductID = string.IsNullOrEmpty(ProductID) ? "115" : ProductID;
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                sqlParam[1] = new SqlParameter("@Role", Role);
                sqlParam[2] = new SqlParameter("@ProductID", Convert.ToInt32(ProductID));
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetInforcementRatingHierarchialData]", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0 && ds.Tables[1].Rows.Count != 0)
                {
                    return ds;
                }
                else
                {
                    return new DataSet();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), Convert.ToInt32(ProductID), ex.ToString(), "GetInforcementRatingHierarchialDataDLL", "SalesViewDLL", "124", UserId.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }

        public static DataTable GetSelfEnforcementRatingDataMonthWiseDLL(long UserId, int month, int year, string ProductID)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                ProductID = string.IsNullOrEmpty(ProductID) ? "115" : ProductID;
                SqlParameter[] sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                sqlParam[1] = new SqlParameter("@Month", month);
                sqlParam[2] = new SqlParameter("@Year", year);
                sqlParam[3] = new SqlParameter("@ProductID", Convert.ToInt32(ProductID));
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSelfInforcementRatingMonthWise]", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId.ToString(), Convert.ToInt32(ProductID), ex.ToString(), "GetSelfEnforcementRatingDataMonthWiseDLL", "SalesViewDLL", "124", UserId.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataTable();
            }
        }

        public static DataTable GetUtmCampaignLinksDLL()
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUTMcampaignLink]");
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("124", 0, ex.ToString(), "GetUTMcampaignLink", "SalesViewDLL", "124", "0", ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataTable();
            }
        }
        public static DataSet IsVirtualRMAlreadyAllocated(long LeadID, long CustomerId, int ProductID)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@LeadID", LeadID);
                sqlParam[1] = new SqlParameter("@CustomerID", CustomerId);
                sqlParam[2] = new SqlParameter("@ProductID", ProductID);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.IsVirtualRMAlreadyAllocated", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    return ds;
                }
                else
                {
                    return new DataSet();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CustomerId.ToString(), LeadID, ex.ToString(), "IsVirtualRMAlreadyAllocatedDLL", "SalesViewDLL", "124", LeadID.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }
        public static DataSet AllocateRMBeforeIssuanceDLL(long LeadID, long CustomerId, int ProductID)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@LeadID", LeadID);
                sqlParam[1] = new SqlParameter("@CustomerID", CustomerId);
                sqlParam[2] = new SqlParameter("@ProductID", ProductID);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.AllocateRMBeforeIssuance", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0 && ds.Tables[1].Rows.Count != 0)
                {
                    return ds;
                }
                else
                {
                    return new DataSet();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(CustomerId.ToString(), LeadID, ex.ToString(), "AllocateRMBeforeIssuanceDLL", "SalesViewDLL", "124", LeadID.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }
        public static DataSet IsFOSAgent(long AgentID, long LeadID)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@AgentID", AgentID);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CheckIsFOSAgent]", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    return ds;
                }
                else
                {
                    return new DataSet();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AgentID.ToString(), LeadID, ex.ToString(), "IsFOSAgent", "SalesViewDLL", "124", AgentID.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }
        public static DataSet UpdateDiscountAvailedDetails(long AgentID, long LeadID, int DiscountAvailedCount)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@AgentID", AgentID);
                sqlParam[1] = new SqlParameter("@LeadID", LeadID);
                sqlParam[2] = new SqlParameter("@InputDiscountAvailedCount", DiscountAvailedCount);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateDiscountAvailedDetails]", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0)
                {
                    return ds;
                }
                else
                {
                    return new DataSet();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(AgentID.ToString(), LeadID, ex.ToString(), "UpdateDiscountAvailedDetails", "SalesViewDLL", AgentID.ToString(), DiscountAvailedCount.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }

        public static DataSet GetLeadProductDetails(long BookingId)
        {

            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@BookingId ", Convert.ToInt64(BookingId));
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadProductDetails]", sqlParam);

        }


        public static Int64 GetSalesAgent(long LeadId, DateTime BookingDate)
        {
            Int64 result = 0;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@LeadId", LeadId);
                sqlParam[1] = new SqlParameter("@BookingDate", BookingDate);
                var obj = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSalesAgentPostBooking]", sqlParam);

                if (Convert.ToInt64(obj) > 0)
                    result = Convert.ToInt64(obj);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "GetSalesAgent", "MatrixCore", "FOSDLL", "", "", DateTime.Now, DateTime.Now);
                result = 0;
            }
            return result;
        }
        public static DataSet GetUpsellAppNo(long LeadId)
        {
            DataSet ds = null;
            DateTime dtreq = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadID", LeadId);
                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[getUpSellApplicationNo]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(LeadId.ToString(), LeadId, ex.ToString(), "GetUpsellAppNo", "MatrixCore", "SalesViewDLL", "", "", dtreq, DateTime.Now);
            }

            return ds;
        }
        public static DataSet GetCountryTimeZones()
        {
            SqlParameter[] sqlParam = new SqlParameter[0];
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GET_TimeZones]", sqlParam);

        }

        public static DataSet GetSmeQuoteAgentData(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetSmeQuoteAgentData]",
                                            sqlParam);
        }

        public static bool IsShowEmail(long userId, long leadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserID", userId);
            sqlParam[1] = new SqlParameter("@LeadId", leadId);
            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[MTX].[IsShowEmail]",
                                                 sqlParam);
            if (result != null)
                return Convert.ToBoolean(result);
            else
                return false;
        }

        public static bool CanPushNewNotification(string empId)
        {
            bool res = false;
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@EmpId", empId);
            var result = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                  CommandType.StoredProcedure,
                                                  "[MTX].[GetProductListByEmpId]",
                                                  sqlParam);
            if (result != null && result.Tables.Count > 0)
            {
                foreach (DataRow row in result.Tables[0].Rows)
                {
                    if (row["ProductId"].ToString() == "131")
                    {
                        res = true;
                        break;
                    }
                }
            }
            return res;
        }

        public static DataSet getBookingDetailsforPG(long LeadID, string Source)
        {
            DataSet ds = null;
            int SourceType = !string.IsNullOrEmpty(Source) && Source == "PaymentDetailsPopupSV" ? 1 : 0;
            try
            {
                if (SourceType > 0)
                {
                    SqlParameter[] sqlParam = new SqlParameter[2];
                    sqlParam[0] = new SqlParameter("@LEADID", LeadID);
                    sqlParam[1] = new SqlParameter("@Source", SourceType);
                    ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetBookingDetailsforPG]", sqlParam);
                }
                else
                {
                    SqlParameter[] sqlParam = new SqlParameter[1];
                    sqlParam[0] = new SqlParameter("@LEADID", LeadID);
                    ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetBookingDetailsforPG]", sqlParam);
                }

            }
            catch (Exception ex)
            {
                ds = null;
            }

            return ds;
        }

        public static DataSet GetPaymentFailedCasesInfo(string UserId, string ProductID)
        {
            DataSet oDataSet = null;
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@UserId", Convert.ToInt64(UserId));
                sqlParam[1] = new SqlParameter("@ProductID", Convert.ToInt32(ProductID));
                oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetPaymentFailedCasesData]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserId, 0, ex.ToString(), "GetPaymentFailedCasesInfo", "SalesviewDLL", "MatrixCore", UserId, ex.ToString(), RequestDatetime, DateTime.Now);
                return null;
            }

            return oDataSet;
        }

        public static void DumpRefreshRequestToDB(RequestPaymentStatusData objRequestPaymentStatusData)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[6];
                sqlParam[0] = new SqlParameter("@LeadID", Convert.ToInt64(objRequestPaymentStatusData.LeadId));
                sqlParam[1] = new SqlParameter("@PolicyNumber", (objRequestPaymentStatusData.PolicyNo).ToString());
                sqlParam[2] = new SqlParameter("@ApplicationNumber", (objRequestPaymentStatusData.ApplicationNo).ToString());
                sqlParam[3] = new SqlParameter("@InsurerID", Convert.ToInt32(objRequestPaymentStatusData.InsurerId));
                sqlParam[4] = new SqlParameter("@ProductID", Convert.ToInt32(objRequestPaymentStatusData.ProductID));
                sqlParam[5] = new SqlParameter("@ProcessType", 1); //1->DumpRefreshRequests
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[ProcessRefreshPaymentOverdueData]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objRequestPaymentStatusData.LeadId, Convert.ToInt64(objRequestPaymentStatusData.LeadId), ex.ToString(), "DumpRefreshRequestToDB", "SalesviewDLL", "MatrixCore", objRequestPaymentStatusData.ProductID, ex.ToString(), RequestDatetime, DateTime.Now);
            }
        }
        public static DataTable GetBookingDeatilsforPaymentFailedCases(long LeadId, string UniqueId)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                string Connectionstring = ConnectionClass.ReplicasqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@LeadId", LeadId);
                SqlParam[1] = new SqlParameter("@UniqueId", UniqueId);
                //DataSet ds = SqlHelper.ExecuteDataset(Connectionstring, CommandType.StoredProcedure, "[MTX].[GetBookingDeatilsforPaymentFailedCases]", SqlParam);
                DataSet ds = SqlHelper.ExecuteDataset(Connectionstring, CommandType.StoredProcedure, "[MTX].[GetPaymentDetailsforCommunication]", SqlParam);
                if (ds != null && ds.Tables.Count > 0)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UniqueId, Convert.ToInt64(LeadId), ex.ToString(), "GetBookingDeatilsforPaymentFailedCases", "MatrixCore", "124", LeadId.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataTable();
            }
        }

        public static DataSet GetLeadAssignedAgent(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@leadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetleadAssignmentDetails]",
                                            sqlParam);
        }

        public static DataSet GetLeadAssignedAgentDetails(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@leadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetleadAssignmentDetails]",
                                            sqlParam);
        }

        public static void InsertInToPaymentFailedCasesTable(DataTable dt, long LeadId)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                string Connectionstring = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@Leads", dt);
                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[ProcessPaymentFailedCases]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "InsertInToPaymentFailedCasesTable", "MatrixCore", "124", LeadId.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
            }
        }

        public static DataSet GetAdditionalDetails(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAdditionalDetails]", sqlParam);
        }


        public static DataSet AddUpdateUploadedDocs(DocsUpload docs, string agentId)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", docs.LeadId);
            sqlParam[1] = new SqlParameter("@AgentId", string.IsNullOrEmpty(agentId) ? 0 : Convert.ToInt64(agentId));
            if (docs.Documents != null && docs.Documents.Count > 0)
            {
                var documents = CoreCommonMethods.SerializeToXml(docs.Documents);
                if (!string.IsNullOrEmpty(documents))
                    sqlParam[2] = new SqlParameter("@DocumentsData", documents);
            }
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[AddUpdateUploadedDocs]",
                                            sqlParam);
        }
        public static DataSet GetFeedBackMaster()
        {
            var sqlParam = new SqlParameter[0];
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetFeedBackMaster]", sqlParam);
        }
        public static void SaveFOSFeedback(Int16 AnswerId, Int16 QuesId, long AppId, long UserId, string Comments)
        {
            SqlParameter[] SqlParam = new SqlParameter[5];
            SqlParam[0] = new SqlParameter("@AnswerId", AnswerId);
            SqlParam[1] = new SqlParameter("@QuesId", QuesId);
            SqlParam[2] = new SqlParameter("@AppId", AppId);
            SqlParam[3] = new SqlParameter("@UserId", UserId);
            SqlParam[4] = new SqlParameter("@Comments", Comments);
            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SaveFOSFeedback]", SqlParam);

        }

        public static DataSet GetCountryDetails(Int32 CountryId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CountryId", CountryId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCountryDetails]", sqlParam);
        }

        public static void UpdateBookedFlag(Int64 ParentId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@ParentId", ParentId);
            SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLeadBooked]", sqlParam);
        }

        public static DataSet GetAgentAPE(long agentId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@agentId", agentId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentAPE]", sqlParam);
        }

        public static DataSet GetLeadStatusID(string LeadIDs)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadList", LeadIDs);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadStatusID]", sqlParam);
        }

        public static DataSet GetLeadIds(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAllLeadIdAndStatusByParent]", sqlParam);
        }

        public static DataSet GetLeadStatus(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadStatusAndSubProduct]", sqlParam);
        }

        public static short GetSubStatusReason(long leadId, short propertyId, int subStatusId)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            sqlParam[1] = new SqlParameter("@PropertyId", propertyId);
            sqlParam[2] = new SqlParameter("@SubStatusId", subStatusId);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[MTX].[GetSubStatusReason]",
                                                 sqlParam);
            if (result != null)
                return Convert.ToInt16(result);
            else
                return 0;
        }
        public static bool UpdatePrimaryMob(string CustId, string MobileNo, string CountryID)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@CustId", CustId);
            sqlParam[1] = new SqlParameter("@MobileNo", MobileNo);
            sqlParam[2] = new SqlParameter("@CountryID", CountryID);
            //sqlParam[3] = new SqlParameter("@Source", "Comm");             
            return Convert.ToBoolean(SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.UpdatePrimaryNumber", sqlParam));
        }
        public static DataSet GetUserAttributeValues(long UserId, long LeadId)
        {
            DataSet ds = new DataSet();
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserID", UserId);
            sqlParam[1] = new SqlParameter("@LeadID", LeadId);
            ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),CommandType.StoredProcedure, "[MTX].[GetUserAttributeValues]", sqlParam);
            return ds;
        }

        public static bool GetIsPotentialBuyer(long customerId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetIsPotentialBuyer]", sqlParam);
            if (result != null)
                return Convert.ToBoolean(result);
            else
                return false;
        }

        public static DataSet GetSetRfqPolicyTypeData(long leadId, short policyTypeId, short policySubTypeId, bool fetchData, string agentId)
        {
            var sqlParam = new SqlParameter[5];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            sqlParam[1] = new SqlParameter("@PolicyTypeId", policyTypeId);
            sqlParam[2] = new SqlParameter("@PolicySubTypeId", policySubTypeId);
            sqlParam[3] = new SqlParameter("@FetchData", fetchData);
            sqlParam[4] = new SqlParameter("@UserId", string.IsNullOrEmpty(agentId) ? DBNull.Value : Convert.ToInt64(agentId));

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetRfqPolicyTypeData]", sqlParam);
        }

        public static bool SaveCallIntent(CallIntentData callData)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[8];
                SqlParam[0] = new SqlParameter("@CallDataId", callData.Calldataid);
                SqlParam[1] = new SqlParameter("@LeadId", callData.LeadId);
                SqlParam[2] = new SqlParameter("@UserId", callData.UserId);
                SqlParam[3] = new SqlParameter("@Intent", callData.Intent);
                SqlParam[5] = new SqlParameter("@Predicted_label", callData.Predicted_label);
                SqlParam[6] = new SqlParameter("@Predicted_Scores", callData.Predicted_Scores);
                SqlParam[7] = new SqlParameter("@Source", callData.Source);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[StoreCallIntentsByAgent]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public static DataSet GetCumulativeAgentBHRData(string EmployeeID, string Role, string ProductID)
        {
            DateTime RequestDatetime = DateTime.Now;
            try
            {
                ProductID = string.IsNullOrEmpty(ProductID) ? "115" : ProductID;
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@EmployeeID", EmployeeID);
                sqlParam[1] = new SqlParameter("@Role", Role);
                sqlParam[2] = new SqlParameter("@ProductID", Convert.ToInt32(ProductID));
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCumulativeAgentBHRData]", sqlParam);
                if (ds != null && ds.Tables.Count != 0 && ds.Tables[0].Rows.Count != 0 && ds.Tables[1].Rows.Count != 0)
                {
                    return ds;
                }
                else
                {
                    return new DataSet();
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(EmployeeID.ToString(), Convert.ToInt32(ProductID), ex.ToString(), "GetCumulativeAgentBHRDataDLL", "SalesViewDLL", "124", EmployeeID.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }

        public static DataSet GetHWEligibleData(long AgentId, string SupervisorIds)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@AgentId", AgentId);
            sqlParam[1] = new SqlParameter("@SupervisorIds", SupervisorIds);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetHWOpportunityData", sqlParam);
        }
        public static DataSet GetLeadCustomerDetails(long LeadID)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadID", LeadID);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.getLeadCustomerDetails", sqlParam);
        }

        public static DataSet GetBasicLeadDetails(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadID", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetBasicLeadDetails]", sqlParam);
        }
        public static DataSet GetCreditChangeRequests(long UserID, int ProductId)
        {
            string procName = string.Empty;
            if(ProductId == 2)
            {
                procName = "MTX.GetCreditChangeRequests";
            }
            else if(ProductId == 117)
            {
                procName = "MTX.GetCreditChangeRequestsMotor";
            }
            else if (ProductId == 7)
            {
                procName = "MTX.GetCreditChangeRequestsTerm";
            }
            else if (ProductId == 115)
            {
                procName = "MTX.GetCreditChangeRequestsInvestment";
            }
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserID", UserID);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, procName, sqlParam);
        }
        public static DataSet GetBookingDetailsForCreditChange(long LeadID, int ProductId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadID);
            sqlParam[1] = new SqlParameter("@ProductId", ProductId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetBookingDetailsForCreditChange", sqlParam);
        }
        public static DataSet GetHierarchialAgentList(long UserId, int ProductID)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserID", UserId);
            sqlParam[1] = new SqlParameter("@ProductID", ProductID);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetHierarchialAgentList", sqlParam);
        }
        public static DataSet GetCreditChangeReasonMaster()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),CommandType.StoredProcedure, "MTX.GetCreditChangeReasons");
        }
        public static DataSet SetUpdateCreditChangeRequest(CreditChangeRequest objCreditChangeRequest, int IsBulkUpload)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            DateTime RequestDatetime = new DateTime();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[11];
                SqlParam[0] = new SqlParameter("@UserID", objCreditChangeRequest.RequestorUserID);
                SqlParam[1] = new SqlParameter("@BookingID", objCreditChangeRequest.BookingID);
                SqlParam[2] = new SqlParameter("@AgentTypeID", objCreditChangeRequest.AgentTypeID);
                SqlParam[3] = new SqlParameter("@NewAdvisorUserID", objCreditChangeRequest.NewAgentID);
                SqlParam[4] = new SqlParameter("@ReasonID", objCreditChangeRequest.ReasonID);
                SqlParam[5] = new SqlParameter("@Comment", objCreditChangeRequest.RequestorRemarks);
                SqlParam[6] = new SqlParameter("@Action", objCreditChangeRequest.Action);//1 - Approve , 2 --decline, 3 --delete
                SqlParam[7] = new SqlParameter("@RequestID", objCreditChangeRequest.RequestID);
                if(string.IsNullOrEmpty(objCreditChangeRequest.ReferenceId))
                    SqlParam[8] = new SqlParameter("@ReferenceId", 0);
                else
                    SqlParam[8] = new SqlParameter("@ReferenceId", Convert.ToInt64(objCreditChangeRequest.ReferenceId));
                SqlParam[9] = new SqlParameter("@IsBulkUpload", IsBulkUpload);
                SqlParam[10] = new SqlParameter("@CurrentStatusUI", objCreditChangeRequest.CurrentStatus != null && Convert.ToInt32(objCreditChangeRequest.CurrentStatus) > 0 ? objCreditChangeRequest.CurrentStatus : 0);


                return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "MTX.UpdateCreditChangeRequest", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objCreditChangeRequest.RequestorUserID.ToString(), Convert.ToInt64(objCreditChangeRequest.BookingID), ex.ToString(), "SetUpdateCreditChangeRequest", "SalesViewDLL", "124", objCreditChangeRequest.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }
        public static DataSet IsAuthorisedCreditChangeApprover(long UserID, long BookingID, int AgentTypeID)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            DateTime RequestDatetime = new DateTime();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@UserID", UserID);
                SqlParam[1] = new SqlParameter("@BookingID", BookingID);
                SqlParam[2] = new SqlParameter("@AgentTypeID", AgentTypeID);
                return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "MTX.IsAuthorisedCreditChangeApprover", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(UserID.ToString(), Convert.ToInt64(BookingID), ex.ToString(), "IsAuthorisedCreditChangeApprover", "SalesViewDLL", "124", AgentTypeID.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }
        public static DataSet GetCreditChangeLogs(long BookingID, int AgentTypeID, int RequestID)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            DateTime RequestDatetime = new DateTime();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@BookingID", BookingID);
                SqlParam[1] = new SqlParameter("@AgentTypeID", AgentTypeID);
                SqlParam[2] = new SqlParameter("@RequestID", RequestID);
                return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "MTX.GetCreditChangeLogs", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(BookingID.ToString(), Convert.ToInt64(BookingID), ex.ToString(), "GetCreditChangeLogs", "SalesViewDLL", "124", AgentTypeID.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }
        public static DataSet GetCreditChangeCommunicationDetails(long BookingID, int AgentTypeID, long UserID, int RequestID, int CurrentStatus)
        {
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            DateTime RequestDatetime = new DateTime();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[5];
                SqlParam[0] = new SqlParameter("@BookingID", BookingID);
                SqlParam[1] = new SqlParameter("@AgentTypeID", AgentTypeID);
                SqlParam[2] = new SqlParameter("@UserId", UserID);
                SqlParam[3] = new SqlParameter("@RequestID", RequestID);
                SqlParam[4] = new SqlParameter("@CurrentStatus", CurrentStatus);

                return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "MTX.GetCreditChangeCommunicationDetails", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(RequestID.ToString(), Convert.ToInt64(BookingID), ex.ToString(), "GetCreditChangeCommunicationDetails", "SalesViewDLL", "124", UserID.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }

        public static DataSet GetCreditChangeBookingHistory(long bookingId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@BookingID", bookingId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCreditChangeBookingHistory]", SqlParam);
        }

        public static DataSet GetAgentOngoingAppt(long userId)
        {
            DateTime RequestDatetime = new DateTime();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[1];
                
                SqlParam[0] = new SqlParameter("@UserId", userId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetAgentOngoingAppt", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", userId, ex.ToString(), "GetAgentOngoingAppt", "SalesViewDLL", "124", userId.ToString(), "", RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }

        public static bool InsertFosSelfApptData(long UserId, long LeadId, DateTime ValidTill, int AppointmentID)
        {
            DateTime dt = DateTime.Now;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[4];
                SqlParam[0] = new SqlParameter("@LeadId", LeadId);
                SqlParam[1] = new SqlParameter("@UserId", UserId);
                SqlParam[2] = new SqlParameter("@ValidTill", ValidTill);
                SqlParam[3] = new SqlParameter("@AppointmentID", AppointmentID);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[InsertFosSelfApptData]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "InsertFosSelfApptData", "SalesViewDLL", "124", UserId.ToString(), "", dt, DateTime.Now);
                return false;
            }

        } 

        public static bool UpdateFosSelfApptData(long UserId)
        {
            DateTime dt = DateTime.Now;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@UserId", UserId);
    
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[UpdateFosSelfApptData]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", UserId, ex.ToString(), "UpdateFosSelfApptData", "SalesViewDLL", "124", UserId.ToString(), "", dt, DateTime.Now);
                return false;
            }

        } 
        public static bool ValidateSalesCreditReferenceLead(long bookingId, long referenceId)
        {
            var res = false;

            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@BookingID", bookingId);
            SqlParam[1] = new SqlParameter("@ReferenceID", referenceId);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[ValidateSalesCreditReferenceLead]", SqlParam);

            if(result != null)
            {
                res = Convert.ToBoolean(result);
            }
            return res;
        }

        public static DataSet CheckLeadPrevAssignToUser(long UserId, long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@AgentId", UserId);
            sqlParam[1] = new SqlParameter("@LeadId", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.CheckLeadPrevAssignToUser", sqlParam);
        }

        public static DataSet LeadVerifyOtpForDuplicate(long CustId, long UserId, int type, long ID)
        {
            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@CustId", CustId);
            sqlParam[1] = new SqlParameter("@UserId", UserId);
            sqlParam[2] = new SqlParameter("@Type", type);//1- Insert into table for verification, 2 - Update OTP Verification, 3 - OTP Verified
            sqlParam[3] = new SqlParameter("@ID", ID);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.LeadVerifyOtpForDuplicate", sqlParam);
        }

        public static DataSet IsReferralLead(long leadID)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadID);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.IsReferralLead", sqlParam);
        }

        public static DataSet GetVirtualNumberList(long LeadId, long UserId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            sqlParam[1] = new SqlParameter("@UserId", UserId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetVirtualNumberByLeadId", sqlParam);
        }

        public static DataTable GetCallSummary(long leadId)
        {
            DataTable data = null;
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId ", leadId);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[MTX].[GetLeadCallDetails]",
                                                 sqlParam);
            if (ds != null && ds.Tables != null && ds.Tables.Count > 0)
                data = ds.Tables[0];

            return data;
        }

        public static DataSet IsUserEligibleForCredit(long bookingId, long referenceId, int productId, long selectedAgent, int agentTypeId)
        {
            SqlParameter[] sqlParam = new SqlParameter[5];
            sqlParam[0] = new SqlParameter("@BookingId ", bookingId);
            sqlParam[1] = new SqlParameter("@ReferenceId", referenceId);
            sqlParam[2] = new SqlParameter("@ProductId", productId);
            sqlParam[3] = new SqlParameter("@UserId", selectedAgent);
            sqlParam[4] = new SqlParameter("@AgentTypeId", agentTypeId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.IsUserEligibleForCredit", sqlParam);

        }
        public static DataSet GetSmeRenewalLeadBookingDetails(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId ", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetSmeRenewalLeadBookingDetails", sqlParam);

        }

        public static DataSet SendEmandateEnableCommunicationTermInv(long leadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId ", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetEmandateDataForLife]", sqlParam);
        }

        public static DataSet ChkEmergencyNoCondition(CustContactInfo objCustDetails)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", objCustDetails.LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[ChkEmergencyNoCondition]", sqlParam);
        }

        public static void MarkEmergencyNumCallable(CallingDataFields callingData, CustContactInfo objCustDetails)
        {
            SqlParameter[] sqlParam = new SqlParameter[8];
            sqlParam[0] = new SqlParameter("@LeadId", objCustDetails.LeadId);
            sqlParam[1] = new SqlParameter("@CustomerId", objCustDetails.CustomerId);
            sqlParam[2] = new SqlParameter("@CustMobId", objCustDetails.CustMobId);
            sqlParam[3] = new SqlParameter("@EncNumber", callingData.EncryptedMobileNo);
            sqlParam[4] = new SqlParameter("@CountryCode", callingData.CountryCode);
            sqlParam[5] = new SqlParameter("@IsCallable", 1);
            sqlParam[6] = new SqlParameter("@CreatedBy", objCustDetails.UserId);
            sqlParam[7] = new SqlParameter("@Reason", callingData.Reason);

            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertEmergencyCallableNum]", sqlParam);
            MarkEmergencyNumCallable_LP(callingData, objCustDetails);
        }

        public static void MarkEmergencyNumCallable_LP(CallingDataFields callingData, CustContactInfo objCustDetails)
        {
            CustEmergencyNo custEmergencyNo = new() {
                CountryCode = callingData.CountryCode,
                EncNumber = callingData.EncryptedMobileNo,
                ValidTill = DateTime.Now.AddDays(90),
                CustMobId = objCustDetails.CustMobId
            };
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var query = (Query<PriorityModel>.EQ(p => p.LeadID, objCustDetails.LeadId));
            UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(x => x.CustEmergencyNo, custEmergencyNo);
            _CommDB.UpdateDocument(query, update, MongoCollection.LPDataCollection());
            //return true;
        }

        public static void DisableEmergencyCallableNum(CustContactInfo objCustDetails)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", objCustDetails.LeadId);
            sqlParam[1] = new SqlParameter("@CustomerId", objCustDetails.CustomerId);
            sqlParam[2] = new SqlParameter("@updatedBy", objCustDetails.UserId);


            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[DisableEmergencyCallableNum]", sqlParam);
            DisableEmergencyCallableNum_LP(objCustDetails);

        }
        public static void DisableEmergencyCallableNum_LP(CustContactInfo objCustDetails)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var query = (Query<PriorityModel>.EQ(p => p.LeadID, objCustDetails.LeadId));
            UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Unset(x => x.CustEmergencyNo);
            _CommDB.UpdateDocument(query, update, MongoCollection.LPDataCollection());
        }

        public static DataSet GetEmergencyCallableNum(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
          
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetEmergencyCallableNum]", sqlParam);
        }

        public static DataSet InvAdvisorVerify(long parentId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadID", parentId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetInvAdvisorVerify]", sqlParam);
        }

        public static void UpsertWaOptOutTrigger (long leadId, long userId, short incrementFlag, string source) {
            var sqlParams = new SqlParameter[]
            {
                new("@LeadId", leadId),
                new("@UserId", userId),
                new("@IncrementFlag", incrementFlag),
                new("@Source", source),
            };

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpsertWAOptOutTrigger]", sqlParams);
        }

        public static DataSet MotorAdvisorVerify(long parentId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadID", parentId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetMotorAdvisorVerify]", sqlParam);
        }

        public static DataSet CreateCreditChangeRequest(CreditChangeRequest objCreditChangeRequest, int isBulkUpload)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            DateTime RequestDatetime = new DateTime();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[11];
                SqlParam[0] = new SqlParameter("@UserID", objCreditChangeRequest.RequestorUserID);
                SqlParam[1] = new SqlParameter("@BookingID", objCreditChangeRequest.BookingID);
                SqlParam[2] = new SqlParameter("@AgentTypeID", objCreditChangeRequest.AgentTypeID);
                SqlParam[3] = new SqlParameter("@NewAdvisorUserID", objCreditChangeRequest.NewAgentID);
                SqlParam[4] = new SqlParameter("@ReasonID", objCreditChangeRequest.ReasonID);
                SqlParam[5] = new SqlParameter("@Comment", objCreditChangeRequest.RequestorRemarks);
                SqlParam[6] = new SqlParameter("@Action", objCreditChangeRequest.Action);//1 - Approve , 2 --decline, 3 --delete
                SqlParam[7] = new SqlParameter("@RequestID", objCreditChangeRequest.RequestID);
                if (string.IsNullOrEmpty(objCreditChangeRequest.ReferenceId))
                    SqlParam[8] = new SqlParameter("@ReferenceId", 0);
                else
                    SqlParam[8] = new SqlParameter("@ReferenceId", Convert.ToInt64(objCreditChangeRequest.ReferenceId));
                SqlParam[9] = new SqlParameter("@IsBulkUpload", isBulkUpload);
                SqlParam[10] = new SqlParameter("@CurrentStatusUI", objCreditChangeRequest.CurrentStatus != null && Convert.ToInt32(objCreditChangeRequest.CurrentStatus) > 0 ? objCreditChangeRequest.CurrentStatus : 0);


                return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "MTX.CreateCreditChangeRequests", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objCreditChangeRequest.RequestorUserID.ToString(), Convert.ToInt64(objCreditChangeRequest.BookingID), ex.ToString(), "CreateCreditChangeRequest", "SalesViewDLL", "124", objCreditChangeRequest.ToString(), ex.ToString(), RequestDatetime, DateTime.Now);
                return new DataSet();
            }
        }

        public static DataSet GetCustomerBookingsByCustomerId(int customerId, int productId)
        {
            SqlParameter[] sqlParams = new SqlParameter[1];
            sqlParams[0] = new SqlParameter("@CustomerId", customerId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerBookingDetailsByCustomerId]", sqlParams);
        }

        public static DataSet GetAssignedAgentDetailsByProductIdAndCustomerId(int customerId, int productId)
        {
            var sqlParams = new[]
            {
                new SqlParameter("@CustomerId", customerId),
                new SqlParameter("@ProductId", productId)
            };

            return SqlHelper.ExecuteDataset(
                ConnectionClass.ReplicasqlConnection(),
                CommandType.StoredProcedure,
                "[MTX].[GetAssignedAgentDetailsFromProductIdAndCustomerId]",
                sqlParams
            );
        }

        public static DataSet FetchBHRBookingDataTLWise(string employeeId, string employeeRole, int productId)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@ECode", employeeId);
            SqlParam[1] = new SqlParameter("@Role", employeeRole);
            SqlParam[2] = new SqlParameter("@ProductId", productId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[FetchBHRBookingDataTLWise]", SqlParam);
        }

        public static DataSet CheckWhatsAppOptinSent(long LeadId)
        {
            SqlParameter[] sqlParams = new SqlParameter[1];
            sqlParams[0] = new SqlParameter("@LeadId", LeadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[CheckWhatsAppOptinSent]", sqlParams);
        }
    }
}