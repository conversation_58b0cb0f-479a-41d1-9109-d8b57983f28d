﻿using DataAccessLibrary;
using DataHelper;
using Newtonsoft.Json;
using PropertyLayers;
using System;
using System.Data;
using System.Data.SqlClient;

namespace DataAccessLayer
{
    public class UserDetailsDLL
    {
        public (int, string) UpdateUserCertificationDetails(UserCertificationDetails objuserdetails)
        {
            try
            {

                SqlParameter[] sqlParam = new SqlParameter[11];
                sqlParam[0] = new SqlParameter("@EmployeeId", objuserdetails.EmployeeId);
                sqlParam[1] = new SqlParameter("@actionBy", 124);
                sqlParam[2] = new SqlParameter("@DateofCertification", ToDateTimeFromEpoch(objuserdetails.CertificationDate));
                sqlParam[3] = new SqlParameter("@IsAVCertified", objuserdetails.IsAvCertified);
                sqlParam[4] = new SqlParameter("@ExamMode", objuserdetails.ExamMode);
                sqlParam[5] = new SqlParameter("@Category", objuserdetails.Category);
                sqlParam[6] = new SqlParameter("@CertificationEndDate", ToDateTimeFromEpoch(objuserdetails.CertificationEndDate));
                sqlParam[7] = new SqlParameter("@WorkLocation", objuserdetails.WorkLocation);
                sqlParam[8] = new SqlParameter("@CertificateType", objuserdetails.CertificateType);

                var sqlParameter = new SqlParameter();
                sqlParameter.Direction = ParameterDirection.Output;
                sqlParameter.ParameterName = "@result";
                sqlParameter.SqlDbType = SqlDbType.TinyInt;
                sqlParam[9] = sqlParameter;

                sqlParameter = new SqlParameter();
                sqlParameter.Direction = ParameterDirection.Output;
                sqlParameter.ParameterName = "@Message";
                sqlParameter.SqlDbType = SqlDbType.VarChar;
                sqlParameter.Size = 300;
                sqlParam[10] = sqlParameter;


                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UdateUserInfo]", 3000, sqlParam);

                return (Convert.ToInt32(sqlParam[9].Value), (sqlParam[10].Value).ToString());
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objuserdetails.EmployeeId.ToString(), 0, ex.ToString(), "UpdateUserCertificationDetails", "UpdateUserCertificationDetailsDLL", "124", JsonConvert.SerializeObject(objuserdetails).ToString(), ex.ToString(), DateTime.Now, DateTime.Now);
                return (0, ex.ToString());
            }

        }
        public DateTime ToDateTimeFromEpoch(long intDate)
        {
            long ticks = DateTime.Now.Ticks;
            var timeInTicks = intDate * TimeSpan.TicksPerSecond;
            return new DateTime(1970, 1, 1, 0, 0, 0, 0, DateTimeKind.Utc).AddTicks(timeInTicks);
        }

        public static DataSet GetSalesPartnerAndSalesSpecialist(string employeeId, string name)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@EmployeeId", string.IsNullOrEmpty(employeeId) ? DBNull.Value : employeeId);
            sqlParam[1] = new SqlParameter("@UserName", string.IsNullOrEmpty(name) ? DBNull.Value : name);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetSalesPartnerAndSalesSpecialist]",
                                            3000, sqlParam);
        }

        public static string GetPrimaryAgent(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),
                                                 "[MTX].[GetPrimaryAgent]",
                                                 sqlParam);
            if (result != null)
                return result.ToString();
            else
                return string.Empty;
        }

        public static DataSet SetAgentStatus(string employeeId, int isActive)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@EmployeeId", employeeId);
            sqlParam[1] = new SqlParameter("@IsActive", isActive);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SetAgentStatus]", sqlParam);

        }
    }
}
