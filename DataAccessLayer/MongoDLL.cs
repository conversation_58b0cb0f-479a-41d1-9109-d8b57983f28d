﻿using DataHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using PropertyLayers;
using MongoDB.Driver.Builders;
using MongoDB.Driver;
using MongoDB.Bson;

namespace DataAccessLibrary
{
    public class MongoDLL
    {

        public static List<SysConfigData> GetConfiValueFromMongo(string Source)
        {
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = null;
                varquery = Query.EQ("source", Source);
                var documents = _CommDB.GetDocuments<SysConfigData>(varquery, MongoCollection.ConfigValues());
                return documents;
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetConfiValueFromMongo." + ex.ToString() + "Source "+ Source);
                return null;
            }
        }

        public static Dictionary<string, SysConfigData> GetConfiValueFromMongo()
        {
            string result = string.Empty;
            try
            {
                IMongoFields Field = Fields.Include("source", "authKey", "clientKey", "EncKey", "EncIV");
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var data = objCommDB.GetDocuments<SysConfigData>(null, Field, MongoCollection.ConfigValues());

                //Dictionary<string, SysConfigData> dictionary = data.ToDictionary(doc => doc.source.ToLower());

                Dictionary<string, SysConfigData> dictionary = data
                .GroupBy(data => data.source.ToLower())
                .ToDictionary(group => group.Key, group => group.First());

                return dictionary;
            }
            catch (Exception ex)
            {
                // Console.WriteLine("Exception in GetConfiValueFromMongo." + ex.ToString());
                return null;
            }
        }
        public static Dictionary<string, object> GetConfiFromMongo_SV(string key)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();
            try
            {
                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.EQ("_id", key);
                var value = objCommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.ConfigFiles());
                result = (Dictionary<string, object>)BsonTypeMapper.MapToDotNetValue(value);
                return result;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static void UpdateDocument(IMongoQuery query, IMongoUpdate updateQuery)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            objCommDB.UpdateDocument(query, updateQuery, DataAccessLibrary.MongoCollection.LPDataCollection());
        }
        public static bool IsValidateCustomer(string EncryptLeadId, string Token)
        {
            bool result = false;
            StringBuilder sb = new();

            try
            {
                sb.Append("EncryptLeadId: " + EncryptLeadId + ", Token= " + Token + "\r\n");
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.And(Query.EQ("encryptLeadId", EncryptLeadId), Query.EQ("Token", Convert.ToInt64(Token)));
                IMongoFields Field = Fields.Include("encryptLeadId", "Token");
                var value = _CommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.CustomerAuthenticate(), Field);
                if (value != null)
                    result = true;

            }
            catch (Exception ex)
            {
                result = false;
                LoggingHelper.LoggingHelper.AddloginQueue(Token, 0, ex.ToString(), "IsValidateCustomer", "MatrixCore", "MongoDLL", sb.ToString(), "", DateTime.Now, DateTime.Now);
            }
            return result;
        }
        public static long getExistRecord(string EncryptLeadId)
        {
            long result = 0;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                IMongoQuery varquery = Query.And(Query.EQ("_id", EncryptLeadId));
                IMongoFields Field = Fields.Include("_id", "Token");
                var value = _CommDB.FindOneDocument<BsonDocument>(varquery, MongoCollection.CustomerAuthenticate(), Field);
                if (value != null)
                    result = Convert.ToInt64(value.GetValue("Token"));

            }
            catch (Exception ex)
            {
                return result;
            }
            return result;
        }
        public static PriorityModel GetPriorityModelMongo(Int64 LeadId)
        {
            List<PriorityModel> oPriorityModel = new();
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = Query<PriorityModel>.EQ(p => p.LeadID, LeadId);
                var lstPriorityModel = _CommDB.GetDocuments<PriorityModel>(query, MongoCollection.LPDataCollection()).ToList();
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                    return lstPriorityModel[0];
                else return null;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(LeadId.ToString(), LeadId, ex.ToString(), "GetPriorityModelMongo_Error", "LeadPrioritizationDLL", "", "", "", DateTime.Now, DateTime.Now);
                return null;
            }
        }
        public static ACLConfigData FindACLMethod(string Method)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            IMongoQuery varquery = Query.And(Query.EQ("method", Method), Query.EQ("isActive", true));
            return objCommDB.FindOneDocument<ACLConfigData>(varquery, MongoCollection.ACLConfigValues());

        }
        public static List<ACLConfigData> GetACLMongoConfig(IMongoFields Field, IMongoQuery varquery)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var data = objCommDB.GetDocuments<ACLConfigData>(varquery, Field, MongoCollection.ACLConfigValues());
            return data;

        }

        public static Dictionary<string, object> GetSVConfigfromMongoV2(string Collection)
        {
            Dictionary<string, object> result = new Dictionary<string, object>();
            string CollectionName = "";
            try
            {

                MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                if (Collection.Equals("SVConfig"))
                {
                    CollectionName = MongoCollection.SVConfig();
                }
                var configs = objCommDB.FindAllDocument<BsonDocument>(CollectionName);

                foreach (var config in configs)
                {
                    var key = config.GetValue("key", null)?.AsString;
                    var value = BsonTypeMapper.MapToDotNetValue(config.GetValue("value", BsonNull.Value));

                    if (!string.IsNullOrEmpty(key))
                    {
                        result[key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                return null;
            }

            return result;
        }

    }
}
