﻿using DataHelper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using PropertyLayers;
using Helper;
using MongoDB.Driver.Builders;
using MongoDB.Driver;
using MongoDB.Bson;

namespace DataAccessLibrary
{
    public class MTXPlusLoginDLL
    {
        public static DataSet GetLDapEmployeeDetails(string UserName)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@EmployeeID", UserName);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[GetLDapEmployeeDetails]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data;
            else
                return null;
        }
        public static bool UpdateUserExpPassword(string UserName, DateTime? PassExpiryDate)
        {
            bool result = false;
            try
            {
                var sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@EmployeeId", UserName);
                sqlParam[1] = new SqlParameter("@PassExpiryDate", PassExpiryDate);
                SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(),CommandType.StoredProcedure,"[MTX].[UpdateUserExpPassword]",sqlParam);
                result = true;
            }
            catch(Exception ex)
            {
                result = false;
                LoggingHelper.LoggingHelper.Log(UserName.ToString(), 0, ex.ToString(), "UpdateUserExpPassword", "MTXPlusLoginDLL", "MatrixCoreAPI", PassExpiryDate.ToString(), "", DateTime.Now, DateTime.Now);
            }
            return result;
        }
        public static long Check_LoginForLdapUser(string employeeID, string password, string IpAddress, bool IsLdapEnabled)
        {
            long result = 0;
            try
            {
                var sqlParam = new SqlParameter[6];
                sqlParam[0] = new SqlParameter("@EmpId", employeeID);
                sqlParam[1] = new SqlParameter("@Password", password);
                sqlParam[2] = new SqlParameter("@CreatedOn", DateTime.Now);
                sqlParam[3] = new SqlParameter("@Active", true);
                sqlParam[4] = new SqlParameter("@IPADDRESS", IpAddress);
                sqlParam[5] = new SqlParameter("@IsLdapEnabled", IsLdapEnabled);

                result = Convert.ToInt64(SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[LDAP_UserLogin]", sqlParam));
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(employeeID.ToString(), 0, ex.ToString(), "Check_LoginForLdapUser", "MTXPlusLoginDLL", "MatrixCoreAPI", IpAddress.ToString(), IsLdapEnabled.ToString(), DateTime.Now, DateTime.Now);
            }
            return result;

        }

        public static DataTable GetBasicUserInfo(long UserID)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", UserID);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[GetBasicUserInfo]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }

        public static DataSet SetLoggedinUserDetails(MTXPlusLogInDTO objLogin, out int status, out string message)
        {
            status = 0;
            message = string.Empty;
            DataSet ds = null;

            try
            {
                var sqlParam = new SqlParameter[13];
                sqlParam[0] = new SqlParameter("@EmpId", objLogin.EmployeeId);
                sqlParam[1] = new SqlParameter("@UPassword", objLogin.Password);
                sqlParam[2] = new SqlParameter("@IpAddress", objLogin.IPAddress);
                sqlParam[3] = new SqlParameter("@LogInBy", objLogin.LogInBy);
                sqlParam[4] = new SqlParameter("@LogInType", objLogin.LogInType);
                sqlParam[5] = new SqlParameter("@LogInURL", objLogin.URL);
                sqlParam[6] = new SqlParameter("@SessionId", objLogin.SessionId);
                sqlParam[7] = new SqlParameter("@Device", objLogin.Device);
                sqlParam[8] = new SqlParameter("@Resolution", objLogin.Resolution);
                sqlParam[9] = new SqlParameter("@DSpeed", objLogin.DSpeed);
                sqlParam[10] = new SqlParameter("@IsLDapEnabled", true);
                sqlParam[11] = new SqlParameter("@ret", SqlDbType.Int) { Direction = ParameterDirection.Output };

                sqlParam[11].Direction = ParameterDirection.Output;
                sqlParam[11].Size = 1;

                sqlParam[12] = new SqlParameter("@message", SqlDbType.VarChar) { Direction = ParameterDirection.Output };
                sqlParam[12].Size = 200;

                SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(),CommandType.StoredProcedure,"[MTX].[SetUserLogin]",sqlParam);

                status = Convert.ToInt32(sqlParam[11].Value);
                message = Convert.ToString(sqlParam[12].Value);
            }
            catch (Exception ex)
            {
                status = -1; // Indicate failure
                message = "An error occurred";
                LoggingHelper.LoggingHelper.Log(objLogin.EmployeeId, 0, ex.ToString(), "SetLoggedinUserDetails", "MTXPlusLoginDLL", "MatrixCoreAPI", objLogin.SessionId, objLogin.Device, DateTime.Now, DateTime.Now);
            }

            return ds;
        }

        public static bool UpdateLoginDetails(MTXPlusLogInDTO objLogin)
        {
            bool res = false;
            try
            {
                var sqlParam = new SqlParameter[7];
                sqlParam[0] = new SqlParameter("@UserId", objLogin.UserId);
                sqlParam[1] = new SqlParameter("@IsActive", objLogin.IsActive);
                sqlParam[2] = new SqlParameter("@LogOutBy", objLogin.LogOutBy);
                sqlParam[3] = new SqlParameter("@LogOutType", objLogin.LogOutType);
                sqlParam[4] = new SqlParameter("@Url", objLogin.URL);
                sqlParam[5] = new SqlParameter("@IpAddress", objLogin.IPAddress);
                sqlParam[6] = new SqlParameter("@LogoutButtonId", objLogin.LoginButtonId);

                SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[CRM].[Update_LoginDetails]", sqlParam);
                res = true;
            }
            catch
            {
                res = false;
            }
            return res;
        }

        public static DataSet GetUserDetails(long UserID)
        {
            DataSet ds = null;
            try
            {
                var sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserID);

                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),CommandType.StoredProcedure,"[CRM].[GetUserInfo]",sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", UserID, ex.ToString(), "GetUserDetails", "MTXPlusLoginDLL", "MatrixCoreAPI", UserID.ToString(), "", DateTime.Now, DateTime.Now);
            }
            return ds;
        }
        public static bool GetUpdateUserLogoutTime(long userId, int eventType, bool updateTime)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@UserId", userId);
            sqlParam[1] = new SqlParameter("@LogoutType", eventType);
            sqlParam[2] = new SqlParameter("@UpdateTime", updateTime);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUpdateUserLogoutTime]", sqlParam);

            if (result != null)
                return Convert.ToBoolean(result);
            else
                return false;
        }

        public static DataSet GetSurveyByLocation(string surveyLocation)
        {
            DataSet ds = null;
            try
            {
                var sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@SurveyLocation", surveyLocation);

                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetSurveyByLocation", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(surveyLocation, 0, ex.ToString(), "GetSurveyByLocation", "MTXPlusLoginDLL", "MatrixCoreAPI", "", "", DateTime.Now, DateTime.Now);
            }
            return ds;
        }

        public static DataSet GetPreLoginSurveyRequirement(long userId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserId", userId);
            sqlParam[1] = new SqlParameter("@IsBMS", false);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetPreloginSurvey]", sqlParam);
        }

        public static void InsertEmployeeSurvey(PreLoginSurveyResult obj)
        {
            var sqlParam = new SqlParameter[7];
            sqlParam[0] = new SqlParameter("@UserId", obj.UserId);
            sqlParam[1] = new SqlParameter("@SurveyId", obj.SurveyId);
            sqlParam[2] = new SqlParameter("@EmployeeId", obj.EmployeeId);
            sqlParam[3] = new SqlParameter("@Response", obj.Response);
            sqlParam[4] = new SqlParameter("@CreatedOn", obj.CreatedOn);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertEmployeeSurvey]", sqlParam);
        }
        public static DataTable IsBizRatingDashboardVisible(long userId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", userId);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                       CommandType.StoredProcedure,
                                                       "[MTX].[IsInforcementRatingVisible]",
                                                       sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }
        public static List<int> GetUserPinnedItems(Int64 UserID)
        {
            List<int> pinnedMenus = null;
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = Query.EQ("UserID", UserID);
                IMongoFields fields = Fields.Exclude("_id");
                List<PinnedMenuItems> objPinnedMenuItems = _CommDB.GetDocuments<PinnedMenuItems>(query, MongoCollection.PinnedMenuItems(), SortBy.Descending("UserID"), fields, 0, 1).ToList();
                if(objPinnedMenuItems!=null && objPinnedMenuItems.Count>0)
                {
                    pinnedMenus = objPinnedMenuItems[0].PinnedMenus;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log("", UserID, ex.ToString(), "GetUserPinnedItems", "MTXPlusLoginDLL", "MatrixCoreAPI", "", "", DateTime.Now, DateTime.Now);
            }
            return pinnedMenus;
        }
        public static void UpdateUserPinnedMenu(long userId, int menuId, bool isPin)
        {
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = Query.EQ("UserID", userId);
                IMongoFields fields = Fields.Exclude("_id");
                var userPinned = _CommDB.GetDocuments<PinnedMenuItems>(query, MongoCollection.PinnedMenuItems(), SortBy.Descending("UserID"), fields, 0, 1).FirstOrDefault();

                if (userPinned == null)
                {
                    // Create new document if not exists and pin
                    if (isPin)
                    {
                        var newPinned = new PinnedMenuItems
                        {
                            UserID = userId,
                            PinnedMenus = new List<int> { menuId }
                        };
                        _CommDB.InsertData(newPinned, MongoCollection.PinnedMenuItems());
                    }
                }
                else
                {
                    var pinnedMenus = userPinned.PinnedMenus ?? new List<int>();
                    if (isPin)
                    {
                        if (!pinnedMenus.Contains(menuId))
                            pinnedMenus.Add(menuId);
                    }
                    else
                    {
                        pinnedMenus.Remove(menuId);
                    }
                    var update = Update.Set("PinnedMenus", new BsonArray(pinnedMenus)); // Convert List<int> to BsonArray
                    _CommDB.UpdateDocument(query, update, MongoCollection.PinnedMenuItems());
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(userId.ToString(), 0, ex.ToString(), "UpdateUserPinnedMenu", "MTXPlusLoginDLL", "MatrixCoreAPI", menuId.ToString(), isPin.ToString(), DateTime.Now, DateTime.Now);
            }
        }
    }
}
