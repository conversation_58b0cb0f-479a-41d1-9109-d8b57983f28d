﻿using DataHelper;
using System;
using System.Data;
using System.Data.SqlClient;
using PropertyLayers;
using MongoDB.Driver.Builders;
using Helper;

namespace DataAccessLibrary
{
    public class SMEDLL
    {

        public static int ReAssignLead(AllocationDetails _AllocationDetails)
        {
            _AllocationDetails.AllocationTrackingEntryFlag = 1;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[10];
            SqlParam[0] = new SqlParameter("@AssignedTo_AgentId", _AllocationDetails.AssigntoUserID);
            SqlParam[1] = new SqlParameter("@AssignedBy_AgentId", _AllocationDetails.AssignbyUserID);
            SqlParam[2] = new SqlParameter("@ProductId", _AllocationDetails.ProductID);
            SqlParam[3] = new SqlParameter("@LeadId", _AllocationDetails.LeadID);
            SqlParam[4] = new SqlParameter("@GroupId", _AllocationDetails.GroupID);
            SqlParam[5] = new SqlParameter("@Flag", _AllocationDetails.AllocationTrackingEntryFlag);
            SqlParam[6] = new SqlParameter("@JobId", _AllocationDetails.JobID);
            SqlParam[7] = new SqlParameter("@FirstSelectedPlanId", _AllocationDetails.InsurerID);
            SqlParam[8] = new SqlParameter("@SelectionCount", _AllocationDetails.SelectionCount);
            SqlParam[9] = new SqlParameter("@LeadRank", _AllocationDetails.LeadRank);
            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[CRM].[Insert_AssignedToAgent]", SqlParam);
        }

        public static bool UnAssignLead(long leadId, Int16 grouPID, Int64 AssignedTo = 0)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var query = (Query<PriorityModel>.EQ(p => p.LeadID, leadId));
            UpdateBuilder<PriorityModel> update = Update<PriorityModel>.Set(x => x.User.AssignedOn, DateTime.Now).Set(x => x.User.UserID, AssignedTo).Set(x => x.User.GroupId, grouPID);
            _CommDB.UpdateDocument(query, update, MongoCollection.LPDataCollection());
            return true;
        }
        public static DataSet GetSMETransferSubProducts()
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[0];
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.GetSMETransferSubProducts", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

        }
        public static DataSet getGroupNameandID(Int64 UserId)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                string query = "SELECT TOP 1 UserGroupID,UserGroupName,ProcessID, ISNULL(UDM.IsSOS,0) AS IsSOS FROM CRM.UserGroupMaster(NOLOCK) UDM INNER JOIN CRM.UserGroupRoleMapNew(NOLOCK) UGR ON UDM.UserGroupID=UGR.GroupId WHERE UGR.UserId=@UserId";
                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.Text, query, sqlParam);
            }
            catch (Exception ex)
            {

            }

            return ds;
        }
        public static DataSet GetSmeIndustryTypes()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSmeIndustryType]");
        }
        public static DataSet GetMyLeadsData(long userId, long LeadId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserId", userId);
            if (LeadId > 0)
            {
                sqlParam[1] = new SqlParameter("@LeadId", LeadId);
            }
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetSmeMyLeadsData]", sqlParam);
        }

        public static DataSet GetMyRenewalData(long userId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", userId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetSmeMyRenewalData]", sqlParam);
        }

        public static DataSet GetSmeInsurerMaster(int type)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@Type", type);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSmeInsurerMaster]", sqlParam);
        }
        public static DataSet UpdateSmeLeadDetails(MyLeadData data, long UserId)
        {
            SqlParameter[] sqlParam = new SqlParameter[32];
            sqlParam[0] = new SqlParameter("@LeadID", data.LeadId);
            sqlParam[1] = new SqlParameter("@ContactPersonName", data.Name);
            if (data.AltContactInformation != null && data.AltContactInformation.Count > 0)
            {
                var info = CoreCommonMethods.SerializeToXml(data.AltContactInformation);
                if (!string.IsNullOrEmpty(info))
                {
                    sqlParam[2] = new SqlParameter("@AltContactInformation", info);
                }
            }
            sqlParam[3] = new SqlParameter("@OtherExistingInsurer", data.OtherExistingInsurer);
            sqlParam[4] = new SqlParameter("@LinkedinConnection", data.LinkedinConnection);
            sqlParam[5] = new SqlParameter("@LinkedinLink", data.LinkedinLink);
            sqlParam[6] = new SqlParameter("@ClientCityId", data.ClientCityId);
            sqlParam[7] = new SqlParameter("@UpdateLeadDetails", 1);
            if (data.PolicyStartDate != null && data.PolicyStartDate > 0 && DateTime.TryParse(Convert.ToInt64(data.PolicyStartDate).ToDateTime().ToString(), out DateTime dt))
            {
                sqlParam[8] = new SqlParameter("@PolicyStartDate", dt);
            }
            sqlParam[9] = new SqlParameter("@PolicyTypeId", data.PolicyType);
            sqlParam[10] = new SqlParameter("@Premium", data.Premium);
            sqlParam[11] = new SqlParameter("@IndustryTypeId", data.IndustryTypeId);
            sqlParam[12] = new SqlParameter("@ClaimHistory", data.ClaimHistory);
            sqlParam[13] = new SqlParameter("@ExistingBroker", data.ExistingBroker);
            sqlParam[14] = new SqlParameter("@ExistingInsurer", data.ExistingInsurer);
            sqlParam[15] = new SqlParameter("@ExistingTPA", data.ExistingTPA);
            sqlParam[16] = new SqlParameter("@Probability", data.Probability);
            sqlParam[17] = new SqlParameter("@SumInsured", data.SumInsured);
            sqlParam[18] = new SqlParameter("@NoOfLives", data.NoOfLives);
            sqlParam[19] = new SqlParameter("@UtmSource", data.UtmSource);
            sqlParam[20] = new SqlParameter("@CrossSellSubProductIds", data.CrossSellSubProductIds);
            sqlParam[21] = new SqlParameter("@WinningStrategy", data.WinningStrategy);
            sqlParam[22] = new SqlParameter("@UtmMedium", data.UtmMedium);
            sqlParam[23] = new SqlParameter("@CreatedBy", UserId);
            sqlParam[24] = new SqlParameter("@CIN", data.CIN);
            sqlParam[25] = new SqlParameter("@DecisionMakerCityId", data.DecisionMakerCityId);
            sqlParam[26] = new SqlParameter("@ParentCompany", data.ParentCompany);
            sqlParam[27] = new SqlParameter("@ExecutiveRole", data.ExecutiveRole);
            sqlParam[28] = new SqlParameter("@CompanyName", data.CompanyName);
            sqlParam[29] = new SqlParameter("@OtherExisitngTPA", data.OtherExistingTPA);
            sqlParam[30] = new SqlParameter("@OtherExistingBroker", data.OtherExistingBroker);
            sqlParam[31] = new SqlParameter("@SubCIN", data.SubCIN);

            var ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertUpdateSMELeadDetails]", sqlParam);
            return ds;
        }
        public static bool CreateSmeMom(MOMData data, long UserId)
        {
            bool result = false;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[14];
                sqlParam[0] = new SqlParameter("@CustomerID", data.CustomerId);
                sqlParam[1] = new SqlParameter("@LeadID", data.LeadId);
                sqlParam[2] = new SqlParameter("@MeetingDate", data.MeetingDate);
                sqlParam[3] = new SqlParameter("@FollowUpDate", data.FollowUpDate);
                sqlParam[4] = new SqlParameter("@MeetingType", data.MeetingType);
                sqlParam[5] = new SqlParameter("@CityId", data.CityId);
                sqlParam[6] = new SqlParameter("@MeetingStatus", data.MeetingStatus);
                sqlParam[7] = new SqlParameter("@ClientName", data.ClientName);
                sqlParam[8] = new SqlParameter("@ClientDesignation", data.ClientDesignation);
                sqlParam[9] = new SqlParameter("@MeetingAgenda", data.MeetingAgenda);
                sqlParam[10] = new SqlParameter("@PbAttendees", data.PbAttendees);
                sqlParam[11] = new SqlParameter("@CreatedBy", UserId);
                sqlParam[12] = new SqlParameter("@LOB", data.LOB);
                sqlParam[13] = new SqlParameter("@CallPriority", data.CallPriority);


                int ds = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertUpdateSmeMom]", sqlParam);
                if (ds > 0)
                {
                    result = true;
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }
        public static DataSet GetSmeMomData(long CustomerId, long UserId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@CustomerId", CustomerId);
            sqlParam[1] = new SqlParameter("@UserId", UserId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetSmeMomDetails]", sqlParam);
        }
        public static bool SaveSmeFeedback(FeedbackData data)
        {
            bool result = false;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@Message", data.Feedback);
                sqlParam[1] = new SqlParameter("@UserId", Convert.ToInt64(data.UserId));
                sqlParam[2] = new SqlParameter("@ProcessType", "Feedback");
                sqlParam[3] = new SqlParameter("@LeadId", 0);

                int ds = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SME_PRBCallbackLogs]", sqlParam);
                if (ds == -1)
                {
                    result = true;
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }
        public static DataSet GetAgentsByType(string AgentType)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@AgentType", AgentType);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentsByType]", sqlParam);
        }

        public static DataSet GetMobileNo(long LeadID, long UserId, int ActionType, long CustomerId, long PrimaryId)
        {
            SqlParameter[] sqlParam = new SqlParameter[5];
            sqlParam[0] = new SqlParameter("@LeadId", LeadID);
            sqlParam[1] = new SqlParameter("@UserId", UserId);
            sqlParam[2] = new SqlParameter("@Action", ActionType);
            sqlParam[3] = new SqlParameter("@CustomerId", CustomerId);
            sqlParam[4] = new SqlParameter("@PrimaryId", PrimaryId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure,
                "[MTX].[GetMobileNoForSme]", sqlParam);
        }

        public static DataSet GetCustomerLeadBySubProduct(long custId, int subProductId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@CustId", custId);
            sqlParam[1] = new SqlParameter("@SubProductId", subProductId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[SME].[GetCustomerLeadBySubProduct]", sqlParam);
        }

        public static DataSet GetCustomerOpenLeads(long CustomerId, int SubProductId, long UserId)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@SubProductId", SubProductId);
            sqlParam[1] = new SqlParameter("@UserId", UserId);
            sqlParam[2] = new SqlParameter("@CustomerId", CustomerId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerOpenLeads]", sqlParam);
        }
    }
}