using DataAccessLibrary;
using DataHelper;
using MongoDB.Driver;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using MongoDB.Driver.Builders;
using Microsoft.Extensions.Configuration;
using Amazon.Auth.AccessControlPolicy;

namespace DataAccessLayer
{
    public class FOSDLL
    {


        public static bool SaveLeadsSubStatus(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[8];
                SqlParam[0] = new SqlParameter("@CustomerID", oLeadsSubStatusModel.CustomerID);
                SqlParam[1] = new SqlParameter("@result", 0);
                SqlParam[2] = new SqlParameter("@LeadID", oLeadsSubStatusModel.LeadID);
                SqlParam[3] = new SqlParameter("@StatusId", oLeadsSubStatusModel.StatusId);
                SqlParam[4] = new SqlParameter("@SubStatusId", oLeadsSubStatusModel.SubStatusId);
                SqlParam[5] = new SqlParameter("@UserID", oLeadsSubStatusModel.UserID);
                SqlParam[6] = new SqlParameter("@Type", 2);
                SqlParam[7] = new SqlParameter("@IsLogSubStatus", oLeadsSubStatusModel.IsLogSubStatus);
                //Read Param  from sql

                SqlParam[1].Direction = ParameterDirection.Output;
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetSetSMEStatus]", SqlParam);
                Int16 val = Convert.ToInt16(SqlParam[1].Value);
                if (val == 1)
                {

                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static DataSet GetAgentAppointments(Int64 UserID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserID);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetAgentAppointments", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }

        public static DataSet GetFOSCustInfo(Int64 LeadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", LeadId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetFOSCustInfo", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }

        public static DataSet GetFOSActivityHistory(Int64 LeadId, Int64 AppointmentId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@LeadId", LeadId);
                sqlParam[1] = new SqlParameter("@AppointmentId", AppointmentId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetFOSActivityHistory", sqlParam);
            }
            catch (Exception e)
            {
                return null;
            }
        }

        public static short GetProductId(Int64 LeadID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];

                string query = "SELECT TOP 1 ProductID FROM MATRIX.CRM.Leaddetails PRD (NOLOCK) WHERE LeadID=@LeadID";
                sqlParam[0] = new SqlParameter("@LeadID", LeadID);

                object obj = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.Text, query, sqlParam);
                if (obj != null)
                    return Convert.ToInt16(obj);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "GetSMEInvestmentId_error", "GetSMEInvestmentId_error", "LeadPrioritizationDLL", "", "", DateTime.Now, DateTime.Now);
            }

            return Convert.ToInt16(0);
        }

        public static bool SetFOSComment(AppointmentsDataModel oAppointmentDataModel)
        {
            try
            {
                string connection = ConnectionClass.LivesqlConnection();
                SqlParameter[] SqlParam = new SqlParameter[7];
                SqlParam[0] = new SqlParameter("@CustomerId", oAppointmentDataModel.CustomerId);
                SqlParam[1] = new SqlParameter("@ProductId", oAppointmentDataModel.ProductId);
                SqlParam[2] = new SqlParameter("@ParentLeadId", oAppointmentDataModel.ParentId);
                SqlParam[3] = new SqlParameter("@PrimaryLeadId", oAppointmentDataModel.ParentId);
                SqlParam[4] = new SqlParameter("@Comment", oAppointmentDataModel.Comments);
                SqlParam[5] = new SqlParameter("@UserId", oAppointmentDataModel.UserId);
                SqlParam[6] = new SqlParameter("@EventType", 48);

                var result = SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, "[MTX].[SaveComment_CV]", SqlParam);

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public static bool MarkAttendance(LogInDTO oLogInDTO)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[6];

                SqlParam[0] = new SqlParameter("@LogInBy", oLogInDTO.LogInBy);
                SqlParam[1] = new SqlParameter("@LogInType", 3);//self login
                SqlParam[2] = new SqlParameter("@LogInURL", "FOSApp");
                SqlParam[3] = new SqlParameter("@Latitude", oLogInDTO.Latitude);
                SqlParam[4] = new SqlParameter("@Longitude", oLogInDTO.Longitude);
                SqlParam[5] = new SqlParameter("@Device", oLogInDTO.Device);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[MarkAttendance]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static bool SaveFOSAppLatAndLongitude(LeadsSubStatusModel oLeadsSubStatusModel)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[5];

                SqlParam[0] = new SqlParameter("@LeadId", oLeadsSubStatusModel.LeadID);
                SqlParam[1] = new SqlParameter("@Latitude", oLeadsSubStatusModel.Latitude);
                SqlParam[2] = new SqlParameter("@Longitude", oLeadsSubStatusModel.Longitude);
                SqlParam[3] = new SqlParameter("@Type", oLeadsSubStatusModel.Type);
                SqlParam[4] = new SqlParameter("@UserId", oLeadsSubStatusModel.UserID);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[SaveFOSAppLatAndLongitude]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static bool IsMarkAttendance(Int64 UserId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@UserId", UserId);

            var DS = SqlHelper.ExecuteDataset(Connectionstring, CommandType.StoredProcedure, "[MTX].[IsUserMarkAttendance]", SqlParam);
            if (DS != null && DS.Tables.Count > 0 && DS.Tables[0].Rows.Count > 0)
            {
                if (Convert.ToBoolean(DS.Tables[0].Rows[0]["IsUserMarkAttendance"]) == true)
                    return true;
                else
                    return false;
            }
            else
                return false;
        }

        public static bool SetCustomerComment(LeadsSubStatusModel oLeadsSubStatusModel, Int16 EventTypeId, string Comment, Int16? ProductId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[7];
                SqlParam[0] = new SqlParameter("@CustomerID", oLeadsSubStatusModel.CustomerID);
                SqlParam[1] = new SqlParameter("@ProductID", ProductId);
                SqlParam[2] = new SqlParameter("@ParentLeadId", oLeadsSubStatusModel.LeadID);
                SqlParam[3] = new SqlParameter("@PrimaryLeadId", oLeadsSubStatusModel.LeadID);
                SqlParam[4] = new SqlParameter("@Comment", Comment);
                SqlParam[5] = new SqlParameter("@UserId", oLeadsSubStatusModel.UserID);
                SqlParam[6] = new SqlParameter("@EventType", EventTypeId);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[SaveComment_CV]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static bool SaveComments(CommentModal oCommentModal)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[7];
                SqlParam[0] = new SqlParameter("@CustomerID", oCommentModal.CustomerID);
                SqlParam[1] = new SqlParameter("@ProductID", oCommentModal.ProductId);
                SqlParam[2] = new SqlParameter("@ParentLeadId", oCommentModal.LeadID);
                SqlParam[3] = new SqlParameter("@PrimaryLeadId", oCommentModal.LeadID);
                SqlParam[4] = new SqlParameter("@Comment", oCommentModal.Comment);
                SqlParam[5] = new SqlParameter("@UserId", oCommentModal.UserID);
                SqlParam[6] = new SqlParameter("@EventType", oCommentModal.EventTypeId);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[SaveComment_CV]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }

        }

        public static bool SetAppointmentSubStatus(PriorityModel AppModel, bool IsAppDone, string Source)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();

            try
            {
                SqlParameter[] SqlParam = new SqlParameter[5];
                SqlParam[0] = new SqlParameter("@LeadID", AppModel.LeadID);
                SqlParam[1] = new SqlParameter("@SubStatusId", AppModel.Appointment.StatusID);
                SqlParam[2] = new SqlParameter("@UserID", AppModel.Appointment.UserID);
                SqlParam[3] = new SqlParameter("@IsAppDone", IsAppDone);
                SqlParam[4] = new SqlParameter("@Source", Source);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[SetAppointmentsubstatus]", SqlParam);

            }
            catch (Exception ex)
            {
                return false;
            }
            return true;
        }

        public static DataSet GetAppointmentDetails(string CustomerId, string ParentId, string AppointmentId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@CustomerId ", Convert.ToInt64(CustomerId));
                sqlParam[1] = new SqlParameter("@LeadId", Convert.ToInt64(ParentId));
                sqlParam[2] = new SqlParameter("@AppointmentId", Convert.ToInt64(AppointmentId));
                sqlParam[3] = new SqlParameter("@Type", 0);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAppointmentDataForApp]", sqlParam);


            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }


        public static DataSet GetUserDetails(string empId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.ReplicasqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@empId ", empId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetUserInfo]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

        }


        public static DataSet GetAgentType(long UserId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.ReplicasqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId ", UserId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentType]", sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }
        public static DataSet GetReminderData(ReminderModal objReminderModal)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.ReplicasqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@UserId", Convert.ToInt64(objReminderModal.UserId));
                sqlParam[1] = new SqlParameter("@startDate", Convert.ToDateTime(objReminderModal.startDate));
                sqlParam[2] = new SqlParameter("@EndDate", Convert.ToDateTime(objReminderModal.EndDate));

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[FOS_GetReminderData]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }

        public static bool SetReminderData(ReminderModal oReminderModal)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[6];
                SqlParam[0] = new SqlParameter("@AppointmentId", oReminderModal.AppointmentId);
                SqlParam[1] = new SqlParameter("@Message", oReminderModal.Description);
                SqlParam[2] = new SqlParameter("@ReminderTopic", oReminderModal.ReminderTopic);
                SqlParam[3] = new SqlParameter("@UserId", oReminderModal.UserId);
                SqlParam[4] = new SqlParameter("@FosReminderId", oReminderModal.FosReminderId);
                SqlParam[5] = new SqlParameter("@ReminderDateTime", oReminderModal.ReminderDateTime);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[FOS_SetReminderData]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in SetReminderData in DLL." + ex.ToString());
                return false;
            }
        }
        public static bool UpdateReminderData(ReminderUpdateModal oReminderModal)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[4];
                SqlParam[0] = new SqlParameter("@FosReminderId", oReminderModal.FosReminderId);
                SqlParam[1] = new SqlParameter("@IsDelete", oReminderModal.IsDelete);
                SqlParam[2] = new SqlParameter("@IsMarked", oReminderModal.IsMarked);
                SqlParam[3] = new SqlParameter("@UserId", oReminderModal.UserId);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[FOS_UpdateReminderData]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                //Console.WriteLine("Exception in SetReminderData in DLL." + ex.ToString());
                return false;
            }
        }

        public static DataSet GetValidUser(string EmployeeID, string password)
        {

            DataSet ds = new DataSet();
            string Connectionstring = ConnectionClass.ReplicasqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@EmpId", EmployeeID);
                sqlParam[1] = new SqlParameter("@Password", password);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUserLoginValidate]", sqlParam);

            }
            catch (SqlException ex)
            {
                ds = null;
            }
            return ds;
        }
        public static DataSet GetAgentCommments(string LeadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@CustomerId ", 0);
                sqlParam[1] = new SqlParameter("@ProductId ", 0);
                sqlParam[2] = new SqlParameter("@Leads ", LeadId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerComment_CV]", sqlParam);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(LeadId), ex.ToString(), "GetAgentCommments", "MatrixCore", "FOSDll", LeadId.ToString(), "", DateTime.Now, DateTime.Now);
                return null;
            }

        }

        public static bool UpdateAppLocation(AppointmentLocation oAppLocation)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[9];

                SqlParam[0] = new SqlParameter("@AppointmentId", oAppLocation.AppointmentId);
                SqlParam[1] = new SqlParameter("@SubStatusId", oAppLocation.SubStatusId);
                SqlParam[2] = new SqlParameter("@Latitude", oAppLocation.Lat);
                SqlParam[3] = new SqlParameter("@Longitude", oAppLocation.Long);
                SqlParam[4] = new SqlParameter("@Distance", oAppLocation.Distance);
                SqlParam[5] = new SqlParameter("@DeviceId", oAppLocation.DeviceId);
                SqlParam[6] = new SqlParameter("@UserId", oAppLocation.UserId);
                SqlParam[7] = new SqlParameter("@Type", oAppLocation.Type);
                SqlParam[8] = new SqlParameter("@Source", oAppLocation.source);

                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[FOS].[SetAppointmentLocations]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", oAppLocation.AppointmentId, ex.ToString(), "UpdateAppLocation", "MatrixCore", "FOSDLL", oAppLocation.AppointmentId.ToString(), "", DateTime.Now, DateTime.Now);
                return false;
            }

        }




        public static DataSet GetLeadAttributes(long UserId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId ", UserId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetLeadAttributes]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }
        public static DataSet GetAppointnmentLocations(long AppointmentId, Int16 Type)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@AppointmentId ", AppointmentId);
                sqlParam[1] = new SqlParameter("@Type ", Type);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAppointnmentLocations]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return oDataSet;
        }


        public static DataSet GetActivityHistory(string LeadId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;

            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@Leads ", LeadId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetActivityHistory]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet getCancallationReason(Int16 Id)
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@Id", Id);

                string query = "select Top 1 Reason from MTX.ReasonMaster(nolock) where id=@Id";
                ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.Text, query, sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

            return ds;
        }

        public static bool Logout(LogInDTO oLogInDTO)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[7];

                SqlParam[0] = new SqlParameter("@UserId", oLogInDTO.LogInBy);
                SqlParam[1] = new SqlParameter("@IsActive", false);
                SqlParam[2] = new SqlParameter("@LogOutBy", oLogInDTO.LogInBy);
                SqlParam[3] = new SqlParameter("@LogOutType", oLogInDTO.LogOutType);
                SqlParam[4] = new SqlParameter("@Url", "FOSApp");
                SqlParam[5] = new SqlParameter("@IpAddress", null);
                SqlParam[6] = new SqlParameter("@LogoutButtonId", null);
                SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[CRM].[Update_LoginDetails]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public static DataSet GetCancelReasonMaster()
        {
            DataSet ds = null;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[0];
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetCancelReasonMaster]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
            return ds;
        }



        public static bool SavePlacecodeLatLongMaster(PlaceLatLongModel placeLatLongModel)
        {
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@PlaceId", placeLatLongModel.place_id);
                SqlParam[1] = new SqlParameter("@lat", placeLatLongModel.Lat);
                SqlParam[2] = new SqlParameter("@long", placeLatLongModel.Long);
                var result = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SavePlaceLatLongMapping]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }



        public static DataSet GetPlaceLatLongMaster()
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;

            try
            {
                SqlParameter[] sqlParam = new SqlParameter[0];
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetPlaceLatLongMaster]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetLeadInfo(Int64 CustomerID)
        {
            DataSet oDataSet = null;
            DateTime requestTime = DateTime.Now;

            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@CustomerID", CustomerID);

                oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerActiveLeads]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustomerID, ex.ToString(), "GetLeadInfo", "MatrixCore", "FOSBLL", string.Empty, string.Empty, requestTime, DateTime.Now);
            }

            return oDataSet;
        }

        public static DataSet GetLeadDetails(Int64 LeadId)
        {
            DataSet oDataSet = null;
            DateTime requestTime = DateTime.Now;

            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", LeadId);

                oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetLeadDetails]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.ToString(), "GetLeadDetails", "MatrixCore", "FOSBLL", string.Empty, string.Empty, requestTime, DateTime.Now);
            }

            return oDataSet;
        }

        public static DataSet getAppointmentData(long LeadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId ", LeadId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAppointmentByleadID]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public static bool SaveCustomerAuthenticateData(CustomerAuthenticateData customerAuthenticateData)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
            try
            {
                _CommDB.InsertData<CustomerAuthenticateData>(customerAuthenticateData, DataAccessLibrary.MongoCollection.CustomerAuthenticate());
                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        public static bool SendCommunication(sendcommunicationResponse oSendcommunicationResponse)
        {
            bool result = false;
            string response = string.Empty;
            string Json = string.Empty;
            string strexception = string.Empty;
            string url = "pbserviceapi".AppSettings();
            ComAPIV2Response comAPIV2Response = new();
            DateTime dt = DateTime.Now;
            try
            {
                Json = JsonConvert.SerializeObject(oSendcommunicationResponse);

                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"REQUESTINGSYSTEM", "Matrix"},{"TOKEN", "pbservicetoken".AppSettings()}};
                response = CommonAPICall.CallAPI(url, Json, "POST", Convert.ToInt32("BMSTicketTimeOut".AppSettings()), "application/json", header);

                result = true;
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {

                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(oSendcommunicationResponse.LeadId), oSendcommunicationResponse.LeadId, strexception, "SendCommunication", "MatrixCoreAPI", "FOSDLL", JsonConvert.SerializeObject(Json), response, dt, DateTime.Now);

                if (!string.IsNullOrEmpty(response))
                {
                    comAPIV2Response = JsonConvert.DeserializeObject<ComAPIV2Response>(response);
                }
                FOSDLL.TriggerCommunicationInsertSMSLead(oSendcommunicationResponse.LeadId, oSendcommunicationResponse.TriggerName, comAPIV2Response?.UUID);
            }

            return result;
        }

        public static void UpdateDocument(IMongoQuery query, IMongoUpdate updateQuery, string collection)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            objCommDB.UpdateDocument(query, updateQuery, collection);
        }

        public static Int64 IsTriger(Int64 LeadID)
        {
            Int64 result = 0;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", LeadID);
                var obj = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[IsTrigerCommunication]", sqlParam);

                if (Convert.ToInt64(obj) > 0)
                    result = Convert.ToInt64(obj);
            }
            catch (Exception ex)
            {
                result = 0;
            }
            return result;
        }

        public static DataSet CheckSurveyAgent(Int64 UserId)
        {

            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@UserId", UserId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CheckSurveyAgent]", sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static DataSet IsUserActive(Int64 UserId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@UserId", UserId);
            return SqlHelper.ExecuteDataset(Connectionstring, CommandType.StoredProcedure, "[MTX].[IsUserActive]", SqlParam);
        }
        public static Int64 ValidateQRCode(AppointmentsDataModel oAppointmentsDataModel)
        {
            Int64 result = 0;

            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@AppointmentId", oAppointmentsDataModel.AppointmentId);
            SqlParam[1] = new SqlParameter("@Code", oAppointmentsDataModel.QRCode);
            SqlParam[2] = new SqlParameter("@leadId", 0);

            SqlParam[2].Direction = ParameterDirection.Output;

            var obj = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[ValidateQRCode]", SqlParam);

            result = SqlParam[2].Value == DBNull.Value ? 0 : Convert.ToInt64(SqlParam[2].Value);


            return result;
        }
        public static DataSet GenerateQRCode(long AppointmentId, String QRCode)
        {

            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@AppointmentId", AppointmentId);
                SqlParam[1] = new SqlParameter("@Code", QRCode);
                SqlParam[2] = new SqlParameter("@UserId", "CustomerUserId".AppSettings());

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SaveQRCode]", SqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

        }



        public static long SaveAppointmentHistory(LeadsSubStatusModel leadsSubStatusModel)
        {
            DateTime dt = DateTime.Now;

            try
            {
                SqlParameter[] SqlParam = new SqlParameter[7];
                SqlParam[0] = new SqlParameter("@AppointmentId", leadsSubStatusModel.AppointmentId);
                SqlParam[1] = new SqlParameter("@Comments", leadsSubStatusModel.Comments);
                SqlParam[2] = new SqlParameter("@UserID", leadsSubStatusModel.UserID);
                SqlParam[3] = new SqlParameter("@SubstatusId", leadsSubStatusModel.SubStatusId);
                SqlParam[4] = new SqlParameter("@EventType", leadsSubStatusModel.EventId);
                SqlParam[5] = new SqlParameter("@Source", leadsSubStatusModel.Source.ToLower());
                SqlParam[6] = new SqlParameter("@AppDateTime", leadsSubStatusModel.AppointmentDateTime);

                var obj = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SaveAppointmentHistory]", SqlParam);

                //                object obj = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SaveAppointmentHistory]", SqlParam);
                if (obj != null)
                    return Convert.ToInt64(obj);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadsSubStatusModel.AppointmentId, ex.ToString(), "SaveAppointmentHistory", "MatrixCoreAPI", "FOSDLL", JsonConvert.SerializeObject(leadsSubStatusModel), "", dt, DateTime.Now);

            }
            return 0;
        }
        public static bool SaveProgressiveTracking(long userId, Int16 eventId)
        {

            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@UserId", userId);
                SqlParam[1] = new SqlParameter("@EventType", eventId);
                SqlParam[2] = new SqlParameter("@TYPE", 1);
                SqlHelper.ExecuteNonQuery(new SqlConnection(ConnectionClass.LivesqlConnection()), CommandType.StoredProcedure, "[FOS].[SaveProgressiveTracking]", SqlParam);
                return true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(userId), ex.ToString(), "SaveProgressiveTracking", "Matrixcore", "FOSDLL", JsonConvert.SerializeObject(eventId), "", DateTime.Now, DateTime.Now);
                return false;
            }


        }

        public static void SetAppointmentHistory(PriorityModel AppModel, string Source)
        {
            try
            {
                LeadsSubStatusModel oLeadsSubStatusModel = new LeadsSubStatusModel()
                {
                    Comments = "-",
                    AppointmentId = AppModel.Appointment.AppointmentId,
                    UserID = Convert.ToInt64(4020),
                    SubStatusId = AppModel.Appointment.StatusID,
                    EventId = (Int16)EnumAppEvents.Status_Update,
                    Source = Source,
                    AppointmentDateTime = AppModel.Appointment.ScheduledOn
                };
                FOSDLL.SaveAppointmentHistory(oLeadsSubStatusModel);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(AppModel.LeadID), ex.ToString(), "SetAppointmentHistory", "Matrixcore", "FOSDLL", JsonConvert.SerializeObject(AppModel), "", DateTime.Now, DateTime.Now);

            }
        }
        public static DataSet GetAsssignmentsByProductPincode(Int16 ProductID, Int32 Pincode)
        {

            DataSet oDataSet = null;


            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@ProductID", ProductID);
            sqlParam[1] = new SqlParameter("@Pincode", Pincode);
            oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAsssignmentsByProductPincode]", sqlParam);



            return oDataSet;
        }
        public static DataSet GetLeadBasicInfo(long LeadID)
        {
            DataSet oDataSet = null;
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadID", LeadID);
            oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadBasicInfo]", sqlParam);
            return oDataSet;
        }

        public static DataSet GetUserBasicDetails(long UserId)
        {
            DataSet oDataSet = null;
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", UserId);
            oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetUserBasicDetails]", sqlParam);
            return oDataSet;
        }
        public static DataSet GetLeadAssignDetails(long LeadID)
        {
            DataSet oDataSet = null;
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadID);
            oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetLeadAssignDetails]", sqlParam);
            return oDataSet;
        }
        public static DataSet GetFosCityMaster()
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@IsFOSCities", 1);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetFosCityMaster]", 3000, sqlParam);
        }

        public static DataSet GetAppointmentSummary(string InputParm, Int16 Type, Int16 @NoOfDays)
        {
            DataSet oDataSet = null;
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@InputParm", InputParm);
            sqlParam[1] = new SqlParameter("@Type", Type);
            sqlParam[2] = new SqlParameter("@NoOfDays", NoOfDays);

            oDataSet = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAppointmentSummary]", sqlParam);
            return oDataSet;
        }
        public static DataSet GetAgentAssignedLeads(Int64 UserID, Int16 NoOfDays)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserId", UserID);
            sqlParam[1] = new SqlParameter("@NoOfDays", NoOfDays);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAgentAssignedLeads]", sqlParam);

        }
        public static DataSet GetProductSourceAssignmentMapping(string Source, Int16 ProductId, Int32 CityID, Int32 PinCode)
        {
            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@Source", Source);
            sqlParam[1] = new SqlParameter("@ProductId", ProductId);
            sqlParam[2] = new SqlParameter("@CityID", CityID);
            sqlParam[3] = new SqlParameter("@PinCode", PinCode);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetProductSourceAssignmentMapping]", sqlParam);

        }


        public static void LinkAppointment(LinkAppointmentRequest request, string agentId)
        {
            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@LeadId", request.LeadId);
            sqlParam[1] = new SqlParameter("@ApptId", request.ApptId);
            sqlParam[2] = new SqlParameter("@Type", request.Type);
            sqlParam[3] = new SqlParameter("@UserId", !string.IsNullOrEmpty(agentId) ? Convert.ToInt64(agentId) : DBNull.Value);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                      CommandType.StoredProcedure,
                                      "[FOS].[LinkAppointment]",
                                      sqlParam);
        }

        public static DataSet GetGroupsByUserId(Int64 UserID)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", UserID);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[CRM].[GetGroupsByUserId]", sqlParam);

        }

        public static bool SaveCustomerLocationHistory(AppointmentsDataModel objAppointmentData)
        {
            DateTime dt = DateTime.Now;
            bool result = false;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[8];
                SqlParam[0] = new SqlParameter("@AppointmentId", objAppointmentData.AppointmentId);
                SqlParam[1] = new SqlParameter("@Address", objAppointmentData.Address);
                SqlParam[2] = new SqlParameter("@Pincode", objAppointmentData.Pincode);
                SqlParam[3] = new SqlParameter("@Landmark", objAppointmentData.Landmark);
                SqlParam[4] = new SqlParameter("@PlaceId", objAppointmentData.place_id);
                SqlParam[5] = new SqlParameter("@Source", objAppointmentData.Source.ToLower());
                SqlParam[6] = new SqlParameter("@CreatedBy", objAppointmentData.UserId);
                SqlParam[7] = new SqlParameter("@IsDropLocationConfirm", objAppointmentData.IsDropLocationConfirm);
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SaveCustomerLocationHistory]", SqlParam);
                result = true;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, objAppointmentData.AppointmentId, ex.ToString(), "SaveCustomerLocationHistory", "MatrixCoreAPI", "FOSDLL", JsonConvert.SerializeObject(objAppointmentData), "", dt, DateTime.Now);
                return false;
            }
            return result;
        }

        public static DataSet GetAgentProfileData(long UserId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@AgentId", UserId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAgentSelfieData]", sqlParam);

        }

        public static bool SaveCustomerLocationData (CustomerLocationModel customerLocationObj)

        {
            DateTime dt = DateTime.Now;
            bool result = false;
            SqlParameter[] SqlParam = new SqlParameter[11];
            SqlParam[0] = new SqlParameter("@LeadId", customerLocationObj.LeadId);
            SqlParam[1] = new SqlParameter("@CityId", customerLocationObj.CityId);
            SqlParam[2] = new SqlParameter("@City", customerLocationObj.City);
            SqlParam[3] = new SqlParameter("@CountryId", customerLocationObj.CountryId);
            SqlParam[4] = new SqlParameter("@PlaceId", customerLocationObj.PlaceId);
            SqlParam[5] = new SqlParameter("@Landmark", customerLocationObj.Landmark);
            SqlParam[6] = new SqlParameter("@Address", customerLocationObj.Address);
            SqlParam[7] = new SqlParameter("@Pincode", customerLocationObj.Pincode);
            SqlParam[8] = new SqlParameter("@Source", customerLocationObj.Source);
            SqlParam[9] = new SqlParameter("@ResponseSource", customerLocationObj.ResponseSource);
            SqlParam[10] = new SqlParameter("@CreatedBy", customerLocationObj.CreatedBy);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SaveCustomerLocationData]", SqlParam);
            result = true;
            return result;
        }

        public static DataSet GetCustomerLocationData(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetCustomerLocationData]", sqlParam);

        }

        public static DataSet CheckCustomerLocationAvailable(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[CheckCustomerLocationAvailable]", sqlParam);

        }

        public static DataSet IsAppointmentCreated(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[IsAppointmentCreated]", sqlParam);

        }

        public static string getProductName(Int32 ProductID)
        {
            string ProductName = string.Empty;
            switch (ProductID)
            {
                case 2:
                    ProductName = "Health Insurance";
                    break;
                case 7:
                    ProductName = "Term Insurance";
                    break;
                case 115:
                    ProductName = "Investments";
                    break;
                case 117:
                    ProductName = "Car Insurance";
                    break;
                case 1000:
                    ProductName = "Term Insurance";
                    break;
                case 139:
                    ProductName = "Commercial Vehicle Insurance";
                    break;
                case 101:
                    ProductName = "Home Insurance";
                    break;
                default:
                    ProductName = "Insurance";
                    break;

            }
            return ProductName;
        }


        public static DataSet CheckAgentAvailabilityInCity(long LeadId, Int32 CityId, Int16 SlotId, DateTime AppointmentDateTime, long UserId)
        {
            SqlParameter[] sqlParam = new SqlParameter[5];
            sqlParam[0] = new SqlParameter("@CityId", CityId);
            sqlParam[1] = new SqlParameter("@SlotId", SlotId);
            sqlParam[2] = new SqlParameter("@AppDateTime", AppointmentDateTime);
            sqlParam[3] = new SqlParameter("@LeadId", LeadId);
            sqlParam[4] = new SqlParameter("@UserId", UserId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[CheckAgentAvailabilityInCity]", sqlParam);

        }


        public static DataSet getAgentAvailabilityInCityMaster(Int32 CityId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CityId", CityId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[getAgentAvailabilityInCityMaster]", sqlParam);
        }

        public static bool UpdateUserExpPassword(string EmployeeId, DateTime? PassExpiryDate)
        {
            string connection = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@EmployeeId", EmployeeId);
            SqlParam[1] = new SqlParameter("@PassExpiryDate", PassExpiryDate);
            SqlHelper.ExecuteNonQuery(connection, CommandType.StoredProcedure, "[MTX].[UpdateUserExpPassword]", SqlParam);
            return true;

        }


        public static DataSet GetOfflineLeadsInCity()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetOfflineLeadsInCity]");
        }
        public static void TriggerCommunicationInsertSMSLead(Int64 LeadID, string TriggerName, string UUID = null, string ActionName = null)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            SqlParam[1] = new SqlParameter("@TriggerName", TriggerName);
            SqlParam[2] = new SqlParameter("@UID", UUID);
            SqlParam[3] = new SqlParameter("@ActionName", ActionName);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertMotorSMSSendDump]", SqlParam);
        }
        public static DataSet GetCustomerLocationDataV1(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetCustomerLocationDataV1]", sqlParam);

        }

        public static DataSet GetCityIdWithPincode(Int32 Pincode)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@Pincode", Pincode);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetCityIdWithPincode]", 3000, sqlParam);
        }


        public static DataSet GetRealTimeData(long UserId,long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@UserId", UserId);
            sqlParam[1] = new SqlParameter("@LeadId", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAgentRealTimeData]", sqlParam);

        }
        public static DataSet GetRealStatusMaster()
        {
            SqlParameter[] sqlParam = new SqlParameter[0];
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetRealStatusMaster]", sqlParam);

        }

        public static AppointmentsDataModel GetAgentRealTimeData(long UserId,IMongoQuery query)
        {
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.MatrixDashBoardDB());
                //var query = Query<AppointmentsDataModel>.EQ(p => p.UserId, UserId);
                var lstPriorityModel = _CommDB.GetDocuments<AppointmentsDataModel>(query, DataAccessLibrary.MongoCollection.RealTimeAppointmentData()).ToList();
                if (lstPriorityModel != null && lstPriorityModel.Count > 0)
                    return lstPriorityModel[0];
                else return null;
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(UserId.ToString(), UserId, ex.ToString(), "GetPriorityModelMongo_Error", "LeadPrioritizationDLL", "", query.ToString(), "", DateTime.Now, DateTime.Now);
                return null;
            }
        }

        public static bool UpdateAgentRealTimeData(AppointmentsDataModel appointmentsDataModel)
        {
            MongoHelper _CommDB = new MongoHelper(SingletonClass.MatrixDashBoardDB());
            _CommDB.InsertData<AppointmentsDataModel>(appointmentsDataModel, DataAccessLibrary.MongoCollection.RealTimeAppointmentData());
            return true;

        }
        public static void UpdateRealTimeStatusDocument(IMongoQuery query, IMongoUpdate updateQuery)
        {
            MongoHelper objCommDB = new MongoHelper(SingletonClass.MatrixDashBoardDB());
            objCommDB.UpdateDocument(query, updateQuery, DataAccessLibrary.MongoCollection.RealTimeAppointmentData());
        }


        public static Boolean IsActiveAppointment(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            return Convert.ToBoolean(SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[IsActiveAppointment]", sqlParam));
        }

        public static Boolean SaveCoreAddressUsage(long LeadId,bool PreviousAddressClicked, bool IsAddressUsed)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            sqlParam[1] = new SqlParameter("@PreviousAddressClicked", PreviousAddressClicked);
            sqlParam[2] = new SqlParameter("@IsAddressUsed", IsAddressUsed);
            return Convert.ToBoolean(SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SaveCoreAddressUsage]", sqlParam));
        }

        public static DataSet GetTotalAppointmentsByCityId(Int32 CityId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CityId", CityId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetTotalAppointmentsByCityId]", sqlParam);
        }

        public static DataSet GetCarDetails(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetCarDetails]", sqlParam);
        }

        public static bool SaveFOSIntent(long LeadId,string source)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            sqlParam[1]= new SqlParameter("@source", source);
            return Convert.ToBoolean(SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[SavFOSIntentLeads]", sqlParam));
        }

        public static DataSet GetAppointmentDetailsforAI(long LeadId, long AppointmentId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            sqlParam[1] = new SqlParameter("@AppointmentId", AppointmentId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAppointmentDetailsforAI]",sqlParam);
        }

        public static void SendAppointmentEventToAI(dynamic msgObj)
        {
            AmazonSqs amazonSqs = new();
            IConfiguration con = Custom.ConfigurationManager.AppSetting;

            string SQSLogingQueueUrl = con.GetSection("Communication").GetSection("ai_fos_appointment_authentication").Value.ToString();
            //TODO List
            string SQSQueueUrl = SQSLogingQueueUrl;
            amazonSqs.SQSSendMessage(SQSQueueUrl, msgObj);

        }
        public static DataSet GetAppointmentExistanceStatus(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAppointmentExistanceStatus]", sqlParam);
        }

        public static object RescheduleAppointment(RescheduleApptData rescheduleAppt)
        {
            DateTime dt = DateTime.Now;
            string Connectionstring = ConnectionClass.LivesqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@LeadId", rescheduleAppt.LeadID);
            SqlParam[1] = new SqlParameter("@AppointmentDateTime", rescheduleAppt.AppointmentDateTime);
            SqlParam[2] = new SqlParameter("@SlotId", rescheduleAppt.SlotId);

            return SqlHelper.ExecuteScalar(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[RescheduleAppointment]", SqlParam);
        }

        public static bool TrackAppointmentPrepone(long LeadId, string source )
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            sqlParam[1] = new SqlParameter("@source", source);
            return Convert.ToBoolean(SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[TrackingAppointmentPrepone]", sqlParam));
        }

        public static DataSet GetAllLeadsforPrepone()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetAllLeadsforPrepone]");
        }
        
    }
}
