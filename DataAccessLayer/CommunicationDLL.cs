﻿using DataHelper;
using System.Data;
using DataAccessLibrary;
using System.Data.SqlClient;
using System;

namespace DataAccessLayer
{
    public class CommunicationDLL
    {
        public static DataTable GetBasicLeadDetails(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            var data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetLeadBasicInfo]",
                                                sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }

        public static DataTable GetCarDetails(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            var data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetCarDetails]",
                                                sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }

        public static DataTable CallBackData(long leadId, long custId, int productId)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            sqlParam[1] = new SqlParameter("@CustomerId", custId);
            sqlParam[2] = new SqlParameter("@ProductId", productId);

            var data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetCustomerCallback_CV]",
                                                sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }

        public static DataSet GetAgentCommLogs(long userId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", userId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetAgentCallLogs]",
                                            sqlParam);
        }

        public static DataSet CallSchedular(long leadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetCallSchedular]",
                                            sqlParam);
        }

        public static DataTable GetLeadBasicDetails(long LeadId)
        {
            DataTable dt = null;
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            sqlParam[1] = new SqlParameter("@IsBooking", 0);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadAndBooking]", 3000, sqlParam);

            if (ds != null && ds.Tables.Count > 0)
                dt = ds.Tables[0];

            return dt;
        }

        public static DataSet GetCallSchedularByCustId(long customerId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetCallSchedularByCustId]",
                                            sqlParam);
        }
    }
}
