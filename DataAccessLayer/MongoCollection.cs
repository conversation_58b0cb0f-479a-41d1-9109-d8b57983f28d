﻿using PropertyLayers;
namespace DataAccessLibrary
{
    public class MongoCollection
    {
        public static string TemplateCollection()
        {
            return "TemplateMaster";
        }
        public static string TicketCollection()
        {
            return "TicketDetails";
        }
        public static string TicketHistoryCollection()
        {
            return "TicketHistory";
        }
        public static string TicketAttachmentCollection()
        {
            return "TicketAttachements";
        }
        public static string TicketIssueMaster()
        {
            return "TicketIssueMaster";
        }
        public static string TransferCollection()
        {
            return "CallTransferData";
        }
        public static string LPDataCollection()
        {
            return "LPData";
        }
        public static string LastAssignedIDCollection()
        {
            return "LastAssignedID";
        }
        public static string CustProfileLog()
        {
            return "CustProfileLog";
        }
        public static string Settings()
        {
            return "Settings";
        }
        
        public static string CommCollection(CommunicationType CommType)
        {
            return CommCollectionName(CommType);
        }
        private static string CommCollectionName(CommunicationType CommType)
        {
            switch (CommType)
            {
                case CommunicationType.Email:
                    return "Email_Collection";
                case CommunicationType.SMS:
                    return "SMS_Collection";
                case CommunicationType.Call:
                    return "Call_Collection";
                case CommunicationType.Chat:
                    return "Chat_Collection";
                case CommunicationType.InboundCall:
                    return "InboundCall_Collection";
                default:
                    return "Email_Collection";
            }
        }
        public static string ChatUserCollection()
        {
            return "users";
        }
        public static string ChatloginHistoryCollection()
        {
            return "rocketchat_loginhistory";
        }
        public static string ChatDepartmentCollection()
        {
            return "rocketchat_livechat_department";
        }
        public static string ChatAgentDepartmentCollection()
        {
            return "rocketchat_livechat_department_agents";
        }
        public static string ChatRoomCollection()
        {
            return "rocketchat_room";
        }
        public static string UsersCollection()
        {
            return "users";
        }
        public static string ChatMessageCollection()
        {
            return "rocketchat_message";
        }
        public static string ChatusersSessions()
        {
            return "usersSessions";
        }
        public static string ChatEnquiry()
        {
            return "rocketchat_livechat_inquiry";
        }
        public static string ChatSubscription()
        {
            return "rocketchat_subscription";
        }
        public static string ChatOfflineCollection()
        {
            return "rocketchat_OfflineMessage";
        }
        public static string InternalIps()
        {
            return "InternalIps";
        }

        public static string LeadPriorityPositionLog()
        {
            return "LPPositionLog";
        }
        public static string ChatWelcomeMessageCollection()
        {
            return "rocketchat_livechat_WelcomeMessage";
        }
        public static string ChatSettingsCollection()
        {
            return "rocketchat_settings";
        }
        public static string CJEventLogCollection()
        {
            return "CJEventLog";
        }

        internal static string WhatsAppMessageCollection()
        {
            return "WhatsAppMessage";
        }

        internal static string MessengerCollection()
        {

            return "MessengerChatMsgs";
        }

        internal static string RevisitCustomers()
        {
            return "RevisitCustomers";
        }

        public static string UserNonWorkedLeads()
        {
            return "UserNonWorkedLeads";
        }

        public static string Next5Leads_OneLead()
        {
            return "Next5Leads_OneLead";
        }

        public static string ChatBotUserInput()
        {
            return "rocketchat_livechat_Chatbot_UserInput";
        }

        public static string ratingCollection()
        {
            return "rocketchat_rating";
        }

        public static string onlinecustomerTracking()
        {
            return "onlinecustomerTracking";
        }

        public static string ctcrevisitleadrankmapping()
        {
            return "ctcrevisitleadrankmapping";
        }

        public static string ctcrevisitleadqueue()
        {
            return "ctcrevisitleadqueue";
        }


        internal static string RetainerPredictive()
        {
            return "RetainerPredictiveDialingData";
        }

        internal static string PredictiveAgentStatus()
        {
            return "PredictiveAgentStatus";
        }

        internal static string SoftPhoneLogin()
        {
            return "SoftPhoneLogin";
        }

        public static string ResetLPDataCollection()
        {
            return "ResetLPData";
        }

        public static string CallRoomCollection()
        {
            return "rocketchat_callbot_room";
        }

        public static string CallMessageCollection()
        {
            return "rocketchat_callbot_message";
        }

        public static string selectionleadcollection()
        {
            return "selectionleadcollection";
        }

        public static string QuoteSharedTracking()
        {
            return "QuoteSharedTracking";
        }  public static string SOSBookingsLog()
        {
            return "SOSBookingsLog";
        }
        public static string ConfigValues()
        {
            return "APIKeySettings";
        }
        public static string ConfigFiles()
        {
            return "ConfigFiles";
        }
        public static string CustomerAuthenticate()
        {
            return "CustomerAuthenticate";
        }
        public static string LotteryContestAgents()
        {
            return "LotteryContestAgents";
        }
        public static string ACLConfigValues()
        {
            return "APIKeySettings_ACL";
        }

        public static string NotificationsData()
        {
            return "NotificationsData";
        }

        public static string ReadNotificationsData()
        {
            return "ReadNotificationsData";
        }
        public static string KnowYoutAdvisor()
        {
            return "VerifyAdvisor";
        }

        public static string customerRevisit()
        {
            return "customerRevisit";
        }
        public static string RealTimeAppointmentData()
        {
            return "RealTimeAppointmentData";
        }

        public static string CustomerNANCAttempts()
        {
            return "CustomerNANCAttempts";
        }

        public static string SVConfig()
        {
            return "SVConfigValues";
        }
        public static string PinnedMenuItems()
        {
            return "PinnedMenuItems";
        }
    }
}
