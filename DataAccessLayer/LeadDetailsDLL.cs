﻿using DataAccessLibrary;
using DataHelper;
using Helper;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using PropertyLayers;
using MongoConfigProject;
using System;
using System.Data;
using System.Data.SqlClient;
using MongoCollection = DataAccessLibrary.MongoCollection;
using Newtonsoft.Json;
using Microsoft.Extensions.Primitives;

namespace DataAccessLayer
{
    public class LeadDetailsDLL
    {
        public static DataSet GetRenewalLeadDetails(long leadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", Convert.ToInt64(leadId));

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetRenewalLeadDetailsForCJ]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static void UpdateRenewalStatus(string renewalId, int status, string message)
        {
            using (SqlConnection conn = new SqlConnection(ConnectionClass.LivesqlConnection()))
            {

                conn.Open();
                SqlCommand cmd = new SqlCommand("[MTX].[UpdateRenewalDetailsStatus]", conn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add(new SqlParameter("@RenewalID", renewalId));
                cmd.Parameters.Add(new SqlParameter("@StatusID", status));
                cmd.Parameters.Add(new SqlParameter("@ErrorMessage", message));
                cmd.ExecuteNonQuery();
            }
        }

        public static DataSet GetHealthRenewalLead(long CustomerId, string PolicyNo)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@PolicyNo", PolicyNo);
                sqlParam[1] = new SqlParameter("@CustomerID", Convert.ToInt64(CustomerId));


                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetRenewallead]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static DataSet checkLeadAssignmentDetails(long leadId)
        {
            DataSet oDataSet = new DataSet();
            string query = string.Empty;
            string connection = ConnectionClass.LivesqlConnection();
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@leadId", Convert.ToInt64(leadId));

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetleadAssignmentDetails]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static DataSet GetCustomerBookingDocumentURL(long leadId, long userId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@leadId", leadId);
                sqlParam[1] = new SqlParameter("@AgentId", userId);

                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerBookingDocumentURL]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }

        }


        public static DataSet GetRenewalleadByPolicyNo(string PolicyNo)
        {

            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@PolicyNo", PolicyNo);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetRenewalleadByPolicyNo]", 3000, sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static DataSet GetAgentCSATScore(long userId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@userId", userId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.GetAgentCSATScore", sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static DataSet IsUserKYAEligible(string Empcode)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@Empcode", Empcode);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[IsUserKYAEligible]", 3000, sqlParam);


            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public static DataSet GetCityList()
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[0];
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[master].[GetCityList]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }
        public static DataSet GetSubStatusDetails()
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[0];
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[Master].[SubstatusList]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetLeadStatusDetails(long leadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@leadId", Convert.ToInt64(leadId));
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadStatusAssignDetails]", 3000, sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

        }
        public static DataSet GetRenewalLeadDetailsforBookingId(long BookingId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@BookingID", Convert.ToInt64(BookingId));
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetRenewalLeaddetails]", 3000, sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }

        }

        public static string Quizid(string UserId) 
        {
            string Quizid = "0";
            Quiz quiz = new Quiz();
            try
            {
                MongoHelper _CommDB = new MongoHelper(SingletonClass.OneLeadDB());
                var query = Query.EQ("Userid", UserId);
                IMongoFields fields = Fields.Include("QuizID");
                var EncData = _CommDB.GetDocuments<Quiz>(query, MongoCollection.LotteryContestAgents(), SortBy.Ascending("_id"), fields, 0, 1);
                Quizid = EncData[0].QuizID;
            }
            catch (Exception ex)
            {
                return null;
            }
            return Quizid;
        }

        //public static DataSet DkdLottery(string UserId)
        //{
        //    try
        //    {
        //        //SqlParameter[] sqlParam = new SqlParameter[1];
        //        //sqlParam[0] = new SqlParameter("@userid", UserId);
        //        //return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[enc].[dkd_IsLotteryAgent]", 3000, sqlParam);


        //    }
        //    catch (Exception ex)
        //    {
        //        return null;
        //    }
        //}

        public static DataSet DkdLottery(Int32 QuizId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@QuizId", QuizId);
                string query = "SELECT TOP 1 IsActive FROM enc.Lottery_QuizMaster(NOLOCK) WHERE ID=@QuizId AND IsActive = 1";
                return SqlHelper.ExecuteDataset(ConnectionClass.IncentiveDBsqlConnection(), CommandType.Text, query, 3000, sqlParam);


            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetUniqueGroups(string UserId, string ProductId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@AgentId", Convert.ToInt64(UserId));
                sqlParam[1] = new SqlParameter("@ProductId", Convert.ToInt32(ProductId));

                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[CRM].[GetUniqueGroups]", 3000, sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetCustomerSelection(Int64 LeadID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@leadId", LeadID);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustSelectionByLeadId]", 3000, sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet InsertUserInfo(UserInfo userInfo)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@Mobile", Convert.ToString(userInfo.Mobile));
                sqlParam[1] = new SqlParameter("@CustId", userInfo.CustId);
                sqlParam[2] = new SqlParameter("@NoOfProfile", userInfo.NoOfProfile);
                sqlParam[3] = new SqlParameter("@Name", userInfo.Name);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertUserInfo]", 3000, sqlParam);

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetLeadDetails(Int64 LeadID, bool IsBooking)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@LeadID", LeadID);
                sqlParam[1] = new SqlParameter("@IsBooking", IsBooking);

                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadAndBooking]", 3000, sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static (Int64, string) CreateLeadByReferralId(LeadRequest leadRequest)
        {
            DateTime requesttime = DateTime.Now;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[19];
                sqlParam[0] = new SqlParameter("@Name", leadRequest.Name);
                sqlParam[1] = new SqlParameter("@DOB", DateTime.ParseExact(leadRequest.DOB, "dd-MM-yyyy", null).ToString("yyyy-MM-dd"));
                sqlParam[2] = new SqlParameter("@MobileNo", Convert.ToInt64(Crypto.Decrytion_Payment_AES(leadRequest.MobileNo, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings())));
                sqlParam[3] = new SqlParameter("@EmailId", Crypto.Decrytion_Payment_AES(leadRequest.EmailId, "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings()));
                sqlParam[4] = new SqlParameter("@CityID", leadRequest.CityID);
                sqlParam[5] = new SqlParameter("@StateID", leadRequest.StateID);
                sqlParam[6] = new SqlParameter("@ProductId", leadRequest.ProductID);
                sqlParam[7] = new SqlParameter("@SupplierId", leadRequest.SupplierId);
                sqlParam[8] = new SqlParameter("@PlanId", leadRequest.PlanId);
                sqlParam[9] = new SqlParameter("@SupplierName", leadRequest.SupplierName);
                sqlParam[10] = new SqlParameter("@PlanName", leadRequest.PlanName);
                sqlParam[11] = new SqlParameter("@ReferralID", leadRequest.ReferralID);
                sqlParam[12] = new SqlParameter("@CustID", leadRequest.CustomerID);
                sqlParam[13] = new SqlParameter("@LastYearPolicyNo", leadRequest.LastYearPolicyNo);
                sqlParam[14] = new SqlParameter("@CustPolicyID ", leadRequest.CustPolicyID);
                sqlParam[15] = new SqlParameter("@PolicyExpiryDate", string.IsNullOrEmpty(leadRequest.PolicyExpiryDate) ? null : DateTime.ParseExact(leadRequest.PolicyExpiryDate, "dd-MM-yyyy", null).ToString("yyyy-MM-dd"));
                sqlParam[16] = new SqlParameter("@IsActualEndDate", leadRequest.IsActualEndDate);

                var sqlParameter = new SqlParameter();
                sqlParameter.Direction = ParameterDirection.Output;
                sqlParameter.ParameterName = "@OutLeadID";
                sqlParameter.SqlDbType = SqlDbType.BigInt;
                sqlParam[17] = sqlParameter;

                sqlParameter = new SqlParameter();
                sqlParameter.Direction = ParameterDirection.Output;
                sqlParameter.ParameterName = "@error";
                sqlParameter.SqlDbType = SqlDbType.VarChar;
                sqlParameter.Size = 100;
                sqlParam[18] = sqlParameter;

                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[CreateRenewalLeadByPCD_breakdown]", 3000, sqlParam);

                return (Convert.ToInt64(sqlParam[17].Value), Convert.ToString(sqlParam[18].Value));
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(leadRequest.ReferralID.ToString(), leadRequest.ReferralID, ex.ToString(), "CreateLeadByReferralId", "MatrixCore", "LeadDetailsDLL", JsonConvert.SerializeObject(leadRequest), ex.ToString(), requesttime, DateTime.Now);
                return (0, ex.ToString());
            }
        }

        public static bool UpdateSoftCopyDocId(long leadId, string docId, string fileName)
        {
            bool result = true;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@LeadID", leadId);
                sqlParam[1] = new SqlParameter("@DocId", docId);
                sqlParam[2] = new SqlParameter("@FileName", fileName);

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateSoftCopyDocId]", sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, leadId, ex.ToString(),
                                                "UpdateSoftCopyDocId", "MatrixCore",
                                                "UpdateSoftCopyDocId", "",
                                                ex.ToString(), DateTime.Now, DateTime.Now);
                result = false;
            }
            return result;
        }

        public static long GetUserId(string empId)
        {
            long userId = 0;
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@EmployeeId", empId);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetUserIdByEmployeeId]", sqlParam);
            if (result != null)
                userId = Convert.ToInt64(result);

            return userId;
        }

        public static DataSet GetLeadDetailsIsActive(Int64 LeadId)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@LeadId", LeadId);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadDetailsIsActive]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet GetAgentID_Allocation(Int16 GroupID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[1];
                sqlParam[0] = new SqlParameter("@GroupId", GroupID);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentAllocation]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static DataSet AssignLead(AllocationDetails _AllocationDetails)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[18];
                sqlParam[0] = new SqlParameter("@AssignedToAgentId", _AllocationDetails.AssigntoUserID);
                sqlParam[1] = new SqlParameter("@AssignedByAgentId", _AllocationDetails.AssignbyUserID);
                sqlParam[2] = new SqlParameter("@ProductId", _AllocationDetails.ProductID);
                sqlParam[3] = new SqlParameter("@LeadId", _AllocationDetails.LeadID);
                sqlParam[4] = new SqlParameter("@GroupId", _AllocationDetails.GroupID);
                sqlParam[5] = new SqlParameter("@Flag", _AllocationDetails.AllocationTrackingEntryFlag);
                sqlParam[6] = new SqlParameter("@JobId", _AllocationDetails.JobID);
                sqlParam[7] = new SqlParameter("@FirstSelectedPlanId", _AllocationDetails.InsurerID);
                sqlParam[8] = new SqlParameter("@SelectionCount", _AllocationDetails.SelectionCount);
                sqlParam[9] = new SqlParameter("@LeadRank", _AllocationDetails.LeadRank);
                sqlParam[10] = new SqlParameter("@AgentGrade", _AllocationDetails.AgentGrade);
                sqlParam[11] = new SqlParameter("@LeadGrade", 0);
                sqlParam[12] = new SqlParameter("@LeadScore", _AllocationDetails.LeadScore);
                sqlParam[13] = new SqlParameter("@LeadStatusId", _AllocationDetails.StatusID);
                sqlParam[14] = new SqlParameter("@LeadSubStatusId", _AllocationDetails.SubStatusID);
                sqlParam[15] = new SqlParameter("@LeadTypeId", 0);
                sqlParam[16] = new SqlParameter("@CustID", _AllocationDetails.CustID);
                sqlParam[17] = new SqlParameter("@AgentAssignedToEmpId", _AllocationDetails.AssignedToEmpId);
                return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[Insert_AssignedToAgent_NewApp]", sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }        

        public static DataSet GetLeadAssignedAgent(Int64 MobileNo, Int16 ProductID)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@MobileNo", MobileNo);
                sqlParam[1] = new SqlParameter("@ProductID", ProductID);

                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadAndAgentByMobileNo]", 3000, sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static bool AssignLead(Int64 LeadID, string EmployeeID, string Source, StringValues? agentId)
        {
            var SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@Leads", LeadID);
            SqlParam[1] = new SqlParameter("@AssigntoEmpID", EmployeeID);
            SqlParam[2] = new SqlParameter("@Source", Source);
            SqlParam[3] = new SqlParameter("@UserID", !string.IsNullOrEmpty(agentId) && Source == "SMEFosCreateLead" ? Convert.ToInt64(agentId) : 124);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[AssignLead]", SqlParam);
            return true;
        }       

        public static DataSet GetAgentDetails(Int64 LeadID, string Process)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@LeadId", LeadID);
                sqlParam[1] = new SqlParameter("@Process", Process);
                return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentDetailsByLeadId]", 3000, sqlParam);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static long GetRenewalBookingStatus(long referralId, int productId, long customerId)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@ReferralId", referralId);
            sqlParam[1] = new SqlParameter("@ProductId", productId);
            sqlParam[2] = new SqlParameter("@CustomerId", customerId);
            
            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[MTX].[GetRenewalBookingStatus]", sqlParam);
            if (result != null)
                return Convert.ToInt64(result);
            else
                return 0;
        }

        public static void UpdateCJExitPointUrl(long leadId, string url)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);
            sqlParam[1] = new SqlParameter("@Url", url);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                      CommandType.StoredProcedure,
                                      "[MTX].[UpdateExitPointCJUrl]", sqlParam);
        }

        public static string GetCJUrl(string leadID)
        {
            var result = string.Empty;
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadID);

            var data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetLeadBasicInfo]", sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                result = data.Tables[0].Rows[0]["ExitPointURL"] == DBNull.Value ? default : Convert.ToString(data.Tables[0].Rows[0]["ExitPointURL"]);
            }
            return result;
        }

        public static DataRow GetJourneyLink(string leadID)
        {
            var result = string.Empty;
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadID);

            var data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetJourneyLinkDetails]", sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                return data.Tables[0].Rows[0];
            }
            return null;
        }

        public static DataRow GetLeadBasicInfo(long leadId)
        {
            DataRow dataRow = null;
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            var data = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetLeadBasicInfo]", sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                dataRow = data.Tables[0].Rows[0];
            }
            return dataRow;
        }

        public static int GetProductIdByLeadId(long leadId)
        {
            int productId = 0;
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            var data = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetProductIdByLeadId]", sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                productId = data.Tables[0].Rows[0]["ProductID"] != DBNull.Value ? Convert.ToInt32(data.Tables[0].Rows[0]["ProductID"]) : 0;
            }
            return productId;
        }

        public static DataSet GetParentChildLeadData(long leadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@ParentLeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAllLeadIdByParent]", sqlParam);
        }

        public static DataSet GetUserProductList(long userId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", userId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetUserProductList]", sqlParam);
        }

        public static DataSet GetleadAssignmentDetails(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@leadId", leadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetleadAssignmentDetails]",
                                            sqlParam);
        }

        public static DataSet GetActiveLeadsByCustId(long customerId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetActiveLeadsByCustId]",
                                            sqlParam);
        }

        public static DataSet GetRenewalLeadDetailsWA(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetLeadDetailsWithAgentInfo]",
                                            sqlParam);
        }

        public static long FetchParentLead(long leadId, int productId, long customerId)
        {
            long parentLeadId = 0;
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[3];
                sqlParam[0] = new SqlParameter("@LeadID", leadId);
                sqlParam[1] = new SqlParameter("@ProductID", productId);
                sqlParam[2] = new SqlParameter("@CustomerID", customerId);

                var result = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), 
                    CommandType.StoredProcedure, 
                    "[MTX].[FetchParentLead]", 
                    sqlParam);

                if (result != null && result != DBNull.Value)
                {
                    parentLeadId = Convert.ToInt64(result);
                }
            }
            catch (Exception)
            {
                // Return 0 if any error occurs
            }
            return parentLeadId;
        }
    }
}

