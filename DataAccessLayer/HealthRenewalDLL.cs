﻿using DataAccessLibrary;
using DataHelper;
using PropertyLayers;
using System;
using System.Data;
using System.Data.SqlClient;

namespace DataAccessLayer
{
    public class HealthRenewalDLL
    {
        public static int SetExclusiveBenifit(long ParentId, short isExclusiveBenifit)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@Type", 1);
            sqlParam[1] = new SqlParameter("@ParentId", ParentId);
            sqlParam[2] = new SqlParameter("@isExclusiveBenifit", isExclusiveBenifit);

            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetExclusiveBenifit]", sqlParam);
        }

        public static DataTable GetExclusiveBenifit(long ParentId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@Type", 2);
            sqlParam[1] = new SqlParameter("@ParentId", ParentId);

            DataSet dsLeads = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetExclusiveBenifit]", sqlParam);

            if (dsLeads.Tables.Count == 0)
                return null;
            else
                return dsLeads.Tables[0];
        }

        public static DataTable UpdateLoading(long RenewalLeadId, long LeadId, bool flag)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@RenewalLeadId", RenewalLeadId);
            sqlParam[1] = new SqlParameter("@LeadId", LeadId);
            sqlParam[2] = new SqlParameter("@flag", Convert.ToInt16(flag));

            DataSet dsLeads = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLoading_v2]", sqlParam);

            if (dsLeads.Tables.Count == 0)
                return null;
            else
                return dsLeads.Tables[0];
        }

        public static DataTable UpdatePED(long RenewalLeadId, long LeadId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@RenewalLeadId", RenewalLeadId);
            sqlParam[1] = new SqlParameter("@LeadId", LeadId);

            DataSet dsLeads = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdatePED]", sqlParam);

            if (dsLeads.Tables.Count == 0)
                return null;
            else
                return dsLeads.Tables[0];
        }
        public static int UpdateLoading(long RenewalLeadId, Loading loading)
        {
            var sqlParam = new SqlParameter[7];
            sqlParam[0] = new SqlParameter("@RenewalLeadId", RenewalLeadId);
            sqlParam[1] = new SqlParameter("@LoadingReason", loading.LoadingReason);
            sqlParam[2] = new SqlParameter("@LoadingPremium", loading.LoadingPremium);
            sqlParam[3] = new SqlParameter("@PremiumWaivedOff", loading.PremiumWaivedOff);
            sqlParam[4] = new SqlParameter("@LoadingPercentage", loading.LoadingPercentage);
            sqlParam[5] = new SqlParameter("@IsLoading", loading.IsLoading);
            sqlParam[6] = new SqlParameter("@type", 2);

            var res = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLoading_v2]", sqlParam);
            return Convert.ToInt32(res);
        }
        public static int UpdatePED(long RenewalLeadId, Loading loading)
        {
            var sqlParam = new SqlParameter[5];
            sqlParam[0] = new SqlParameter("@RenewalLeadId", RenewalLeadId);
            sqlParam[1] = new SqlParameter("@type", 2);
            sqlParam[2] = new SqlParameter("@PEDInfo", loading.PEDInfo);
            sqlParam[3] = new SqlParameter("@STPStatus", loading.STPStatus);
            sqlParam[4] = new SqlParameter("@NSTPReason", loading.NSTPReason);

            var res = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdatePED]", sqlParam);
            return Convert.ToInt32(res);
        }

        public static int SetPortDetails(RenewalPortDetails details)
        {
            var sqlParam = new SqlParameter[11];
            sqlParam[0] = new SqlParameter("@Type", 1);
            sqlParam[1] = new SqlParameter("@RenewalLeadId", details.LeadId);
            sqlParam[2] = new SqlParameter("@PreviousPolicyNo", details.PreviousPolicyNo);
            sqlParam[3] = new SqlParameter("@PreviousInsurer", details.PreviousInsurer);
            sqlParam[4] = new SqlParameter("@EnrollmentDate", details.EnrollmentDate);
            sqlParam[5] = new SqlParameter("@PreviousSI", details.PreviousSI);
            sqlParam[6] = new SqlParameter("@PreviousTerm", details.PreviousTerm);
            sqlParam[7] = new SqlParameter("@PreviousNCB", details.PreviousNCB);
            sqlParam[8] = new SqlParameter("@Claim", details.Claim);
            sqlParam[9] = new SqlParameter("@PED", details.PED);
            sqlParam[10] = new SqlParameter("@PortabilityReason", details.PortabilityReason);

            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetPortDetails]", sqlParam);
        }

        public static DataTable GetPortDetails(long LeadId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@Type", 2);
            sqlParam[1] = new SqlParameter("@RenewalLeadId", LeadId);

            DataSet dsLeads = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetPortDetails]", sqlParam);

            if (dsLeads.Tables.Count == 0)
                return null;
            else
                return dsLeads.Tables[0];
        }

        public static DataTable GetAHCURL(long LeadId, long UserID)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@BookingID", LeadId);
            sqlParam[1] = new SqlParameter("@UserID", UserID);

            DataSet dsLeads = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAHCURL]", sqlParam);

            if (dsLeads.Tables.Count == 0)
                return null;
            else
                return dsLeads.Tables[0];
        }
        public static int SetInceptionBooking(long RenewalLeadId, long InceptionBooking)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@Leadid", RenewalLeadId);
            sqlParam[1] = new SqlParameter("@InceptionBooking", InceptionBooking);
            sqlParam[2] = new SqlParameter("@type", 2);

            var res = SqlHelper.ExecuteScalar(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetInceptionBookingID]", sqlParam);
            return Convert.ToInt32(res);
        }
        public static DataTable GetInceptionBooking(long RenewalLeadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@Leadid", RenewalLeadId);

            DataSet dsLeads = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetInceptionBookingID]", sqlParam);

            if (dsLeads.Tables.Count == 0)
                return null;
            else
                return dsLeads.Tables[0];
        }

        public static DataSet FetchHealthRenewalPaymentLink(long Id, long AgentId)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@ID", Id);
            sqlParam[1] = new SqlParameter("@type", 8);
            sqlParam[2] = new SqlParameter("@RequestAgentID", AgentId);

            DataSet dsLeads = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[FetchHealthRenewalPaymentLink]", sqlParam);

            if (dsLeads.Tables.Count == 0)
                return null;
            else
            {
                if (dsLeads.Tables[0].Rows.Count > 0)
                    return dsLeads;
                else { return null; }
            }
        }

        public static bool SetHealthRenewalPaymentLink(long Id, long AgentId, string paymentlink)
        {
            var sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@ID", Id);
            sqlParam[1] = new SqlParameter("@type", 2);
            sqlParam[2] = new SqlParameter("@LinkCreatedBy", AgentId);
            sqlParam[3] = new SqlParameter("@PaymentLink", paymentlink);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SetHealthRenewalPaymentLink]", sqlParam);
            return true;
            
        }
        
        public static int UpdateRenewalRiders(long LeadId, string Riders, decimal PremiumCJ)
        {
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadID", LeadId);
            sqlParam[1] = new SqlParameter("@Riders", Riders);
            sqlParam[2] = new SqlParameter("@PremiumCJ", PremiumCJ);

            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateRenewalRiders]", sqlParam);
        }
    }
}

