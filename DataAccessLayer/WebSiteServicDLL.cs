﻿using DataAccessLibrary;
using DataHelper;
using MongoDB.Driver;
using MongoDB.Driver.Builders;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Dynamic;
using System.Linq;
using System.Xml.Linq;
using Helper;


namespace DataAccessLayer
{
    public class WebSiteServicDLL
    {
        public static int? SaveSelectedQuotes(SelectedQuote obj)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[14];
                sqlParam[0] = new SqlParameter("@MatrixLeadId", obj.LeadID);
                sqlParam[1] = new SqlParameter("@PlanID", obj.PlanID);
                sqlParam[2] = new SqlParameter("@ProductId", obj.ProductID);
                sqlParam[3] = new SqlParameter("@Premium", obj.Premium);
                sqlParam[4] = new SqlParameter("@SelectedBy", obj.SelectedBy);
                sqlParam[5] = new SqlParameter("@IPAddress", Convert.ToString(obj.IPAddress));
                sqlParam[6] = new SqlParameter("@SupplierID", obj.SupplierId);
                sqlParam[7] = new SqlParameter("@EnquiryId", obj.EnquiryId);
                sqlParam[8] = new SqlParameter("@NeedID", obj.NeedId);
                sqlParam[9] = new SqlParameter("@SourcePage", Convert.ToString(obj.SourcePage));

                var sqlParameter = new SqlParameter();
                sqlParameter.Direction = ParameterDirection.Output;
                sqlParameter.ParameterName = "@Message";
                sqlParameter.SqlDbType = SqlDbType.TinyInt;
                sqlParam[10] = sqlParameter;
                sqlParam[11] = new SqlParameter("@PlanFeature", obj.PlanFeature);
                sqlParam[12] = new SqlParameter("@AddonComboId", obj.AddonComboId);
                sqlParam[13] = new SqlParameter("@SpecialPlanType", obj.SpecialPlanType);

                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertSelectedQuotes_cv]", 3000, sqlParam);
                return Convert.ToInt32(sqlParam[10].Value);

            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static int? SaveCustomerIntent(DataTable customerIntent)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[2];

                sqlParam[0] = new SqlParameter("@LeadIds", customerIntent);

                var sqlParameter = new SqlParameter();
                sqlParameter.Direction = ParameterDirection.Output;
                sqlParameter.ParameterName = "@Output";
                sqlParameter.SqlDbType = SqlDbType.TinyInt;
                sqlParam[1] = sqlParameter;

                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveCustomerIntent]", 3000, sqlParam);
                return Convert.ToInt32(sqlParam[1].Value);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, "", "SaveCustomerIntent", "WebSiteServiceBLL", "MatrixCoreAPI", customerIntent.ToString(), ex.ToString(), DateTime.Now, DateTime.Now);
                return null;
            }
        }

        public static (bool, string) SaveLeadAction(LeadAction leadAction)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[11];
                sqlParam[0] = new SqlParameter("@LeadId", leadAction.LeadID);
                if (leadAction.Action == "Term Compare Click")
                    sqlParam[1] = new SqlParameter("@TermCompareCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Limited Pay")
                    sqlParam[2] = new SqlParameter("@LimitedPayCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Trop Selected")
                    sqlParam[3] = new SqlParameter("@TropSelectedCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Term HLV")
                    sqlParam[4] = new SqlParameter("@TermHLVCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Term Monthly Premium")
                    sqlParam[5] = new SqlParameter("@TermMonthlyCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Term Annual Premium")
                    sqlParam[6] = new SqlParameter("@TermAnnualCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Life Cover Amount Change")
                    sqlParam[7] = new SqlParameter("@LifeCoverCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Cover Till Age Change")
                    sqlParam[8] = new SqlParameter("@CoverTillCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Premium Payment Frequency Toggle")
                    sqlParam[9] = new SqlParameter("@PremiumFrequencyCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));
                if (leadAction.Action == "Filter Tab Click")
                    sqlParam[10] = new SqlParameter("@FilterCreatedOn", DateTime.ParseExact(leadAction.CreatedOn, "dd-MM-yyyy HH:mm:ss.fff", null));

                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveLeadAction]", 3000, sqlParam);
                return (true, "Success");

            }
            catch (Exception ex)
            {
                return (false, ex.ToString());
            }
        }

        public static LeadStatusResponse GetLeadQuoteUploadStatus(long leadId)
        {
            var response = new LeadStatusResponse();
            try
            {
                var sqlParams = new SqlParameter[3];
                sqlParams[0] = new SqlParameter("@LeadId", leadId);
                sqlParams[1] = new SqlParameter
                {
                    Direction = ParameterDirection.Output,
                    ParameterName = "@IsAllowed",
                    SqlDbType = SqlDbType.Int
                };
                sqlParams[2] = new SqlParameter
                {
                    Direction = ParameterDirection.Output,
                    ParameterName = "@Message",
                    SqlDbType = SqlDbType.VarChar,
                    Size = 200
                };
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                          CommandType.StoredProcedure,
                                          "[MTX].[GetLeadQuoteUploadStatus]",
                                          sqlParams);
                response.IsAllowed = Convert.ToBoolean(sqlParams[1].Value);
                response.Message = Convert.ToString(sqlParams[2].Value);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.Log(null, leadId, ex.Message,
                                                "GetLeadQuoteUploadStatus", "MatrixCore",
                                                "GetLeadQuoteUploadStatus", "",
                                                ex.ToString(), DateTime.Now, DateTime.Now);
                response.IsAllowed = false;
                response.Message = ex.Message;
            }
            return response;
        }

        public static DataSet GetOfflineBookedLeads(string date, int productId)
        {
            var response = new DataSet();
            try
            {
                var sqlParam = new SqlParameter[2];
                sqlParam[0] = new SqlParameter("@Date", date);
                sqlParam[1] = new SqlParameter("@ProductId", productId);

                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                    CommandType.StoredProcedure,
                                                    "[MTX].[GetOfflineBookedLeads]", 3000, sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, "", "GetOfflineBookedLeads", "WebSiteServiceDLL", "MatrixCoreAPI", "", ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static DataSet GetLeadDetailsForCustId(long customerId)
        {
            var response = new DataSet();
            try
            {
                var sqlParam = new SqlParameter[1]
                {
                     new SqlParameter("@CustomerId", customerId)
                };

                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                    CommandType.StoredProcedure,
                                                    "[MTX].[GetLeadDetailsForCustId]",
                                                    sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, customerId, ex.Message,
                                                          "GetLeadDetailsForCustId", "MatrixCore",
                                                          "GetLeadDetailsForCustId", "",
                                                          ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static DataSet GetCallData(long leadId, DateTime creationDateTime, string callType)
        {
            var response = new DataSet();
            try
            {
                var sqlParam = new SqlParameter[3]
                {
                     new SqlParameter("@LeadId", leadId),
                     new SqlParameter("@DateTime", creationDateTime),
                     new SqlParameter("@calltype", string.IsNullOrEmpty(callType) ? DBNull.Value : callType),
                };
                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                    CommandType.StoredProcedure,
                                                    "[MTX].[GetCallDataForLeadId]",
                                                    sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadId, ex.Message,
                                                          "GetCallData", "MatrixCore",
                                                          "GetCallData", "",
                                                          ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static DataRow GetEmployeeDetailsByLeadId(long leadId)
        {
            DataRow response = null;
            try
            {
                var sqlParam = new SqlParameter[1]
                {
                     new SqlParameter("@LeadId", leadId),
                };
                var result = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                      CommandType.StoredProcedure,
                                                      "[MTX].[GetEmployeeDetailsByLeadId]",
                                                      sqlParam);
                response = result.Tables[0].Rows[0];
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadId, ex.Message,
                                                          "GetEmployeeDetailsByLeadId", "MatrixCore",
                                                          "GetEmployeeDetailsByLeadId", "",
                                                          ex.ToString(), DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static int getPreBookingTalkTime(long LeadId)
        {
            int TalkTime = -1;

            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            var TalkTimeoutput = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "MTX.getLeadTotalTTBeforeBooked", sqlParam);
            TalkTime = string.IsNullOrEmpty(Convert.ToString(TalkTimeoutput)) ? TalkTime : Convert.ToInt32(TalkTimeoutput);

            return TalkTime;
        }

        public static DataSet GetUnansweredCommLeads()
        {
            var response = new DataSet();
            try
            {

                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                    CommandType.StoredProcedure,
                                                    "[MTX].[GetUnansweredCommLeads]"
                                             );

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.Message,
                                                      "getunansweredcommleads", "matrixcore",
                                                    "getunansweredcommleads", "",
                                                 "", DateTime.Now, DateTime.Now);
                response = null;
            }
            return response;
        }
        public static bool UpdateUnansweredCommLeads(long LeadId, int Template)
        {
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[2];
                SqlParam[0] = new SqlParameter("@LeadId", LeadId);
                SqlParam[1] = new SqlParameter("@Template", Template);
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateUnansweredCommLeads]", SqlParam);
                return true;
            }
            catch
            {
                return false;
            }
        }



        public static bool AssignLead(long LeadID, string EmpCode, Int16 CallType, string TransferType = "", Int64 AssignedBy = 124)
        {
            SqlParameter[] SqlParam = new SqlParameter[6];
            SqlParam[0] = new SqlParameter("@LeadID", LeadID);
            SqlParam[1] = new SqlParameter("@EmployeeId", EmpCode);
            SqlParam[2] = new SqlParameter("@InboundGroupId", 0);
            SqlParam[3] = new SqlParameter("@CreatedBy", AssignedBy);
            SqlParam[4] = new SqlParameter("@CallType", CallType);
            SqlParam[5] = new SqlParameter("@TransferType", TransferType);
            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[CTC].[ConvertOutboundtoInboundNAssignment]", SqlParam);
            return true;
        }

        public static string InsertCallData(DialerDispDetails _DispositionUpdate)
        {
            SqlParameter[] sqlParam = new SqlParameter[22];
            sqlParam[0] = new SqlParameter("@CallID", _DispositionUpdate.CallId);
            if (_DispositionUpdate.IsService.Equals(1))
                sqlParam[1] = new SqlParameter("@LeadID", _DispositionUpdate.LeadID);
            else
                sqlParam[1] = new SqlParameter("@LeadID", _DispositionUpdate.ParentID);


            sqlParam[2] = new SqlParameter("@EmployeeID", _DispositionUpdate.AgentCode);
            sqlParam[3] = new SqlParameter("@CallDate", _DispositionUpdate.callDate);
            sqlParam[4] = new SqlParameter("@Duration", _DispositionUpdate.Duration);
            sqlParam[5] = new SqlParameter("@talktime", _DispositionUpdate.talktime);
            sqlParam[6] = new SqlParameter("@CallType", _DispositionUpdate.CallType);
            sqlParam[7] = new SqlParameter("@Status", _DispositionUpdate.Status);
            sqlParam[8] = new SqlParameter("@productId", _DispositionUpdate.ProductID);
            if (!string.IsNullOrEmpty(_DispositionUpdate.Disposition))
                sqlParam[9] = new SqlParameter("@disposition", _DispositionUpdate.Disposition);
            sqlParam[10] = new SqlParameter("@Context", _DispositionUpdate.Context);
            sqlParam[11] = new SqlParameter("@IsBMS", _DispositionUpdate.IsBMS);
            if (!string.IsNullOrEmpty(_DispositionUpdate.CallTrackingID))
                sqlParam[12] = new SqlParameter("@CallDataID", Convert.ToInt64(_DispositionUpdate.CallTrackingID));
            else
                sqlParam[12] = new SqlParameter("@CallDataID", 0);

            sqlParam[12].Direction = ParameterDirection.InputOutput;
            sqlParam[13] = new SqlParameter("@countryCode", _DispositionUpdate.CountryCode);
            sqlParam[14] = new SqlParameter("@phone", _DispositionUpdate.dst);
            sqlParam[15] = new SqlParameter("@IP", _DispositionUpdate.AsteriskIP);
            sqlParam[16] = new SqlParameter("@C2CID", _DispositionUpdate.C2CID);

            //InsertCheckFlag Added by sundarthapa 21-02-2019
            sqlParam[17] = new SqlParameter("@InsertCompleted", _DispositionUpdate.InsertCheckFlag);
            sqlParam[17].Direction = ParameterDirection.InputOutput;
            sqlParam[18] = new SqlParameter("@t_type", _DispositionUpdate.t_type);
            sqlParam[19] = new SqlParameter("@RecFileName", _DispositionUpdate.recfile);
            sqlParam[20] = new SqlParameter("@CallingNo", _DispositionUpdate.CallingNo);
            sqlParam[21] = new SqlParameter("@CustConnectTime", DateTime.Now);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertCallData]", sqlParam);


            _DispositionUpdate.InsertCheckFlag = Convert.ToByte(sqlParam[17].Value);
            return Convert.ToString(sqlParam[12].Value);

        }

        public static bool SendCommunicationtoUnAnsLeads(sendcommunicationResponse oSendcommunicationResponse)
        {
            bool result = false;
            string response = string.Empty;
            string Json = string.Empty;
            string strexception = string.Empty;
            string url = "pbserviceapi".AppSettings();
            DateTime dt = DateTime.Now;
            SendCommResponse sendCommResponse = new SendCommResponse();
            try
            {
                Json = JsonConvert.SerializeObject(oSendcommunicationResponse);

                Dictionary<object, object> header = new Dictionary<object, object>(){
                                    {"REQUESTINGSYSTEM", "Matrix"},{"TOKEN", "pbservicetoken".AppSettings()}};
                response = CommonAPICall.CallAPI(url, Json, "POST", Convert.ToInt32("BMSTicketTimeOut".AppSettings()), "application/json", header);
                if (!string.IsNullOrEmpty(response))
                {
                    sendCommResponse = JsonConvert.DeserializeObject<SendCommResponse>(response);
                }

                result = sendCommResponse.IsSuccess;
            }
            catch (Exception ex)
            {
                strexception = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(Convert.ToString(oSendcommunicationResponse.LeadId), oSendcommunicationResponse.LeadId, strexception, "SendCommunicationtoUnAnsLeads", "MatrixCoreAPI", "WebSiteServiceDLL", JsonConvert.SerializeObject(Json), response, dt, DateTime.Now);
            }

            return result;
        }

        public static DataSet GetExitPointURL(long LeadId, long CustomerId, short ProductId)
        {
            var response = new DataSet();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[3];
                SqlParam[0] = new SqlParameter("@LeadID", LeadId);
                SqlParam[1] = new SqlParameter("@CustomerId", CustomerId);
                SqlParam[2] = new SqlParameter("@ProductId", ProductId);

                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                    CommandType.StoredProcedure,
                                                    "[MTX].[getExitPointURLbyLeadId]",
                                                    SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.Message,
                                                          "getExitPointURLbyLeadId", "MatrixCore",
                                                          "WebSiteServiceDLL", "",
                                                          "", DateTime.Now, DateTime.Now);
            }
            return response;

        }

        public static DataSet GetAgentProfileDataDLL(long LeadId)
        {
            var response = new DataSet();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@LeadID", LeadId);

                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentProfileData]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, LeadId, ex.Message, "GetAgentProfileDataDLL", "MatrixCore", "WebSiteServiceDLL", "", "", DateTime.Now, DateTime.Now);
            }
            return response;

        }

        public static DataSet GetAgentDetails(long userId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@UserId", userId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetDetailsByAgentId]",
                                            sqlParam);
        }

        public static DataSet GetCityIdByPincode(int Pincode, long LeadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@Pincode", Pincode);
            SqlParam[1] = new SqlParameter("@LeadId", LeadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetCityIdByPincode]", SqlParam);
        }

        public static DataSet GetLeadDetails(long leadId, bool isBooking)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@LeadID", leadId);
            sqlParam[1] = new SqlParameter("@IsBooking", isBooking);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetLeadAndBooking]",
                                            sqlParam);
        }
        public static DataSet GetStoreDetails(int CityID)
        {
            var response = new DataSet();
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@CityID", CityID);

                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetStorteDetailsByCityID]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.Message, "GetStoreDetails", "MatrixCore", "WebSiteServiceDLL", CityID.ToString(), "", DateTime.Now, DateTime.Now);
            }
            return response;
        }

        public static int InsertDialerDataLeadDataMapping(long leadID, string uniqueId, long mobileNo, bool isNewLead)
        {
            try
            {
                var sqlParam = new SqlParameter[11];
                sqlParam[0] = new SqlParameter("@LeadID", leadID);
                sqlParam[1] = new SqlParameter("@DialerUniqueID", uniqueId);
                sqlParam[2] = new SqlParameter("@ParentDialerUniqueID", string.Empty);
                sqlParam[3] = new SqlParameter("@MobileNo", mobileNo);
                sqlParam[4] = new SqlParameter("@CallDate", DateTime.Now);
                sqlParam[5] = new SqlParameter("@CallStatusType", 0)
                {
                    Value = 0
                };
                sqlParam[6] = new SqlParameter("@LeadType", 3);
                sqlParam[7] = new SqlParameter("@IsNewLead", isNewLead);
                sqlParam[8] = new SqlParameter("@CreatedBy", "LCIC");
                sqlParam[9] = new SqlParameter("@IsCallbackSet", 0);
                sqlParam[10] = new SqlParameter("@CreatedOn", DateTime.Now);

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[CTC].[INSERTDialerLeadMapping]",
                                                 sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, leadID, ex.Message, "INSERTDialerLeadMapping", "MatrixCore", "WebSiteServiceDLL", uniqueId, "", DateTime.Now, DateTime.Now);
            }
            return 1;
        }

        public static DataSet GetLeadDetailsbyMobileNoProductId(string mobileNo, int productId, int subProductId, string teamName, string leadSource, string utmSource, string utmMedium, string subProductIdList, string RegistrationNo)
        {
            var sqlParam = new SqlParameter[9];
            sqlParam[0] = new SqlParameter("@MobileNo", mobileNo);
            sqlParam[1] = new SqlParameter("@ProductId", productId);
            sqlParam[2] = new SqlParameter("@SubProductID", subProductId);
            sqlParam[3] = new SqlParameter("@TeamName", teamName);
            sqlParam[4] = new SqlParameter("@LeadSource", leadSource);
            sqlParam[5] = new SqlParameter("@UtmSource", utmSource);
            sqlParam[6] = new SqlParameter("@UtmMedium", utmMedium);
            sqlParam[7] = new SqlParameter("SubProductIdList", subProductIdList);
            sqlParam[8] = new SqlParameter("@RegistrationNo", string.IsNullOrEmpty(RegistrationNo) ? string.Empty : RegistrationNo);

            var data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetLeadDetailsByMobileNoProductId]",
                                                sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0)
            {
                return data;
            }
            return null;
        }

        public static SaveInfo<string> CreateLead(UTMLeadDetails details, bool IsAssigned = false)
        {
            var data = new SaveInfo<string>();
            long customerId = 0;
            string errorMessage;
            long leadId;
            long ParentleadId = 0;
            string AssignedToEcode = "";
            string AssignedToAgentName = "";
            int AssignedToGroupID = 0;
            try
            {
                var sqlParams = new Dictionary<object, object>
                {
                    { "Name", details.Name ?? string.Empty },
                    { "Gender", details.Gender },
                    { "DOB", Convert.ToDateTime(string.IsNullOrEmpty(details.DOB) ? "01/01/1990" :  details.DOB) },
                    { "Category ", details.Category },
                    { "IsDNC", details.IsDNC },
                    { "MobileNo", Convert.ToInt64(details.MobileNo) },
                    { "EmailId", details.EmailId },
                    { "CityID", details.CityID },
                    { "StateID", details.StateID },
                    { "Country", details.Country },
                    { "PostCode", details.PostCode },
                    { "LeadSource", details.LeadSource },
                    { "ProductId", details.ProductId },
                    { "Utm_source", details.Utm_source },
                    { "UTM_Medium", details.UTM_Medium },
                    { "Utm_term", details.Utm_term },
                    { "Utm_campaign", details.Utm_campaign },
                    { "OutLeadID", 0 },
                    { "error", string.Empty },
                    { "OutParentLeadID", 0},
                    { "CustomerId", details.CustomerID },
                    { "ReferralID", details.ReferralLead },
                     { "CreatedBy", details.UserId }
                };
                if (!string.IsNullOrEmpty(details.RegNo))
                {
                    sqlParams.Add("ReqNo", details.RegNo.Trim());
                }
                sqlParams.Add("SupplierId", details.SupplierId);
                sqlParams.Add("PlanId", details.PlanId);
                sqlParams.Add("SumInsured", details.SumInsured);
                sqlParams.Add("Premium", details.TotalPremium);
                sqlParams.Add("Source", details.Source);
                if(details.IsSmeFosCreateLead)
                {
                    sqlParams.Add("LeadCreationSource", "SMEFosCreateLeadPanel");
                }
                else
                {
                    sqlParams.Add("LeadCreationSource", "matrixcore-" + details.ApiSource);
                }
                sqlParams.Add("countryCode", details.CountryCode);
                sqlParams.Add("SubProductId", details.SubProductId);
                sqlParams.Add("AnnualIncome", Convert.ToString(details.AnnualIncome));
                sqlParams.Add("CompanyName", details.CompanyName);
                sqlParams.Add("OccupationId", details.OccupationId);
                sqlParams.Add("CreatedByEmpId", string.IsNullOrEmpty(details.CreatedByEmpId) ? DBNull.Value : details.CreatedByEmpId);
                sqlParams.Add("AssociationId", details.AssociationId);
                sqlParams.Add("MaxStatus", details.MaxStatusID > 0 ? details.MaxStatusID : 1);
                sqlParams.Add("CreatedByEmpName", details.CreatedByEmpName);
                SqlParameter[] parameters = SetParamterFromDictionary(sqlParams);
                parameters[17].Direction = ParameterDirection.Output;
                parameters[17].DbType = DbType.Int64;
                parameters[17].Size = 20;
                parameters[18].Direction = ParameterDirection.Output;
                parameters[18].DbType = DbType.String;
                parameters[18].Size = 500;
                parameters[19].Direction = ParameterDirection.Output;
                parameters[19].DbType = DbType.Int64;
                parameters[19].Size = 20;

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                          CommandType.StoredProcedure,
                                          "[MTX].[CreateLead]",
                                          parameters);
                errorMessage = Convert.ToString(parameters[18].Value);
                leadId = Convert.ToInt64(parameters[17].Value);
                ParentleadId = parameters[19].Value != DBNull.Value ? Convert.ToInt64(parameters[19].Value) : 0;

                bool assignLead = true;
                if (details.ProductId == 131 && !string.IsNullOrEmpty(details.AssignLead))
                {
                    assignLead = details.AssignLead == "true";
                }
                if (details.ISHNICustomer == true && details.ProductId == 2)
                {
                    AllocationLeadData LeadAllocationData = null;
                    var response = GetLeadAllocationData(leadId, "UHNI", true);
                    var _allocateLeadResponse = JsonConvert.DeserializeObject<AllocateLeadResponse>(response);
                    if (_allocateLeadResponse != null && _allocateLeadResponse.LeadDetails != null && _allocateLeadResponse.LeadDetails.Count > 0)
                    {
                        LeadAllocationData = _allocateLeadResponse.LeadDetails[0];
                        AssignedToEcode = LeadAllocationData.assignedToEcode != null ? Convert.ToString(LeadAllocationData.assignedToEcode) : "";
                        AssignedToAgentName = LeadAllocationData.assignedToAgentName != null ? Convert.ToString(LeadAllocationData.assignedToAgentName) : "";
                        AssignedToGroupID = LeadAllocationData.groupID != 0 ? Convert.ToInt32(LeadAllocationData.groupID) : 0;
                    }
                    //DataTable dt = AssignLeadToAgentByGroupID(details.AssignTogroupID, leadId, details.ProductId, 100);
                    //if (dt != null && dt.Rows != null && dt.Rows.Count > 0)
                    //{
                    //    AssignedToEcode = dt.Rows[0]["AssignedToEcode"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["AssignedToEcode"]) : "";
                    //    AssignedToAgentName = dt.Rows[0]["AssignedToAgentName"] != DBNull.Value ? Convert.ToString(dt.Rows[0]["AssignedToAgentName"]) : "";
                    //}
                }
                else
                {
                    if (ParentleadId == 0 && IsAssigned && details.UserId > 0 && assignLead && details.AssignToUserId == 0 && details.AssignTogroupID == 0)//assign to agent in case of parent 0                
                        AssignLeadToAgent(leadId, details.UserId, details.UserId, details.ProductId, 0, 79);
                    if (ParentleadId == 0 && details.AssignToUserId > 0 && details.AssignTogroupID > 0)//assign to assigned agent in case of parent 0 and supervisor creating a lead               
                        AssignLeadToAgent(leadId, details.AssignToUserId, details.UserId, details.ProductId, details.AssignTogroupID, 79);
                }

                if (details.ProductId == 131 && (details.OccupationId > 0 || details.AssociationId > 0))
                {
                    InsertSmeAdditionalInfo(details.SubProductId, details.OccupationId, details.AssociationId, leadId);
                }
            }
            catch (Exception ex)
            {
                data.IsSaved = false;
                data.Message = "Internal Error";
                data.Output = ex.Message;
                return data;
            }

            if (leadId == 0)
            {
                data.IsSaved = false;
                data.Message = "Unable to create Lead";
                data.Output = errorMessage;
            }
            else if (!string.IsNullOrEmpty(errorMessage))
            {
                data.IsSaved = false;
                data.Message = errorMessage;
                data.Output = Convert.ToString(leadId);
                data.StatusCode = 2;
                data.CustomerId = customerId;
                data.AssignedToEcode = AssignedToEcode;
                data.AssignedToAgentName = AssignedToAgentName;
                data.AssignedToGroupID = AssignedToGroupID;
            }
            else
            {
                data.IsSaved = true;
                data.Message = ParentleadId > 0 && leadId > 0 ? leadId + " Child lead is created successfully, " + ParentleadId + " - Parent lead is already active in system" : IsAssigned ? "Lead Created successfully " + Convert.ToString(leadId) : "Lead Created successfully";
                data.Output = Convert.ToString(leadId);
                data.StatusCode = 1;
                data.CustomerId = customerId;
                data.AssignedToEcode = AssignedToEcode;
                data.AssignedToAgentName = AssignedToAgentName;
                data.AssignedToGroupID = AssignedToGroupID;
            }
            return data;
        }

        private static void InsertSmeAdditionalInfo(short subProductId, short occupationId, long associationId, long leadId)
        {
            try
            {
                var sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@SubProductId", subProductId);
                sqlParam[1] = new SqlParameter("@OccupationId", occupationId);
                sqlParam[2] = new SqlParameter("@AssociationId", associationId);
                sqlParam[3] = new SqlParameter("@MatrixLeadID", leadId);
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                         CommandType.StoredProcedure,
                                         "[MTX].[InsertSmeAddInfo]",
                                         sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.Message, "InsertSmeAdditionalInfo", "WebSiteServiceDLL", "MatrixCore", string.Empty, string.Empty, DateTime.Now, DateTime.Now);
            }
        }

        public static SaveInfo<string> CreateLeadForFutureDate(UTMLeadDetails details)
        {
            var data = new SaveInfo<string>();
            long customerId = 0;
            Boolean IsRecordInserted = false;
            try
            {
                var sqlParams = new Dictionary<object, object>
                {
                    { "Name", details.Name ?? string.Empty },
                    { "Gender", details.Gender },
                    { "DOB", Convert.ToDateTime(string.IsNullOrEmpty(details.DOB) ? "01/01/1990" :  details.DOB) },
                    { "Category ", details.Category },
                    { "IsDNC", details.IsDNC },
                    { "MobileNo", Convert.ToInt64(details.MobileNo) },
                    { "EmailId", details.EmailId },
                    { "CityID", details.CityID },
                    { "StateID", details.StateID },
                    { "Country", details.Country },
                    { "PostCode", details.PostCode },
                    { "LeadSource", details.LeadSource },
                    { "ProductId", details.ProductId },
                    { "Utm_source", details.Utm_source },
                    { "UTM_Medium", details.UTM_Medium },
                    { "Utm_term", details.Utm_term },
                    { "Utm_campaign", details.Utm_campaign },
                    { "CustomerId", details.CustomerID }
                };
                if (!string.IsNullOrEmpty(details.RegNo))
                {
                    sqlParams.Add("ReqNo", details.RegNo.Trim());
                }
                sqlParams.Add("SupplierId", details.SupplierId);
                sqlParams.Add("PlanId", details.PlanId);
                sqlParams.Add("SumInsured", details.SumInsured);
                sqlParams.Add("Premium", details.TotalPremium);
                sqlParams.Add("Source", details.Source);
                sqlParams.Add("LeadCreationSource", "Whatsapp");
                sqlParams.Add("countryCode", details.CountryCode);
                sqlParams.Add("SubProductId", details.SubProductId);
                sqlParams.Add("LeadCreationDate", details.LeadCreationDate);
                sqlParams.Add("IsDataInserted", 0);
                sqlParams.Add("Comments", details.Comments);

                SqlParameter[] parameters = SetParamterFromDictionary(sqlParams);
                parameters[27].Direction = ParameterDirection.Output;
                parameters[27].DbType = DbType.Boolean;
                parameters[27].Size = 10;

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                          CommandType.StoredProcedure,
                                          "[MTX].[InsertDataForFutureLeadCreation]",
                                          parameters);
                IsRecordInserted = Convert.ToBoolean(parameters[27].Value);
            }
            catch (Exception ex)
            {
                data.IsSaved = false;
                data.Message = "Internal Error";
                data.Output = ex.Message;
                return data;
            }

            if (IsRecordInserted == false)
            {
                data.IsSaved = false;
                data.Message = "Lead will not be created, please try again";
            }
            else
            {
                data.IsSaved = true;
                data.Message = "Lead will be created on " + details.LeadCreationDate;
                data.StatusCode = 1;
                data.CustomerId = customerId;
            }
            return data;
        }

        public static string GetLeadAllocationData(long leadID, string assignmentProcess, bool toAssign = false)
        {
            DateTime requestTime = DateTime.Now;
            string data = string.Empty;
            string url = "AllocationApi".AppSettings();
            string reqData = "";
            try
            {
                Dictionary<object, object> _Dict = new()
                {
                    { "source", "matrix" },
                    { "authKey", "matrixAPIauthKey".AppSettings() },
                    { "clientKey", "matrixAPIclientKey".AppSettings() }
                };
                int timeout = Convert.ToInt32(1000);
                url += "Allocation/AllocateHealthLeads";

                dynamic dataToPost = new ExpandoObject();
                dataToPost.leadId = leadID;
                dataToPost.toAssign = toAssign;
                dataToPost.assignmentProcess = assignmentProcess;
                reqData = JsonConvert.SerializeObject(dataToPost);

                if (!string.IsNullOrEmpty(url))
                    data = CommonAPICall.CallAPI(url, reqData, "POST", timeout, "application/json", _Dict);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", Convert.ToInt64(leadID), ex.ToString(), "AllocateHealthLeadsApi", "MatrixCore", "WebSiteDLL", reqData, data.ToString(), requestTime, DateTime.Now);
                return null;
            }
            return data;
        }

        public static SqlParameter[] SetParamterFromDictionary(Dictionary<object, object> param)
        {
            if (param.Count <= 0)
                return Array.Empty<SqlParameter>();
            else
                return
                    param.Select(s => new SqlParameter("@" + s.Key, s.Value)).ToArray();
        }

        public static bool LogLeadHistory(long userId, long leadId, string comments, int EventType = 2)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                var sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@UserID", userId);
                sqlParam[1] = new SqlParameter("@LeadID", leadId);
                sqlParam[2] = new SqlParameter("@EventType", EventType);
                sqlParam[3] = new SqlParameter("@Comments", comments);
                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                         CommandType.StoredProcedure,
                                         "[MTX].[SaveLeadHistory]",
                                         sqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, ex.Message, "LogLeadHistory",
                                                          "WebSiteServiceDLL", "MatrixCore", string.Empty,
                                                          string.Empty, requestTime, DateTime.Now);
                return false;
            }
            return true;
        }
        
        public static (bool, string) UploadUTMCampaign(UTMCampaignDetails request)
        {
            try
            {
                SqlParameter[] sqlParam = new SqlParameter[6];
                sqlParam[0] = new SqlParameter("@UtmCode", string.IsNullOrEmpty(request.UtmCode) == false ? request.UtmCode : "");
                sqlParam[1] = new SqlParameter("@UtmCampaign", string.IsNullOrEmpty(request.UtmCampaign) == false ? request.UtmCampaign : "");
                sqlParam[2] = new SqlParameter("@link", request.link);
                sqlParam[3] = new SqlParameter("@UTMSOURCE", string.IsNullOrEmpty(request.UtmSource) == false ? request.UtmSource.ToLower() : "");
                sqlParam[4] = new SqlParameter("@UTMMedium", string.IsNullOrEmpty(request.UtmMedium) == false ? request.UtmMedium : "");
                sqlParam[5] = new SqlParameter("@LeadSource", string.IsNullOrEmpty(request.LeadSource) == false ? request.LeadSource.ToLower() : "");

                SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UploadUTMCampaignDetails]", 3000, sqlParam);
                return (true, "Success");

            }
            catch (Exception ex)
            {
                return (false, ex.ToString());
            }
        }


        public static DataSet FOSProductMaster(string Source)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@Source", Source);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetFOSProductMaster]", 3000, sqlParam);
        }

        public static DataSet GetCityGroupMapping(Int32 CityId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            //sqlParam[0] = new SqlParameter("@ProductId", ProductId);
            sqlParam[0] = new SqlParameter("@CityId", CityId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetStoreAndAdvisorInfo]", 3000, sqlParam);
        }
        public static DataSet GetStoreMaster()
        {
            SqlParameter[] sqlParam = new SqlParameter[0];
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetStoreAndAdvisorInfo]", 3000, sqlParam);
        }
        public static DataSet GetGroupLangMaster()
        {
            SqlParameter[] sqlParam = new SqlParameter[0];
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetGroupLangMaster]", 3000, sqlParam);
        }
        public static DataSet GetFosCityMaster(Int32 ProductId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@ProductId", ProductId);
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[GetFosCityMaster]", 3000, sqlParam);
        }



        public static DataSet GetCustomerLeads(CustomerLeadsRequest request)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@SearchInput", request.SearchInput);
            sqlParam[1] = new SqlParameter("@SearchType", request.SearchType);
            sqlParam[2] = new SqlParameter("@UserId", request.UserId);


            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetDuplicateLeadsForUAE]",
                                            3000,
                                            sqlParam);
        }

        public static DataSet GetLeadsByCustId(CustomerLeadsRequest request)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@CustomerID", request.SearchInput);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetLeadsByCustId]",
                                            3000, 
                                            sqlParam);
        }
        public static DataSet GetActiveLeadAppSet(long CustId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CustId", CustId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[FOS].[GetActiveLeadAppSet]", 3000, sqlParam);
        }

        public static bool SetCsatAssignLead(CSATModel oCSATModel)
        {
            bool result = false;
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", oCSATModel.LeadId);
            sqlParam[1] = new SqlParameter("@IsFos", oCSATModel.IsFos);

            var sqlParameter = new SqlParameter();
            sqlParameter.Direction = ParameterDirection.Output;
            sqlParameter.ParameterName = "@Result";
            sqlParameter.SqlDbType = SqlDbType.Bit;
            sqlParam[2] = sqlParameter;
            SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[FOS].[CSATAssignLead]", 3000, sqlParam);
            if (sqlParam[2].Value != DBNull.Value)
                result = Convert.ToBoolean(sqlParam[2].Value);

            return result;
        }


        public static DataRow AddUpdateCallAnalysis(CallAnalyserRequest request)
        {
            string errorMessage = string.Empty;
            var sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@LeadId", request.LeadId);
            sqlParam[1] = new SqlParameter("@CallDataId", request.CallDataId);
            sqlParam[2] = new SqlParameter("@CallStatusId", request.CallStatusId);

            var result = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                                 CommandType.StoredProcedure,
                                                 "[MTX].[AddUpdateCallAnalysisDataV2]",
                                                 sqlParam);
            if (result != null && result.Tables != null && result.Tables.Count > 0 && result.Tables[0].Rows != null && result.Tables[0].Rows.Count > 0)
            {
                return result.Tables[0].Rows[0];
            }
            return null;
        }

        public static void AssignLeadToAgent(long leadId, long agentId, long AssignedByAgentId, int productId, long AssignToGroupId, short jobId)
        {
            string error = string.Empty;
            try
            {
                var sqlParam = new SqlParameter[6];
                sqlParam[0] = new SqlParameter("@AssignedTo_AgentId", agentId);
                sqlParam[1] = new SqlParameter("@AssignedBy_AgentId", AssignedByAgentId);
                sqlParam[2] = new SqlParameter("@ProductId", productId);
                sqlParam[3] = new SqlParameter("@LeadId", leadId);
                sqlParam[4] = new SqlParameter("@GroupId", AssignToGroupId);
                sqlParam[5] = new SqlParameter("@JobId", jobId);

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),
                                          CommandType.StoredProcedure,
                                          "[CRM].[Insert_AssignedToAgent]",
                                          sqlParam);
            }
            catch (Exception ex)
            {
                error = ex.ToString();
            }
            finally
            {
                if (productId == 131 || !string.IsNullOrEmpty(error))
                {
                    string res = "AssignedTo_AgentId-" + agentId + ",AssignedBy_AgentId-" + AssignedByAgentId + ",GroupId" + AssignToGroupId;
                    LoggingHelper.LoggingHelper.AddloginQueue(leadId.ToString(), leadId, error, "AssignLeadToAgent", "WebSiteServicDLL", agentId.ToString(), res, string.Empty, DateTime.Now, DateTime.Now);
                }
            }
        }

        public static bool SavePlanRecommendation(PlanRecommendationModel planRecommModel)
        {
            var xEle = new XElement("PlanRecommendationList",
                                        from dr in planRecommModel.AlternatePlanDetails.SelectedPlans
                                        select new XElement("PlanRecommendationList",
                                                       new XAttribute("PlanId", dr.PlanId),
                                                       new XAttribute("SupplierId", dr.SupplierId),
                                                       new XAttribute("Remarks", dr.Remarks),
                                                       new XAttribute("CreatedBy", dr.CreatedBy)
                                                   )).ToString();
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@XMLdata", xEle);
            SqlParam[0].SqlDbType = SqlDbType.Xml;
            SqlParam[1] = new SqlParameter("@LeadId", planRecommModel.BookingId);
            SqlParam[2] = new SqlParameter("@CreatedBy", planRecommModel.CreatedBy);
            SqlHelper.ExecuteDataset(new SqlConnection(ConnectionClass.LivesqlConnection()), CommandType.StoredProcedure, "[MTX].[SavePlanRecommendation]", SqlParam);
            return true;
        }
        public static DataSet GetPlanRecommendation(long LeadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[mtx].[GetPlanRecommendation]", 3000, sqlParam);
        }

        public static DataSet GetAgentDetailsByVirtualNo(string virtualNo)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@VirtualNo", virtualNo);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentDetailsByVirtualNo]", 3000, sqlParam);
        }


        public static DataSet GetProductListByEmpId(string empId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@EmpId", empId);
            sqlParam[1] = new SqlParameter("@RoleId", 13);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetProductListByEmpIdV2]", 3000, sqlParam);
        }

        public static bool PushNotification<T>(T request, string collectionTable)
        {
            var mongoHelper = new MongoHelper(SingletonClass.OneLeadDB());
            try
            {
                mongoHelper.InsertData(request, collectionTable);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public static NotificationsResponse GetNotificationsData(string userId, short? tillNumberOfDays)
        {
            string empId = string.Empty;
            DateTime tillDate = DateTime.Now.AddDays(tillNumberOfDays != null ? (short)tillNumberOfDays : -7);
            var mongoHelper = new MongoHelper(SingletonClass.OneLeadDB());
            var response = new NotificationsResponse();
            IMongoFields fields = Fields.Exclude("_id");

            DataSet data = GetAgentDetails(Convert.ToInt64(userId));
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                empId = data.Tables[0].Rows[0]["EmployeeId"] != DBNull.Value ? data.Tables[0].Rows[0]["EmployeeId"].ToString() : default;
            }
            if (!string.IsNullOrEmpty(empId))
            {
                IMongoQuery query = Query<NotificationRequest>.Where(x => x.EmpId == empId && x.DateTime >= tillDate);
                response.Notifications = mongoHelper.GetDocuments<NotificationRequest>(query, DataAccessLibrary.MongoCollection.NotificationsData(), SortBy.Descending("DateTime"), fields, 0, 100);
            }

            if (response.Notifications != null && response.Notifications.Count > 0)
            {
                foreach (var notification in response.Notifications)
                {
                    if (notification.TimeStamp == null || string.IsNullOrEmpty(notification.TimeStamp.ToString()))
                    {
                        notification.TimeStamp = DateTime.Now.ToString("yyyyMMddHHmmss");
                    }
                }
                IMongoQuery query = Query<ReadNotificationRequest>.Where(x => x.UserId == userId && x.DateTime >= tillDate);
                List<ReadNotificationRequest> readNotifications = mongoHelper.GetDocuments<ReadNotificationRequest>(query, DataAccessLibrary.MongoCollection.ReadNotificationsData(), SortBy.Ascending("DateTime"), fields, 0, 100);

                if (readNotifications != null && readNotifications.Count > 0)
                    response.ReadNotifications = readNotifications.Select(x => x.NotificationId).ToList();
            }
            return response;
        }

        public static string CreateLeadValidation(CreateLeadRequest request)
        {
            string msg = string.Empty;
            try
            {
                Dictionary<object, object> sqlparam = new()
                {
                    { "LeadSource", request.LeadSource },
                    { "ProductId", request.ProductId },
                    { "ReferralId", request.ReferralLead },
                    { "UserID", request.UserId },
                    { "Name", request.Name },
                    { "CustomerId", request.CustomerId },
                    { "MobileNo", request.MobileNo },
                    { "UtmMedium", request.UtmMedium }
                };

                var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CreateLeadValidation]", SetParamterFromDictionary(sqlparam));
                if (result != null)
                    return Convert.ToString(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error in CreateLeadValidation-" + ex.Message.ToString() + " - " + DateTime.Now);
                msg = string.Empty;
            }
            return msg;
        }

        public static bool ValidateCustomerContact(long mobileNo)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@MobileNo", mobileNo);
            var data = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[ChkInvalidMobileNo]", sqlParam);

            return data != null &&
                   data.Tables.Count > 0 &&
                   data.Tables[0].Rows.Count > 0 &&
                   Convert.ToBoolean(data.Tables[0].Rows[0]["IsInvalidNo"]);
        }

        public static DataTable GetSubProductByVN(string virtualNo)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@VirtualNo", virtualNo);
            DataSet data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSMESubProductByVN]", sqlParam);
            if (data != null && data.Tables.Count > 0)
                return data.Tables[0];
            else
                return null;
        }
        public static void MarkMobileNoAsValid(long mobileNo)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@MobileNo", mobileNo);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[MarkMobileNoAsValid]", sqlParam);
        }

        public static bool SaveAIAudioData(AIAudioModel aiModel)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@CallDataID", aiModel.CallDataID);
            SqlParam[1] = new SqlParameter("@LeadID", aiModel.LeadID);
            SqlParam[2] = new SqlParameter("@AudioClipPath", aiModel.audioClip);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveAIAudioData]", SqlParam);
            return true;

        }

        public static List<KnowYoutAdvisorModel> GetKnowYoutAdvisorData(long CustId)
        {
            MongoDB.Driver.IMongoFields Field = Fields.Include("CustId", "CreatedOn", "UpdatedOn", "Count");
            MongoDB.Driver.IMongoQuery varquery = Query<KnowYoutAdvisorModel>.EQ(p => p.CustId, CustId);
            MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
            var data = objCommDB.GetDocuments<KnowYoutAdvisorModel>(varquery, Field, DataAccessLibrary.MongoCollection.KnowYoutAdvisor());
            return data;
        }

        public static Int16 InsertUpdateAdvisoreData(KnowYoutAdvisorModel _KnowYoutAdvisorModel, Int16 IsUpdate = 0)
        {
            Int16 status = 0;
            string msg = string.Empty;
            DateTime dt = DateTime.Now;
            UpdateBuilder<KnowYoutAdvisorModel> update = null;
            var mongoHelper = new MongoHelper(SingletonClass.OneLeadDB());
            try
            {

                IMongoQuery varquery = Query.And(
                    Query<KnowYoutAdvisorModel>.EQ(p => p.CustId, _KnowYoutAdvisorModel.CustId),
                    Query<KnowYoutAdvisorModel>.EQ(p => p._id, _KnowYoutAdvisorModel._id)
                    );
                if (IsUpdate == 1)
                {
                    update = Update<KnowYoutAdvisorModel>.Set(p => p.Count, _KnowYoutAdvisorModel.Count + 1)
                                                   .Set(p => p.UpdatedOn, DateTime.Now);

                    MongoHelper objCommDB = new MongoHelper(SingletonClass.OneLeadDB());
                    objCommDB.UpdateDocument(varquery, update, DataAccessLibrary.MongoCollection.KnowYoutAdvisor());
                }
                else
                {
                    dynamic request = new ExpandoObject();
                    request.Count = 1;
                    request.EmployeeId = _KnowYoutAdvisorModel.EmployeeId;
                    request.CustId = _KnowYoutAdvisorModel.CustId;
                    request.CreatedOn = DateTime.Now;

                    mongoHelper.InsertData(request, DataAccessLibrary.MongoCollection.KnowYoutAdvisor());
                }

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, ex.ToString(), "InsertUpdateAdvisoreData", "webSiteServiceBll", "Matrixcore", JsonConvert.SerializeObject(_KnowYoutAdvisorModel), "", dt, DateTime.Now);

            }
            return status;
        }        

        public static void UpdateReferralId(long ReferralId, long LeadId, long UserId)
        {
            DateTime dt = DateTime.Now;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[4];
                SqlParam[0] = new SqlParameter("@ReferralId", ReferralId);
                SqlParam[1] = new SqlParameter("@LeadId", LeadId);
                SqlParam[2] = new SqlParameter("@CreatedBy", UserId);
                SqlParam[3] = new SqlParameter("@MaxStatus", 4);

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateReferralId]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", ReferralId, "", "UpdateReferralId", "WebSiteServiceBLL", "MatrixCoreAPI", LeadId.ToString(), string.Empty, dt, DateTime.Now);

            }


        }

        public static DataSet GetLeadAssignDetails(long LeadID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", Convert.ToInt64(LeadID));
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAssignedAgentData]", SqlParam);

        }
        public static void InsertAgentAPEData(InsertAgentAPEDataRequest objreq, DateTime StartTime, DateTime EndTime)
        {
            DateTime dt = DateTime.Now;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[7];
                SqlParam[0] = new SqlParameter("@AgentId", Convert.ToInt64(objreq.AgentId));
                SqlParam[1] = new SqlParameter("@Location", (!string.IsNullOrEmpty(Convert.ToString(objreq.Location))) ? Convert.ToString(objreq.Location) : "Header");
                SqlParam[2] = new SqlParameter("@Message", Convert.ToString(objreq.Message));
                SqlParam[3] = new SqlParameter("@Description", Convert.ToString(objreq.Description));
                SqlParam[4] = new SqlParameter("@CreatedBy", Convert.ToInt64(objreq.CreatedBy));
                SqlParam[5] = new SqlParameter("@StartTime", StartTime);
                SqlParam[6] = new SqlParameter("@EndTime", EndTime);

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertAgentAPEData]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(objreq.AgentId.ToString(), objreq.AgentId, ex.ToString(), "InsertAgentAPEData", "WebSiteServiceDLL", "MatrixCoreAPI", objreq.ToString(), "StartTime - " + StartTime + "  EndTime - " + EndTime, dt, DateTime.Now);

            }
        }

        public static DataSet GetApplicationMonitoringMasterDLL()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetApplicationMonitoringMasterData]", 3000);
        }

        public static DataSet PushApplicationMonitoringDetails(AppMonitorReqModel objAppMonitorReqModel)
        {
            DataSet ds = null;
            Guid obj = Guid.NewGuid();
            var xEle = new XElement("ApplicationMonitoringList",
                                        from dr in objAppMonitorReqModel.MemberDetails
                                        select new XElement("ApplicationMonitoringList",
                                                       new XAttribute("Name", dr.Name),
                                                       new XAttribute("MobileNo", dr.MobileNo),
                                                       new XAttribute("Level", dr.Level)
                                                   )).ToString();
            SqlParameter[] SqlParam = new SqlParameter[8];
            SqlParam[0] = new SqlParameter("@XMLdata", xEle);
            SqlParam[0].SqlDbType = SqlDbType.Xml;
            SqlParam[1] = new SqlParameter("@TeamID", objAppMonitorReqModel.TeamID);
            SqlParam[2] = new SqlParameter("@ApplicationID", objAppMonitorReqModel.ApplicationID);
            SqlParam[3] = new SqlParameter("@IssueCategoryID", objAppMonitorReqModel.IssueCategoryID);
            SqlParam[4] = new SqlParameter("@IsRingAllForSameLevel", (objAppMonitorReqModel.IsRingAllForSameLevel == true ? 1 : 0));
            SqlParam[5] = new SqlParameter("@UniqueID", ((objAppMonitorReqModel.ID != null && objAppMonitorReqModel.ID > 0) ? objAppMonitorReqModel.UniqueID : (obj.ToString()).Replace("-", "")).ToLower());
            SqlParam[6] = new SqlParameter("@AlertMessage", objAppMonitorReqModel.AlertMessage);
            SqlParam[7] = new SqlParameter("@ID", (objAppMonitorReqModel.ID != null && objAppMonitorReqModel.ID > 0) ? objAppMonitorReqModel.ID : 0);
            ds = SqlHelper.ExecuteDataset(new SqlConnection(ConnectionClass.LivesqlConnection()), CommandType.StoredProcedure, "[MTX].[SaveApplicationMonitoringDetails]", SqlParam);
            return ds;
        }
        public static bool SaveURLForApplnMonitor(string UniqueID, string URL)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@URL", URL.ToString());
            SqlParam[1] = new SqlParameter("@UniqueID", UniqueID.ToString());
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveURLForApplnMonitor]", SqlParam);
            return true;
        }
        public static DataSet GetApplicationMonitoringDataDLL()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetApplicationMonitoringData]", 3000);
        }
        public static bool DeleteApplicationData(int ID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@ID", ID);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[DeleteApplnMonitorData]", SqlParam);
            return true;
        }

        public static int GetCountryID(int countryCode)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CountryCode", countryCode);

            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCountryIdByCode]", sqlParam);
            if (result != null)
                return Convert.ToInt32(result);
            else
                return 392;
        }

        public static DataSet GetRenewalLeadData(long leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);
            SqlParam[1] = new SqlParameter("@ProductId", 131);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetRenewalLeadByRefferalId]",
                                            SqlParam);
        }

        public static void SetPetDetails(PetDetails petDetail)
        {
            var sqlParam = new SqlParameter[8];
            sqlParam[0] = new SqlParameter("@LeadID", petDetail.LeadID);
            sqlParam[1] = new SqlParameter("@BreedType", petDetail.BreedType);
            sqlParam[2] = new SqlParameter("@Name", petDetail.Name);
            sqlParam[3] = new SqlParameter("@Age", petDetail.Age);
            sqlParam[4] = new SqlParameter("@Weight", petDetail.Weight);
            sqlParam[5] = new SqlParameter("@Category", petDetail.Category);
            sqlParam[6] = new SqlParameter("@PetPrice", Convert.ToDecimal(petDetail.PetPrice));
            sqlParam[7] = new SqlParameter("@SA", Convert.ToDecimal(petDetail.SumInsured));

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.SetPetInsurance", sqlParam);

        }

        public static DataSet GetSmeLeadData(long leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetSmeData]",
                                            SqlParam);

        }

        public static DataSet GetSuppliers()
        {
            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetCoreServiceSupplier]",
                                            null);
        }

        public static DataRow GetLeadBasicInfo(long leadId)
        {
            DataRow dataRow = null;
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            var data = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetLeadBasicInfo]", sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows.Count > 0)
            {
                dataRow = data.Tables[0].Rows[0];
            }
            return dataRow;
        }



        //public static DataSet GetLeadsByCustId(CustomerLeadsRequest request)
        //{
        //    SqlParameter[] sqlParam = new SqlParameter[3];
        //    sqlParam[0] = new SqlParameter("@CustomerID", request.SearchInput);

        //    return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
        //                                    CommandType.StoredProcedure,
        //                                    "[MTX].[GetLeadsByCustId]",
        //                                    3000,
        //                                    sqlParam);
        //}

        public static bool UpdateSelfiesData(SelfieModel selfieModel)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@AppointmentId", selfieModel.AppointmentId);
            SqlParam[1] = new SqlParameter("@AccuracyFlag", selfieModel.AccuracyFlag);
            SqlParam[2] = new SqlParameter("@AccuracyPct", selfieModel.AccuracyPct);
            SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[FOS].[UpdateSelfieData]", SqlParam);
            return true;
        }

        public static void SetCustomerCtcClick(long leadId, bool isCallBackScheduled)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);
            SqlParam[1] = new SqlParameter("@IsCallBackScheduled", isCallBackScheduled);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SetCustomerCtcClick]", SqlParam);
        }

        public static bool InsertShortCallData(ShortCallData obj)
        {
            SqlParameter[] SqlParam = new SqlParameter[5];
            SqlParam[0] = new SqlParameter("@LeadID", obj.LeadID);
            SqlParam[1] = new SqlParameter("@CallDataID", obj.CallDataID);
            SqlParam[2] = new SqlParameter("@ProductID", obj.ProductID);
            SqlParam[3] = new SqlParameter("@TalkTime", obj.TalkTime);
            SqlParam[4] = new SqlParameter("@CallDate", obj.CallDate);
            return SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertShortCallData]", SqlParam) > 0;
        }

        public static void PushHWEligibleData(HWEligibleData obj)
        {
            SqlParameter[] SqlParam = new SqlParameter[5];
            SqlParam[0] = new SqlParameter("@LeadId", obj.LeadId);
            SqlParam[1] = new SqlParameter("@IsHWWorking", obj.IsHWWorking);
            SqlParam[2] = new SqlParameter("@IsHWPitched", obj.IsHWPitched);
            SqlParam[3] = new SqlParameter("@IsSpousePlanBooked", obj.IsSpousePlanBooked);
            SqlParam[4] = new SqlParameter("@UserId", obj.UserId);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[StoreHWEligibleData]", SqlParam);
        }
        
        public static bool SetCouponRedeemValue(long customerId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@TypeId", 1);
            return SqlHelper.ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetSetCouponRedeem]", sqlParam) > 0;
        }

        public static bool GetCouponRedeemValue(long customerId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@TypeId", 2);
            var response = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetCouponRedeem]", sqlParam);
            return Convert.ToBoolean(response);
        }

        public static DataSet SetCouponRaiseRequest(CouponRedeemModel obj)
        {
            bool res = false;
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@CustomerId", obj.CustomerId);
            sqlParam[1] = new SqlParameter("@LeadId", obj.LeadId);
            sqlParam[2] = new SqlParameter("@UserId", obj.UserId);
            sqlParam[3] = new SqlParameter("@TypeId", 3);
            return SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetSetCouponRedeem]", sqlParam);
            
        }

        public static DataSet GetCouponRaiseRequest(string UserId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@TypeId", 4);
            sqlParam[1] = new SqlParameter("@UserId", Convert.ToInt64(UserId));

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetCouponRedeem]", sqlParam);
        }

        public static bool UpdateCouponRaiseRequest(long customerId, long releasedBy)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@ReleasedBy", releasedBy);
            sqlParam[2] = new SqlParameter("@TypeId", 5);
            return SqlHelper .ExecuteNonQuery(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[GetSetCouponRedeem]", sqlParam) > 0;
        }

        public static void PushVCPitchData(VCPitchData obj)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", obj.LeadID);
            SqlParam[1] = new SqlParameter("@CallDataID", obj.CallDataID);
            SqlParam[2] = new SqlParameter("@ProductID", obj.ProductID);
            SqlParam[3] = new SqlParameter("@Talktime", obj.Talktime);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[StorePushVCPitchData]", SqlParam);
        }


        public static void SaveFeatureData(FeatureData featureData)
        {
            SqlParameter[] SqlParam = new SqlParameter[2];
            SqlParam[0] = new SqlParameter("CustomerId", featureData.CustomerId);
            SqlParam[1] = new SqlParameter("Feature", featureData.Feature);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveCustomerFeatureData]", SqlParam);
        }

        public static DataSet GetActiveLeads(string InputValue, int TypeId, string source)
        {
            string ProcName = "[MTX].[GetActiveLeadsForBMS]";

            SqlParameter[] sqlParam = new SqlParameter[2];
            if(TypeId == 0)
            {
                sqlParam[0] = new SqlParameter("@CustomerId", Convert.ToInt64(InputValue));
            }
            else if(TypeId == 1)
            {
                sqlParam[0] = new SqlParameter("@EmailId", InputValue);
            }
            sqlParam[1] = new SqlParameter("@TypeId", TypeId);

            if(source.ToLower() == "pbapp")
            {
                ProcName = "[MTX].[GetActiveLeadsForApp]";
            }

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, ProcName, sqlParam);
        }

        public static int RejectAllLeads(string ECode, string source,long LeadId, string CustomerName, bool RejectAll, string RegistrationNumber, string RejectionReason, int ReasonId)
        {
            SqlParameter[] sqlParam = new SqlParameter[9];
            sqlParam[0] = new SqlParameter("@EmpCode", Convert.ToString(ECode));
            sqlParam[1] = new SqlParameter("@Source", Convert.ToString(source));
            sqlParam[2] = new SqlParameter("@LeadID", Convert.ToInt64(LeadId));
            sqlParam[3] = new SqlParameter("@name", Convert.ToString(CustomerName));
            sqlParam[4] = new SqlParameter("@rejectAll", Convert.ToBoolean(RejectAll));
            sqlParam[5] = new SqlParameter("@regNo", Convert.ToString(RegistrationNumber));
            sqlParam[6] = new SqlParameter("@RejectionReason", Convert.ToString(RejectionReason));

            var sqlParameter = new SqlParameter();
            sqlParameter.Direction = ParameterDirection.Output;
            sqlParameter.ParameterName = "@res";
            sqlParameter.SqlDbType = SqlDbType.TinyInt;
            sqlParam[7] = sqlParameter;
            sqlParam[8] = new SqlParameter("@SubStatusID", ReasonId);

            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[RejectAllLeads]", 3000, sqlParam);
           
            return Convert.ToInt32(sqlParam[7].Value);

        }

        public static DataSet GetLeadByCustomerId(long customerId, string regNo)
        {
            SqlParameter[] sqlParam = new SqlParameter[3];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@RegNo", regNo);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetLeadByCustomerIdAndRegNo]", 3000, sqlParam);
        }

        public static bool IsCustomerDNC(long mobileNo)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@MobileNo", mobileNo);
            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(),CommandType.StoredProcedure,"[MTX].[IsCustomerDNC]",SqlParam);
            
            return Convert.ToBoolean(result);
        }

        public static void SaveAssistanceData(AssistanceData obj)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@CustomerId", obj.CustomerId);
            SqlParam[1] = new SqlParameter("@ProductID", obj.ProductID);
            SqlParam[2] = new SqlParameter("@NeedAssistance", obj.NeedAssistance);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveAssistMultiProduct]", SqlParam);
        }

        public static void UnassignDumpLead(long leadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UnassignDumpLead]", SqlParam);
        }

        public static DataSet GetCouponDataByLeadId(long leadId)
        {
            SqlParameter[] sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@TypeId", 6);
            sqlParam[1] = new SqlParameter("@LeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetCouponRedeem]", sqlParam);
        }
        public static bool InsertIVRFeedBack(string agentid, string leadid, string rating, string source, string process, long MobileNo, int ProductId, long CallDataId, long AppointmentId)
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();
            SqlParameter[] sqlparm = new SqlParameter[9];
            sqlparm[0] = new SqlParameter("@EmployeeId", agentid);
            sqlparm[1] = new SqlParameter("@LeadId", leadid);
            sqlparm[2] = new SqlParameter("@Rating", rating);
            sqlparm[3] = new SqlParameter("@Source", source);
            sqlparm[4] = new SqlParameter("@Process", process);
            sqlparm[5] = new SqlParameter("@MobileNo", MobileNo);
            sqlparm[6] = new SqlParameter("@ProductId", ProductId);
            sqlparm[7] = new SqlParameter("@CallDataId", CallDataId);
            sqlparm[8] = new SqlParameter("@AppointmentId", AppointmentId);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "MTX.InsertUpdateIVRFeedBack", sqlparm);
            return true;
        }


        public static DataSet GetQueueDetailsForDailer(long leadId, int productId, int subProductId, string leadSource, bool IsFOSIbProcess)
        {
            var sqlParam = new SqlParameter[4];
            sqlParam[0] = new SqlParameter("@leadId", leadId);
            sqlParam[1] = new SqlParameter("@ProductId", productId);
            sqlParam[2] = new SqlParameter("@SubProductID", subProductId);
            sqlParam[2] = new SqlParameter("@leadSource", leadSource);
            sqlParam[3] = new SqlParameter("@IsFOSIbProcess", IsFOSIbProcess);
            var data = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                                CommandType.StoredProcedure,
                                                "[MTX].[GetQueueDetailsForDailer]",
                                                sqlParam);
            if (data != null && data.Tables != null && data.Tables.Count > 0 && data.Tables[0].Rows != null && data.Tables[0].Rows.Count > 0)
            {
                return data;
            }
            return null;
        }

        public static void InsertUpdateCustNANCInMongo(CustomerNANC oCustomerNANC)
        {
            if (oCustomerNANC.CallDate == DateTime.MinValue || oCustomerNANC.CustID == 0 || oCustomerNANC.CallDate.Date != DateTime.Today) return;
            

            MongoHelper objOneleadDB = new(SingletonClass.OneLeadDB());
            var query = Query<CustomerNANC>.Where((CustomerData) => CustomerData.CustID == oCustomerNANC.CustID);
            var customerNANCData = objOneleadDB.FindOneDocument<CustomerNANC>(query, DataAccessLibrary.MongoCollection.CustomerNANCAttempts());        

            if (customerNANCData != null)
            {
                short TodayNANCCount = (short)(customerNANCData.TodayNANCCount + 1);

                if (customerNANCData.CallDate == oCustomerNANC.CallDate) return;  // data already updated
                
                if (customerNANCData.CallDate.Date != DateTime.Now.Date) { // Today's first NANC
                    TodayNANCCount = 1;
                }
                UpdateBuilder<CustomerNANC> update = Update<CustomerNANC>.Set(p => p.CallDate, oCustomerNANC.CallDate)
                                                                            .Set(p => p.TodayNANCCount, TodayNANCCount)
                                                                            .Set(p => p.UpdatedOn, DateTime.Now);

                objOneleadDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.CustomerNANCAttempts());
            }
            else
            {
                oCustomerNANC.TodayNANCCount = 1;
                objOneleadDB.InsertData(oCustomerNANC, DataAccessLibrary.MongoCollection.CustomerNANCAttempts());
            }
        }
        
        public static bool AssignChatLead(long leadId, string EmployeeId, int CreatedBy, int leadrank)
        {
           
            SqlParameter[] param = new SqlParameter[5];
            param[0] = new SqlParameter("@leadId", leadId);
            param[1] = new SqlParameter("@EmployeeId", EmployeeId);
            param[2] = new SqlParameter("@CreatedBy", CreatedBy);
            param[3] = new SqlParameter("@res", 0);
            param[3].Direction = ParameterDirection.Output;
            param[4] = new SqlParameter("@Rank", leadrank);

            int result = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(),"MTX.AssignChatLead", param);
            return true;
        }

        public static bool IsValidMobileNo(long MobileNo)
        {
            bool result = false;

            SqlParameter[] param = new SqlParameter[2];
            param[0] = new SqlParameter("@MobileNo", MobileNo);

            var sqlParameter = new SqlParameter();
            sqlParameter.Direction = ParameterDirection.Output;
            sqlParameter.ParameterName = "@IsActive";
            sqlParameter.SqlDbType = SqlDbType.Bit;
            param[1] = sqlParameter;
            SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[IsValidMobileNo]", 3000, param);

            result = Convert.ToBoolean(param[1].Value);
            return result;
        }

        public static DataSet GetTermLeadsPayUIncome(long CustomerID)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("CustomerId", CustomerID);

            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[TermAllocation_GetCustPayUIncome]", SqlParam);

            if (ds == null || ds.Tables.Count <= 0 || ds.Tables[0].Rows.Count == 0)
                return null;

            return ds;
        }

        public static void Insert2LacLeadsPushtoTerm(long leadId, long AnnualIncome=0, long CustomerID=0, string DerivedFrom="")
        {
            string Connectionstring = ConnectionClass.LivesqlConnection();

            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadID", leadId);
            SqlParam[1] = new SqlParameter("@AnnualIncome", AnnualIncome);
            SqlParam[2] = new SqlParameter("@CustomerID", CustomerID);
            SqlParam[3] = new SqlParameter("@Derivedfrom", DerivedFrom);

            SqlHelper.ExecuteDataset(new SqlConnection(Connectionstring), CommandType.StoredProcedure, "[MTX].[2LacLeads_PushtoTerm_Track]",SqlParam);
        }

        public static DataSet GetAgentProfileDataByECodeDLL(string ECode)
        {
            var response = new DataSet();
            DateTime rqstTime = DateTime.Now;
            try
            {
                SqlParameter[] SqlParam = new SqlParameter[1];
                SqlParam[0] = new SqlParameter("@ECode", ECode);

                response = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetAgentProfileByEmpID]", SqlParam);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue(ECode, 0, ex.Message, "GetAgentProfileDataByECodeDLL", "MatrixCore", "WebSiteServiceDLL", "", "", rqstTime, DateTime.Now);
            }
            return response;

        }

        public static void SetSmeLeadDetails(CreateLeadRequest request, long LeadId)
        {
            SqlParameter[] SqlParam = new SqlParameter[26];
            SqlParam[0] = new SqlParameter("@LeadID", Convert.ToInt64(LeadId));
            SqlParam[1] = new SqlParameter("@ParentCompany", Convert.ToString(request.ParentCompany));
            SqlParam[2] = new SqlParameter("@LinkedinConnection", Convert.ToInt16(request.LinkedinConnection));
            SqlParam[3] = new SqlParameter("@LinkedinLink", Convert.ToString(request.LinkedinLink));
            SqlParam[4] = new SqlParameter("@DecisionMakerCityId", Convert.ToString(request.DecisionMakerCityId));
            SqlParam[5] = new SqlParameter("@ExecutiveRole", Convert.ToString(request.ExecutiveRole));
            SqlParam[6] = new SqlParameter("@IndustryTypeId", Convert.ToInt32(request.IndustryTypeId));
            SqlParam[7] = new SqlParameter("@CreatedBy", Convert.ToInt32(request.UserId));
            SqlParam[8] = new SqlParameter("@Premium", Convert.ToDecimal(request.Premium));
            SqlParam[9] = new SqlParameter("@CIN", Convert.ToString(request.CINNumber));
            SqlParam[10] = new SqlParameter("@PolicyTypeId", Convert.ToInt16(request.PolicyType));
            if (request.PolicyStartDate != null && request.PolicyStartDate > 0 && DateTime.TryParse(Convert.ToInt64(request.PolicyStartDate).ToDateTime().ToString(), out DateTime dt))
            {
                SqlParam[11] = new SqlParameter("@PolicyStartDate", dt);
            }
            if (request.AltContactInformation != null && request.AltContactInformation.Count > 0)
            {
                var info = CoreCommonMethods.SerializeToXml(request.AltContactInformation);
                if (!string.IsNullOrEmpty(info))
                {
                    SqlParam[12] = new SqlParameter("@AltContactInformation", info);
                }
            }
            SqlParam[13] = new SqlParameter("@OtherExistingInsurer", request.OtherExistingInsurer);
            SqlParam[14] = new SqlParameter("@ClaimHistory", request.ClaimHistory);
            SqlParam[15] = new SqlParameter("@ExistingBroker", request.ExistingBroker);
            SqlParam[16] = new SqlParameter("@ExistingInsurer", request.ExistingInsurer);
            SqlParam[17] = new SqlParameter("@ExistingTPA", request.ExistingTPA);
            SqlParam[18] = new SqlParameter("@Probability", request.Probability);
            SqlParam[19] = new SqlParameter("@SumInsured", request.SumInsured);
            SqlParam[20] = new SqlParameter("@NoOfLives", request.NoOfLives);
            SqlParam[21] = new SqlParameter("@CrossSellSubProductIds", request.CrossSellSubProductIds);
            SqlParam[22] = new SqlParameter("@WinningStrategy", request.WinningStrategy);
            SqlParam[23] = new SqlParameter("@OtherExisitngTPA", request.OtherExisitngTPA);
            SqlParam[24] = new SqlParameter("@OtherExistingBroker", request.OtherExistingBroker);
            SqlParam[25] = new SqlParameter("@SubCIN", Convert.ToString(request.SubCIN));
            
            SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertUpdateSMELeadDetails]", SqlParam);

        }
        public static void SchCTCConnectCall(CTCSchdular _CTCSchdular, DateTime ScheduledTime)
        {

            SqlParameter[] SqlParam = new SqlParameter[18];
            SqlParam[0] = new SqlParameter("@MobileNO", _CTCSchdular.MobileNO);
            SqlParam[1] = new SqlParameter("@LeadID", _CTCSchdular.LeadID);
            SqlParam[2] = new SqlParameter("@GroupID", _CTCSchdular.GroupID);
            SqlParam[3] = new SqlParameter("@LeadSource", _CTCSchdular.LeadSource);
            SqlParam[4] = new SqlParameter("@SchTime", ScheduledTime);
            SqlParam[5] = new SqlParameter("@SourceIP", _CTCSchdular.SourceIP);
            SqlParam[6] = new SqlParameter("@issue", _CTCSchdular.Issue);
            SqlParam[7] = new SqlParameter("@subissue", _CTCSchdular.SubIssue);
            SqlParam[8] = new SqlParameter("@IsPicked", _CTCSchdular.IsPicked);
            SqlParam[9] = new SqlParameter("@NotPickedReason", _CTCSchdular.NotPickedReason);
            SqlParam[10] = new SqlParameter("@CTCLeadID", _CTCSchdular.CTCLeadID);
            SqlParam[11] = new SqlParameter("@Comment", _CTCSchdular.Comments);
            SqlParam[12] = new SqlParameter("@callnow", _CTCSchdular.CallNow);
            SqlParam[13] = new SqlParameter("@IsGoogleInvite", _CTCSchdular.IsGoogleInvite);
            SqlParam[14] = new SqlParameter("@ProcessName", _CTCSchdular.ProcessName);
            SqlParam[15] = new SqlParameter("@IssueCode", _CTCSchdular.IssueCode);
            SqlParam[16] = new SqlParameter("@SubIssueCode", _CTCSchdular.SubIssueCode);
            SqlParam[17] = new SqlParameter("@EncryptMobileNo", _CTCSchdular.encryptMobileNo);
            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "CTC.InsertCTCSchedular", SqlParam);

        }
        public static DataSet CheckProgressiveLeadAssignment(long leadId, int inbound)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);
            SqlParam[1] = new SqlParameter("@CTC", 1);
            SqlParam[2] = new SqlParameter("@IB", inbound);
            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetProgLeadAssignDetails]", SqlParam);
        }        

        public static bool SaveFOSApptCallAnalysisAI(FOSApptCallDataAI objFosApptCallData)
        {
            DateTime rqstTime = DateTime.Now;
            try 
            {   
                SqlParameter[] SqlParam = new SqlParameter[17];
                SqlParam[0] = new SqlParameter("@AppointmentId", objFosApptCallData.AppointmentId);
                SqlParam[1] = new SqlParameter("@LeadID", objFosApptCallData.LeadID);
                SqlParam[2] = new SqlParameter("@CallDataId", objFosApptCallData.CallDataId);
                SqlParam[3] = new SqlParameter("@HomeVisitPitch", objFosApptCallData.HomeVisitPitch);
                SqlParam[4] = new SqlParameter("@CustomerAgree", objFosApptCallData.CustomerAgree);
                SqlParam[5] = new SqlParameter("@CustomerRefusal", objFosApptCallData.CustomerRefusal);
                SqlParam[6] = new SqlParameter("@MeetingTime", objFosApptCallData.MeetingTime);
                SqlParam[7] = new SqlParameter("@MeetingAddress", objFosApptCallData.MeetingAddress);
                SqlParam[8] = new SqlParameter("@InvPlanDiscussed", objFosApptCallData.InvPlanDiscussed);
                SqlParam[9] = new SqlParameter("@CustomerWantsNxt", objFosApptCallData.CustomerWantsNxt);
                SqlParam[10] = new SqlParameter("@ReasonForAppt", objFosApptCallData.ReasonForAppt);
                SqlParam[11] = new SqlParameter("@City", objFosApptCallData.City);
                SqlParam[12] = new SqlParameter("@Pincode", objFosApptCallData.Pincode);
                SqlParam[13] = new SqlParameter("@CustSentiment", objFosApptCallData.CustSentiment);
                SqlParam[14] = new SqlParameter("@InterestLevel", objFosApptCallData.InterestLevel);
                SqlParam[15] = new SqlParameter("@PurposeOfVisit", objFosApptCallData.PurposeOfVisit);
                SqlParam[16] = new SqlParameter("@ThirdPartyReceiver", objFosApptCallData.ThirdPartyReceiver);

                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveFOSApptCallAnalysisData]", SqlParam);
                return true;
            } 
            catch (Exception ex) 
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", objFosApptCallData.LeadID, ex.Message, "SaveFOSApptCallAnalysisAI", "WebSiteServiceBLL", "MatrixCoreAPI", "", ex.ToString(), rqstTime, DateTime.Now);
                return false;
            }
        }

        public static bool InsertFOSApptCallAnalysisAI(FOSApptCallDataAI objFosApptCallData)
        {   
            DateTime requestTime = DateTime.Now;

            SqlParameter[] sqlParams = new SqlParameter[]
            {
                new("@AppointmentId", objFosApptCallData.AppointmentId),
                new("@LeadID", objFosApptCallData.LeadID),
                new("@CallDataId", objFosApptCallData.CallDataId),
                new("@ModelGrade", objFosApptCallData.ModelGrade),
                new("@Remarks", objFosApptCallData.Remarks)
            };

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[InsertFOSAppointmentCallAnalysis]", sqlParams);
            
            return true;
        }

        public static DataSet VerifyCallBackDateTime(ShortCallData obj)
        {
            SqlParameter[] SqlParam = new SqlParameter[4];
            SqlParam[0] = new SqlParameter("@LeadId", obj.LeadID);
            SqlParam[1] = new SqlParameter("@CallBackTime", obj.CallBackTime);
            SqlParam[2] = new SqlParameter("@CallDataId", obj.CallDataID);
            SqlParam[3] = new SqlParameter("@CallDate", obj.CallDate);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[VerifyCallBackDateTime]", SqlParam);
            
        }

        public static bool SaveFollowUpComment(long leadID, long userID, string comment, int eventtype)
        {
            bool result = false;
            try
            {
                SqlParameter[] param = new SqlParameter[4];
                param[0] = new SqlParameter("@LeadID", leadID);
                param[1] = new SqlParameter("@UserID", userID);
                param[2] = new SqlParameter("@Comments", comment);
                param[3] = new SqlParameter("@EventType", eventtype);
               
                int res = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveFollowUpComment]", param);
                if (res > 0)
                {
                    result = true;
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }
        public static bool DisableScheduledC2C(long leadId, string source)
        {
            bool result = false;
            try
            {
                SqlParameter[] param = new SqlParameter[2];
                param[0] = new SqlParameter("@LeadID", leadId);
                param[1] = new SqlParameter("@Source", source);

                int res = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[DisableScheduledC2C]", param);
                if (res > 0)
                {
                    result = true;
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }

        public static DataSet GetActiveLeadsForCustomer(string mobileNo, short subProductId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@MobileNo", mobileNo);
            sqlParam[1] = new SqlParameter("@SubProductId", subProductId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetActiveLeadsForCustomer]",
                                            3000,
                                            sqlParam);
        }

        public static DataTable GetCustomerFeatureData(long customerId)
        {
            SqlParameter[] SqlParam = new SqlParameter[1];
            SqlParam[0] = new SqlParameter("@CustomerId", customerId);
            DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerFeatureData]", SqlParam);


            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0) {
                return ds.Tables[0];
            }

            return null;
        }

        public static void SaveCustomerFeatureData(long customerId, string Feature, int Value)
        {
            SqlParameter[] SqlParam = new SqlParameter[3];
            SqlParam[0] = new SqlParameter("@CustomerId", customerId);
            SqlParam[1] = new SqlParameter("@Feature", Feature);
            SqlParam[2] = new SqlParameter("@Value",Value);
            

            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SaveCustomerFeatureData]", SqlParam);
        }
        
        public static DataSet LeadDetailsForWhatsappBot(long customerId, short productId)
        {
            var sqlParam = new SqlParameter[2];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);
            sqlParam[1] = new SqlParameter("@ProductId", productId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),CommandType.StoredProcedure,"[MTX].[LeadDetailsForWhatsappBot]",3000,sqlParam);
        }

        public static DataSet GetLeadDetailsForAI(long leadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", leadId);

            return SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),CommandType.StoredProcedure,"[MTX].[GetLeadDetailsForAI]",3000,sqlParam);
        }

        public static DataTable GetActiveLeadDetails(long CustomerId, int ProductID, string Source)
        {
            DateTime reqdt = DateTime.Now;
            DataTable dt = new DataTable();
            try
            {
                SqlParameter[] sqlparam = new SqlParameter[3];
                sqlparam[0] = new SqlParameter("@CustomerId", CustomerId);
                sqlparam[1] = new SqlParameter("@ProductID", ProductID);
                sqlparam[2] = new SqlParameter("@Source", Source);
                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetActiveLeadsDetailsByCustIdPrdID]", sqlparam);
                if (ds != null && ds.Tables.Count > 0)
                    return ds.Tables[0];

            }
            catch(Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustomerId, ex.Message, "GetActiveLeadDetails", "WebSiteServiceBLL", "MatrixCoreAPI", ProductID.ToString(), ex.ToString(), reqdt, DateTime.Now);
            }
            return dt;
        }
        public static DataTable AssignLeadToAgentByGroupID(long GroupID,long LeadId, int ProductId, int JobId)
        {
            DateTime reqdt = DateTime.Now;
            DataTable dt = new DataTable();
            try
            {
                var sqlParam = new SqlParameter[4];
                sqlParam[0] = new SqlParameter("@GroupId", GroupID);
                sqlParam[1] = new SqlParameter("@LeadID", LeadId);
                sqlParam[2] = new SqlParameter("@ProductID", ProductId);
                sqlParam[3] = new SqlParameter("@JobId", JobId);

                DataSet ds = SqlHelper.ExecuteDataset(ConnectionClass.LivesqlConnection(),
                                          CommandType.StoredProcedure,
                                          "[MTX].[AssignLeadToAgentByGroupID]",
                                          sqlParam);
                if (ds != null && ds.Tables.Count > 0)
                    return ds.Tables[0];
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.Message, "AssignLeadToAgentByGroupID", "WebSiteServiceBLL", "MatrixCoreAPI", GroupID.ToString(), ex.ToString(), reqdt, DateTime.Now);
            }
            return dt;
        }

        public static bool CheckIsLeadInvalidAI (long LeadId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@LeadId", LeadId);
            var result = SqlHelper.ExecuteScalar(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[CheckIsLeadInValidAI]", sqlParam);
            if (result != null)
                return Convert.ToBoolean(result);
            else
                return false;
        }

        public static void SetReferralLeadCreationData(long leadId, string json, long userId, string ActionName)
        {
            SqlParameter[] SqlParam = new SqlParameter[5];
            SqlParam[0] = new SqlParameter("@LeadId", leadId);
            SqlParam[1] = new SqlParameter("@JsonData", json);
            SqlParam[2] = new SqlParameter("@CreatedBy", userId);
            SqlParam[3] = new SqlParameter("@ActionName", ActionName);
            SqlParam[4] = new SqlParameter("@Type", 1);


            SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[GetSetReferralLeadsCreationData]", SqlParam);
        }
        public static bool SetHealthRenewalFOS(long leadId)
        {
            bool result = false;
            try
            {
                SqlParameter[] param = new SqlParameter[1];
                param[0] = new SqlParameter("@LeadID", leadId);
    
                int res = SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[SetHealthRenewalFOS]", param);
                if (res > 0)
                {
                    result = true;
                }
            }
            catch (Exception ex)
            {

            }
            return result;
        }

        public static void UpdateDNCLastIbCallTime(long leadId, DateTime scheduledTime)
        {
            DateTime reqTime = DateTime.Now;
            try
            {
                PriorityModel oPriorityModel = LeadPrioritizationDLL.GetPriorityModelMongo(leadId);
                
                if (oPriorityModel != null && oPriorityModel.DNC != null)
                {
                    MongoHelper objCommDB = new(SingletonClass.OneLeadDB());

                    var update = Update<PriorityModel>.Set(p => p.DNC.LastIbCallTime, scheduledTime);
                    var query = Query<PriorityModel>.EQ(p => p.LeadID, leadId);
                    objCommDB.UpdateDocument(query, update, DataAccessLibrary.MongoCollection.LPDataCollection());
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", leadId, ex.ToString(), "UpdateDNCLastIbCallTime", "WebSiteServicDLL", "MatrixCoreAPI", string.Empty, string.Empty, reqTime, DateTime.Now);
            }
        }

        public static DataSet GetCustomerLeadCityforAI(long customerId)
        {
            var sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CustomerId", customerId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(), CommandType.StoredProcedure, "[MTX].[GetCustomerLeadCityforAI]", 3000, sqlParam);
        }

        public static void UpdateLeadDetailsOnCreateLead(long LeadId, long EnquiryID)
        {
            DateTime reqdt = DateTime.Now;
            try
            {
                SqlParameter[] sqlparam = new SqlParameter[2];
                sqlparam[0] = new SqlParameter("@LeadID", LeadId);
                sqlparam[1] = new SqlParameter("@EnquiryID", EnquiryID);
                SqlHelper.ExecuteNonQuery(ConnectionClass.LivesqlConnection(), CommandType.StoredProcedure, "[MTX].[UpdateLeadDetailsOnCreateLead]", sqlparam);

            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, ex.Message, "UpdateLeadDetailsOnCreateLead", "WebSiteServiceBLL", "MatrixCoreAPI", EnquiryID.ToString(), ex.ToString(), reqdt, DateTime.Now);
            }
        }

        public static DataSet GetLeadMobileNoByCustId(long custId)
        {
            SqlParameter[] sqlParam = new SqlParameter[1];
            sqlParam[0] = new SqlParameter("@CustomerID", custId);

            return SqlHelper.ExecuteDataset(ConnectionClass.ReplicasqlConnection(),
                                            CommandType.StoredProcedure,
                                            "[MTX].[GetMobileNoByCustId]",
                                            3000,
                                            sqlParam);
        }
    }
}